const { Op } = require('sequelize');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_ROLE_EXISTED,
  ERROR_NOT_FOUND,
  ERROR_INVALID_PARAMETER,
} = require('../configs/error.vi');
const { Role, Account } = require('../models');
const { isValidNumber } = require('../utils');
const _ = require('lodash');
const permissionCodesJSON = require('../assets/json/permission_list.json');
const { ACCOUNT_LEVEL } = require('../configs/constants/account.constant');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */

  async create(req, res, next) {
    try {
      const { name, permissionCodes } = req.body;
      if (!name) throw new Error(ERROR_MISSING_PARAMETERS);

      // check invalid permission
      if (permissionCodes && !_.isArray(permissionCodes))
        throw new Error(ERROR_INVALID_PARAMETER);

      permissionCodes.map(permissionCode => {
        if (
          !permissionCodesJSON.some(
            permission => permission.permissionCode === permissionCode,
          )
        )
          throw new Error(ERROR_INVALID_PARAMETER);
      });

      // check role is existed
      const role = await Role.findOne({
        where: { name, companyId: req.account.companyId },
      });

      if (role) throw new Error(ERROR_ROLE_EXISTED);

      const newRole = await Role.create({
        name,
        companyId: req.account.companyId,
        permissionCodes,
      });

      res.send({
        result: 'success',
        newRole,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      const { roleId } = req.params;

      const role = await Role.findOne({
        where: { roleId, companyId: req?.account?.companyId },
      });

      if (!role) throw new Error(ERROR_NOT_FOUND);
      return res.send({
        result: 'success',
        role,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let { q, limit, page, orderField, orderMode } = req.query;

      q = q ?? '';
      orderField = orderField ?? 'updatedAt';
      orderMode = orderMode ?? 'DESC';

      if (!isValidNumber(limit) || !isValidNumber(page)) {
        let rs = (
          await Role.findAll({
            where: {
              name: { [Op.like]: `%${q}%` },
              companyId: req.account.companyId,
            },
            include: [{ model: Account }],
            order: [[orderField, orderMode]],
          })
        ).map(r => r.toJSON());

        rs = rs.map(item => {
          item.totalEmployees = item.Accounts.length;

          delete item.Accounts;
          return item;
        });

        const permissionCode = require('../assets/json/permission_list.json');

        rs = _.concat(
          [
            {
              name: 'Quản trị viên',
              readonly: true,
              companyId: req.account.companyId,
              totalEmployees: 1,
              roleId: -1,
              permissionCodes: require('../assets/json/permission_list.json'),
            },
            {
              name: 'Người dùng thường',
              readonly: true,
              companyId: req.account.companyId,
              totalEmployees: (
                await Account.findAll({
                  where: {
                    companyId: req.account.companyId,
                    roleId: { [Op.is]: null },
                    accountLevel: ACCOUNT_LEVEL.COMPANY_USER,
                  },
                })
              ).length,
              roleId: 0,
              permissionCodes: [],
            },
          ],
          rs,
        );

        res.send({ result: 'success', role: rs });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        let rs = await Role.findAndCountAll({
          where: {
            name: { [Op.like]: `%${q}%` },
            companyId: req.account.companyId,
          },
          include: [{ model: Account }],
          limit,
          offset: limit * page,
          order: [[orderField, orderMode]],
        });

        rs.rows = rs.rows
          .map(item => item.toJSON())
          .map(item => {
            item.totalEmployees = item.Accounts.length;

            delete item.Accounts;
            return item;
          });

        res.send({
          result: 'success',
          page,
          total: rs.count,
          count: rs.rows.length,
          instructions: rs.rows,
        });
      }
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    try {
      let { roleId } = req.params;

      let role = await Role.findOne({
        where: { roleId, companyId: req.account.companyId },
      });

      if (!role) throw new Error(ERROR_NOT_FOUND);

      let { name, permissionCodes } = req.body;

      // check invalid permission
      if (permissionCodes && !_.isArray(permissionCodes))
        throw new Error(ERROR_INVALID_PARAMETER);

      permissionCodes.map(permissionCode => {
        if (
          !permissionCodesJSON.some(
            permission => permission.permissionCode === permissionCode,
          )
        )
          throw new Error(ERROR_INVALID_PARAMETER);
      });

      if (name) {
        if (
          !!(await Role.findOne({
            where: {
              name,
              companyId: req.account.companyId,
              roleId: { [Op.ne]: roleId },
            },
          }))
        ) {
          throw new Error(ERROR_ROLE_EXISTED);
        }
      }

      await role.update({ name, permissionCodes });

      res.send({ result: 'success', role });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    try {
      let { roleIds } = req.body;

      if (!_.isArray(roleIds)) throw new Error(ERROR_INVALID_PARAMETER);

      let deleteCount = await Role.destroy({
        where: {
          roleId: { [Op.in]: roleIds },
          companyId: req.account.companyId,
        },
      });

      res.send({ result: 'success', deleteCount });
    } catch (error) {
      next(error);
    }
  },
};
