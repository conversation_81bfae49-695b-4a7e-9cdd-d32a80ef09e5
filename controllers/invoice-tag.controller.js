const { Op } = require('sequelize');
const {
  ERROR_NOT_FOUND,
  ERROR_INVALID_PARAMETER,
  ERROR_MISSING_PARAMETERS,
} = require('../configs/error.vi');
const { InvoiceTag, Tag, Invoice } = require('../models');
const _ = require('lodash');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    try {
      let { invoiceIds, tagIds } = req.body;

      let { organizationId, organizationDepartmentId } = req.account;

      if (!_.isArray(invoiceIds)) throw new Error(ERROR_MISSING_PARAMETERS);
      if (!_.isArray(tagIds)) throw new Error(ERROR_INVALID_PARAMETER);

      const findInvoices = await Invoice.findAll({
        where: {
          organizationId,
          organizationDepartmentId,
          invoiceId: { [Op.in]: invoiceIds },
        },
      });

      invoiceIds = findInvoices.map(item => item.invoiceId);
      await InvoiceTag.destroy({
        where: {
          [Op.and]: [
            {
              invoiceId: { [Op.in]: invoiceIds },
            },
          ],
        },
      });

      let tags = await Tag.findAll({
        where: {
          accountId: req.account.accountId,
          tagId: { [Op.in]: tagIds },
        },
      });

      tagIds = tags.map(i => i.tagId);

      let invoiceTags = await InvoiceTag.bulkCreate(
        _.concat(
          ...invoiceIds.map(invoiceId =>
            tagIds.map(tagId => ({ tagId, invoiceId })),
          ),
        ),
        { ignoreDuplicates: true },
      );

      res.send({
        result: 'success',
        invoiceTags,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    try {
      let { invoiceTagIds } = req.body;

      if (!_.isArray(invoiceTagIds)) throw new Error(ERROR_INVALID_PARAMETER);

      let invoiceTags = await InvoiceTag.findAll({
        where: { invoiceTagId: { [Op.in]: invoiceTagIds } },
        include: [
          { model: Tag, where: [{ accountId: req.account.accountId }] },
        ],
      });

      invoiceTagIds = invoiceTags.map(item => item.tagId);

      const deleteCount = await InvoiceTag.destroy({
        where: { invoiceTagId: { [Op.in]: invoiceTagIds } },
      });

      res.send({
        result: 'success',
        deleteCount,
      });
    } catch (error) {
      next(error);
    }
  },
};
