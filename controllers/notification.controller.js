const { Op } = require('sequelize');
const _ = require('lodash');
const fs = require('fs');
const { isValidNumber, isValidTaxCode } = require('../utils');
const {
  Notification,
  Sequelize,
  Account,
  AccountNotification,
} = require('../models');
const {
  ERROR_NOT_FOUND,
  ERROR_INVALID_PARAMETER,
  ERROR_MISSING_PARAMETERS,
  ERROR_USER_NOT_FOUND,
} = require('../configs/error.vi');
const { PUBLIC_UPLOAD_DIR } = require('../configs/env');
const utils = require('../utils');
const path = require('path');
const {
  NOTIFICATION_TYPE,
} = require('../configs/constants/notification.constant');
const moment = require('moment');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { description, url, title, type, thumbnail, accountIds } = req.body;

      if (!title || !type) throw new Error(ERROR_MISSING_PARAMETERS);

      if (!Object.values(NOTIFICATION_TYPE).includes(type))
        throw new Error(ERROR_INVALID_PARAMETER);

      if (
        [NOTIFICATION_TYPE.ACTIVITY, NOTIFICATION_TYPE.PROMOTION].includes(type)
      ) {
        if (type == NOTIFICATION_TYPE.ACTIVITY) {
          if (!_.isArray(accountIds) || !accountIds.length) {
            throw new Error(ERROR_MISSING_PARAMETERS);
          }
        } else {
          if (accountIds && (!_.isArray(accountIds) || !accountIds.length)) {
            throw new Error(ERROR_MISSING_PARAMETERS);
          }
        }

        if (accountIds) {
          let accounts = await Account.findAll({
            where: { accountId: { [Op.in]: accountIds } },
          });

          if (
            accountIds.some(
              accountId => !accounts.find(item => item.accountId == accountId),
            )
          ) {
            throw new Error(ERROR_USER_NOT_FOUND);
          }

          accountIds = accounts.map(item => item.accountId);
        }
      } else {
        accountIds = undefined;
      }

      let notification = await Notification.create(
        {
          description,
          url,
          title,
          type,
          thumbnail,
        },
        { transaction: t },
      );

      if (req.files?.['thumbnail']) {
        let file = req.files['thumbnail'];
        if (_.isArray(file)) file = file[0];

        const filepath = `${PUBLIC_UPLOAD_DIR}/notification-thumbnail/${notification.notificationId}`;
        const filename = utils.escapeFilename(file.name);

        if (fs.existsSync(filepath)) fs.rmSync(filepath, { recursive: true });
        fs.mkdirSync(filepath);

        await file.mv(path.join(filepath, filename));

        thumbnail = `resources/notification-thumbnail/${notification.notificationId}/${filename}`;

        await notification.update({ thumbnail }, { transaction: t });
      }

      if (accountIds) {
        await AccountNotification.bulkCreate(
          accountIds.map(accountId => ({
            accountId,
            notificationId: notification.notificationId,
          })),
          { transaction: t },
        );
      }

      await t.commit();

      return res.send({
        result: 'success',
        notification,
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let { q, limit, page, orderField, orderMode, type, from, to } = req.query;

      q = q ?? '';
      orderField = orderField ?? 'updatedAt';
      orderMode = orderMode ?? 'DESC';

      if (!utils.isValidDate(from)) from = undefined;
      if (!utils.isValidDate(to)) to = undefined;

      let typeOptions = [];

      if (_.isString(type)) {
        if (type.includes('|')) {
          typeOptions = type.split('|').map(item => _.trim(item));
        } else {
          typeOptions = [type];
        }
      }

      /**
       * @type {import('sequelize').WhereOptions<import('../types').Notification>}
       */
      let conditions = {
        [Op.or]: [
          {
            description: { [Op.like]: `%${q}%` },
          },
          { title: { [Op.like]: `%${q}%` } },
        ],
        [Op.and]: [
          typeOptions.length >= 1 ? { type: { [Op.in]: typeOptions } } : {},
          from
            ? {
                createdAt: {
                  [Op.gte]: moment(from).startOf('date').toString(),
                },
              }
            : {},
          to
            ? { createdAt: { [Op.lte]: moment(to).endOf('date').toString() } }
            : {},
          {
            [Op.or]: [
              req.account
                ? {
                    type: NOTIFICATION_TYPE.ACTIVITY,
                    '$Accounts.accountId$': req.account.accountId,
                  }
                : {},
              {
                type: NOTIFICATION_TYPE.PROMOTION,
                [Op.or]: [
                  req.account
                    ? {
                        '$Accounts.accountId$': req.account.accountId,
                      }
                    : {},

                  {
                    '$Accounts.accountId$': { [Op.is]: null },
                  },
                ],
              },
              {
                type: NOTIFICATION_TYPE.SYSTEM,
              },
            ],
          },
        ],
      };

      let totalNotifications = await Notification.count({
        include: [{ model: Account, attributes: [] }],
        distinct: true,
        where: [
          {
            [Op.or]: [
              req.account
                ? {
                    type: NOTIFICATION_TYPE.ACTIVITY,
                    '$Accounts.accountId$': req.account.accountId,
                  }
                : {},
              {
                type: NOTIFICATION_TYPE.PROMOTION,
                [Op.or]: [
                  req.account
                    ? {
                        '$Accounts.accountId$': req.account.accountId,
                      }
                    : {},
                  {
                    '$Accounts.accountId$': { [Op.is]: null },
                  },
                ],
              },
              {
                type: NOTIFICATION_TYPE.SYSTEM,
              },
            ],
          },
        ],
      });

      if (!isValidNumber(limit) || !isValidNumber(page)) {
        let rs = (
          await Notification.findAll({
            where: conditions,
            include: [
              {
                model: Account,
                attributes: [],
              },
            ],
            order: [[orderField, orderMode]],
          })
        ).map(notification => notification.toJSON());

        res.send({
          result: 'success',
          notifications: rs,
          total: rs.length,
          totalNotifications,
        });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        let ns = await Notification.findAndCountAll({
          where: conditions,
          include: [{ model: Account, attributes: [] }],
          limit,
          offset: limit * page,
          order: [[orderField, orderMode]],
          subQuery: false,
          distinct: true,
        });

        res.send({
          result: 'success',
          page,
          total: ns.count,
          count: ns.rows.length,
          notifications: ns.rows,
          totalNotifications,
        });
      }
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      const { notificationId } = req.params;

      let { thumbnail, description, url, title, accountIds } = req.body;

      const notification = await Notification.findOne({
        where: { notificationId },
      });
      if (!notification) throw new Error(ERROR_NOT_FOUND);

      if (
        [NOTIFICATION_TYPE.ACTIVITY, NOTIFICATION_TYPE.PROMOTION].includes(
          notification.type,
        )
      ) {
        if (accountIds) {
          if (utils.isJSONArray(accountIds))
            accountIds = _.toArray(JSON.parse(accountIds));

          if (!_.isArray(accountIds) || !accountIds.length)
            throw new Error(ERROR_MISSING_PARAMETERS);

          let accounts = await Account.findAll({
            where: { accountId: { [Op.in]: accountIds } },
          });

          if (
            accountIds.some(
              accountId => !accounts.find(item => item.accountId == accountId),
            )
          ) {
            throw new Error(ERROR_USER_NOT_FOUND);
          }

          accountIds = accounts.map(item => item.accountId);
        }
      } else {
        accountIds = undefined;
      }

      if (req.files?.['thumbnail']) {
        let file = req.files['thumbnail'];
        if (_.isArray(file)) file = file[0];

        const filepath = `${PUBLIC_UPLOAD_DIR}/notification-thumbnail/${notification.notificationId}`;
        const filename = utils.escapeFilename(file.name);

        if (fs.existsSync(filepath)) fs.rmSync(filepath, { recursive: true });
        fs.mkdirSync(filepath);

        await file.mv(path.join(filepath, filename));

        thumbnail = `resources/notification-thumbnail/${notification.notificationId}/${filename}`;
      }

      await notification.update(
        {
          thumbnail: thumbnail ?? thumbnail,
          description,
          url,
          title,
        },
        {
          transaction: t,
        },
      );

      if (accountIds) {
        await AccountNotification.destroy({
          where: {
            accountId: { [Op.in]: accountIds },
            notificationId: notification.notificationId,
          },
        });

        await AccountNotification.bulkCreate(
          accountIds.map(accountId => ({
            accountId,
            notificationId: notification.notificationId,
          })),
          { transaction: t },
        );
      }

      await t.commit();

      return res.send({
        result: 'success',
        notification,
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    try {
      const { notificationIds } = req.body;
      if (!_.isArray(notificationIds)) throw new Error(ERROR_INVALID_PARAMETER);

      let deleteCount = await Notification.destroy({
        where: {
          notificationId: { [Op.in]: notificationIds },
        },
      });

      return res.send({
        result: 'success',
        deleteCount,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      const { notificationId } = req.params;
      const notification = await Notification.findOne({
        where: { notificationId },
      });
      if (!notification) throw new Error(ERROR_NOT_FOUND);
      return res.send({
        result: 'success',
        notification,
      });
    } catch (error) {
      next(error);
    }
  },
};
