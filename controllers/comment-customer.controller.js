const { CommentCustomer, Sequelize } = require('../models');
const _ = require('lodash');
const utils = require('../utils');
const fs = require('fs');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_INVALID_PARAMETER,
} = require('../configs/error.vi');
const { Op } = require('sequelize');
const { PUBLIC_UPLOAD_DIR } = require('../configs/env');
const path = require('path');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let { limit, page, display } = req.query;
      let objCommentCustomers;
      limit = _.toNumber(limit);
      page = _.toNumber(page);
      let condition = {};
      if (display !== undefined) {
        condition.display = utils.parseBoolean(display);
      }
      if (!utils.isValidNumber(limit) || !utils.isValidNumber(page)) {
        page = undefined;
        limit = undefined;
        objCommentCustomers = await CommentCustomer.findAndCountAll({
          where: condition,
        });
      } else {
        objCommentCustomers = await CommentCustomer.findAndCountAll({
          where: condition,
          limit,
          offset: limit * page,
        });
      }

      res.send({
        result: 'success',
        page,
        total: objCommentCustomers.count,
        count: objCommentCustomers.rows.length,
        commentCustomers: objCommentCustomers.rows,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      let { commentCustomerId } = req.params;

      let commentCustomer = (
        await CommentCustomer.findOne({
          where: {
            commentCustomerId,
          },
        })
      )?.toJSON();

      res.send({
        result: 'success',
        commentCustomer,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    const t = await Sequelize.transaction();

    try {
      let {
        commentCustomerContent,
        customerName,
        customerPosition,
        customerAvatar,
        display,
      } = req.body;

      if (!commentCustomerContent || !customerName || !customerPosition)
        throw new Error(ERROR_MISSING_PARAMETERS);
      if (display !== undefined) {
        display = utils.parseBoolean(display);
      }

      let commentCustomer = await CommentCustomer.create(
        {
          commentCustomerContent,
          customerName,
          customerPosition,
          display,
          customerAvatar: null,
        },
        { transaction: t },
      );

      if (req.files?.['customerAvatar']) {
        let file = req.files['customerAvatar'];

        if (_.isArray(file)) file = file[0];

        let filepath = `${PUBLIC_UPLOAD_DIR}/avatar-customer/${commentCustomer.commentCustomerId}`;
        let filename = `${file.name}`;

        filename = utils.escapeFilename(filename);

        if (fs.existsSync(filepath)) fs.rmSync(filepath, { recursive: true });
        fs.mkdirSync(filepath);

        await file.mv(path.join(filepath, filename));

        customerAvatar = `resources/avatar-customer/${commentCustomer.commentCustomerId}/${filename}`;
        await commentCustomer.update({ customerAvatar }, { transaction: t });
      }
      await t.commit();

      res.send({
        result: 'success',
        commentCustomer,
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { commentCustomerId } = req.params;
      const { companyId } = req.account;

      let {
        commentCustomerContent,
        customerName,
        customerPosition,
        customerAvatar,
        display,
      } = req.body;
      const commentCustomer = await CommentCustomer.findOne({
        where: { commentCustomerId, companyId },
      });
      let update = {};
      if (commentCustomerContent || commentCustomerContent === '')
        update.commentCustomerContent = commentCustomerContent;
      if (customerName || customerName === '')
        update.customerName = customerName;
      if (customerPosition || customerPosition === '')
        update.customerPosition = customerPosition;
      if (display !== undefined) {
        update.display = utils.parseBoolean(display);
      }
      if (req.files?.['customerAvatar']) {
        let file = req.files['customerAvatar'];

        if (_.isArray(file)) file = file[0];

        let filepath = `${PUBLIC_UPLOAD_DIR}/avatar-customer/${commentCustomer.commentCustomerId}`;
        let filename = `${file.name}`;

        filename = utils.escapeFilename(filename);

        if (fs.existsSync(filepath)) fs.rmSync(filepath, { recursive: true });
        fs.mkdirSync(filepath);

        await file.mv(path.join(filepath, filename));

        customerAvatar = `resources/avatar-customer/${commentCustomer.commentCustomerId}/${filename}`;
        update.customerAvatar = customerAvatar;
      }
      await commentCustomer.update(update, { transaction: t });

      await t.commit();

      res.send({
        result: 'success',
        commentCustomer,
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    const t = await Sequelize.transaction();

    try {
      let { commentCustomerIds } = req.body;

      if (!_.isArray(commentCustomerIds))
        throw new Error(ERROR_INVALID_PARAMETER);
      let deleteCount = await CommentCustomer.destroy({
        where: {
          commentCustomerId: { [Op.in]: commentCustomerIds },
        },
        transaction: t,
      });
      await t.commit();

      return res.send({
        result: 'success',
        deleteCount,
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
};
