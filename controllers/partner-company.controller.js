/* eslint-disable @typescript-eslint/no-unused-vars */
const { Op } = require('sequelize');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_INVALID_TAXCODE,
  ERROR_NOT_FOUND,
  ERROR_INVALID_PARAMETER,
  ERROR_INVALID_PHONE,
  ERROR_INVALID_EMAIL,
  ERROR_INVALID_DATE,
  ERROR_TAXCODE_EXISTED,
  ERROR_USER_NOT_IN_ORGANIZATION,
  ERROR_INTERNAL,
  ERROR_INVALID_FILE,
  ERROR_UNITCODE_EXISTED,
} = require('../configs/error.vi');
const {
  PartnerCompany,
  RiskyCompany,
  Invoice,
  Notification,
  AccountNotification,
  AcceptPartnerCompanyDetail,
  Account,
  Sequelize,
  BusinessCompany,
  Business,
} = require('../models');
const {
  isValidTaxCode,
  isValid<PERSON>umber,
  isValid<PERSON><PERSON>,
  isValidEmail,
  isValidDate,
} = require('../utils');
const _ = require('lodash');
const {
  PARTNER_COMPANY_STATUS,
  PARTNER_COMPANY_TYPE,
} = require('../configs/constants/company.constant');
const { WS_CODE } = require('../configs/constants/websocket.constant');
const {
  TARGET_CODE,
  NOTIFICATION_TYPE,
} = require('../configs/constants/notification.constant');
const moment = require('moment');
const { cqt_regdStatus } = require('../utils/cqt.helper');
const notificationHelper = require('../utils/notification.helper');
const { emitSocketEvent } = require('../utils/websocket.helper');
const nntHelper = require('../utils/nnt.helper');
const utils = require('../utils');
const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');
const { PUBLIC_UPLOAD_DIR } = require('../configs/env');
const {
  SHEET_NAME_SAMPLE,
} = require('../configs/constants/local-path.constant');
const partnerCompanyHelper = require('../utils/partner-company.helper');
const { getPublicAccountAttributes } = require('../utils/account.helper');
module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    try {
      let {
        taxCode,
        unitCode,
        partnerCompanyName,
        bankAccountNumber,
        bankAccountName,
        bank,
        bankBranch,
        expirePayment,
        contactName,
        contactEmail,
        contactPhone,
        contactPosition,
        dateCheck,
        status,
        address,
      } = req.body;
      const { accountId, organizationId, organizationDepartmentId } =
        req.account;
      if (!accountId || !taxCode || !partnerCompanyName)
        throw new Error(ERROR_MISSING_PARAMETERS);
      if (!isValidTaxCode(taxCode)) throw new Error(ERROR_INVALID_TAXCODE);
      if (contactPhone && !isValidPhone(contactPhone))
        throw new Error(ERROR_INVALID_PHONE);
      if (contactEmail && !isValidEmail(contactEmail))
        throw new Error(ERROR_INVALID_EMAIL);
      if (dateCheck && !isValidDate(dateCheck))
        throw new Error(ERROR_INVALID_DATE);

      const findPartnerCompany = await PartnerCompany.findOne({
        where: { taxCode, organizationId, organizationDepartmentId },
      });
      if (findPartnerCompany) throw new Error(ERROR_TAXCODE_EXISTED);

      if (unitCode) {
        const findUnitCodePartnerCompany = await PartnerCompany.findOne({
          where: { unitCode, organizationId, organizationDepartmentId },
        });
        if (findUnitCodePartnerCompany) throw new Error(ERROR_UNITCODE_EXISTED);
      } else {
        unitCode = null;
      }

      const partnerCompany = await PartnerCompany.create({
        taxCode,
        organizationId,
        organizationDepartmentId,
        unitCode,
        partnerCompanyName,
        bankAccountNumber,
        bankAccountName,
        bank,
        bankBranch,
        expirePayment,
        contactName,
        contactEmail,
        contactPhone,
        contactPosition,
        dateCheck,
        status,
        address,
      });
      return res.send({
        result: 'success',
        partnerCompany,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      const { partnerCompanyId } = req.params;
      const { organizationId, organizationDepartmentId } = req.account;

      const partnerCompany = await PartnerCompany.findOne({
        where: {
          partnerCompanyId,
          organizationDepartmentId,
          organizationId,
        },
      });

      if (!partnerCompany) throw new Error(ERROR_NOT_FOUND);
      return res.send({
        result: 'success',
        partnerCompany,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      const { partnerCompanyId } = req.params;
      const { organizationDepartmentId, organizationId } = req.account;
      const partnerCompany = await PartnerCompany.findOne({
        where: { partnerCompanyId, organizationDepartmentId, organizationId },
      });

      let {
        unitCode,
        partnerCompanyName,
        bankAccountNumber,
        bankAccountName,
        bank,
        bankBranch,
        expirePayment,
        contactName,
        contactEmail,
        contactPhone,
        contactPosition,
        status,
        address,
        acceptPartnerCompanyDetails,
      } = req.body;

      if (!partnerCompany) throw new Error(ERROR_NOT_FOUND);
      if (contactPhone && !isValidPhone(contactPhone))
        throw new Error(ERROR_INVALID_PHONE);
      if (contactEmail && !isValidEmail(contactEmail))
        throw new Error(ERROR_INVALID_EMAIL);

      if (partnerCompanyName === '' || partnerCompanyName === ' ')
        throw new Error(ERROR_INVALID_PARAMETER);

      if (unitCode) {
        const findUnitCodePartnerCompany = await PartnerCompany.findOne({
          where: {
            unitCode,
            organizationId,
            organizationDepartmentId,
            partnerCompanyId: { [Op.ne]: partnerCompanyId },
          },
          transaction: t,
        });
        if (findUnitCodePartnerCompany) throw new Error(ERROR_UNITCODE_EXISTED);
      } else {
        unitCode = null;
      }

      await partnerCompany.update(
        {
          unitCode,
          partnerCompanyName,
          bankAccountNumber,
          bankAccountName,
          bank,
          bankBranch,
          expirePayment,
          contactName,
          contactEmail,
          contactPhone,
          contactPosition,
          status,
          address,
        },
        { transaction: t },
      );

      if (_.isArray(acceptPartnerCompanyDetails)) {
        if (
          acceptPartnerCompanyDetails.some(item => {
            let { acceptName, acceptAddress, from, to } = item;

            if (!acceptName && !acceptAddress) return true;

            if (!isValidDate(from) || !isValidDate(to)) return true;

            return false;
          })
        ) {
          throw new Error(ERROR_MISSING_PARAMETERS);
        }

        await AcceptPartnerCompanyDetail.destroy({
          where: {
            taxCode: partnerCompany.taxCode,
            organizationDepartmentId,
            organizationId,
          },
          transaction: t,
        });

        let _acceptPartnerCompanyDetails =
          await AcceptPartnerCompanyDetail.bulkCreate(
            acceptPartnerCompanyDetails.map(item => {
              return {
                taxCode: partnerCompany.taxCode,
                acceptBy: req.account.accountId,
                acceptAddress: item.acceptAddress,
                acceptName: item.acceptName,
                from: item.from,
                to: item.to,
                organizationDepartmentId,
                organizationId,
              };
            }),
            {
              transaction: t,
            },
          );

        _acceptPartnerCompanyDetails = _acceptPartnerCompanyDetails.map(
          item => {
            item.Account = _.pick(req.account, getPublicAccountAttributes());

            return item;
          },
        );

        partnerCompany.setDataValue(
          'acceptPartnerCompanyDetails',
          _acceptPartnerCompanyDetails,
        );
      }

      await t.commit();

      res.send({
        result: 'success',
        partnerCompany,
      });
    } catch (error) {
      await t.rollback();

      console.log(error);
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let { q, limit, page, orderField, orderMode, status, type } = req.query;
      let { organizationId, organizationDepartmentId, taxCode } = req.account;

      q = q ?? '';
      orderField = orderField ?? 'updatedAt';
      orderMode = orderMode ?? 'DESC';

      if (status && !Object.values(PARTNER_COMPANY_STATUS).includes(status))
        throw new Error(ERROR_INVALID_PARAMETER);

      /**
       * @type {import('sequelize').WhereOptions<import('../types').PartnerCompany>}
       */
      let conditions = {
        [Op.and]: [
          {
            [Op.or]: [
              {
                partnerCompanyName: { [Op.like]: `%${q}%` },
              },
              { partnerCompanyId: { [Op.like]: `%${q}%` } },
              { unitCode: { [Op.like]: `%${q}%` } },
              { status: { [Op.like]: `%${q}%` } },
              { contactPhone: { [Op.like]: `%${q}%` } },
              { contactName: { [Op.like]: `%${q}%` } },
              { contactEmail: { [Op.like]: `%${q}%` } },
              { taxCode: { [Op.like]: `%${q}%` } },
            ],
          },
          { organizationDepartmentId, organizationId },
          status
            ? {
                status: status,
              }
            : {},
          // type == PARTNER_COMPANY_TYPE.BUYER
          //   ? { buyInvoices: { [Op.gt]: 0 } }
          //   : {},
          // type == PARTNER_COMPANY_TYPE.SELLER
          //   ? { sellInvoices: { [Op.gt]: 0 } }
          //   : {},
          // type == `${PARTNER_COMPANY_TYPE.BUYER}-${PARTNER_COMPANY_TYPE.SELLER}`
          //   ? {
          //       [Op.or]: [
          //         { buyInvoices: { [Op.gt]: 0 } },
          //         { sellInvoices: { [Op.gt]: 0 } },
          //       ],
          //     }
          //   : {},
        ],
      };

      let pcs = (
        await PartnerCompany.findAll({
          where: conditions,
          order: [[orderField, orderMode]],
        })
      ).map(r => r.toJSON());

      let riskyCompanyDict = (
        await RiskyCompany.findAll({
          where: { taxCode: { [Op.in]: pcs.map(item => item.taxCode) } },
        })
      ).reduce((prev, curr) => {
        prev[curr.taxCode] = true;

        return prev;
      }, {});

      pcs = pcs.map(item => {
        item.isRisky = !!riskyCompanyDict[item.taxCode];

        return item;
      });

      let invoices = await Invoice.findAll({
        where: {
          [Op.or]: [
            { sellerTaxCode: { [Op.in]: pcs.map(item => item.taxCode) } },
            { buyerTaxCode: { [Op.in]: pcs.map(item => item.taxCode) } },
          ],
          organizationDepartmentId,
          organizationId,
        },
      });

      pcs = pcs.map(item => {
        item.sellInvoices = invoices.filter(
          i => i.sellerTaxCode == taxCode && i.buyerTaxCode == item.taxCode,
        ).length;
        item.buyInvoices = invoices.filter(
          i => i.buyerTaxCode == taxCode && i.sellerTaxCode == item.taxCode,
        ).length;

        return item;
      });

      if (type) {
        if (type == PARTNER_COMPANY_TYPE.BUYER) {
          pcs = pcs.filter(item => item.sellInvoices > 0);
        } else if (type == PARTNER_COMPANY_TYPE.SELLER) {
          pcs = pcs.filter(item => item.buyInvoices > 0);
        } else if (
          type == `${PARTNER_COMPANY_TYPE.BUYER}_${PARTNER_COMPANY_TYPE.SELLER}`
        ) {
          pcs = pcs.filter(
            item => item.sellInvoices > 0 || item.buyInvoices > 0,
          );
        }
      }

      let total = pcs.length;

      let taxCodes = pcs.map(item => item.taxCode);

      const acceptPartnerCompanyDetails =
        await AcceptPartnerCompanyDetail.findAll({
          where: {
            organizationDepartmentId,
            organizationId,
            taxCode: { [Op.in]: taxCodes },
          },
          include: [
            { model: Account, attributes: getPublicAccountAttributes() },
          ],
          order: [
            ['from', 'ASC'],
            ['to', 'DESC'],
          ],
        });

      pcs = pcs.map(pc => {
        pc.acceptPartnerCompanyDetails = acceptPartnerCompanyDetails.filter(
          item => item.taxCode == pc.taxCode,
        );
        return pc;
      });

      if (!isValidNumber(limit) || !isValidNumber(page)) {
        res.send({
          result: 'success',
          partnerCompanies: pcs,
          total,
        });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        pcs = pcs.slice(limit * page, limit * page + limit);

        res.send({
          result: 'success',
          page,
          total: total,
          count: pcs.length,
          partnerCompanies: pcs,
        });
      }
    } catch (error) {
      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    try {
      let { partnerCompanyIds } = req.body;
      let { organizationId, organizationDepartmentId } = req.account;

      if (!_.isArray(partnerCompanyIds))
        throw new Error(ERROR_INVALID_PARAMETER);

      let deleteCount = await PartnerCompany.destroy({
        where: {
          partnerCompanyId: { [Op.in]: partnerCompanyIds },
          organizationId,
          organizationDepartmentId,
        },
      });
      return res.send({
        result: 'success',
        deleteCount,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async synchronize(req, res, next) {
    try {
      let { organizationId, organizationDepartmentId, taxCode } = req.account;

      if (!organizationId && !organizationDepartmentId) {
        throw new Error(ERROR_USER_NOT_IN_ORGANIZATION);
      }

      res.send({ result: 'success' });
      /* TODO: sync from invoice */

      await partnerCompanyHelper.synchronize({
        organizationDepartmentId,
        organizationId,
        taxCode,
      });

      try {
        let [noti] =
          await notificationHelper.createNotificationAndSendSyncedEvent([
            {
              type: NOTIFICATION_TYPE.ACTIVITY,
              targetCode: TARGET_CODE.PARTNER_SYNC,
              title: `Hoàn tất kiểm tra Nhà cung cấp/Khách hàng`,
              Accounts: [{ accountId: req.account.accountId }],
            },
          ]);
      } catch (error) {}
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async importFile(req, res, next) {
    try {
      let { organizationDepartmentId, organizationId, taxCode } = req.account;

      if (!organizationDepartmentId && !organizationId) {
        throw new Error(ERROR_USER_NOT_IN_ORGANIZATION);
      }

      let { file } = req.files;
      if (!req.files['file']) throw new Error(ERROR_MISSING_PARAMETERS);

      let importFile = req.files['file'];
      if (_.isArray(file)) importFile = file[0];

      file = XLSX.readFile(importFile.tempFilePath);

      let json = XLSX.utils.sheet_to_json(file.Sheets[SHEET_NAME_SAMPLE], {
        header: [
          'STT',
          'Mã đơn vị',
          'Mã số thuế',
          'Tên nhà cung cấp/khách hàng',
          'Địa chỉ',
          'Hạn thanh toán (ngày)',
          'Số tài khoản',
          'Tên tài khoản',
          'Tên ngân hàng',
          'Chi nhánh',
          'Người liên hệ',
          'Email',
          'Số điện thoại',
          'Chức vụ',
        ],
        blankrows: true,
      });

      let json_ = [];
      //  let header = json[1];
      for (let i = 3; i < json.length; i++) {
        if (
          json[i]['Mã số thuế'] !== taxCode &&
          json[i]['Tên nhà cung cấp/khách hàng']
        ) {
          json_.push(json[i]);
        }
      }

      // 1: 'Mã đơn vị'
      // 2: 'Mã số thuế'
      // 3: 'Tên nhà cung cấp/khách hàng'
      // 4: 'Địa chỉ'
      // 5: 'Địa chỉ'
      // 6: 'Hạn thanh toán (ngày)'
      // 7: 'Số tài khoản'
      // 8: 'Tên tài khoản'
      // 9: 'Tên ngân hàng'
      // 10: 'Chi nhánh'
      // 11: 'Người liên hệ'
      // 12: 'Email'
      // 13: 'Số điện thoại'
      // 14: 'Chức vụ'

      let rs = await PartnerCompany.bulkCreate(
        json_.map(item => ({
          unitCode: item['Mã đơn vị'],
          taxCode: item['Mã số thuế'],
          partnerCompanyName: item['Tên nhà cung cấp/khách hàng'],
          address: item['Địa chỉ'],
          // partnerCompanyName: item['Địa chỉ'],
          expirePayment: isValidNumber(item['Hạn thanh toán (ngày)'])
            ? item['Hạn thanh toán (ngày)']
            : undefined,
          bankAccountNumber: item['Số tài khoản'],
          bankAccountName: item['Tên tài khoản'],
          bank: item['Tên ngân hàng'],
          bankBranch: item['Chi nhánh'],
          contactName: item['Người liên hệ'],
          contactEmail: item['Email'],
          contactPhone: item['Số điện thoại'],
          contactPosition: item['Chức vụ'],
          organizationDepartmentId,
          organizationId,
        })),
        {
          updateOnDuplicate: [
            'address',
            'contactName',
            'expirePayment',
            'partnerCompanyName',
            'contactEmail',
            'contactPhone',
            'contactPosition',
            'bank',
            'bankAccountName',
            'bankAccountNumber',
            'bankBranch',
          ],

          // logging: console.log,
        },
      );

      res.send({
        result: 'success',
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async getBusinesses(req, res, next) {
    try {
      let { taxCode } = req.query;

      if (!isValidTaxCode(taxCode)) {
        throw new Error(ERROR_INVALID_TAXCODE);
      }

      let businesses = (
        await BusinessCompany.findAll({
          where: { taxCode },
          include: [{ model: Business }],
        })
      ).map(item => item.Business);

      let mainBusinessName;
      if (businesses.length == 0) {
        let company = await nntHelper.getCompany(taxCode).catch(err => {
          return {};
        });
        businesses = company.businesses ?? [];
        mainBusinessName = company.businessLine?.businessLineDetail;
      }

      res.send({ result: 'success', businesses, mainBusinessName });
    } catch (error) {
      next(error);
    }
  },
};
