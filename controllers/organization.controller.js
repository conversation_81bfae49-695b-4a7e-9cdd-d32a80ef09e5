const { Op } = require('sequelize');
const { ORGANIZATION_TYPE } = require('../configs/constants/company.constant');
const { INVOICE_MAILBOX_DOMAIN } = require('../configs/env');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_INVALID_TAXCODE,
  ERROR_INVALID_EMAIL,
  ERROR_INVALID_INVOICE_MAILBOX,
  ERROR_INVALID_PARAMETER,
  ERROR_ORGANIZATION_HQ_NOT_FOUND,
  ERROR_INVALID_DATE,
  ERROR_INVOICE_MAILBOX_USED,
  ERROR_ORGANIZATION_DATE_COLLIDE,
  ERROR_ORGANIZATION_BUSINESS_DATE_INVALID,
  ERROR_MOVE_INVOICE_BEFORE_DELETE,
  ERROR_INVALID_PHONE,
  ERROR_ORGANIZATION_NOT_GRANTED,
} = require('../configs/error.vi');
const {
  Organization,
  OrganizationDepartment,
  Sequelize,
  AccountOrganizationAccess,
  OrganizationAddress,
  Invoice,
  AcceptPartnerCompanyDetail,
  Account,
} = require('../models');
const {
  isValidTaxCode,
  isValidEmail,
  isValidDate,
  replaceInString,
  isValidPhone,
} = require('../utils');
const { checkInvoiceMailboxUsed } = require('../utils/company.helper');
const _ = require('lodash');
const { ACCOUNT_LEVEL } = require('../configs/constants/account.constant');
const roleHelper = require('../utils/role.helper');
const moment = require('moment');
const organizationHelper = require('../utils/organization.helper');
const { getPublicAccountAttributes } = require('../utils/account.helper');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      const { organizationDepartmentId, organizationId } = req.account;

      let accountOrganizationAccesses = await AccountOrganizationAccess.findAll(
        {
          where: { accountId: req.account.accountId },
        },
      );

      let organizations = (
        await Organization.findAll({
          where: {
            organizationType: ORGANIZATION_TYPE.HQ,
            companyId: req.account.companyId,
          },
          include: [
            {
              association: 'OrganizationBranches',
              include: [
                {
                  model: OrganizationDepartment,
                  separate: true,
                  order: [['createdAt', 'ASC']],
                },
                { model: OrganizationAddress },
              ],
              separate: true,
              order: [['createdAt', 'ASC']],
            },
            { model: OrganizationAddress },
          ],
          order: [['createdAt', 'ASC']],
        })
      ).map(item => item.toJSON());

      let organizationIds = organizations.map(item => item.organizationId);

      organizationIds.push(
        ..._.concat(
          organizations.map(item =>
            item.OrganizationBranches.map(i => i.organizationId),
          ),
        ),
      );

      let deparmentIds = _.concat(
        ..._.concat(
          ...organizations.map(item =>
            item.OrganizationBranches.map(i =>
              i.OrganizationDepartments.map(j => j.organizationDepartmentId),
            ),
          ),
        ),
      );

      let allEmployeeAccesses = await AccountOrganizationAccess.findAll({
        where: {
          [Op.or]: [
            { organizationId: { [Op.in]: organizationIds } },
            { organizationDepartmentId: { [Op.in]: deparmentIds } },
          ],
        },
      });

      let employeeIds = allEmployeeAccesses.map(item => item.accountId);

      let allEmployees = (
        await Account.findAll({
          attribute: getPublicAccountAttributes(),
          where: {
            accountId: { [Op.in]: employeeIds },
          },
        })
      ).map(item => item.toJSON());

      if (req.account.accountLevel == ACCOUNT_LEVEL.COMPANY_USER) {
        organizations = organizations
          .map(organization =>
            roleHelper.buildWithOrganizationAccess(
              organization,
              accountOrganizationAccesses,
            ),
          )
          .filter(_.isObject);
      }

      /* format results */
      for (let organizationHQ of organizations) {
        organizationHQ.OrganizationBranches =
          await organizationHelper.mergeCountedInvoicesOrg(
            organizationHQ.OrganizationBranches,
          );

        organizationHQ.OrganizationBranches =
          await organizationHelper.mergeAcceptDetailsOrg(
            organizationHQ.OrganizationBranches,
            { organizationDepartmentId, organizationId },
          );

        for (let organizationBranch of organizationHQ.OrganizationBranches) {
          organizationBranch.OrganizationDepartments =
            await organizationHelper.mergeCountedInvoicesDep(
              organizationBranch.OrganizationDepartments,
            );

          organizationBranch.OrganizationDepartments =
            await organizationHelper.mergeAcceptDetailsDep(
              organizationBranch.OrganizationDepartments,
              organizationBranch.taxCode,
              { organizationId, organizationDepartmentId },
            );

          /* set taxCode */
          organizationBranch.OrganizationDepartments.forEach(department => {
            department.taxCode = organizationBranch.taxCode;
          });

          organizationBranch.ChildrenOrganizations =
            organizationBranch.OrganizationDepartments;
        }

        organizationHQ.ChildrenOrganizations =
          organizationHQ.OrganizationBranches;
      }

      organizations = await organizationHelper.mergeCountedInvoicesOrg(
        organizations,
      );

      organizations = await organizationHelper.mergeAcceptDetailsOrg(
        organizations,
        { organizationDepartmentId, organizationId },
      );

      organizations = organizations.map(organization => {
        let accessAccountIds = allEmployeeAccesses
          .filter(i => i.organizationId == organization.organizationId)
          .map(i => i.accountId);

        let employees = allEmployees.filter(i =>
          accessAccountIds.includes(i.accountId),
        );

        organization.employees = employees;

        organization.OrganizationBranches =
          organization.OrganizationBranches.map(branch => {
            // eslint-disable-next-line @typescript-eslint/no-shadow
            let accessAccountIds = allEmployeeAccesses
              .filter(i => i.organizationId == branch.organizationId)
              .map(i => i.accountId);

            // eslint-disable-next-line @typescript-eslint/no-shadow
            let employees = allEmployees.filter(i =>
              accessAccountIds.includes(i.accountId),
            );

            branch.employees = employees;

            branch.OrganizationDepartments = branch.OrganizationDepartments.map(
              department => {
                // eslint-disable-next-line @typescript-eslint/no-shadow
                let accessAccountIds = allEmployeeAccesses
                  .filter(
                    i =>
                      i.organizationDepartmentId ==
                      department.organizationDepartmentId,
                  )
                  .map(i => i.accountId);

                // eslint-disable-next-line @typescript-eslint/no-shadow
                let employees = allEmployees.filter(i =>
                  accessAccountIds.includes(i.accountId),
                );

                department.employees = employees;

                return department;
              },
            );

            return branch;
          });

        return organization;
      });

      res.send({ result: 'success', organizations: organizations });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let {
        organizationName,
        taxCode,
        organizationType,
        parentOrganizationId,
        invoiceMailbox,
        businessPermitAddress,
        businessPermitDate,
        representativeEmail,
        representativeJobTitle,
        representativeName,
        representativePhone,
      } = req.body;

      /* TODO: check if is first child */

      /*  */
      if (
        !taxCode ||
        !organizationName ||
        !organizationType ||
        !invoiceMailbox ||
        !businessPermitAddress ||
        !businessPermitDate
      )
        throw new Error(ERROR_MISSING_PARAMETERS);

      if (!isValidTaxCode(taxCode)) throw new Error(ERROR_INVALID_TAXCODE);

      if (!isValidEmail(invoiceMailbox)) throw new Error(ERROR_INVALID_EMAIL);

      if (!isValidDate(businessPermitDate)) throw new Error(ERROR_INVALID_DATE);

      if (representativeEmail && !isValidEmail(representativeEmail))
        throw new Error(ERROR_INVALID_EMAIL);

      if (representativePhone && !isValidPhone(representativePhone))
        throw new Error(ERROR_INVALID_PHONE);

      if (!Object.values(ORGANIZATION_TYPE).includes(organizationType))
        throw new Error(ERROR_INVALID_PARAMETER);

      if (organizationType == ORGANIZATION_TYPE.BRANCH) {
        if (!parentOrganizationId) throw new Error(ERROR_MISSING_PARAMETERS);
      } else {
        parentOrganizationId = undefined;
        organizationType = ORGANIZATION_TYPE.HQ;
      }

      if (!invoiceMailbox.endsWith(INVOICE_MAILBOX_DOMAIN))
        throw new Error(
          replaceInString(ERROR_INVALID_INVOICE_MAILBOX, {
            domain: INVOICE_MAILBOX_DOMAIN,
          }),
        );

      /*  */
      if (parentOrganizationId) {
        let parentOrganization = await Organization.findOne({
          where: {
            organizationId: parentOrganizationId,
            companyId: req.account.companyId,
            organizationType: ORGANIZATION_TYPE.HQ,
          },
        });

        if (!parentOrganization)
          throw new Error(ERROR_ORGANIZATION_HQ_NOT_FOUND);

        if (req.account.accountLevel != ACCOUNT_LEVEL.COMPANY_ADMIN) {
          let access = await AccountOrganizationAccess.findOne({
            where: {
              organizationId: parentOrganizationId,
              accountId: req.account.accountId,
            },
          });

          if (!access) {
            throw new Error(ERROR_ORGANIZATION_NOT_GRANTED);
          }
        }
      }

      /*  */
      await checkInvoiceMailboxUsed(invoiceMailbox, req.account.companyId);

      /*  */
      let organization = await Organization.create(
        {
          companyId: req.account.companyId,
          invoiceMailbox,
          parentOrganizationId,
          organizationName,
          organizationType,
          taxCode,
          businessPermitAddress,
          businessPermitDate,
          representativeEmail,
          representativeJobTitle,
          representativeName,
          representativePhone,
        },
        { transaction: t },
      );

      let { OrganizationAddresses } = req.body;

      if (_.isArray(OrganizationAddresses)) {
        OrganizationAddresses = OrganizationAddresses.map(item => {
          if (!item.businessPermitDateEnd) delete item.businessPermitDateEnd;

          return item;
        });

        /*  */
        for (let address of OrganizationAddresses) {
          let {
            // eslint-disable-next-line @typescript-eslint/no-shadow
            businessPermitAddress,
            businessPermitDateEnd,
            businessPermitDateStart,
            // eslint-disable-next-line @typescript-eslint/no-shadow
            organizationName,
          } = address;

          if (
            !businessPermitAddress ||
            // !businessPermitDateEnd ||
            !businessPermitDateStart ||
            !organizationName
          ) {
            throw new Error(ERROR_MISSING_PARAMETERS);
          }

          if (
            !isValidDate(businessPermitDateStart) ||
            !isValidDate(businessPermitDateEnd)
          ) {
            throw new Error(ERROR_INVALID_DATE);
          }

          if (moment(businessPermitDateEnd).isBefore(businessPermitDateStart)) {
            throw new Error(ERROR_ORGANIZATION_BUSINESS_DATE_INVALID);
          }

          address.organizationId = organization.organizationId;
          address.acceptBy = req.account.email;
        }

        /* date collide: start1 <= start2 <= end1 */
        if (
          OrganizationAddresses.some((address, index, arr) => {
            if (
              arr.some(
                (other, j) =>
                  j != index &&
                  ((moment(other.businessPermitDateEnd).isSameOrAfter(
                    address.businessPermitDateEnd,
                    'date',
                  ) &&
                    moment(other.businessPermitDateStart).isBefore(
                      address.businessPermitDateEnd,
                      'date',
                    )) ||
                    (moment(other.businessPermitDateEnd).isAfter(
                      address.businessPermitDateStart,
                      'date',
                    ) &&
                      moment(other.businessPermitDateStart).isSameOrBefore(
                        address.businessPermitDateStart,
                        'date',
                      ))),
              )
            ) {
              return true;
            }
            return false;
          })
        ) {
          throw new Error(ERROR_ORGANIZATION_DATE_COLLIDE);
        }

        /* rm olds */
        await OrganizationAddress.destroy({
          where: { organizationId: organization.organizationId },
          transaction: t,
        });

        /* create news */
        let organizationAddresses = await OrganizationAddress.bulkCreate(
          OrganizationAddresses,
          { transaction: t },
        );

        let organizationAddress =
          organizationAddresses[organizationAddresses.length - 1];

        await organization.update(
          {
            organizationName: organizationAddress.organizationName,
            businessPermitAddress: organizationAddress.businessPermitAddress,
            businessPermitDate: organizationAddress.businessPermitDateStart,
          },
          { transaction: t },
        );

        organization.setDataValue(
          'OrganizationAddresses',
          organizationAddresses,
        );
      }

      if (
        !req.account.organizationId &&
        !req.account.organizationDepartmentId
      ) {
        await Account.update(
          { organizationId: organization.organizationId },
          { transaction: t, where: { accountId: req.account.accountId } },
        );
      }

      await t.commit();

      /* auto grant access */
      if (req.account.accountLevel != ACCOUNT_LEVEL.COMPANY_ADMIN) {
        await AccountOrganizationAccess.create({
          accountId: req.account.accountId,
          organizationId: organization.organizationId,
        });
      }

      res.send({ result: 'success', organization });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { organizationId } = req.params;

      let organization = await Organization.findOne({
        where: { companyId: req.account.companyId, organizationId },
        transaction: t,
      });

      if (!organization) throw new Error(ERROR_ORGANIZATION_HQ_NOT_FOUND);

      let {
        organizationName,
        businessPermitDate,
        businessPermitAddress,
        representativeEmail,
        representativeJobTitle,
        representativeName,
        representativePhone,
      } = req.body;

      if (representativeEmail && !isValidEmail(representativeEmail))
        throw new Error(ERROR_INVALID_EMAIL);

      if (representativePhone && !isValidPhone(representativePhone))
        throw new Error(ERROR_INVALID_PHONE);

      await organization.update(
        {
          organizationName,
          businessPermitAddress,
          businessPermitDate,
          representativeEmail,
          representativeJobTitle,
          representativeName,
          representativePhone,
        },
        { transaction: t },
      );

      let { OrganizationAddresses } = req.body;

      if (_.isArray(OrganizationAddresses)) {
        OrganizationAddresses = OrganizationAddresses.map(item => {
          if (!item.businessPermitDateEnd) delete item.businessPermitDateEnd;

          return item;
        });

        /*  */
        for (let address of OrganizationAddresses) {
          let {
            // eslint-disable-next-line @typescript-eslint/no-shadow
            businessPermitAddress,
            businessPermitDateEnd,
            businessPermitDateStart,
            // eslint-disable-next-line @typescript-eslint/no-shadow
            organizationName,
          } = address;

          if (
            !businessPermitAddress ||
            // !businessPermitDateEnd ||
            !businessPermitDateStart ||
            !organizationName
          ) {
            throw new Error(ERROR_MISSING_PARAMETERS);
          }

          if (
            !isValidDate(businessPermitDateStart) ||
            !isValidDate(businessPermitDateEnd)
          ) {
            throw new Error(ERROR_INVALID_DATE);
          }

          if (moment(businessPermitDateEnd).isBefore(businessPermitDateStart)) {
            throw new Error(ERROR_ORGANIZATION_BUSINESS_DATE_INVALID);
          }

          address.organizationId = organization.organizationId;
          address.acceptBy = req.account.email;
        }

        /* date collide: start1 <= start2 <= end1 */
        if (
          OrganizationAddresses.some((address, index, arr) => {
            if (
              arr.some(
                (other, j) =>
                  j != index &&
                  ((moment(other.businessPermitDateEnd).isSameOrAfter(
                    address.businessPermitDateEnd,
                    'date',
                  ) &&
                    moment(other.businessPermitDateStart).isBefore(
                      address.businessPermitDateEnd,
                      'date',
                    )) ||
                    (moment(other.businessPermitDateEnd).isAfter(
                      address.businessPermitDateStart,
                      'date',
                    ) &&
                      moment(other.businessPermitDateStart).isSameOrBefore(
                        address.businessPermitDateStart,
                        'date',
                      ))),
              )
            ) {
              return true;
            }
            return false;
          })
        ) {
          throw new Error(ERROR_ORGANIZATION_DATE_COLLIDE);
        }

        /* rm olds */
        await OrganizationAddress.destroy({
          where: { organizationId: organization.organizationId },
          transaction: t,
        });

        /* create news */
        let organizationAddresses = await OrganizationAddress.bulkCreate(
          OrganizationAddresses,
          { transaction: t },
        );

        let organizationAddress =
          organizationAddresses[organizationAddresses.length - 1];

        await organization.update(
          {
            organizationName: organizationAddress.organizationName,
            businessPermitAddress: organizationAddress.businessPermitAddress,
            businessPermitDate: organizationAddress.businessPermitDateStart,
          },
          { transaction: t },
        );

        organization.setDataValue(
          'OrganizationAddresses',
          organizationAddresses,
        );
      }

      let { acceptPartnerCompanyDetails } = req.body;

      if (_.isArray(acceptPartnerCompanyDetails)) {
        if (
          acceptPartnerCompanyDetails.some(item => {
            let { acceptName, acceptAddress, from, to } = item;

            if (!acceptName && !acceptAddress) return true;

            if (!isValidDate(from) || !isValidDate(to)) return true;

            return false;
          })
        ) {
          throw new Error(ERROR_MISSING_PARAMETERS);
        }

        await AcceptPartnerCompanyDetail.destroy({
          where: {
            taxCode: organization.taxCode,
            organizationDepartmentId: req.account.organizationDepartmentId,
            organizationId: req.account.organizationId,
          },
          transaction: t,
        });

        let _acceptPartnerCompanyDetails =
          await AcceptPartnerCompanyDetail.bulkCreate(
            acceptPartnerCompanyDetails.map(item => {
              return {
                taxCode: organization.taxCode,
                acceptBy: req.account.accountId,
                acceptAddress: item.acceptAddress,
                acceptName: item.acceptName,
                from: item.from,
                to: item.to,
                organizationDepartmentId: req.account.organizationDepartmentId,
                organizationId: req.account.organizationId,
              };
            }),
            {
              transaction: t,
            },
          );

        _acceptPartnerCompanyDetails = _acceptPartnerCompanyDetails.map(
          item => {
            item.Account = _.pick(req.account, getPublicAccountAttributes());

            return item;
          },
        );

        organization.setDataValue(
          'acceptPartnerCompanyDetails',
          _acceptPartnerCompanyDetails,
        );
      }

      await t.commit();

      res.send({ result: 'success', organization });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    try {
      let { organizationIds } = req.body;

      if (!_.isArray(organizationIds)) throw new Error(ERROR_INVALID_PARAMETER);

      /* TODO: check invoice before deletion */
      if (
        await Invoice.findOne({
          where: {
            organizationId: { [Op.in]: organizationIds },
            invoiceCategory: { [Op.not]: null },
          },
        })
      ) {
        throw new Error(ERROR_MOVE_INVOICE_BEFORE_DELETE);
      }

      let deleteCount = await Organization.destroy({
        where: {
          organizationId: { [Op.in]: organizationIds },
          companyId: req.account.companyId,
        },
      });

      res.send({ result: 'success', deleteCount });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async createWithManyAddress(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let {
        taxCode,
        organizationType,
        parentOrganizationId,
        invoiceMailbox,
        OrganizationAddresses,
      } = req.body;

      if (!isValidTaxCode(taxCode)) {
        throw new Error(ERROR_INVALID_TAXCODE);
      }

      if (
        !taxCode ||
        !organizationType ||
        !invoiceMailbox ||
        !_.isArray(OrganizationAddresses)
      ) {
        throw new Error(ERROR_MISSING_PARAMETERS);
      }

      if (!OrganizationAddresses.length) {
        throw new Error(ERROR_MISSING_PARAMETERS);
      }

      if (!Object.values(ORGANIZATION_TYPE).includes(organizationType))
        throw new Error(ERROR_INVALID_PARAMETER);

      if (organizationType == ORGANIZATION_TYPE.BRANCH) {
        if (!parentOrganizationId) throw new Error(ERROR_MISSING_PARAMETERS);
      } else {
        parentOrganizationId = undefined;
        organizationType = ORGANIZATION_TYPE.HQ;
      }

      if (!invoiceMailbox.endsWith(INVOICE_MAILBOX_DOMAIN))
        throw new Error(
          replaceInString(ERROR_INVALID_INVOICE_MAILBOX, {
            domain: INVOICE_MAILBOX_DOMAIN,
          }),
        );

      if (parentOrganizationId) {
        let parentOrganization = await Organization.findOne({
          where: {
            organizationId: parentOrganizationId,
            companyId: req.account.companyId,
            organizationType: ORGANIZATION_TYPE.HQ,
          },
        });

        if (!parentOrganization)
          throw new Error(ERROR_ORGANIZATION_HQ_NOT_FOUND);
      }

      OrganizationAddresses = OrganizationAddresses.map(item => {
        if (!item.businessPermitDateEnd) delete item.businessPermitDateEnd;

        return item;
      });

      /*  */
      await checkInvoiceMailboxUsed(invoiceMailbox, req.account.companyId);

      /*  */
      let organization = await Organization.create(
        {
          companyId: req.account.companyId,
          invoiceMailbox,
          parentOrganizationId,
          organizationType,
          taxCode,
        },
        { transaction: t },
      );

      /*  */
      for (let address of OrganizationAddresses) {
        let {
          businessPermitAddress,
          businessPermitDateEnd,
          businessPermitDateStart,
          organizationName,
        } = address;

        if (
          !businessPermitAddress ||
          // !businessPermitDateEnd ||
          !businessPermitDateStart ||
          !organizationName
        ) {
          throw new Error(ERROR_MISSING_PARAMETERS);
        }

        if (
          !isValidDate(businessPermitDateStart) ||
          !isValidDate(businessPermitDateEnd)
        ) {
          throw new Error(ERROR_INVALID_DATE);
        }

        if (moment(businessPermitDateEnd).isBefore(businessPermitDateStart)) {
          throw new Error(ERROR_ORGANIZATION_BUSINESS_DATE_INVALID);
        }

        address.organizationId = organization.organizationId;
        address.acceptBy = req.account.email;
      }
      /* date collide: start1 <= start2 <= end1 */
      if (
        OrganizationAddresses.some((address, index, arr) => {
          if (
            arr.some(
              (other, j) =>
                j != index &&
                ((moment(other.businessPermitDateEnd).isSameOrAfter(
                  address.businessPermitDateEnd,
                  'date',
                ) &&
                  moment(other.businessPermitDateStart).isBefore(
                    address.businessPermitDateEnd,
                    'date',
                  )) ||
                  (moment(other.businessPermitDateEnd).isAfter(
                    address.businessPermitDateStart,
                    'date',
                  ) &&
                    moment(other.businessPermitDateStart).isSameOrBefore(
                      address.businessPermitDateStart,
                      'date',
                    ))),
            )
          ) {
            return true;
          }
          return false;
        })
      ) {
        throw new Error(ERROR_ORGANIZATION_DATE_COLLIDE);
      }

      let organizationAddresses = await OrganizationAddress.bulkCreate(
        OrganizationAddresses,
        { transaction: t },
      );

      /* TODO: check if is first child */

      /*  */

      let organizationAddress =
        organizationAddresses[organizationAddresses.length - 1];

      await organization.update(
        {
          organizationName: organizationAddress.organizationName,
          businessPermitAddress: organizationAddress.businessPermitAddress,
          businessPermitDate: organizationAddress.businessPermitDateStart,
        },
        { transaction: t },
      );

      await t.commit();

      organization.setDataValue('OrganizationAddresses', organizationAddresses);

      res.send({ result: 'success', organization });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async updateWithManyAddress(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { organizationId } = req.params;

      let organization = await Organization.findOne({
        where: { companyId: req.account.companyId, organizationId },
      });

      if (!organization) throw new Error(ERROR_ORGANIZATION_HQ_NOT_FOUND);

      let { OrganizationAddresses } = req.body;

      if (!_.isArray(OrganizationAddresses) || !OrganizationAddresses.length) {
        throw new Error(ERROR_MISSING_PARAMETERS);
      }

      OrganizationAddresses = OrganizationAddresses.map(item => {
        if (!item.businessPermitDateEnd) delete item.businessPermitDateEnd;

        return item;
      });

      /*  */
      for (let address of OrganizationAddresses) {
        let {
          businessPermitAddress,
          businessPermitDateEnd,
          businessPermitDateStart,
          organizationName,
        } = address;

        if (
          !businessPermitAddress ||
          // !businessPermitDateEnd ||
          !businessPermitDateStart ||
          !organizationName
        ) {
          throw new Error(ERROR_MISSING_PARAMETERS);
        }

        if (
          !isValidDate(businessPermitDateStart) ||
          !isValidDate(businessPermitDateEnd)
        ) {
          throw new Error(ERROR_INVALID_DATE);
        }

        if (moment(businessPermitDateEnd).isBefore(businessPermitDateStart)) {
          throw new Error(ERROR_ORGANIZATION_BUSINESS_DATE_INVALID);
        }

        address.organizationId = organization.organizationId;
        address.acceptBy = req.account.email;
      }

      /* date collide: start1 <= start2 <= end1 */
      if (
        OrganizationAddresses.some((address, index, arr) => {
          if (
            arr.some(
              (other, j) =>
                j != index &&
                ((moment(other.businessPermitDateEnd).isSameOrAfter(
                  address.businessPermitDateEnd,
                  'date',
                ) &&
                  moment(other.businessPermitDateStart).isBefore(
                    address.businessPermitDateEnd,
                    'date',
                  )) ||
                  (moment(other.businessPermitDateEnd).isAfter(
                    address.businessPermitDateStart,
                    'date',
                  ) &&
                    moment(other.businessPermitDateStart).isSameOrBefore(
                      address.businessPermitDateStart,
                      'date',
                    ))),
            )
          ) {
            return true;
          }
          return false;
        })
      ) {
        throw new Error(ERROR_ORGANIZATION_DATE_COLLIDE);
      }

      /* rm olds */
      await OrganizationAddress.destroy({
        where: { organizationId: organization.organizationId },
        transaction: t,
      });

      /* create news */
      let organizationAddresses = await OrganizationAddress.bulkCreate(
        OrganizationAddresses,
        { transaction: t },
      );

      let organizationAddress =
        organizationAddresses[organizationAddresses.length - 1];

      await organization.update(
        {
          organizationName: organizationAddress.organizationName,
          businessPermitAddress: organizationAddress.businessPermitAddress,
          businessPermitDate: organizationAddress.businessPermitDateStart,
        },
        { transaction: t },
      );

      await t.commit();

      organization.setDataValue('OrganizationAddresses', organizationAddresses);

      res.send({ result: 'success', organization });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
};
