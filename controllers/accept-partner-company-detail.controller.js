const {
  ERROR_NOT_FOUND,
  ERROR_MISSING_PARAMETERS,
  ERROR_INVALID_TAXCODE,
  ERROR_INVALID_PARAMETER,
} = require('../configs/error.vi');
const _ = require('lodash');

const { AcceptPartnerCompanyDetail, Invoice } = require('../models');
const utils = require('../utils');
const { cqt_validate_invoice } = require('../utils/cqt.helper');
module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    try {
      const { invoiceId } = req.params;
      const { organizationId, organizationDepartmentId, accountId } =
        req.account;

      let invoice;
      if (invoiceId) {
        invoice = await Invoice.findOne({
          where: { invoiceId, organizationDepartmentId, organizationId },
        });

        if (!invoice) throw new Error(ERROR_NOT_FOUND);
      }

      const { acceptName, acceptAddress, taxCode, from, to } = req.body;

      // console.log(req.account);
      if (!utils.isValidTaxCode(taxCode))
        throw new Error(ERROR_INVALID_TAXCODE);
      if (!acceptAddress && !acceptName)
        throw new Error(ERROR_MISSING_PARAMETERS);
      if (!taxCode || !from || !to) throw new Error(ERROR_MISSING_PARAMETERS);
      if (!utils.isValidDate(from) || !utils.isValidDate(to))
        throw new Error(ERROR_INVALID_PARAMETER);

      const acceptPartnerCompanyDetail =
        await AcceptPartnerCompanyDetail.create({
          organizationDepartmentId,
          organizationId,
          acceptBy: accountId,
          from: from,
          to: to,
          taxCode: taxCode,
          acceptAddress: acceptAddress,
          acceptName: acceptName,
        });

      if (invoice) {
        await cqt_validate_invoice(invoice);
      }

      res.send({
        result: 'success',
        acceptPartnerCompanyDetail,
      });
    } catch (error) {
      next(error);
    }
  },
};
