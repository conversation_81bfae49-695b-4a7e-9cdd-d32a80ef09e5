const {
  ERROR_INVALID_PARAMETER,
  ERROR_NOT_FOUND,
  ERROR_USER_NOT_FOUND,
  ERROR_CANNOT_USE_ANOTHER_TRIAL,
} = require('../configs/error.vi');
const _ = require('lodash');
const {
  Package,
  Account,
  Company,
  ResourceHistory,
  Sequelize,
} = require('../models');
const { ACCOUNT_LEVEL } = require('../configs/constants/account.constant');
const {
  SUPPLY_TYPE,
} = require('../configs/constants/resource-history.constant');
const { PACKAGE_TYPE } = require('../configs/constants/package.constant');
const moment = require('moment');
const { Op } = require('sequelize');
const { isValidNumber } = require('../utils');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async themGoiChoKhachHang(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { resourceItems, metaAccountCode, dealer, supplier } = req.body;
      if (!_.isArray(resourceItems)) throw new Error(ERROR_INVALID_PARAMETER);

      let packageCodes = resourceItems.map(i => i.packageCode);

      const account = await Account.findOne({
        where: {
          metaAccountCode,
        },
        include: [{ model: Company }],
      });

      if (!account) throw new Error(ERROR_USER_NOT_FOUND);

      const company = await Company.findOne({
        where: {
          companyId: account.Company.companyId,
        },
      });

      const supplyType = dealer
        ? SUPPLY_TYPE.DEALER_SUPPLY
        : supplier
        ? SUPPLY_TYPE.SUPPLIER_SUPPLY
        : SUPPLY_TYPE.ORDER;

      let resourceHistories = [];
      let quantityPurchased = 0,
        quantityRemain = 0,
        dateExpired = 0;

      let packages = await Package.findAll({
        where: { packageCode: { [Op.in]: packageCodes } },
        transaction: t,
      });

      /* ko dc them 2 loai KM cung luc */
      let trialPackage;
      if ((trialPackage = packages.find(i => i.kmCode.startsWith('TRIAL')))) {
        if (
          packages.find(
            j =>
              j.kmCode.startsWith('TRIAL') &&
              j.packageType == trialPackage.packageType &&
              j.packageCode != trialPackage.packageCode,
          )
        ) {
          throw new Error(ERROR_CANNOT_USE_ANOTHER_TRIAL);
        }
      }

      packageCodes = packages.map(i => i.packageCode);
      /* check dung thu TRIAL KM */
      if (packages.find(i => i.kmCode.startsWith('TRIAL'))) {
        let previousTrial = await ResourceHistory.findOne({
          where: {
            companyId: company.companyId,
            packageType: PACKAGE_TYPE.QUANTITY,
            package: { kmCode: { [Op.like]: `%TRIAL%` } },
          },
        });

        if (previousTrial) {
          /* neu da dung thu */
          let anotherTrialCode = packages.find(
            i =>
              i.kmCode.startsWith('TRIAL') &&
              i.packageType == previousTrial.package.packageType &&
              i.packageCode != previousTrial.package.packageCode,
          );

          if (anotherTrialCode) {
            throw new Error(ERROR_CANNOT_USE_ANOTHER_TRIAL);
          }
        }
      }

      let packageDict = packages.reduce((prev, curr) => {
        prev[curr.packageCode] = curr.toJSON();
        return prev;
      }, {});

      resourceItems = resourceItems.filter(i => packageDict[i.packageCode]);

      for (let { packageCode, purchasedPrice, listPrice } of resourceItems) {
        let package = packageDict[packageCode];

        /**
         * @type {import('../types').ResourceHistory}
         */
        const resourceHistory = {
          active: !!company.daKyHopDongNCC,
          supplyDate: new Date(),
          // expiredDate: package.duration,
          package: package,
          left:
            package.packageType === PACKAGE_TYPE.QUANTITY
              ? package.quantity
              : package.duration,
          total:
            package.packageType === PACKAGE_TYPE.QUANTITY
              ? package.quantity
              : package.duration,
          companyId: account.Company.companyId,
          packageType: package.packageType,
          supplyBy: supplier ?? dealer,
          supplyType,
          purchasedPrice: isValidNumber(purchasedPrice)
            ? purchasedPrice
            : undefined,
          listPrice: isValidNumber(listPrice) ? listPrice : undefined,
        };

        resourceHistories.push(resourceHistory);
        if (package.packageType === PACKAGE_TYPE.QUANTITY) {
          quantityPurchased += package.quantity;
          quantityRemain += package.quantity;
        } else {
          dateExpired += package.duration;
        }
      }

      await company.update(
        {
          quantityPurchased: company.quantityPurchased + quantityPurchased,
          quantityRemain: company.quantityRemain + quantityRemain,
          dateExpiredPackage: moment(
            company.dateExpiredPackage
              ? moment().isSameOrAfter(company.dateExpiredPackage)
                ? undefined
                : company.dateExpiredPackage
              : undefined,
          )
            .add(dateExpired, 'days')
            .toDate(),
        },

        { transaction: t },
      );

      await ResourceHistory.bulkCreate(resourceHistories, { transaction: t });
      await t.commit();
      res.send({
        result: 'success',
        resourceHistories,
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
};
