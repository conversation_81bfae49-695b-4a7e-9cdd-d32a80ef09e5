const _ = require('lodash');
const {
  ERROR_INVALID_PARAMETER,
  ERROR_EXIST_TAXCODE_EMAIL,
  ERROR_MISSING_PARAMETERS,
  ERROR_EXIST_TRANSFER_INVOICE,
  ERROR_INTERNAL,
  ERROR_NOT_FOUND,
} = require('../configs/error.vi');
const { Op } = require('sequelize');
const {
  Supplier,
  AutoTransferInvoice,
  Sequelize,
  Organization,
  OrganizationDepartment,
  PartnerCompany,
} = require('../models');
module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      const {
        autoTransferInvoices,
        // partnerCompanyIds,
        // autoTransferOrganizationId,
        // autoTransferOrganizationDepartmentId,
      } = req.body;

      const { organizationId, organizationDepartmentId } = req.account;

      if (!_.isArray(autoTransferInvoices))
        throw new Error(ERROR_INVALID_PARAMETER);

      // delete all
      await AutoTransferInvoice.destroy(
        {
          where: {
            organizationId,
            organizationDepartmentId,
          },
        },
        { transaction: t },
      );

      for (let autoTransferInvoice of autoTransferInvoices) {
        if (!_.isArray(autoTransferInvoice.partnerCompanyIds))
          throw new Error(ERROR_INVALID_PARAMETER);

        // find partnerCompanyId
        for (let i = 0; i < autoTransferInvoice.partnerCompanyIds.length; i++) {
          const partnerCompany = await PartnerCompany.findOne({
            where: {
              partnerCompanyId: autoTransferInvoice.partnerCompanyIds[i],
            },
          });

          if (!partnerCompany) throw new Error(ERROR_NOT_FOUND);
        }

        // find organazition
        if (autoTransferInvoice.autoTransferOrganizationId) {
          const findOrganization = await Organization.findOne({
            where: {
              organizationId: autoTransferInvoice.autoTransferOrganizationId,
            },
          });

          if (!findOrganization) throw new Error(ERROR_NOT_FOUND);
        }

        // find organazitionDepartment
        if (autoTransferInvoice.autoTransferOrganizationDepartmentId) {
          const findOrganizationDepartment =
            await OrganizationDepartment.findOne({
              where: {
                organizationDepartmentId:
                  autoTransferInvoice.autoTransferOrganizationDepartmentId,
              },
            });

          if (!findOrganizationDepartment) throw new Error(ERROR_NOT_FOUND);
        }

        await AutoTransferInvoice.create(
          {
            organizationDepartmentId,
            organizationId,
            autoTransferOrganizationId:
              autoTransferInvoice.autoTransferOrganizationId,
            autoTransferOrganizationDepartmentId:
              autoTransferInvoice.autoTransferOrganizationDepartmentId,
            partnerCompanyIds: autoTransferInvoice.partnerCompanyIds,
          },
          { transaction: t },
        );
      }

      await t.commit();

      res.send({
        result: 'success',
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      const { organizationDepartmentId, organizationId } = req.account;
      const conditions = {
        [Op.and]: [
          true ? { organizationDepartmentId } : {},
          true ? { organizationId } : {},
        ],
      };
      const autoTransferInvoices = await AutoTransferInvoice.findAll({
        where: conditions,
        include: [
          {
            model: Organization,
          },
          {
            model: OrganizationDepartment,
          },
        ],
      });

      for (let autoTransferInvoice of autoTransferInvoices) {
        const partnerCompanies = await PartnerCompany.findAll({
          where: {
            partnerCompanyId: {
              [Op.in]: autoTransferInvoice.partnerCompanyIds,
            },
          },
        });

        autoTransferInvoice.PartnerCompanies = partnerCompanies;

        autoTransferInvoice.setDataValue('PartnerCompanies', partnerCompanies);
      }

      res.send({ result: 'success', autoTransferInvoices });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      const { autoTransferInvoiceId } = req.params;
      const { organizationDepartmentId, organizationId } = req.account;

      const autoTransferInvoice = await AutoTransferInvoice.findOne({
        where: {
          organizationDepartmentId,
          organizationId,
          autoTransferInvoiceId,
        },
      });

      if (!autoTransferInvoice) throw new Error(ERROR_NOT_FOUND);

      const partnerCompanyIds = autoTransferInvoice.partnerCompanyIds;

      let partnerCompanies = [];
      for (let i = 0; i < partnerCompanyIds.length; i++) {
        const partnerCompany = await PartnerCompany.findOne({
          where: {
            // organizationDepartmentId,
            // organizationId,
            partnerCompanyId: partnerCompanyIds[i],
          },
          attribute: ['taxCode', 'partnerCompanyName', 'address'],
        });

        if (partnerCompany) partnerCompanies.push(partnerCompany);
      }

      res.send({
        result: 'success',
        autoTransferInvoice,
        partnerCompanies,
      });
    } catch (error) {
      next(error);
    }
  },
};
