const { Op } = require('sequelize');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_NOT_FOUND,
} = require('../configs/error.vi');
const { FAQ } = require('../models');
const { isValidNumber } = require('../utils');
const _ = require('lodash');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    try {
      const { answer, question } = req.body;
      if (!answer || !question) throw new Error(ERROR_MISSING_PARAMETERS);

      let faq = await FAQ.create({
        answer,
        question,
      });

      res.send({
        result: 'success',
        faq: faq,
      });
    } catch (error) {
      // console.log(error?.message);
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      const { faqId } = req.params;
      if (!faqId) throw new Error(ERROR_MISSING_PARAMETERS);
      const faq = await FAQ.findOne({
        where: { faqId },
      });
      if (!faq) throw new Error(ERROR_NOT_FOUND);
      return res.send({
        result: 'success',
        faq,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */

  async find(req, res, next) {
    try {
      let { q, limit, page, orderField, orderMode } = req.query;
      q = q ?? '';
      orderField = orderField ?? 'updatedAt';
      orderMode = orderMode ?? 'DESC';

      let conditions = {
        [Op.or]: [
          { answer: { [Op.like]: `%${q}%` } },
          { question: { [Op.like]: `%${q}%` } },
        ],
      };

      if (!isValidNumber(limit) || !isValidNumber(page)) {
        let faq = await FAQ.findAll({
          where: conditions,
          order: [[orderField, orderMode]],
        });
        res.send({
          result: 'success',
          faqs: faq,
        });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        let faq = await FAQ.findAndCountAll({
          limit,
          offset: limit * page,
          where: conditions,
          order: [[orderField, orderMode]],
        });
        res.send({
          result: 'success',
          page,
          faqs: faq.rows,
          total: faq.count,
          count: faq.rows.length,
        });
      }
    } catch (error) {
      // console.log(error?.message);
      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    try {
      const { answer, question } = req.body;
      const { faqId } = req.params;
      if (!faqId) throw new Error(ERROR_MISSING_PARAMETERS);
      const faq = await FAQ.findOne({
        where: { faqId },
      });
      if (!faq) throw new Error(ERROR_NOT_FOUND);
      await faq.update({
        answer,
        question,
      });
      res.send({
        result: 'success',
        faq,
      });
    } catch (error) {
      next(error);
    }
  },

  async delete(req, res, next) {
    try {
      const { faqIds } = req.body;
      if (!_.isArray(faqIds)) throw new Error(ERROR_MISSING_PARAMETERS);
      const deleteCount = await FAQ.destroy({
        where: { faqId: { [Op.in]: faqIds } },
      });
      res.send({
        result: 'success',
        count: deleteCount,
      });
    } catch (error) {
      next(error);
    }
  },
};
