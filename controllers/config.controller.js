const _ = require('lodash');
const { Op } = require('sequelize');
const utils = require('../utils');
const { Config } = require('../models');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_NOT_FOUND,
  ERROR_CONFIG_KEY_EXISTS,
  ERROR_INVALID_PARAMETER,
} = require('../configs/error.vi');
const { INVOICE_MAILBOX_DOMAIN } = require('../configs/env');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let { q, limit, page } = req.query;
      q = q ?? '';
      /**
       * @type {import('sequelize').WhereOptions<import('../types').Config>}
       */
      let conditions = [
        {
          [Op.or]: [
            { name: { [Op.like]: `%${q}%` } },
            { value: { [Op.like]: `%${q}%` } },
            { key: { [Op.like]: `%${q}%` } },
          ],
        },
      ];
      let configs;
      limit = _.toNumber(limit);
      page = _.toNumber(page);
      if (!utils.isValidNumber(limit) || !utils.isValidNumber(page)) {
        page = undefined;
        limit = undefined;
        configs = await Config.findAndCountAll({
          where: conditions,
        });

        /* if missing mail domain */
        if (!configs.rows.find(item => item.key == 'MAIL_SERVER_DOMAIN')) {
          configs.rows.push({
            key: 'MAIL_SERVER_DOMAIN',
            configId: 0,
            name: 'Tên miền email',
            value: INVOICE_MAILBOX_DOMAIN,
          });
        }
      } else {
        configs = await Config.findAndCountAll({
          where: conditions,
          limit,
          offset: limit * page,
        });
      }

      res.send({
        result: 'success',
        page,
        total: configs.count,
        count: configs.rows.length,
        configs: configs.rows,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      let { configId } = req.params;
      if (!configId) {
        throw new Error(ERROR_MISSING_PARAMETERS);
      }

      let config = await Config.findOne({
        where: {
          configId,
        },
      });
      if (!config) {
        throw new Error(ERROR_NOT_FOUND);
      }
      res.send({
        result: 'success',
        config,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    try {
      let { key, name, value } = req.body;
      if (!key || !name || !value) throw new Error(ERROR_MISSING_PARAMETERS);
      let checkConfigKey = await Config.findOne({ where: { key } });
      if (checkConfigKey) throw new Error(ERROR_CONFIG_KEY_EXISTS);
      let config = await Config.create({ key, name, value });
      res.send({
        result: 'success',
        config,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    try {
      let { key, name, value } = req.body;
      let { configId } = req.params;
      let update = {};
      if (key) update.key = key;
      if (name) update.name = name;
      if (value) update.value = value;
      let config = await Config.findOne({ where: { configId } });
      if (!config) throw new Error(ERROR_NOT_FOUND);
      if (key) {
        let checkConfig = await Config.findOne({
          where: {
            configId: {
              [Op.ne]: configId,
            },
            key,
          },
        });
        if (checkConfig) throw new Error(ERROR_CONFIG_KEY_EXISTS);
      }
      await config.update(update);
      res.send({
        result: 'success',
        config,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    try {
      const { configIds } = req.body;

      if (!_.isArray(configIds)) throw new Error(ERROR_INVALID_PARAMETER);

      const deleteCount = await Config.destroy({
        where: { configId: { [Op.in]: configIds } },
      });

      res.send({
        result: 'success',
        deleteCount,
      });
    } catch (error) {
      next(error);
    }
  },
};
