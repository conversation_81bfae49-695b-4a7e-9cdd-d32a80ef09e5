const { Op } = require('sequelize');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_NOT_FOUND,
  ERROR_INVALID_PARAMETER,
} = require('../configs/error.vi');
const _ = require('lodash');
const { isValidNumber } = require('../utils');
const { Instruction, InstructionCategory, Sequelize } = require('../models');
const {
  INSTRUCTION_TYPE,
} = require('../configs/constants/instruction.constant');
const { PUBLIC_UPLOAD_DIR } = require('../configs/env');
const utils = require('../utils');
const fs = require('fs');
const path = require('path');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { title, description, instructionCategoryId, type, thumbnail } =
        req.body;

      if (!title || !instructionCategoryId)
        throw new Error(ERROR_MISSING_PARAMETERS);

      if (!Object.values(INSTRUCTION_TYPE).includes(type))
        throw new Error(ERROR_INVALID_PARAMETER);

      if (instructionCategoryId) {
        let instructionCategory = await InstructionCategory.findOne({
          where: { instructionCategoryId },
        });

        if (!instructionCategory) throw new Error(ERROR_NOT_FOUND);
      }

      let instruction = await Instruction.create({
        title,
        description,
        instructionCategoryId,
        type,
      });

      if (req.files?.['thumbnail']) {
        let file = req.files['thumbnail'];

        if (_.isArray(file)) file = file[0];

        let filepath = `${PUBLIC_UPLOAD_DIR}/instruction/thumbnail/${instruction.instructionId}`;
        let filename = `${file.name}`;

        filename = utils.escapeFilename(filename);

        if (fs.existsSync(filepath)) fs.rmSync(filepath, { recursive: true });
        fs.mkdirSync(filepath);

        await file.mv(path.join(filepath, filename));

        thumbnail = `resources/instruction/thumbnail/${instruction.instructionId}/${filename}`;
        await instruction.update({ thumbnail }, { transaction: t });
      }
      if (req.files?.['file']) {
        let file = req.files['file'];

        if (_.isArray(file)) file = file[0];

        let filepath = `${PUBLIC_UPLOAD_DIR}/instruction/url/${instruction.instructionId}`;
        let filename = `${file.name}`;

        filename = utils.escapeFilename(filename);

        if (fs.existsSync(filepath)) fs.rmSync(filepath, { recursive: true });
        fs.mkdirSync(filepath);

        await file.mv(path.join(filepath, filename));

        let url = `resources/instruction/url/${instruction.instructionId}/${filename}`;
        await instruction.update({ url }, { transaction: t });
      }

      await t.commit();

      res.send({ result: 'success', instruction });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      const { instructionId } = req.params;
      if (!instructionId) throw new Error(ERROR_MISSING_PARAMETERS);
      const instruction = await Instruction.findOne({
        where: { instructionId },
      });
      if (!instruction) throw new Error(ERROR_NOT_FOUND);
      return res.send({
        result: 'success',
        instruction,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let {
        q,
        limit,
        page,
        orderField,
        orderMode,
        type,
        instructionCategoryId,
      } = req.query;

      q = q ?? '';
      orderField = orderField ?? 'updatedAt';
      orderMode = orderMode ?? 'DESC';

      /**
       * @type {sequelize.WhereOptions<import('../types').Instruction>}
       */
      let conditions = {
        [Op.or]: [
          { title: { [Op.like]: `%${q}%` } },
          { description: { [Op.like]: `%${q}%` } },
          { '$InstructionCategory.title$': { [Op.like]: `%${q}%` } },
          { '$InstructionCategory.description$': { [Op.like]: `%${q}%` } },
        ],
        [Op.and]: [
          type ? { type } : undefined,
          instructionCategoryId ? { instructionCategoryId } : {},
        ],
      };

      if (!isValidNumber(limit) || !isValidNumber(page)) {
        let rs = (
          await Instruction.findAll({
            where: conditions,
            include: [{ model: InstructionCategory }],
            order: [[orderField, orderMode]],
          })
        ).map(r => r.toJSON());

        res.send({ result: 'success', instructions: rs });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        let rs = await Instruction.findAndCountAll({
          where: conditions,
          include: [{ model: InstructionCategory }],
          limit,
          offset: limit * page,
          order: [[orderField, orderMode]],
        });

        res.send({
          result: 'success',
          page,
          total: rs.count,
          count: rs.rows.length,
          instructions: rs.rows,
        });
      }
    } catch (error) {
      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { instructionId } = req.params;

      let instruction = await Instruction.findOne({
        where: { instructionId },
      });

      if (!instruction) throw new Error(ERROR_NOT_FOUND);

      let { title, description, url, thumbnail, instructionCategoryId, type } =
        req.body;

      if (type) {
        if (!['VIDEO', 'OTHER'].includes(type))
          throw new Error(ERROR_INVALID_PARAMETER);
      }

      if (instructionCategoryId) {
        let instructionCategory = await InstructionCategory.findOne({
          where: { instructionCategoryId },
        });

        if (!instructionCategory) throw new Error(ERROR_NOT_FOUND);
      }

      if (req.files?.['thumbnail']) {
        let file = req.files['thumbnail'];

        if (_.isArray(file)) file = file[0];

        let filepath = `${PUBLIC_UPLOAD_DIR}/instruction/thumbnail/${instruction.instructionId}`;
        let filename = `${file.name}`;

        filename = utils.escapeFilename(filename);

        if (fs.existsSync(filepath)) fs.rmSync(filepath, { recursive: true });
        fs.mkdirSync(filepath);

        await file.mv(path.join(filepath, filename));

        thumbnail = `resources/instruction/thumbnail/${instruction.instructionId}/${filename}`;
        await instruction.update({ thumbnail }, { transaction: t });
      }

      if (req.files?.['file']) {
        let file = req.files['file'];

        if (_.isArray(file)) file = file[0];

        let filepath = `${PUBLIC_UPLOAD_DIR}/instruction/url/${instruction.instructionId}`;
        let filename = `${file.name}`;

        filename = utils.escapeFilename(filename);

        if (fs.existsSync(filepath)) fs.rmSync(filepath, { recursive: true });
        fs.mkdirSync(filepath);

        await file.mv(path.join(filepath, filename));

        file = `resources/instruction/url/${instruction.instructionId}/${filename}`;
        await instruction.update({ url: file }, { transaction: t });
      }

      await instruction.update(
        {
          title,
          description,
          url,
          thumbnail,
          instructionCategoryId,
          type,
        },
        { transaction: t },
      );

      await t.commit();

      res.send({ result: 'success', instruction });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    try {
      let { instructionIds } = req.body;

      let deleteCount = await Instruction.destroy({
        where: { instructionId: { [Op.in]: instructionIds } },
      });

      res.send({ result: 'success', deleteCount });
    } catch (error) {
      next(error);
    }
  },
};
