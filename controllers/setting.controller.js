const _ = require('lodash');
const {
  ERROR_INVALID_PARAMETER,
  ERROR_NOT_FOUND,
  ERROR_PERMISSION_DENIED,
} = require('../configs/error.vi');
const { Organization, OrganizationDepartment } = require('../models');
const { isValidNumber } = require('../utils');
module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async setting(req, res, next) {
    try {
      const {
        remindNotify,
        remindBeforeDays, // number
        nameFileBuyerTaxCode,
        nameFileSerial,
        nameFileInvoiceDate,
        nameFileSample,
        nameFileInvoiceNum,
        nameFileSupplierName,
        downloadAllOutputInvoice,
        downloadOutputInvoiceNotTable,
        selectDownloadTypeOutputInvoice, // json
        adjustNotDownloadWithSupplier,
        autoAdjustInvoice,
        downloadInputInvoiceNotTable,
        selectDownloadTypeInputInvoice, // json
        autoAdjustWithBuyerTaxCode,
        warningInListInvoice,
      } = req.body;

      const { organizationDepartmentId, organizationId } = req.account;

      if (
        !_.isBoolean(remindNotify ?? true) ||
        !_.isBoolean(nameFileBuyerTaxCode ?? true) ||
        !_.isBoolean(nameFileSerial ?? true) ||
        !_.isBoolean(nameFileInvoiceDate ?? true) ||
        !_.isBoolean(nameFileSample ?? true) ||
        !_.isBoolean(nameFileInvoiceNum ?? true) ||
        !_.isBoolean(nameFileSupplierName ?? true) ||
        !_.isBoolean(downloadAllOutputInvoice ?? true) ||
        !_.isBoolean(downloadOutputInvoiceNotTable ?? true) ||
        !_.isBoolean(adjustNotDownloadWithSupplier ?? true) ||
        !_.isBoolean(autoAdjustInvoice ?? true) ||
        !_.isBoolean(downloadInputInvoiceNotTable ?? true) ||
        !_.isBoolean(autoAdjustWithBuyerTaxCode ?? true) ||
        !_.isBoolean(warningInListInvoice ?? true) ||
        !isValidNumber(remindBeforeDays ?? 0)
      )
        throw new Error(ERROR_INVALID_PARAMETER);

      if (organizationId) {
        const organization = await Organization.findOne({
          where: { organizationId },
        });
        if (!organization) throw new Error(ERROR_NOT_FOUND);
        await organization.update({
          remindNotify,
          remindBeforeDays,
          nameFileBuyerTaxCode,
          nameFileSerial,
          nameFileInvoiceDate,
          nameFileSample,
          nameFileInvoiceNum,
          nameFileSupplierName,
          downloadAllOutputInvoice,
          downloadOutputInvoiceNotTable,
          selectDownloadTypeOutputInvoice,
          adjustNotDownloadWithSupplier,
          autoAdjustInvoice,
          downloadInputInvoiceNotTable,
          selectDownloadTypeInputInvoice,
          autoAdjustWithBuyerTaxCode,
          warningInListInvoice,
        });
        res.send({
          result: 'success',
          organization,
        });
      } else if (organizationDepartmentId) {
        const organizationDepartment = await OrganizationDepartment.findOne({
          where: { organizationDepartmentId },
        });
        if (!organizationDepartmentId) throw new Error(ERROR_NOT_FOUND);
        await organizationDepartment.update({
          remindNotify,
          remindBeforeDays,
          nameFileBuyerTaxCode,
          nameFileSerial,
          nameFileInvoiceDate,
          nameFileSample,
          nameFileInvoiceNum,
          nameFileSupplierName,
          downloadAllOutputInvoice,
          downloadOutputInvoiceNotTable,
          selectDownloadTypeOutputInvoice,
          adjustNotDownloadWithSupplier,
          autoAdjustInvoice,
          downloadInputInvoiceNotTable,
          selectDownloadTypeInputInvoice,
          autoAdjustWithBuyerTaxCode,
          warningInListInvoice,
        });
        res.send({
          result: 'success',
          organizationDepartment,
        });
      } else {
        throw new Error(ERROR_PERMISSION_DENIED);
      }
    } catch (error) {
      next(error);
    }
  },
};
