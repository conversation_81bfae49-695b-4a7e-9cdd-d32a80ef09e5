const { Op } = require('sequelize');
const { INVOICE_CATEGORY } = require('../configs/constants/invoice.constant');
const {
  ERROR_PERMISSION_DENIED,
  ERROR_INVALID_DATE,
  ERROR_NOT_FOUND,
  ERROR_MISSING_PARAMETERS,
  ERROR_ORGANIZATION_DEPARTMENT_NOT_FOUND,
  ERROR_CONNECT_CQT_FAIL,
  ERROR_USER_NOT_IN_ORGANIZATION,
  ERROR_DANG_DONG_BO,
} = require('../configs/error.vi');
const {
  CqtInvoiceHistory,
  OrganizationDepartment,
  Organization,
  Invoice,
  CqtInvoice,
} = require('../models');
const { isValidDate, isValidNumber } = require('../utils');
const _ = require('lodash');
const moment = require('moment');
const {
  cqt_getHDDV,
  cqt_getHDDR,
  cqt_authenticate,
  cqt_getHDDV_MTT,
  cqt_getHDDR_MTT,
} = require('../utils/cqt.helper');
const jwt = require('jsonwebtoken');
const notificationHelper = require('../utils/notification.helper');
const {
  NOTIFICATION_TYPE,
  TARGET_CODE,
} = require('../configs/constants/notification.constant');
const invoiceHelper = require('../utils/invoice.helper');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      const { organizationId, organizationDepartmentId } = req.account;
      if (!organizationId && !organizationDepartmentId)
        throw new Error(ERROR_USER_NOT_IN_ORGANIZATION);

      let { limit, page, orderMode, from, to, type } = req.query;
      let orderField = 'syncDate';
      orderMode = orderMode ?? 'DESC';
      if (from && !isValidDate(from)) throw new Error(ERROR_INVALID_DATE);
      if (to && !isValidDate(to)) throw new Error(ERROR_INVALID_DATE);
      console.log(moment.parseZone(to).endOf('date'));
      const conditions = [
        {
          organizationId,
          organizationDepartmentId,
        },
        type ? { type } : {},
        {
          syncDate: {
            [Op.between]: [
              moment.parseZone(from).startOf('date'),
              moment.parseZone(to).endOf('date'),
            ],
          },
        },
      ];
      let totalHDDV = 0;
      let totalHDDR = 0;
      let cqtInvoiceHistories;
      let cqtInvoiceHistories_HDDVs;
      let cqtInvoiceHistories_HDDRs;
      cqtInvoiceHistories_HDDVs = await CqtInvoiceHistory.findAll({
        where: [
          {
            organizationId,
            organizationDepartmentId,
            type: INVOICE_CATEGORY.INPUT_INVOICE,
            // syncDate: {
            //   [Op.between]: [
            //     moment.parseZone(from).startOf('date').toDate(),
            //     moment.parseZone(to).endOf('date').toDate(),
            //   ],
            // },
          },
        ],
        order: [[orderField, orderMode]],
      });
      cqtInvoiceHistories_HDDRs = await CqtInvoiceHistory.findAll({
        where: [
          {
            organizationId,
            organizationDepartmentId,
            type: INVOICE_CATEGORY.OUTPUT_INVOICE,
            // syncDate: {
            //   [Op.between]: [
            //     moment.parseZone(from).startOf('date').toDate(),
            //     moment.parseZone(to).endOf('date').toDate(),
            //   ],
            // },
          },
        ],
        order: [[orderField, orderMode]],
      });
      cqtInvoiceHistories_HDDVs?.forEach(item => {
        totalHDDV += item.succeededDownload ?? 0;
      });
      cqtInvoiceHistories_HDDRs?.forEach(item => {
        totalHDDR += item.succeededDownload ?? 0;
      });
      if (isValidNumber(limit) && isValidNumber(page)) {
        limit = _.toNumber(limit);
        page = _.toNumber(page);
        cqtInvoiceHistories = await CqtInvoiceHistory.findAndCountAll({
          where: conditions,
          limit,
          offset: limit * page,
          order: [[orderField, orderMode]],
        });
      } else {
        cqtInvoiceHistories = await CqtInvoiceHistory.findAndCountAll({
          where: conditions,
          order: [[orderField, orderMode]],
        });
      }
      res.send({
        result: 'success',
        page,
        totalHDDV,
        totalHDDR,
        total: cqtInvoiceHistories.count,
        count: cqtInvoiceHistories.rows.length,
        cqtInvoiceHistories: cqtInvoiceHistories.rows,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      const { cqtInvoiceHistoryId } = req.params;
      const { organizationId, organizationDepartmentId } = req.account;

      if (!organizationId && !organizationDepartmentId)
        throw new Error(ERROR_USER_NOT_IN_ORGANIZATION);
      if (!cqtInvoiceHistoryId) throw new Error(ERROR_MISSING_PARAMETERS);

      let { limit, page, q } = req.query;
      q = q ?? '';
      let conditions = [
        {
          cqtInvoiceHistoryId,
        },
        {
          [Op.or]: [
            {
              sellerName: { [Op.like]: `%${q}%` },
            },
            { invoiceNumber: { [Op.like]: `%${q}%` } },
          ],
        },
      ];

      const cqtInvoiceHistory = (
        await CqtInvoiceHistory.findOne({
          where: {
            organizationId,
            organizationDepartmentId,
            cqtInvoiceHistoryId,
          },
          include: [{ model: CqtInvoice, where: conditions }],
        })
      )?.toJSON();

      if (!cqtInvoiceHistory) throw new Error(ERROR_NOT_FOUND);

      let total = cqtInvoiceHistory.CqtInvoices.length;
      if (isValidNumber(limit) && isValidNumber(page)) {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        cqtInvoiceHistory.CqtInvoices = cqtInvoiceHistory.CqtInvoices.slice(
          limit * page,
          limit * page + limit,
        );
      } else {
      }

      let invoices = (
        await Invoice.findAll({
          where: {
            organizationDepartmentId,
            organizationId,
            [Op.or]: [
              {
                sellerTaxCode: {
                  [Op.in]: cqtInvoiceHistory.CqtInvoices.map(
                    i => i.sellerTaxCode,
                  ),
                },
              },
              {
                serial: {
                  [Op.in]: cqtInvoiceHistory.CqtInvoices.map(i => i.serial),
                },
              },
              {
                invoiceNumber: {
                  [Op.in]: cqtInvoiceHistory.CqtInvoices.map(
                    i => i.invoiceNumber,
                  ),
                },
              },
            ],
          },
        })
      ).map(i => i.toJSON());

      let hasCode = 0;
      let noCode = 0;

      cqtInvoiceHistory.CqtInvoices = cqtInvoiceHistory.CqtInvoices.map(
        item => {
          if (item.serial.charAt(0) == 'C') hasCode += 1;
          if (item.serial.charAt(0) == 'K') noCode += 1;

          let savedInvoice = invoices.find(_item =>
            invoiceHelper.isSameInvoice(_item, item),
          );

          if (savedInvoice) {
            Object.assign(item, { invoiceId: savedInvoice.invoiceId });
          }

          return item;
        },
      );
      res.send({
        result: 'success',
        hasCode,
        noCode,
        total: total,
        count: cqtInvoiceHistory.CqtInvoices.length,
        cqtInvoices: cqtInvoiceHistory.CqtInvoices,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async reload(req, res, next) {
    const { organizationId, organizationDepartmentId } = req.account;
    try {
      const { cqtInvoiceHistoryId } = req.body;

      if (!organizationId && !organizationDepartmentId)
        throw new Error(ERROR_USER_NOT_IN_ORGANIZATION);

      /* neu dang dong bo => throw error */
      let dangDongBo = false;

      if (organizationDepartmentId) {
        dangDongBo = (
          await OrganizationDepartment.findOne({
            where: { organizationDepartmentId },
          })
        )?.dangDongBo;
      } else {
        dangDongBo = (await Organization.findOne({ where: { organizationId } }))
          ?.dangDongBo;
      }

      if (dangDongBo) {
        throw new Error(ERROR_DANG_DONG_BO);
      }

      /* set dangDongBo = true */
      if (organizationId) {
        await Organization.update(
          { dangDongBo: true },
          { where: { organizationId } },
        );
      } else if (organizationDepartmentId) {
        await OrganizationDepartment.update(
          { dangDongBo: true },
          { where: { organizationDepartmentId } },
        );
      }

      if (!cqtInvoiceHistoryId) throw new Error(ERROR_MISSING_PARAMETERS);

      const cqtInvoiceHistory = await CqtInvoiceHistory.findOne({
        where: {
          cqtInvoiceHistoryId,
        },
      });
      if (!cqtInvoiceHistory) throw new Error(ERROR_NOT_FOUND);

      res.send({
        result: 'success',
      });

      // lấy token từ organization
      let token;
      let username;
      let password;
      let organizationDepartment;
      let organization;
      if (organizationDepartmentId) {
        organizationDepartment = await OrganizationDepartment.findOne({
          where: {
            organizationDepartmentId,
          },
        });
        if (!organizationDepartment)
          throw new Error(ERROR_ORGANIZATION_DEPARTMENT_NOT_FOUND);
        token = organizationDepartment.cqtToken;
        username = organizationDepartment.username;
        password = organizationDepartment.password;
      } else if (organizationId) {
        organization = await Organization.findOne({
          where: {
            organizationId,
          },
        });
        if (!organization)
          throw new Error(ERROR_ORGANIZATION_DEPARTMENT_NOT_FOUND);
        token = organization.cqtToken;
        username = organization.username;
        password = organization.password;
      }
      let decode = jwt.decode(token, {});
      if (parseInt(decode.exp * 1000) < Date.now()) {
        token = (await cqt_authenticate(username, password)).token;
        if (!token) throw new Error(ERROR_CONNECT_CQT_FAIL);
        if (organizationDepartment)
          await organizationDepartment.update({ cqtToken: token });
        if (organization) await organization.update({ cqtToken: token });
      }
      let syncDate = cqtInvoiceHistory.syncDate;
      let from = moment(syncDate).startOf('date').format('DD/MM/yyyyTHH:mm:ss');
      let to = moment(syncDate).endOf('date').format('DD/MM/yyyyTHH:mm:ss');
      let search = `tdlap=ge=${from};tdlap=le=${to}`;
      let config = {
        params: {
          size: 50,
          search,
        },
        headers: { Authorization: `Bearer ${token}` },
      };
      let taxCode =
        req.account.OrganizationDepartment?.OrganizationBranch.taxCode ??
        req.account.Organization?.taxCode;
      let totalHDDV = 0;
      let totalHDDR = 0;
      if (cqtInvoiceHistory.type === INVOICE_CATEGORY.INPUT_INVOICE) {
        try {
          let stateCQT = 'start';
          while (stateCQT !== null) {
            if (stateCQT !== 'start') {
              config.params.state = stateCQT;
            } else {
              delete config.params.state;

              // config.params.state = undefined;
            }
            let { datas, state } = await cqt_getHDDV(
              config,
              organizationId,
              organizationDepartmentId,
              taxCode,
              syncDate,
              syncDate,
            );
            totalHDDV += datas?.length;
            stateCQT = state;
          }
        } catch (error) {
          console.log(error);
        }
      }

      // kiem tra cấu hình nếu downloadAllOutputInvoice ko bật thì không chạy cqt_getHDDR cqt_getHDDR_MTT
      const isDownloadAllOutputInvoice =
        !!organization?.downloadAllOutputInvoice ||
        !!organizationDepartment?.downloadAllOutputInvoice;

      if (cqtInvoiceHistory.type === INVOICE_CATEGORY.OUTPUT_INVOICE) {
        try {
          let stateCQT = 'start';
          while (stateCQT !== null) {
            if (stateCQT !== 'start') {
              config.params.state = stateCQT;
            } else {
              delete config.params.state;

              // config.params.state = undefined;
            }

            let { datas, state } = await cqt_getHDDR(
              config,
              organizationId,
              organizationDepartmentId,
              taxCode,
              syncDate,
              syncDate,
            );
            totalHDDR += datas?.length;
            stateCQT = state;
          }
        } catch (error) {
          console.log(error);
        }
      }

      /* MTT */
      if (cqtInvoiceHistory.type === INVOICE_CATEGORY.INPUT_INVOICE) {
        try {
          let stateCQT = 'start';
          while (stateCQT !== null) {
            if (stateCQT !== 'start') {
              config.params.state = stateCQT;
            } else {
              delete config.params.state;

              // config.params.state = undefined;
            }
            let { datas, state } = await cqt_getHDDV_MTT(
              config,
              organizationId,
              organizationDepartmentId,
              taxCode,
              syncDate,
              syncDate,
            );
            totalHDDV += datas?.length;
            stateCQT = state;
          }
        } catch (error) {
          console.log(error);
        }
      }
      if (
        cqtInvoiceHistory.type === INVOICE_CATEGORY.OUTPUT_INVOICE &&
        !!isDownloadAllOutputInvoice
      ) {
        try {
          let stateCQT = 'start';
          while (stateCQT !== null) {
            if (stateCQT !== 'start') config.params.state = stateCQT;
            else {
              delete config.params.state;

              // config.params.state = undefined;
            }
            let { datas, state } = await cqt_getHDDR_MTT(
              config,
              organizationId,
              organizationDepartmentId,
              taxCode,
              syncDate,
              syncDate,
            );
            totalHDDR += datas?.length;
            stateCQT = state;
          }
        } catch (error) {
          console.log(error);
        }
      }

      let [noti] =
        await notificationHelper.createNotificationAndSendSyncedEvent([
          {
            type: NOTIFICATION_TYPE.ACTIVITY,
            targetCode: TARGET_CODE.CQT_SYNC,
            title: `Hoàn tất tải lại danh sách hoá đơn từ CQT từ ${moment(
              syncDate,
            ).format('DD/MM/YYYY')}}`,
            Accounts: [{ accountId: req.account.accountId }],
          },
        ]);

      /* set dangDongBo => false */
      if (organizationId) {
        await Organization.update(
          { dangDongBo: false },
          { where: { organizationId } },
        );
      } else if (organizationDepartmentId) {
        await OrganizationDepartment.update(
          { dangDongBo: false },
          { where: { organizationDepartmentId } },
        );
      }
    } catch (error) {
      /* set dangDongBo => false */
      if (organizationId) {
        await Organization.update(
          { dangDongBo: false },
          { where: { organizationId } },
        );
      } else if (organizationDepartmentId) {
        await OrganizationDepartment.update(
          { dangDongBo: false },
          { where: { organizationDepartmentId } },
        );
      }

      next(error);
    }
  },
};
