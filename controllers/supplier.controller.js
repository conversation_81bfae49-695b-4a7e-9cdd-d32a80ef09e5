const path = require('path');
const {
  SUPPLIER_TYPE,
  SUPPLIER_VISIBLE,
} = require('../configs/constants/supplier.constant');
const { PUBLIC_UPLOAD_DIR } = require('../configs/env');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_INVALID_PARAMETER,
  ERROR_NOT_FOUND,
} = require('../configs/error.vi');
const { Supplier, Sequelize } = require('../models');
const utils = require('../utils');
const fs = require('fs');
const _ = require('lodash');
const { Op } = require('sequelize');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { companyName, supplierType, logo, urls, description, visible } =
        req.body;
      if (!companyName || !supplierType)
        throw new Error(ERROR_MISSING_PARAMETERS);

      if (
        !Object.values(SUPPLIER_TYPE).includes(supplierType) ||
        !Object.values(SUPPLIER_VISIBLE).includes(visible)
      )
        throw new Error(ERROR_INVALID_PARAMETER);

      if (urls) {
        if (!_.isArray(urls)) {
          urls = [urls];
        }
        //if(urls.includes("")) throw new Error(ERROR_INVALID_PARAMETER)
      }

      const supplier = await Supplier.create({
        companyName,
        supplierType,
        urls,
        description,
        visible,
      });

      if (req.files?.['logo']) {
        let file = req.files['logo'];
        if (_.isArray(file)) file = file[0];

        const filepath = `${PUBLIC_UPLOAD_DIR}/supplier-logo/${supplier.supplierId}`;
        const filename = utils.escapeFilename(file.name);

        if (fs.existsSync(filepath)) fs.rmSync(filepath, { recursive: true });
        fs.mkdirSync(filepath);

        await file.mv(path.join(filepath, filename));

        logo = `resources/supplier-logo/${supplier.supplierId}/${filename}`;

        await supplier.update({ logo }, { transaction: t });
      }

      //console.log(supplier);
      await t.commit();
      res.send({
        result: 'success',
        supplier,
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      const { supplierId } = req.params;
      const supplier = await Supplier.findOne({
        where: { supplierId },
      });

      if (!supplier) throw new Error(ERROR_NOT_FOUND);
      res.send({
        result: 'success',
        supplier,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let { q, limit, page, orderField, orderMode, supplierType } = req.query;
      q = q ?? '';
      orderField = orderField ?? 'updatedAt';
      orderMode = orderMode ?? 'DESC';

      let conditions = {
        [Op.and]: [
          {
            [Op.or]: [
              { companyName: { [Op.like]: `%${q}%` } },
              { description: { [Op.like]: `%${q}%` } },
            ],
          },
          supplierType
            ? {
                supplierType,
              }
            : {},
        ],
      };

      if (!utils.isValidNumber(limit) || !utils.isValidNumber(page)) {
        let ss = (
          await Supplier.findAll({
            where: conditions,
            order: [[orderField, orderMode]],
          })
        ).map(r => r.toJSON());

        res.send({
          result: 'success',
          total: ss.length,
          suppliers: ss,
        });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        let ss = await Supplier.findAndCountAll({
          where: conditions,
          limit,
          offset: limit * page,
          order: [[orderField, orderMode]],
        });

        res.send({
          result: 'success',
          page,
          total: ss.count,
          count: ss.rows.length,
          suppliers: ss.rows,
        });
      }
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { companyName, supplierType, logo, urls, description } = req.body;
      const { supplierId } = req.params;
      if (supplierType && !Object.values(SUPPLIER_TYPE).includes(supplierType))
        throw new Error(ERROR_INVALID_PARAMETER);

      if (urls) {
        if (!_.isArray(urls)) {
          urls = [urls];
        }
        //if(urls.includes("")) throw new Error(ERROR_INVALID_PARAMETER)
      }

      const supplier = await Supplier.findOne({
        where: { supplierId },
      });

      if (!supplier) throw new Error(ERROR_NOT_FOUND);

      if (req.files?.['logo']) {
        let file = req.files['logo'];
        if (_.isArray(file)) file = file[0];

        const filepath = `${PUBLIC_UPLOAD_DIR}/supplier-logo/${supplier.supplierId}`;
        const filename = utils.escapeFilename(file.name);

        if (fs.existsSync(filepath)) fs.rmSync(filepath, { recursive: true });
        fs.mkdirSync(filepath);

        await file.mv(path.join(filepath, filename));

        logo = `resources/supplier-logo/${supplier.supplierId}/${filename}`;
      }

      await supplier.update(
        {
          companyName,
          supplierType,
          logo,
          urls,
          description,
        },
        { transaction: t },
      );

      // console.log(logo);
      await t.commit();

      res.send({
        result: 'success',
        supplier,
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    try {
      const { supplierIds } = req.body;
      if (!_.isArray(supplierIds)) throw new Error(ERROR_INVALID_PARAMETER);

      let deleteCount = await Supplier.destroy({
        where: {
          supplierId: { [Op.in]: supplierIds },
        },
      });
      return res.send({
        result: 'success',
        deleteCount,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async setVisible(req, res, next) {
    try {
      const { supplierId } = req.params;
      const { visible } = req.body;
      if (!Object.values(SUPPLIER_VISIBLE).includes(visible))
        throw new Error(ERROR_INVALID_PARAMETER);

      const supplier = await Supplier.findOne({
        where: { supplierId },
      });
      if (!supplier) throw new Error(ERROR_NOT_FOUND);
      await supplier.update({
        visible,
      });
      res.send({
        result: 'success',
        supplier,
      });
    } catch (error) {
      next(error);
    }
  },
};
