const { Op } = require('sequelize');
const { isValidN<PERSON>ber, replaceInString, isValidTaxCode } = require('../utils');
const { RiskyCompany, Sequelize } = require('../models');
const _ = require('lodash');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_NOT_FOUND,
  ERROR_INVALID_PARAMETER,
  ERROR_COMPANY_TAXCODE_EXISTED,
  ERROR_MISSING_FILE,
  ERROR_INVALID_FILE,
} = require('../configs/error.vi');
const { RISKY_COMPANY_TYPE } = require('../configs/constants/company.constant');
const fs = require('fs');
const XLSX = require('xlsx');
const {
  SHEET_NAME_RISKY_COMPANY,
} = require('../configs/constants/local-path.constant');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let { q, limit, page, type, orderField, orderMode } = req.query;

      q = q ?? '';
      orderField = orderField ?? 'updatedAt';
      orderMode = orderMode ?? 'DESC';

      /**
       * @type {import('sequelize').WhereOptions<import('../types').RiskyCompany>}
       */
      let conditions = [
        {
          [Op.or]: [
            { cqtName: { [Op.like]: `%${q}%` } },
            { taxCode: { [Op.like]: `%${q}%` } },
            { companyName: { [Op.like]: `%${q}%` } },
            { note: { [Op.like]: `%${q}%` } },
          ],
          [Op.and]: [type ? { type } : {}],
        },
      ];

      if (!isValidNumber(limit) || !isValidNumber(page)) {
        let rs = await RiskyCompany.findAll({
          where: conditions,
          order: [[orderField, orderMode]],
        });

        res.send({ result: 'success', RiskyCompanies: rs });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        let rs = await RiskyCompany.findAndCountAll({
          limit,
          offset: limit * page,
          where: conditions,
          order: [[orderField, orderMode]],
        });

        res.send({
          result: 'success',
          page,
          total: rs.count,
          count: rs.rows.length,
          RiskyCompanies: rs.rows,
        });
      }
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      let { companyId } = req.params;
      if (!companyId) {
        throw new Error(ERROR_MISSING_PARAMETERS);
      }

      const riskyCompany = await RiskyCompany.findOne({
        where: {
          companyId,
        },
      });

      if (!riskyCompany) {
        throw new Error(ERROR_NOT_FOUND);
      }

      res.send({
        result: 'success',
        riskyCompany,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      const { companyName, taxCode, cqtCode, cqtName, note, type } = req.body;
      if (!taxCode || !companyName)
        throw new Error(
          replaceInString(ERROR_COMPANY_TAXCODE_EXISTED, {
            taxCode: taxCode,
          }),
        );

      if (type && !Object.values(RISKY_COMPANY_TYPE).includes(type))
        throw new Error(ERROR_INVALID_PARAMETER);

      const company = await RiskyCompany.findOne(
        {
          where: {
            taxCode,
          },
        },
        {
          transaction: t,
        },
      );

      if (company)
        throw new Error(
          replaceInString(ERROR_COMPANY_TAXCODE_EXISTED, {
            taxCode: taxCode,
          }),
        );

      let riskyCompany = await RiskyCompany.create(
        {
          companyName,
          taxCode,
          cqtCode,
          cqtName,
          note,
          type,
        },
        { transaction: t },
      );

      await t.commit();

      res.send({
        result: 'success',
        riskyCompany,
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      const { companyId } = req.params;
      const riskyCompany = await RiskyCompany.findOne(
        {
          where: { companyId },
        },
        { transaction: t },
      );

      if (!riskyCompany) throw new Error(ERROR_NOT_FOUND);

      const { companyName, cqtCode, cqtName, note, type, taxCode } = req.body;

      if (type && !Object.values(RISKY_COMPANY_TYPE).includes(type))
        throw new Error(ERROR_INVALID_PARAMETER);

      if (
        taxCode &&
        (await RiskyCompany.findOne(
          {
            where: { taxCode },
          },
          { transaction: t },
        ))
      )
        throw new Error(
          replaceInString(ERROR_COMPANY_TAXCODE_EXISTED, {
            taxCode: taxCode,
          }),
        );

      await riskyCompany.update(
        { companyName, cqtCode, cqtName, note, type, taxCode },
        { transaction: t },
      );

      await t.commit();
      res.send({
        result: 'success',
        riskyCompany,
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      const { companyIds } = req.body;
      if (!_.isArray(companyIds)) throw new Error(ERROR_INVALID_PARAMETER);
      const deleteCount = await RiskyCompany.destroy(
        {
          where: {
            companyId: { [Op.in]: companyIds },
          },
        },
        { transaction: t },
      );
      await t.commit();
      res.send({
        result: 'success',
        deleteCount,
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async uploadFile(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { type, override } = req.body;

      if (!req?.files?.['file']) throw new Error(ERROR_MISSING_FILE);

      let importFile = req.files['file'];
      if (_.isArray(importFile)) importFile = importFile[0];

      if (!importFile) throw new Error(ERROR_MISSING_FILE);

      importFile = XLSX.readFile(importFile.tempFilePath);

      let json = XLSX.utils.sheet_to_json(
        importFile.Sheets[SHEET_NAME_RISKY_COMPANY],
        {},
      );

      /**
       * @type {import('../types').RiskyCompany
       */
      let companies = [];
      let taxCodes = [];
      json.forEach(i => {
        console.log(
          'abc 1: ',
          i?.['MST'],
          'abc 2: ',
          isValidTaxCode(i?.['MST']),
          'abc 3: ',
          i?.['Tên Công ty'],
          'abc 4: ',
          i?.['Cơ quan thuế'],
          'abc 5: ',
          i?.['Mã cơ quan thuế'],
          'abc 6: ',
          i?.['Ghi chú'],
        );
        if (
          i?.['MST'] &&
          isValidTaxCode(i?.['MST']) &&
          i?.['Tên Công ty']
          // i?.['Cơ quan thuế'] &&
          // i?.['Mã cơ quan thuế'] &&
          // i?.['Ghi chú']
        ) {
          taxCodes.push(`${i?.['MST']}`);
          companies.push({
            taxCode: i?.['MST'],
            companyName: i?.['Tên Công ty'],
            cqtName: i?.['Cơ quan thuế'],
            cqtCode: i?.['Mã cơ quan thuế'],
            note: i?.['Ghi chú'],
            type:
              type == RISKY_COMPANY_TYPE.BUON_BAN
                ? RISKY_COMPANY_TYPE.BUON_BAN
                : RISKY_COMPANY_TYPE.RUI_RO,
          });
        } else {
          throw new Error(ERROR_INVALID_FILE);
        }
      });

      res.send({
        result: 'success',
      });

      if (override) {
        await RiskyCompany.bulkCreate(
          companies,
          {
            updateOnDuplicate: [
              'companyName',
              'cqtCode',
              'cqtName',
              'note',
              'type',
            ],
          },
          {
            transaction: t,
          },
        );
      } else {
        await RiskyCompany.destroy({
          where: {
            taxCode: { [Op.in]: taxCodes },
          },
          transaction: t,
        });

        await RiskyCompany.bulkCreate(companies, {
          transaction: t,
        });
      }

      await t.commit();
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
};
