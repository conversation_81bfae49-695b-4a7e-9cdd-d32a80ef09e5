const { ERROR_NOT_FOUND } = require('../configs/error.vi');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async get(req, res, next) {
    try {
      const permissionCode = require('../assets/json/permission_list.json');
      if (!permissionCode) throw new Error(ERROR_NOT_FOUND);
      return res.send({
        result: 'success',
        permissionCode,
      });
    } catch (error) {
      next(error);
    }
  },
};
