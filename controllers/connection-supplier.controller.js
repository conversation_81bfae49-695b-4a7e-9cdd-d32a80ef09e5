const {
  ConnectionSupplier,
  OrganizationDepartment,
  Supplier,
  Sequelize,
  Organization,
  ConnectionSupplierHistory,
  ConnectionSupplierInvoice,
  Notification,
  AccountNotification,
} = require('../models');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_CONNECTION_SUPPLIER_EXISTED,
  ERROR_INVALID_PARAMETER,
  ERROR_NOT_FOUND,
  ERROR_USER_NOT_IN_ORGANIZATION,
  ERROR_WRONG_PASSWORD,
  ERROR_CONFIG_NON_EXISTENT,
  ERROR_INTERNAL,
  ERROR_CONNECTION_SUPPLIER_FAILED,
  ERROR_PERMISSION_DENIED,
  ERROR_CONNECTION_SUPPLIER_NOT_EXISTED,
  ERROR_DANG_DONG_BO,
} = require('../configs/error.vi');
const _ = require('lodash');
const moment = require('moment');
const { isValidNumber, isValidDate } = require('../utils');
const { login, _synchronize } = require('../vietinvoice/vietinvoice.helper');
const { Op, and } = require('sequelize');
const { default: axios } = require('axios');
const {
  VIETINVOICE_INVOICE_URL,
  VIETINVOICE_AUTH_URL,
} = require('../vietinvoice/vietinvoice.constant');
const {
  filterInvoicesNotHaveDuplicatedByInvoiceId,
} = require('../utils/supplier-invoice.helper');
const supplierInvoiceHelper = require('../utils/supplier-invoice.helper');
const invoiceHelper = require('../utils/invoice.helper');
const { SUPPLIER_KEY } = require('../configs/constants/supplier.constant');
const { vietinvoiceHelper } = require('../vietinvoice');
const {
  NOTIFICATION_TYPE,
  TARGET_CODE,
} = require('../configs/constants/notification.constant');
const { WS_CODE } = require('../configs/constants/websocket.constant');
const notificationHelper = require('../utils/notification.helper');
const { emitSocketEvent } = require('../utils/websocket.helper');
const connectionSupplierHelper = require('../utils/connection-supplier.helper');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async connect(req, res, next) {
    try {
      const { supplierId, url, passwordConnection, usernameConnection } =
        req.body;

      const { organizationDepartmentId, organizationId } = req.account;

      if (!organizationDepartmentId && !organizationId)
        throw new Error(ERROR_USER_NOT_IN_ORGANIZATION);

      if (!supplierId || !passwordConnection || !usernameConnection)
        throw new Error(ERROR_MISSING_PARAMETERS);

      const findSupplier = await Supplier.findOne({ where: { supplierId } });
      if (!findSupplier) throw new Error(ERROR_NOT_FOUND);

      if (url && !findSupplier.urls.includes(url))
        throw new Error(ERROR_INVALID_PARAMETER);

      // const findOrganization = await Organization.findOne({
      //   where: { organizationId },
      // });
      // const findOrganizationDepartment = await OrganizationDepartment.findOne({
      //   where: { organizationDepartmentId },
      // });
      // if (!findOrganization && !findOrganizationDepartment)
      //   throw new Error(ERROR_USER_NOT_IN_ORGANIZATION);

      //console.log(findSupplier);

      if (findSupplier.supplierKey == SUPPLIER_KEY.VIETINVOICE) {
        const findConnectionSupplier = await ConnectionSupplier.findOne({
          where: {
            supplierId,
            organizationDepartmentId,
            organizationId,
            usernameConnection,
            // passwordConnection,
          },
        });

        if (findConnectionSupplier)
          throw new Error(ERROR_CONNECTION_SUPPLIER_EXISTED);

        const cookie = await login(usernameConnection, passwordConnection);
        if (!cookie) throw new Error(ERROR_INTERNAL);

        if (cookie) {
          const connectionSupplier = await ConnectionSupplier.create({
            organizationDepartmentId,
            organizationId,
            supplierId,
            url,
            usernameConnection,
            passwordConnection,
            xCSRFToken: cookie,
          });

          res.send({
            result: 'success',
            connectionSupplierId: connectionSupplier.connectionSupplierId,
            xCSRFToken: connectionSupplier.xCSRFToken,
          });

          return;
        }

        throw new Error(ERROR_NOT_FOUND);
      }

      throw new Error(ERROR_CONFIG_NON_EXISTENT);
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      const { organizationDepartmentId, organizationId } = req.account;
      let { q, limit, page, orderField, orderMode, supplierId } = req.query;
      q = q ?? '';
      orderField = orderField ?? 'updatedAt';
      orderMode = orderMode ?? 'DESC';
      let conditions = {
        [Op.and]: [
          {
            [Op.or]: [
              { usernameConnection: { [Op.like]: `%${q}%` } },
              { supplierId: { [Op.like]: `%${q}%` } },
            ],
          },
          { organizationDepartmentId },
          { organizationId },
          supplierId ? { supplierId: supplierId } : {},
        ],
      };
      if (!isValidNumber(limit) || !isValidNumber(page)) {
        let css = await ConnectionSupplier.findAll({
          where: conditions,
          order: [[orderField, orderMode]],
        });
        res.send({
          result: 'success',
          total: conditions.length,
          connectionSuppliers: css,
        });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);
        let css = await ConnectionSupplier.findAndCountAll({
          limit,
          offset: limit * page,
          where: conditions,
        });
        res.send({
          result: 'success',
          page,
          total: css.count,
          count: css.rows.length,
          connectionSuppliers: css.rows,
        });
      }
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    try {
      const { connectionSupplierId } = req.params;
      const { passwordConnection, usernameConnection } = req.body;

      const { organizationId, organizationDepartmentId } = req.account;

      const connectionSupplier = await ConnectionSupplier.findOne({
        where: {
          [Op.and]: [
            {
              connectionSupplierId: connectionSupplierId,
            },
            organizationId
              ? {
                  organizationId: organizationId,
                }
              : {},
            true ? { organizationDepartmentId } : {},
          ],
        },
      });

      if (!connectionSupplier) throw new Error(ERROR_NOT_FOUND);

      if (usernameConnection) {
        const findConnectionSupplier = await ConnectionSupplier.findOne({
          where: {
            [Op.and]: [
              { url: connectionSupplier.url },
              {
                connectionSupplierId: { [Op.ne]: connectionSupplierId },
              },
              organizationId
                ? {
                    organizationId,
                  }
                : {},
              true ? { organizationDepartmentId } : {},
              { usernameConnection },
            ],
          },
        });
        if (findConnectionSupplier)
          throw new Error(ERROR_CONNECTION_SUPPLIER_EXISTED);
      }

      // reconnect
      const cookie = await login(usernameConnection, passwordConnection);

      await connectionSupplier.update({
        passwordConnection,
        usernameConnection,
        xCSRFToken: cookie,
      });

      res.send({
        result: 'success',
        connectionSupplier,
        // connectionSupplierId: connectionSupplier.connectionSupplierId,
        // xCSRFToken: connectionSupplier.xCSRFToken,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    try {
      const { connectionSupplierIds } = req.body;
      const { organizationDepartmentId, organizationId } = req.account;
      if (!organizationDepartmentId && !organizationId)
        throw new Error(ERROR_MISSING_PARAMETERS);
      if (!_.isArray(connectionSupplierIds))
        throw new Error(ERROR_INVALID_PARAMETER);

      const deleteCount = await ConnectionSupplier.destroy({
        where: {
          [Op.and]: [
            { connectionSupplierId: { [Op.in]: connectionSupplierIds } },
            true ? { organizationDepartmentId } : {},
            true ? { organizationId } : {},
          ],
        },
      });
      res.send({
        result: 'success',
        count: deleteCount,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async synchronize(req, res, next) {
    const t = undefined;
    let { organizationDepartmentId, organizationId, taxCode } = req.account;
    try {
      let { supplierId } = req.body;
      let { from, to } = req.body;

      let listInvoice;
      if (!supplierId) throw new Error(ERROR_MISSING_PARAMETERS);
      const supplier = await Supplier.findOne({
        where: { supplierId },
      });
      const connectionSuppliers = await ConnectionSupplier.findAll({
        where: {
          supplierId,
          organizationDepartmentId,
          organizationId,
        },
        include: [{ model: Supplier }],
      });
      if (!connectionSuppliers.length || !supplier)
        throw new Error(ERROR_NOT_FOUND);
      if (!organizationDepartmentId && !organizationId)
        throw new Error(ERROR_PERMISSION_DENIED);

      /* neu dang dong bo => throw error */
      let dangDongBo = false;

      if (organizationDepartmentId) {
        dangDongBo = (
          await OrganizationDepartment.findOne({
            where: { organizationDepartmentId },
            transaction: t,
          })
        )?.dangDongBo;
      } else {
        dangDongBo = (
          await Organization.findOne({
            where: { organizationId },
            transaction: t,
          })
        )?.dangDongBo;
      }

      if (dangDongBo) {
        throw new Error(ERROR_DANG_DONG_BO);
      }

      /* set dangDongBo = true */
      if (organizationId) {
        await Organization.update(
          { dangDongBo: true },
          { where: { organizationId }, transaction: t },
        );
      } else if (organizationDepartmentId) {
        await OrganizationDepartment.update(
          { dangDongBo: true },
          { where: { organizationDepartmentId }, transaction: t },
        );
      }

      if (!from || !to) throw new Error(ERROR_MISSING_PARAMETERS);

      if (!isValidDate(from) || !isValidDate(to))
        throw new Error(ERROR_INVALID_PARAMETER);

      from = moment(from);
      to = moment(to);

      res.send({
        result: 'success',
      });

      await connectionSupplierHelper.synchronize(
        from,
        to,
        { organizationDepartmentId, organizationId, taxCode, supplierId },
        t,
      );

      await t?.commit();

      try {
        let [noti] =
          await notificationHelper.createNotificationAndSendSyncedEvent([
            {
              type: NOTIFICATION_TYPE.ACTIVITY,
              targetCode: TARGET_CODE.SUPPLIER_SYNC,
              title: `Hoàn tất đồng bộ danh sách hoá đơn từ NCC từ ${moment(
                from,
              ).format('DD-MM-YYYY')} đến ${moment(to).format('DD-MM-YYYY')}`,
              Accounts: [{ accountId: req.account.accountId }],
            },
          ]);
      } catch (error) {
        console.warn(error);
      }

      /* set dangDongBo => false */
      if (organizationId) {
        await Organization.update(
          { dangDongBo: false },
          { where: { organizationId } },
        );
      } else if (organizationDepartmentId) {
        await OrganizationDepartment.update(
          { dangDongBo: false },
          { where: { organizationDepartmentId } },
        );
      }
    } catch (error) {
      /* set dangDongBo => false */
      if (organizationId) {
        await Organization.update(
          { dangDongBo: false },
          { where: { organizationId } },
        );
      } else if (organizationDepartmentId) {
        await OrganizationDepartment.update(
          { dangDongBo: false },
          { where: { organizationDepartmentId } },
        );
      }

      await t?.rollback();
      next(error);
    }
  },
};
