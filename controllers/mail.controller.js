/* eslint-disable no-redeclare */
const { Op } = require('sequelize');
const { isValidNumber } = require('../utils');
const { Mail, Sequelize, StatusMail, Account } = require('../models');
const _ = require('lodash');
const moment = require('moment');
const {
  STATUS_VALID_MAIL,
  TYPE_MAIL,
  TYPE_SEND_MAIL,
  READ_STATUS,
  ZIMBRA_MAIL_HOST_SERVER,
  ZIMBRA_DEFAULT_EMAIL_PASSWORD,
} = require('../configs/constants/mail.constant');
const {
  ERROR_INVALID_PARAMETER,
  ERROR_NOT_FOUND,
  ERROR_INTERNAL,
  ERROR_MISSING_PARAMETERS,
  ERROR_USER_NOT_IN_ORGANIZATION,
} = require('../configs/error.vi');
const { synchronizeMailbox } = require('../utils/mailbox.helper');
const {
  SMTP_USERNAME,
  SMTP_PASSWORD,
  SMTP_SERVER,
  UPLOAD_DIR,
  PUBLIC_UPLOAD_DIR,
} = require('../configs/env');
const { sendEmail } = require('../utils/nodemailer.helper');
const notificationHelper = require('../utils/notification.helper');
const {
  NOTIFICATION_TYPE,
  TARGET_CODE,
} = require('../configs/constants/notification.constant');
const mailboxHelper = require('../utils/mailbox.helper');
const { parseFromHTMLTemplate } = require('../utils/email.helper');

module.exports = {
  /**
   *  @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      const { organizationId, organizationDepartmentId } = req.account;
      // console.log(organizationId, organizationDepartmentId);

      let {
        q,
        limit,
        page,
        orderField,
        orderMode,
        statusValidMail,
        typeMail,
        from,
        to,
        readStatus,
      } = req.query;
      q = q ?? '';
      orderField = orderField ?? 'receiveDate';
      orderMode = orderMode ?? 'DESC';

      if (readStatus && !Object.values(READ_STATUS).includes(readStatus))
        throw new Error(ERROR_INVALID_PARAMETER);
      if (
        statusValidMail &&
        !Object.values(STATUS_VALID_MAIL).includes(statusValidMail)
      ) {
        throw new Error(ERROR_INVALID_PARAMETER);
      }

      if (typeMail && !Object.values(TYPE_MAIL).includes(typeMail)) {
        throw new Error(ERROR_INVALID_PARAMETER);
      }

      /**
       * @type {import('sequelize').WhereOptions<import('../types').Mail>}
       */
      let conditions = {
        [Op.and]: [
          {
            [Op.or]: [
              { subject: { [Op.like]: `%${q}%` } },
              { senderEmail: { [Op.like]: `%${q}%` } },
            ],
          },
          {
            receiveDate: {
              [Op.between]: [
                from
                  ? moment(from).startOf('date')
                  : moment().subtract(30, 'day').startOf('date'),
                to ? moment(to).endOf('date') : moment().endOf('date'),
              ],
            },
          },
          readStatus ? { readStatus } : {},
          { organizationId },
          { organizationDepartmentId },
          typeMail
            ? typeMail == TYPE_MAIL.TRASH
              ? { isTrash: true }
              : typeMail == TYPE_MAIL.IMPORTANT
              ? { [Op.and]: [{ isImportant: true, isTrash: false }] }
              : {}
            : { isTrash: false },
        ],
      };

      if (!isValidNumber(limit) || !isValidNumber(page)) {
        let ms = (
          await Mail.findAll({
            where: conditions,
            order: [[orderField, orderMode]],
            include: [
              {
                model: StatusMail,
                where: [statusValidMail ? { statusValidMail } : {}],
                required: !!statusValidMail,
              },
            ],
          })
        ).map(mail => mail.toJSON());

        ms = ms.map(item => {
          if (item.StatusMails.length == 0) {
            item.StatusMails = [
              {
                count: item.attachments.length,
                mailId: item.mailId,
                statusValidMail: STATUS_VALID_MAIL.CHECKING,
              },
            ];
          }

          return item;
        });

        res.send({ result: 'success', mails: ms, total: ms.length });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        let ms = await Mail.findAndCountAll({
          where: conditions,
          limit,
          offset: limit * page,
          include: [
            {
              model: StatusMail,
              where: [statusValidMail ? { statusValidMail } : {}],
              required: !!statusValidMail,
            },
          ],
          order: [[orderField, orderMode]],
        });

        ms.rows = ms.rows
          .map(item => item.toJSON())
          .map(item => {
            if (item.StatusMails.length == 0) {
              item.StatusMails = [
                {
                  count: item.attachments.length,
                  mailId: item.mailId,
                  statusValidMail: STATUS_VALID_MAIL.CHECKING,
                },
              ];
            }

            return item;
          });

        res.send({
          result: 'success',
          page,
          total: ms.count,
          count: ms.rows.length,
          mails: ms.rows,
        });
      }
    } catch (error) {
      next(error);
    }
  },

  /**
   *  @type {import('../types').RequestHandler}
   */
  async deleteMail(req, res, next) {
    try {
      const { mailIds } = req.body;
      const { organizationDepartmentId, organizationId } = req.account;
      if (!_.isArray(mailIds)) throw new Error(ERROR_INVALID_PARAMETER);

      let deleteCount = await Mail.destroy({
        where: {
          mailId: { [Op.in]: mailIds },
          organizationDepartmentId,
          organizationId,
        },
      });
      return res.send({
        result: 'success',
        deleteCount,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   *  @type {import('../types').RequestHandler}
   */
  async intoTrash(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      const { mailIds } = req.body;
      const { organizationDepartmentId, organizationId } = req.account;
      if (!_.isArray(mailIds)) throw new Error(ERROR_INVALID_PARAMETER);

      //   const mails = await Mail.findAll({
      //     where: {
      //       mailId: { [Op.in]: mailIds },
      //     },
      //   });

      //   let listMailIds = [];
      //   mails.map(mail => {
      //     listMailIds.push(mail.mailId);
      //   });

      await Mail.update(
        {
          isTrash: true,
          readStatus: READ_STATUS.READ,
          isImportant: false,
        },
        {
          where: {
            mailId: { [Op.in]: mailIds },
            organizationDepartmentId,
            organizationId,
          },
          transaction: t,
        },
      );
      await t.commit();
      res.send({
        result: 'success',
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   *  @type {import('../types').RequestHandler}
   */
  async outTrash(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      const { mailIds } = req.body;
      if (!_.isArray(mailIds)) throw new Error(ERROR_INVALID_PARAMETER);

      //   const mails = await Mail.findAll({
      //     where: {
      //       mailId: { [Op.in]: mailIds },
      //     },
      //   });

      //   let listMailIds = [];
      //   mails.map(mail => {
      //     listMailIds.push(mail.mailId);
      //   });

      await Mail.update(
        {
          isTrash: false,
        },
        {
          where: {
            mailId: { [Op.in]: mailIds },
          },
          transaction: t,
        },
      );
      await t.commit();
      res.send({
        result: 'success',
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   *  @type {import('../types').RequestHandler}
   */
  async setImportantMail(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      const { mailIds } = req.body;
      if (!_.isArray(mailIds)) throw new Error(ERROR_INVALID_PARAMETER);

      //   const mails = await Mail.findAll({
      //     where: {
      //       mailId: { [Op.in]: mailIds },
      //     },
      //   });

      //   let listMailIds = [];
      //   mails.map(mail => {
      //     listMailIds.push(mail.mailId);
      //   });

      await Mail.update(
        {
          isImportant: true,
        },
        {
          where: {
            mailId: { [Op.in]: mailIds },
          },
          transaction: t,
        },
      );
      await t.commit();
      res.send({
        result: 'success',
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   *  @type {import('../types').RequestHandler}
   */
  async setUnimportantMail(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      const { mailIds } = req.body;
      if (!_.isArray(mailIds)) throw new Error(ERROR_INVALID_PARAMETER);
      await Mail.update(
        {
          isImportant: false,
        },
        {
          where: {
            mailId: { [Op.in]: mailIds },
          },
          transaction: t,
        },
      );
      await t.commit();
      res.send({
        result: 'success',
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   *  @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      let { organizationDepartmentId, organizationId } = req.account;
      const { mailId } = req.params;
      const mail = await Mail.findOne({
        where: { mailId, organizationDepartmentId, organizationId },
      });
      if (!mail) throw new Error(ERROR_NOT_FOUND);
      await mail.update({
        readStatus: READ_STATUS.READ,
      });

      const totalUnreadEmail = await Mail.findAndCountAll({
        where: {
          organizationDepartmentId,
          organizationId,
          readStatus: READ_STATUS.UNREAD,
        },
      });

      //  console.log(organizationDepartmentId, organizationId);

      res.send({
        result: 'success',
        mail,
        totalUnreadEmail: totalUnreadEmail.count,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   *  @type {import('../types').RequestHandler}
   */
  async setUnread(req, res, next) {
    try {
      const { mailIds } = req.body;
      const { organizationDepartmentId, organizationId } = req.account;
      if (!_.isArray(mailIds)) throw new Error(ERROR_MISSING_PARAMETERS);

      await Mail.update(
        {
          readStatus: READ_STATUS.UNREAD,
        },
        {
          where: {
            mailId: { [Op.in]: mailIds },
            organizationId,
            organizationDepartmentId,
          },
        },
      );

      const totalUnreadEmail = await Mail.findAndCountAll({
        where: {
          organizationDepartmentId,
          organizationId,
          readStatus: READ_STATUS.UNREAD,
        },
      });

      res.send({
        result: 'success',
        totalUnreadEmail: totalUnreadEmail.count,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   *  @type {import('../types').RequestHandler}
   */
  async setRead(req, res, next) {
    try {
      const { mailIds } = req.body;
      const { organizationDepartmentId, organizationId } = req.account;
      if (!_.isArray(mailIds)) throw new Error(ERROR_MISSING_PARAMETERS);

      await Mail.update(
        {
          readStatus: READ_STATUS.READ,
        },
        { where: { mailId: { [Op.in]: mailIds } } },
      );

      const totalUnreadEmail = await Mail.findAndCountAll({
        where: {
          organizationDepartmentId,
          organizationId,
          readStatus: READ_STATUS.UNREAD,
        },
      });

      res.send({
        result: 'success',
        totalUnreadEmail: totalUnreadEmail.count,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   *  @type {import('../types').RequestHandler}
   */
  async forward(req, res, next) {
    try {
      let {
        Organization,
        OrganizationDepartment,
        organizationId,
        organizationDepartmentId,
      } = req.account;

      if (!Organization && !OrganizationDepartment) {
        throw new Error(ERROR_USER_NOT_IN_ORGANIZATION);
      }

      let invoiceMailbox;

      if (Organization) {
        invoiceMailbox = Organization.invoiceMailbox;
      } else {
        invoiceMailbox = OrganizationDepartment.invoiceMailbox;
      }

      const { mailId } = req.params;
      const { ccSomeone, subject, body = '' } = req.body;

      if (!subject) throw new Error(ERROR_MISSING_PARAMETERS);

      const mail = await Mail.findOne({
        where: {
          mailId: mailId,
          organizationDepartmentId,
          organizationId,
        },
      });

      if (!mail) throw new Error(ERROR_NOT_FOUND);
      if (!_.isArray(ccSomeone)) throw new Error(ERROR_INVALID_PARAMETER);

      res.send({
        result: 'success',
      });
      // create MailObject

      let html = `${mail.content}`;

      html = `${body}<br/> Begin forwarded message: ${mail.content}`;

      await sendEmail({
        html,
        recipients: ccSomeone,
        subject,
        references: [mail.messageId],
        attachments: mail.attachments.map(attachment => ({
          path: attachment.url.replace('uploads', UPLOAD_DIR),
        })),
        saveToHistory: false,
        sender: {
          name: organizationId
            ? Organization.organizationName
            : OrganizationDepartment.departmentName,
          address: invoiceMailbox,
        },
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async synchronize(req, res, next) {
    try {
      let {
        Organization,
        OrganizationDepartment,
        organizationId,
        organizationDepartmentId,
      } = req.account;

      if (!Organization && !OrganizationDepartment) {
        throw new Error(ERROR_USER_NOT_IN_ORGANIZATION);
      }

      res.send({
        result: 'success',
      });

      let invoiceMailbox;

      if (Organization) {
        invoiceMailbox = Organization.invoiceMailbox;
      } else {
        invoiceMailbox = OrganizationDepartment.invoiceMailbox;
      }

      mailboxHelper.synchronizeMailbox({
        organizationId,
        organizationDepartmentId,
        taxCode: req.account.taxCode,
      });

      try {
        let [noti] =
          await notificationHelper.createNotificationAndSendSyncedEvent([
            {
              type: NOTIFICATION_TYPE.ACTIVITY,
              targetCode: TARGET_CODE.MAILBOX_SYNC,
              title: `Đồng bộ hòm thư thành công`,
              Accounts: [{ accountId: req.account.accountId }],
            },
          ]);
      } catch (error) {
        console.warn(error);
      }
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async validate(req, res, next) {
    try {
      let { mailIds } = req.body;

      if (!_.isArray(mailIds)) {
        throw new Error(ERROR_MISSING_PARAMETERS);
      }

      let { organizationDepartmentId, organizationId, taxCode } = req.account;

      let mails = (
        await Mail.findAll({
          where: {
            mailId: { [Op.in]: mailIds },
            organizationDepartmentId,
            organizationId,
          },
        })
      ).map(item => item.toJSON());

      mailIds = mails.map(item => item.mailId);

      /* set to checking */
      await StatusMail.destroy({ where: { mailId: { [Op.in]: mailIds } } });

      await StatusMail.bulkCreate(
        mails.map(mail => ({
          mailId: mail.mailId,
          count: mail.attachments.length,
          statusValidMail: STATUS_VALID_MAIL.CHECKING,
        })),
      );

      mails = mails.map(item => {
        item.StatusMails = [
          {
            mailId: item.mailId,
            count: item.attachments.length,
            statusValidMail: STATUS_VALID_MAIL.CHECKING,
          },
        ];

        return item;
      });

      res.send({ result: 'success', mails });

      await mailboxHelper.validateEmail(
        mails,
        {
          organizationDepartmentId,
          organizationId,
          taxCode,
        },
        true,
      );

      await notificationHelper.syncEventToOrganization(
        {
          organizationDepartmentId,
          organizationId,
        },
        TARGET_CODE.VALIDATE_MAIL,
      );
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async testSendEmail(req, res, next) {
    try {
      const { accountId } = req.account;
      let account = await Account.findOne({ where: { accountId } });
      const filePath = './assets/html/active_account_template.html';
      let recipient = {
        fullname: 'string',
        email: 'string',
        taxCode: 'string',
      };
      const html = await parseFromHTMLTemplate(filePath, {
        account: req.account,
        recipient,
      });

      res.send(html);
    } catch (error) {
      next(error);
    }
  },
};
