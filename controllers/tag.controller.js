const { Op } = require('sequelize');
const _ = require('lodash');
const { isValidNumber, isValidTaxCode } = require('../utils');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_NOT_FOUND,
  ERROR_INVALID_PARAMETER,
  ERROR_INVALID_TAXCODE,
  ERROR_USER_NOT_IN_ORGANIZATION,
} = require('../configs/error.vi');
const { Tag } = require('../models');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    try {
      const { name, color, partnerCompanyTaxCodes } = req.body;

      if (!name) throw new Error(ERROR_MISSING_PARAMETERS);

      if (partnerCompanyTaxCodes && !_.isArray(partnerCompanyTaxCodes))
        throw new Error(ERROR_INVALID_PARAMETER);

      if (partnerCompanyTaxCodes) {
        partnerCompanyTaxCodes.map(taxCode => {
          if (!isValidTaxCode(taxCode)) throw new Error(ERROR_INVALID_TAXCODE);
        });
      }

      const tag = await Tag.create({
        name,
        color,
        partnerCompanyTaxCodes: partnerCompanyTaxCodes,
        accountId: req.account.accountId,
      });

      return res.send({
        result: 'success',
        tag: tag,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      const { tagId } = req.params;
      const tag = await Tag.findOne({
        where: { tagId, accountId: req.account.accountId },
      });

      if (!tag) throw new Error(ERROR_NOT_FOUND);
      return res.send({
        result: 'success',
        tag,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let { q, limit, page, orderField, orderMode } = req.query;
      q = q ?? '';
      orderField = orderField ?? 'updatedAt';
      orderMode = orderMode ?? 'DESC';

      /**
       * @type {import('sequelize').WhereOptions<import('../types').Tag>}
       */
      let conditions = {
        [Op.and]: [
          {
            [Op.or]: [
              { name: { [Op.like]: `%${q}%` } },
              { color: { [Op.like]: `%${q}%` } },
            ],
          },
          { accountId: req.account.accountId },
        ],
      };

      if (!isValidNumber(limit) || !isValidNumber(page)) {
        let ls = (
          await Tag.findAll({
            where: conditions,
            order: [[orderField, orderMode]],
          })
        ).map(tag => tag.toJSON());

        res.send({ result: 'success', tags: ls });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        let ls = await Tag.findAndCountAll({
          where: conditions,
          limit,
          offset: limit * page,
          order: [[orderField, orderMode]],
        });

        res.send({
          result: 'success',
          page,
          total: ls.count,
          count: ls.rows.length,
          tags: ls.rows,
        });
      }
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    try {
      const { tagId } = req.params;

      const { name, color, partnerCompanyTaxCodes } = req.body;

      if (partnerCompanyTaxCodes && !_.isArray(partnerCompanyTaxCodes))
        throw new Error(ERROR_INVALID_PARAMETER);

      if (partnerCompanyTaxCodes) {
        partnerCompanyTaxCodes.map(taxCode => {
          if (!isValidTaxCode(taxCode)) throw new Error(ERROR_INVALID_TAXCODE);
        });
      }

      const tag = await Tag.findOne({
        where: { tagId, accountId: req.account.accountId },
      });

      if (!tag) throw new Error(ERROR_NOT_FOUND);

      await tag.update({
        name,
        color,
        partnerCompanyTaxCodes,
      });

      return res.send({
        result: 'success',
        tag,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    try {
      let { tagIds } = req.body;

      if (!_.isArray(tagIds)) throw new Error(ERROR_INVALID_PARAMETER);

      let deleteCount = await Tag.destroy({
        where: {
          tagId: { [Op.in]: tagIds },
          accountId: req.account.accountId,
        },
      });
      return res.send({
        result: 'success',
        deleteCount,
      });
    } catch (error) {
      next(error);
    }
  },
};
