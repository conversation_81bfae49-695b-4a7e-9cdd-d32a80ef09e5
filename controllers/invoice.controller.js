const {
  INVOICE_CATEGORY,
  DAT<PERSON>_TYPE,
  INVOICE_STATUS,
  INVOICE_STATUS_TEXT,
  TAX_STATUS_TEXT,
  INVOICE_TYPE_CODE,
  VAT_TYPE,
  TYPE_INVOICE_RECEIVE,
  PRODUCT_TYPE,
} = require('../configs/constants/invoice.constant');
const html_to_pdf = require('html-pdf-node');
const {
  ERROR_INVOICE_NOT_FOUND,

  ERROR_PERMISSION_DENIED,
  ERROR_NOT_FOUND,
  ERROR_MISSING_PARAMETERS,
  ERROR_PDF_FILE,
  ERROR_XML_FILE,
  ERROR_XML_INVALID,
  ERROR_XML_NOT_INVOICE,
  ERROR_XML_EXISTS_IN_INVOICE,
  ERROR_INVOICE_NOT_FOUND_PAYMENT,
  ERROR_NO_INVOICE_PRODUCTS,
  ERROR_INVOICE_NOT_REDUCE_TAX_RESOLUTION,
  ERROR_INVALID_INVOICE_TAXCODE,
  ERROR_INVALID_INVOICE,
  ERROR_INVALID_PARAMETER,
  ERROR_INVALID_INVOICE_TYPE_CODE,
  ERROR_INVALID_INVOICE_SERIAL,
  ERROR_MISSING_INVOICE_PRODUCTS,
  ERROR_INVALID_INVOICE_PRODUCTS,
  ERROR_MISSING_INVOICE_FILES,
  ERROR_CANNOT_READ_INVOICE_FROM_FILE,
  ERROR_DUPLICATE_INVOICE,
  ERROR_MALFORM_INVOICE,
  ERROR_OUT_OF_INVOICES,
  ERROR_DANG_EXPORT,
  ERROR_USER_NOT_IN_ORGANIZATION,
  ERROR_ORGANIZATION_HQ_NOT_FOUND,
} = require('../configs/error.vi');
const { isValidNumber } = require('../utils');
const _ = require('lodash');
const {
  Invoice,
  Organization,
  OrganizationDepartment,
  InvoiceHistory,
  Sequelize,
  InvoiceValidate,
  InvoiceTag,
  Tag,
  InvoiceAttach,
  InvoiceProduct,
  PartnerCompany,
  TaxReduceValidate,
  AcceptTaxReduce,
  RiskyCompany,
  Company,
  ResourceHistory,
} = require('../models');
const invoiceHelper = require('../utils/invoice.helper');
const moment = require('moment');
const { Op } = require('sequelize');
const {
  DATE_FORMAT,
  REQ_ERROR_CODE,
} = require('../configs/constants/other.constant');
const {
  cqt_lookup,
  cqt_validate_invoice,
  check_invoice,
} = require('../utils/cqt.helper');
const { readFileSync } = require('fs');
const { UPLOAD_DIR, PUBLIC_UPLOAD_DIR } = require('../configs/env');
const notificationHelper = require('../utils/notification.helper');
const {
  TARGET_CODE,
  NOTIFICATION_TYPE,
} = require('../configs/constants/notification.constant');
const utils = require('../utils');
const fs = require('fs');
const path = require('path');
const { sendEmail } = require('../utils/nodemailer.helper');
const { default: axios } = require('axios');
const nntHelper = require('../utils/nnt.helper');
const {
  PARTNER_COMPANY_STATUS,
} = require('../configs/constants/company.constant');

const companyHelper = require('../utils/company.helper');
const { PACKAGE_TYPE } = require('../configs/constants/package.constant');
const { ACCOUNT_LEVEL } = require('../configs/constants/account.constant');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    let { organizationDepartmentId, organizationId } = req.account;
    let { exporting = false } = req.query;

    /* admin can sync across multiple organizations */
    if (req.query.organizationId) {
      if (req.account.accountLevel == ACCOUNT_LEVEL.COMPANY_ADMIN) {
        if (
          await Organization.findOne({
            where: { organizationId, companyId: req.account.companyId },
          })
        ) {
          organizationId = req.query.organizationId;
          organizationDepartmentId = null;
        }
      }
    }

    exporting = utils.parseBoolean(exporting);

    try {
      let {
        q,
        limit,
        page,
        invoiceCategory,
        dateType,
        fromDate,
        toDate,
        dateAccounting,
        status,
        tagId,
        typeInvoiceReceive,
        statusInvoice,
        typeInvoice,
        isDeleted,
        orderField,
        orderMode,
        invoiceTypeCode,
        invoiceSerialType,
      } = req.query;
      q = q ?? '';
      let numberOrder;
      if (!orderField) {
        numberOrder = ['invoiceNumber', 'DESC'];
      }

      orderField = orderField ?? 'invoiceDate';
      orderMode = orderMode ?? 'DESC';

      if (!fromDate || !toDate) throw new Error('Thiếu dữ liệu ngày');
      if (!dateType) throw new Error(ERROR_NOT_FOUND);

      if (!organizationDepartmentId && !organizationId) {
        throw new Error(ERROR_USER_NOT_IN_ORGANIZATION);
      }

      if (exporting) {
        let isExporting = false;
        /* check dangExport */
        let organization, organizationDepartment;
        if (organizationId) {
          organization = await Organization.findOne({
            where: { organizationId },
          });

          isExporting = organization.dangExport;
        } else if (organizationDepartmentId) {
          organizationDepartment = await OrganizationDepartment.findOne({
            where: { organizationDepartmentId },
          });

          isExporting = organizationDepartment.dangExport;
        }

        if (isExporting) {
          throw new Error(ERROR_DANG_EXPORT);
        }

        if (organizationId) {
          await Organization.update(
            { dangExport: true },
            { where: { organizationId } },
          );
        } else if (organizationDepartmentId) {
          await OrganizationDepartment.update(
            { dangExport: true },
            { where: { organizationDepartmentId } },
          );
        }
      }

      dateAccounting =
        !!dateAccounting && dateAccounting != '0' ? dateAccounting : 0;
      status = !!status && status != '0' ? status : 0;
      tagId = tagId && tagId != '0' ? tagId : 0;
      typeInvoiceReceive =
        typeInvoiceReceive && typeInvoiceReceive != '0'
          ? typeInvoiceReceive
          : 0;
      statusInvoice = statusInvoice && statusInvoice != '0' ? statusInvoice : 0;

      /**
       * @type {import('sequelize').WhereOptions<import('../types').Invoice>}
       */
      let conditions = [
        // {
        //   invoiceCheckStatus: 2,
        // },
        organizationId ? { organizationId } : { organizationDepartmentId },
        q
          ? {
              [Op.or]: [
                {
                  invoiceNumber: { [Op.like]: `%${q}%` },
                },
                {
                  sellerName: { [Op.like]: `%${q}%` },
                },
                {
                  sellerTaxCode: { [Op.like]: `%${q}%` },
                },
                {
                  buyerName: { [Op.like]: `%${q}%` },
                },
                {
                  buyerTaxCode: { [Op.like]: `%${q}%` },
                },
                {
                  noDocument: { [Op.like]: `%${q}%` },
                },
                {
                  noteInvoice: { [Op.like]: `%${q}%` },
                },
                {
                  serial: { [Op.like]: `%${q}%` },
                },
              ],
            }
          : {},
        isDeleted
          ? {
              isDeleted,
            }
          : {
              [Op.or]: [
                {
                  isDeleted: {
                    [Op.is]: null,
                  },
                },
                {
                  isDeleted: {
                    [Op.eq]: false,
                  },
                },
              ],
            },
        !!INVOICE_TYPE_CODE[invoiceTypeCode] ? { invoiceTypeCode } : {},
        ['C', 'K'].includes(invoiceSerialType)
          ? { serial: { [Op.like]: `${invoiceSerialType}%` } }
          : {},
      ];
      if (Object.values(INVOICE_CATEGORY).includes(invoiceCategory)) {
        conditions.push({ invoiceCategory });
      } else {
        conditions.push({ invoiceCategory: { [Op.not]: null } });
      }

      if (dateType) {
        const fDateType = DATE_TYPE.filter(item => item.key == dateType)[0];
        let conditionDateType = {};
        conditionDateType[fDateType.attribute] = {
          [Op.between]: [
            moment(fromDate).startOf('date').toDate(),
            moment(toDate).endOf('date').toDate(),
          ],
        };
        conditions.push(conditionDateType);
        if (dateType == 6) conditions.push({ statusPayment: 5 });
      }
      if (dateAccounting == 1) {
        conditions.push({
          accountingDate: {
            [Op.ne]: null,
          },
        });
      }
      if (dateAccounting == 2) {
        conditions.push({
          accountingDate: {
            [Op.eq]: null,
          },
        });
      }
      if (status == 1) conditions.push({ statusHandleInvoice: 2 });

      /**
       * @type {import('sequelize').WhereOptions<import('../types').InvoiceValidate>}
       */
      let conditionInvoiceValidate = [];
      if (status == 2)
        conditionInvoiceValidate.push({
          checkResultBuyerName: true,
          checkResultBuyerTaxCode: true,
          checkResultBuyerAddress: true,
          checkResultSellerName: true,
          checkResultSellerAddress: true,
          checkResultSignatureNCC: true,
          checkResultSignatureCQT: true,
          checkResultHasInvoiceCode: true,
          checkResultHasCQTRecord: true,
        });
      if (status == 3)
        conditionInvoiceValidate.push({
          [Op.or]: [
            {
              checkResultBuyerName: false,
            },
            {
              checkResultBuyerTaxCode: false,
            },
            {
              checkResultBuyerAddress: false,
            },
            {
              checkResultSellerName: false,
            },
            {
              checkResultSellerAddress: false,
            },
            {
              checkResultSignatureNCC: false,
            },
            {
              checkResultSignatureCQT: false,
            },
            {
              checkResultHasInvoiceCode: false,
            },
            {
              checkResultHasCQTRecord: false,
            },
          ],
        });
      if (status == 4) conditions.push({ statusHandleInvoice: 3 });
      if (status == 5) conditions.push({ xmlFile: { [Op.eq]: null } });

      /**
       * @type {import('sequelize').WhereOptions<import('../types').Tag>}
       */
      let conditionsInvoiceTag = [{ accountId: req.account.accountId }];
      if (tagId != 0) {
        conditionsInvoiceTag.push({ tagId });
      }
      if (statusInvoice != 0) {
        conditions.push({ statusInvoice });
      }
      if (typeInvoice == 1) {
        conditions.push({ xmlFile: { [Op.ne]: null } });
      }
      if (typeInvoice == 2) {
        conditions.push({ xmlFile: { [Op.eq]: null } });
      }
      if (typeInvoiceReceive !== 0) conditions.push({ typeInvoiceReceive });
      if (!isValidNumber(page) || !isValidNumber(limit)) {
        if (utils.parseBoolean(exporting)) {
          let invoices = await Invoice.findAll({
            where: conditions,
            include: [
              {
                model: Tag,
                required: conditionsInvoiceTag.length > 1 ? true : false,
                where:
                  conditionsInvoiceTag.length > 0 ? conditionsInvoiceTag : null,
              },
              {
                model: InvoiceValidate,
                where:
                  conditionInvoiceValidate.length > 0
                    ? conditionInvoiceValidate
                    : null,
              },
            ],
            order: [[orderField, orderMode]],
          });

          if (exporting) {
            if (organizationId) {
              await Organization.update(
                { dangExport: false },
                { where: { organizationId } },
              );
            } else if (organizationDepartmentId) {
              await OrganizationDepartment.update(
                { dangExport: false },
                { where: { organizationDepartmentId } },
              );
            }
          }

          res.send({
            result: 'success',
            invoiceIds: invoices.map(i => i.invoiceId),
          });

          return;
        }

        let invoices = await Invoice.findAll({
          where: conditions,
          include: [
            // { model: Organization, include: [{ model: Organization }] },
            // {
            //   model: OrganizationDepartment,
            //   include: [
            //     {
            //       model: Organization,
            //       as: 'OrganizationBranch',
            //       include: [{ model: Organization }],
            //     },
            //   ],
            // },
            {
              model: Tag,
              required: conditionsInvoiceTag.length > 1 ? true : false,
              where:
                conditionsInvoiceTag.length > 0 ? conditionsInvoiceTag : null,
            },
            {
              model: InvoiceValidate,
              where:
                conditionInvoiceValidate.length > 0
                  ? conditionInvoiceValidate
                  : null,
            },
            // { model: InvoiceAttach },
            // { model: InvoiceHistory },
            // { model: InvoiceProduct },
          ],
          order: [[orderField, orderMode]],
        });

        if (exporting) {
          if (organizationId) {
            await Organization.update(
              { dangExport: false },
              { where: { organizationId } },
            );
          } else if (organizationDepartmentId) {
            await OrganizationDepartment.update(
              { dangExport: false },
              { where: { organizationDepartmentId } },
            );
          }
        }

        res.send({ result: 'success', invoices });
      } else {
        page = _.toNumber(page);
        limit = _.toNumber(limit);
        let invoices = await Invoice.findAndCountAll({
          limit,
          offset: limit * page,
          where: conditions,
          include: [
            // { model: Organization, include: [{ model: Organization }] },
            // {
            //   model: OrganizationDepartment,
            //   include: [
            //     {
            //       model: Organization,
            //       as: 'OrganizationBranch',
            //       include: [{ model: Organization }],
            //     },
            //   ],
            // },
            {
              model: Tag,
              required: conditionsInvoiceTag.length > 1 ? true : false,
              where:
                conditionsInvoiceTag.length > 0 ? conditionsInvoiceTag : null,
            },
            {
              model: InvoiceValidate,
              where:
                conditionInvoiceValidate.length > 0
                  ? conditionInvoiceValidate
                  : null,
            },
            // { model: InvoiceAttach },
            // { model: InvoiceHistory },
            // { model: InvoiceProduct },
            // { model: TaxReduceValidate },
          ],

          distinct: true,
          order: [[orderField, orderMode], numberOrder].filter(item => !!item),
        });

        let invoiceAttachs = await InvoiceAttach.findAll({
          where: {
            invoiceId: { [Op.in]: invoices.rows.map(i => i.invoiceId) },
          },
        });

        let invoiceTaxReduceValidates = await TaxReduceValidate.findAll({
          where: {
            invoiceId: { [Op.in]: invoices.rows.map(i => i.invoiceId) },
          },
        });

        let invoiceAttachDict = invoiceAttachs.reduce((prev, curr) => {
          if (!prev[curr.invoiceId]) {
            prev[curr.invoiceId] = [];
          }

          prev[curr.invoiceId].push(curr.toJSON());
          return prev;
        }, {});

        let invoiceTaxValidateDict = invoiceTaxReduceValidates.reduce(
          (prev, curr) => {
            if (!prev[curr.invoiceId]) {
              prev[curr.invoiceId] = [];
            }

            prev[curr.invoiceId].push(curr.toJSON());
            return prev;
          },
          {},
        );

        const totalAmountBeforeVAT = _.sumBy(invoices.rows, function (inv) {
          return Number(inv.amountBeforeVat);
        });
        const totalAmountVAT = _.sumBy(invoices.rows, function (inv) {
          return Number(inv.amountVat);
        });
        const totalFinalAmount = _.sumBy(invoices.rows, function (inv) {
          return Number(inv.finalAmount);
        });

        let partnerCompanyDict = (
          await PartnerCompany.findAll({
            where: {
              organizationDepartmentId,
              organizationId,
              [Op.or]: [
                {
                  taxCode: { [Op.in]: invoices.rows.map(i => i.sellerTaxCode) },
                },
                {
                  taxCode: { [Op.in]: invoices.rows.map(i => i.buyerTaxCode) },
                },
              ],
            },
          })
        ).reduce((prev, curr) => {
          prev[curr.taxCode] = curr.toJSON();
          return prev;
        }, {});
        invoices.rows = invoices.rows
          .map(item => item.toJSON())
          .map(item => {
            item.sellerCode = partnerCompanyDict[item.sellerTaxCode]?.unitCode;
            item.buyerCode = partnerCompanyDict[item.buyerTaxCode]?.unitCode;

            item.unitCode = item.sellerCode;

            return item;
          });
        for (const idx in invoices.rows) {
          let taxCodePartner;
          if (
            invoices.rows[idx].invoiceCategory == INVOICE_CATEGORY.INPUT_INVOICE
          )
            taxCodePartner = invoices.rows[idx].sellerTaxCode;
          if (
            invoices.rows[idx].invoiceCategory ==
            INVOICE_CATEGORY.OUTPUT_INVOICE
          )
            taxCodePartner = invoices.rows[idx].buyerTaxCode;
          // const partnerCompany = await PartnerCompany.findOne({
          //   where: { taxCode: taxCodePartner },
          // });
          invoices.rows[idx].partnerCompany =
            partnerCompanyDict[taxCodePartner];

          invoices.rows[idx].TaxReduceValidates =
            invoiceTaxValidateDict[invoices.rows[idx].invoiceId] ?? [];
          invoices.rows[idx].InvoiceAttachs =
            invoiceAttachDict[invoices.rows[idx].invoiceId] ?? [];
        }

        if (exporting) {
          if (organizationId) {
            await Organization.update(
              { dangExport: false },
              { where: { organizationId } },
            );
          } else if (organizationDepartmentId) {
            await OrganizationDepartment.update(
              { dangExport: false },
              { where: { organizationDepartmentId } },
            );
          }
        }

        res.send({
          result: 'success',
          limit,
          page,
          total: invoices.count,
          count: invoices.rows.length,
          totalAmountBeforeVAT,
          totalAmountVAT,
          totalFinalAmount,
          invoices: invoices.rows,
        });
      }
    } catch (error) {
      if (exporting) {
        if (organizationId) {
          await Organization.update(
            { dangExport: false },
            { where: { organizationId } },
          );
        } else if (organizationDepartmentId) {
          await OrganizationDepartment.update(
            { dangExport: false },
            { where: { organizationDepartmentId } },
          );
        }
      }

      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { invoiceId } = req.params;
      let { organizationId, organizationDepartmentId } = req.account;

      /* admin can sync across multiple organizations */
      if (req.query.organizationId) {
        if (req.account.accountLevel == ACCOUNT_LEVEL.COMPANY_ADMIN) {
          if (
            await Organization.findOne({
              where: { organizationId, companyId: req.account.companyId },
            })
          ) {
            organizationId = req.query.organizationId;
            organizationDepartmentId = null;
          }
        }
      }

      if (!organizationDepartmentId && !organizationId) {
        throw new Error(ERROR_USER_NOT_IN_ORGANIZATION);
      }

      let invoice = await Invoice.findOne({
        where: {
          invoiceId,
          organizationId,
          organizationDepartmentId,
        },
        include: [
          { model: Organization, include: [{ model: Organization }] },
          {
            model: OrganizationDepartment,
            include: [
              {
                model: Organization,
                as: 'OrganizationBranch',
                include: [{ model: Organization }],
              },
            ],
          },
          {
            model: Tag,
            where: { accountId: req.account.accountId },
            required: false,
          },
          { model: InvoiceValidate },
          { model: InvoiceAttach },
          { model: InvoiceProduct },
          { model: InvoiceHistory },
          { model: TaxReduceValidate },
        ],
        transaction: t,
      });

      if (!invoice) throw new Error(ERROR_INVOICE_NOT_FOUND);

      /* get detail from cqt */
      await invoiceHelper.getCqtDetailInvoice(
        invoice,
        {
          organization: req.account.Organization,
          organizationDepartment: req.account.OrganizationDepartment,
        },
        t,
      );

      /* get xml from cqt */
      await invoiceHelper.getCqtXML(
        invoice,
        {
          organization: req.account.Organization,
          organizationDepartment: req.account.OrganizationDepartment,
        },
        t,
      );
      /* from from supplier e.g Vietinvoice */
      await invoiceHelper.mergeWithSupplierInvoice(invoice, t);
      /* merge origin invoice */

      /* merge xml */
      await invoiceHelper.mergeWithXML(invoice, t);

      /* get lookup website */
      if (!invoice.supplierLookupWebsite) {
        let { supplierLookupWebsite } =
          await invoiceHelper.getSupplierReference(invoice);

        if (!supplierLookupWebsite) {
          supplierLookupWebsite = await invoiceHelper.findSpecialLookupWebsite(
            invoice,
          );
        }

        if (supplierLookupWebsite) {
          await invoice.update({ supplierLookupWebsite }, { transaction: t });
        }
      }

      await invoiceHelper.addPreviewPdf(invoice, t);

      await invoiceHelper.validateVatReduce(invoice, t);

      await t.commit();

      await invoice.reload({
        include: [
          { model: Organization, include: [{ model: Organization }] },
          {
            model: OrganizationDepartment,
            include: [
              {
                model: Organization,
                as: 'OrganizationBranch',
                include: [{ model: Organization }],
              },
            ],
          },
          {
            model: Tag,
            where: { accountId: req.account.accountId },
            required: false,
          },
          { model: InvoiceValidate },
          { model: InvoiceAttach },
          { model: InvoiceProduct },
          { model: InvoiceHistory },
          { model: TaxReduceValidate },
        ],
      });

      if (invoice.invoiceCategory == INVOICE_CATEGORY.INPUT_INVOICE) {
        let isRiskyInvoice = await RiskyCompany.findOne({
          where: { [Op.or]: [{ taxCode: invoice.buyerTaxCode }] },
        });

        invoice.setDataValue('isRiskyInvoice', !!isRiskyInvoice);
      }

      res.send({ result: 'success', invoice });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async printResult(req, res, next) {
    try {
      const { invoiceId } = req.params;
      const { organizationDepartmentId, organizationId } = req.account;
      //console.log(req.account);
      const invoice = await Invoice.findOne({
        where: {
          invoiceId,
          organizationId,
          organizationDepartmentId,
        },
        include: {
          model: InvoiceProduct,
        },
      });
      if (!invoice) throw new Error(ERROR_INVOICE_NOT_FOUND);

      const warningInListInvoice = req.account.Organization
        ? req.account.Organization.warningInListInvoice
        : req.account.OrganizationDepartment
        ? req.account.OrganizationDepartment.warningInListInvoice
        : false;
      const hideCheckCaculator =
        (warningInListInvoice && invoice.amountVat10) ||
        (warningInListInvoice && invoice.amountVat8);

      const invoiceValidate = await InvoiceValidate.findOne({
        where: {
          invoiceId,
          // organizationId,
          // organizationDepartmentId,
        },
        include: {
          model: Invoice,
          // where: { organizationId, organizationDepartmentId },
        },
      });

      if (!invoice || !invoiceValidate)
        throw new Error(ERROR_INVOICE_NOT_FOUND);

      const Handlebars = require('handlebars');
      let html = fs.readFileSync('./assets/html/print-result.html', 'utf8');

      let template = Handlebars.compile(html);

      let colorInvoiceText = '';
      switch (invoice.statusInvoice) {
        case '1': // hoa don moi
          colorInvoiceText = '#28A043';
          break;
        case '2': // hoa don thay the
          colorInvoiceText = '#7177A8';
          break;
        case '3': // hoa don dieu chinh
          colorInvoiceText = '#7177A8';
          break;
        case '4': // hoa don bi thay the
          colorInvoiceText = '#F44141';
          break;
        case '5': // hoa don da bi dieu chinh
          colorInvoiceText = '#F44141';
          break;
        case '6':
          colorInvoiceText = '#28A043';
          break;
        default:
          colorInvoiceText = '#28A043';
          break;
      }
      let invoiceTypeText = '';
      switch (invoice.invoiceType) {
        case '01':
          invoiceTypeText = 'Hóa đơn giá trị gia tăng';
          break;
        case '02':
          invoiceTypeText = 'Hóa đơn bán hàng';
          break;
        case '03':
          invoiceTypeText = 'Hóa đơn bán tài sản công';
          break;
        case '04':
          invoiceTypeText = 'Hóa đơn bán hàng dự trữ quốc gia';
          break;
        case '05':
          invoiceTypeText = 'Tem, vé, thẻ điện tử';
          break;
        case '06':
          invoiceTypeText = 'Phiếu xuất kho điện tử';
          break;
        default:
          invoiceTypeText = 'Không có thông tin';
      }

      let startsWith =
        _.startsWith(invoice.serial.toString(), 'K') ||
        _.startsWith(invoice.serial.toString(), 'k');

      let checkCalculation =
        invoice?.amountVat5 !==
          invoice?.InvoiceProducts?.reduce((total, item) => {
            if (item?.vat === '5%') {
              total + item?.vatAmount, 0;
            }
          }) &&
        invoice?.amountVat8 !==
          invoice?.InvoiceProducts?.reduce((total, item) => {
            if (item?.vat === '8%') {
              total + item?.vatAmount, 0;
            }
          }) &&
        invoice?.amountVat10 !==
          invoice?.InvoiceProducts?.reduce((total, item) => {
            if (item?.vat === '10%') {
              total + item?.vatAmount, 0;
            }
          }) &&
        invoice?.amountBeforeVat5 !==
          invoice?.InvoiceProducts?.reduce((total, item) => {
            if (item?.vat === '5%') {
              total + item?.amountTotal, 0;
            }
          }) &&
        invoice?.amountBeforeVat8 !==
          invoice?.InvoiceProducts?.reduce((total, item) => {
            if (item?.vat === '8%') {
              total + item?.amountTotal, 0;
            }
          }) &&
        invoice?.amountBeforeVat10 !==
          invoice?.InvoiceProducts?.reduce((total, item) => {
            if (item?.vat === '10%') {
              total + item?.amountTotal, 0;
            }
          }) &&
        invoice?.amountBeforeVat ===
          invoice?.InvoiceProducts?.reduce(
            (total, item) => total + item?.amountTotal,
            0,
          ) &&
        invoice?.amountVat ===
          invoice?.InvoiceProducts?.reduce(
            (total, item) => total + item?.vatAmount,
            0,
          );

      const _invoice = {
        // ngày tra cứu
        checkDate: moment(invoiceValidate.checkDate).format('DD/MM/YYYY'),

        sellerName: invoice.sellerName,
        sellerTaxCode: invoice.sellerTaxCode,
        sellerAddress: invoice.sellerAddress ?? 'Không có thông tin',

        buyerName: invoice.buyerName,
        buyerTaxCode: invoice.buyerTaxCode,
        buyerAddress: invoice.buyerAddress ?? 'Không có thông tin',

        amountBeforeVat: Intl.NumberFormat('de-DE').format(
          invoice.amountBeforeVat,
        ),
        amountVat0: invoice.amountVat0
          ? Intl.NumberFormat('de-DE').format(invoice.amountVat0 ?? 0)
          : 0,
        amountVat5: invoice.amountVat5
          ? Intl.NumberFormat('de-DE').format(invoice.amountVat5 ?? 0)
          : 0,
        amountVat8: invoice.amountVat8
          ? Intl.NumberFormat('de-DE').format(invoice.amountVat8 ?? 0)
          : 0,
        amountVat10: invoice.amountVat10
          ? Intl.NumberFormat('de-DE').format(invoice.amountVat10 ?? 0)
          : 0,
        amountVat: invoice.amountVat
          ? Intl.NumberFormat('de-DE').format(invoice.amountVat ?? 0)
          : 0,
        amountAfterVat: invoice.amountAfterVat
          ? Intl.NumberFormat('de-DE').format(invoice.amountAfterVat ?? 0)
          : 0,

        finalAmount: Intl.NumberFormat('de-DE').format(
          invoice.finalAmount ?? 0,
        ),

        // kiem tra so lieu
        checkCalculation,

        invoiceType: invoice.invoiceType,
        invoiceTypeText: invoiceTypeText,
        // invoiceTypeText: invoice.invoiceTyp,
        // mẫu số
        serial: invoice.serial,
        startWith: startsWith,
        invoiceNumber: invoice.invoiceNumber,
        // ngày lập
        invoiceDate: invoice.invoiceDate
          ? moment(invoice.invoiceDate).format('DD/MM/YYYY')
          : 'Không có thông tin',
        // ngay ki
        signDate: invoice.sellerCKS?.SigningTime
          ? moment(invoice.sellerCKS?.SigningTime).format('DD/MM/YYYY')
          : 'Không có thông tin',
        providedCodeDate: invoice.cqtCKS?.SigningTime
          ? moment(invoice.cqtCKS?.SigningTime).format('DD/MM/YYYY')
          : 'Không có thông tin',
        // ngày kí trung ngày lặp
        checkSignWithInvoiceDate: invoice.signDate
          ? moment(invoice.signDate).isSame(invoice.invoiceDate, 'day')
          : 'Không có thông tin',
        // ngày cấp trùng ngày kí
        checkProvideWithSignDate: invoice.sellerCKS?.SigningTime
          ? moment(invoice.cqtCKS?.SigningTime ?? '').isSame(
              invoice.cqtSignDate,
              'day',
            )
          : 'Không có thông tin',
        statusInvoice: invoice.statusInvoice,
        statusInvoiceText: invoice.statusInvoiceText,
        colorInvoiceText: colorInvoiceText,
        isManualInput: invoice.isManualInput,
        // ngày kí
        cqtCKSSigningTime: invoice.cqtCKS?.SigningTime
          ? moment(invoice.cqtCKS?.SigningTime).format('DD/MM/YYYY')
          : 'Không có thông tin',

        // sai thông tin người mua
        checkResultBuyerInfo:
          invoiceValidate.checkResultBuyerName &&
          invoiceValidate.checkResultBuyerTaxCode &&
          invoiceValidate.checkResultBuyerAddress,
        checkResultBuyerName: invoiceValidate.checkResultBuyerName,
        checkResultBuyerTaxCode: invoiceValidate.checkResultBuyerTaxCode,
        checkResultBuyerAddress: invoiceValidate.checkResultBuyerAddress,

        // sai thông tin người ban
        checkResultSellerInfo:
          invoiceValidate.checkResultSellerName &&
          invoiceValidate.checkResultSellerAddress &&
          invoiceValidate.checkResultSellerTaxCode,
        checkResultSellerName: invoiceValidate.checkResultSellerName,
        checkResultSellerAddress: invoiceValidate.checkResultSellerAddress,
        checkResultSellerTaxCode: invoiceValidate.checkResultSellerTaxCode,
        resultSellerTaxCode: invoiceValidate.resultSellerTaxCode,
        // Không đủ thông tin
        checkEnoughSellerInfo:
          invoiceValidate.checkResultSignatureNCC &&
          invoiceValidate.checkResultSignatureCQT,
        checkResultSignatureNCC: invoiceValidate.checkResultSignatureNCC,
        checkResultSignatureCQT: invoiceValidate.checkResultSignatureCQT,

        checkResultInCQT:
          invoiceValidate.checkResultHasCQTRecord &&
          invoiceValidate.checkResultHasInvoiceCode,
        checkResultHasCQTRecord: invoiceValidate.checkResultHasCQTRecord,
        checkResultHasInvoiceCode: invoiceValidate.checkResultHasInvoiceCode,

        notSignedByCQT:
          invoiceValidate.cqt_cts_con_hieu_luc === null &&
          invoiceValidate.cqt_cts_hop_le === null &&
          invoiceValidate.cqt_file_xml_chua_bi_sua === null &&
          invoiceValidate.cqt_thong_tin_cts_hop_le === null,

        maCQT: invoice.maCQT,
        // chữ kí số người bán
        sellerCKSSignDate: invoice?.sellerCKS?.SigningTime
          ? moment(invoice?.sellerCKS?.SigningTime).format('DD/MM/YYYY')
          : 'Không có thông tin',
        isSellerCKSSignDate: invoice?.sellerCKS?.SigningTime,
        sellerCKSSubject: invoice?.sellerCKS?.Subject ?? 'Không có thông tin',
        IssuerSeller: invoice?.sellerCKS?.Issuer ?? 'Không có thông tin',
        sellerCKSSerialNumber: invoice?.sellerCKS?.SerialNumber,
        sellerCKSNotAfter: invoice?.sellerCKS?.NotAfter
          ? moment(invoice?.sellerCKS?.NotAfter).format('DD/MM/YYYY')
          : 'Không có thông tin',
        sellerCKSNotBefore: invoice?.sellerCKS?.NotBefore
          ? moment(invoice?.sellerCKS?.NotBefore).format('DD/MM/YYYY')
          : 'Không có thông tin',
        // issuerCQTCKS
        issuerCQTCKS: invoice?.cqtCKS?.Issuer ?? 'Không có thông tin',
        subjectCQTCKS: invoice?.cqtCKS?.Subject ?? 'Không có thông tin',
        signDateCQTCKS: invoice.cqtCKS?.SigningTime
          ? moment(invoice?.cqtCKS?.SigningTime).format('DD/MM/YYYY')
          : 'Không có thông tin',
        isSignDateCQTCKS: invoice.cqtCKS?.SigningTime,
        sellerCKS: invoice.sellerCKS,
        cqtCKS: invoice.cqtCKS,
        hideCheckCaculator,

        // check manual input
        nb_dang_hoat_dong: invoiceValidate.nb_dang_hoat_dong,
        nb_khong_rui_ro_tai_tdlap: invoiceValidate.nb_khong_rui_ro_tai_tdlap,

        tb_phat_hanh:
          invoiceValidate.mauso_kyhieu_da_tb &&
          invoiceValidate.so_hd_thuoc_khoang_phat_hanh &&
          invoiceValidate.ngay_hoa_don_tu_ngay_su_dung,
        text_tb_phat_hanh:
          invoiceValidate.mauso_kyhieu_da_tb &&
          invoiceValidate.so_hd_thuoc_khoang_phat_hanh &&
          invoiceValidate.ngay_hoa_don_tu_ngay_su_dung
            ? 'Hóa đơn đúng thông báo phát hành'
            : invoiceValidate.mauso_kyhieu_da_tb === null ||
              invoiceValidate.so_hd_thuoc_khoang_phat_hanh === null ||
              invoiceValidate.ngay_hoa_don_tu_ngay_su_dung === null
            ? 'Hóa đơn chưa thông báo phát hành'
            : invoiceValidate.mauso_kyhieu_da_tb === false ||
              invoiceValidate.so_hd_thuoc_khoang_phat_hanh === false ||
              invoiceValidate.ngay_hoa_don_tu_ngay_su_dung === false
            ? 'Hóa đơn chưa đúng với thông báo phát hành'
            : 'Hóa đơn chưa đúng với thông báo phát hành',
        color_tb_phat_hanh:
          invoiceValidate?.mauso_kyhieu_da_tb &&
          invoiceValidate?.so_hd_thuoc_khoang_phat_hanh &&
          invoiceValidate?.ngay_hoa_don_tu_ngay_su_dung
            ? '#28A043'
            : invoiceValidate?.mauso_kyhieu_da_tb === null ||
              invoiceValidate?.so_hd_thuoc_khoang_phat_hanh === null ||
              invoiceValidate?.ngay_hoa_don_tu_ngay_su_dung === null
            ? '#FFA318'
            : invoiceValidate?.mauso_kyhieu_da_tb === false ||
              invoiceValidate?.so_hd_thuoc_khoang_phat_hanh === false ||
              invoiceValidate?.ngay_hoa_don_tu_ngay_su_dung === false
            ? '#f44141'
            : '#f44141',
        icon_tb_phat_hanh:
          invoiceValidate?.mauso_kyhieu_da_tb &&
          invoiceValidate?.so_hd_thuoc_khoang_phat_hanh &&
          invoiceValidate?.ngay_hoa_don_tu_ngay_su_dung
            ? true
            : invoiceValidate?.mauso_kyhieu_da_tb === null ||
              invoiceValidate?.so_hd_thuoc_khoang_phat_hanh === null ||
              invoiceValidate?.ngay_hoa_don_tu_ngay_su_dung === null
            ? null
            : invoiceValidate?.mauso_kyhieu_da_tb === false ||
              invoiceValidate?.so_hd_thuoc_khoang_phat_hanh === false ||
              invoiceValidate?.ngay_hoa_don_tu_ngay_su_dung === false
            ? false
            : false,
        mauso_kyhieu_da_tb: invoiceValidate.mauso_kyhieu_da_tb,
        so_hd_thuoc_khoang_phat_hanh:
          invoiceValidate.so_hd_thuoc_khoang_phat_hanh,
        ngay_hoa_don_tu_ngay_su_dung:
          invoiceValidate.ngay_hoa_don_tu_ngay_su_dung,
      };

      Handlebars.registerHelper(
        'ifCond',
        function (v1, v2, v3, v4, v5, v6, options) {
          if (v1 && v2 && v3 && v4 && v5 && v6) {
            return options.fn(this);
          }
          return options.inverse(this);
        },
      );

      Handlebars.registerHelper('ifCondEqual', function (v1, v2, options) {
        if (v1 == v2) {
          return options.fn(this);
        }
        return options.inverse(this);
      });

      Handlebars.registerHelper('ifNull', function (v1, options) {
        if (v1 === null) {
          return options.fn(this);
        }
        return options.inverse(this);
      });

      Handlebars.registerHelper('ifFalse', function (v1, options) {
        if (v1 == false) {
          return options.fn(this);
        }
        return options.inverse(this);
      });

      let result = template(_invoice);

      let options = { format: 'A4' };
      res.setHeader('Content-Type', 'application/pdf;charset=UTF-8');
      let pdf = await html_to_pdf.generatePdf({ content: result }, options);

      // let sellerNNTStatus;
      // let buyerNNTStatus;

      // if (invoice.invoiceCategory == INVOICE_CATEGORY.INPUT_INVOICE) {
      //   let partnerCompany = await PartnerCompany.findOne({
      //     where: { taxCode: invoice.sellerTaxCode },
      //   });

      //   sellerNNTStatus = partnerCompany?.nntStatus;
      // } else if (invoice.invoiceCategory == INVOICE_CATEGORY.OUTPUT_INVOICE) {
      //   let partnerCompany = await PartnerCompany.findOne({
      //     where: { taxCode: invoice.buyerTaxCode },
      //   });

      //   buyerNNTStatus = partnerCompany?.nntStatus;
      // }

      // if (!sellerNNTStatus) {
      //   let _rs = await nntHelper
      //     .getCompany(invoice.sellerTaxCode)
      //     .catch(console.log);

      //   if (_rs) {
      //     let status = PARTNER_COMPANY_STATUS.UNKNOWN;
      //     switch (utils.escapeStr(_rs.status)) {
      //       case utils.escapeStr('Đang hoạt động (đã được cấp GCN ĐKT)'):
      //         status = PARTNER_COMPANY_STATUS.ACTIVE;
      //         break;
      //       case utils.escapeStr('Không hoạt động tại địa chỉ đã đăng ký'):
      //         status = PARTNER_COMPANY_STATUS.INACTIVE;
      //         break;
      //       case utils.escapeStr('Tạm nghỉ kinh doanh có thời hạn'):
      //         status = PARTNER_COMPANY_STATUS.INACTIVE;
      //         break;
      //       case utils.escapeStr(
      //         'Ngừng hoạt động nhưng chưa hoàn thành thủ tục đóng MST',
      //       ):
      //         status = PARTNER_COMPANY_STATUS.INACTIVE;
      //         break;
      //     }

      //     sellerNNTStatus=status
      //   }
      // }

      // if (!buyerNNTStatus) {
      //   let _rs = await nntHelper
      //     .getCompany(invoice.buyerTaxCode)
      //     .catch(console.log);

      //   if (_rs) {
      //     let status = PARTNER_COMPANY_STATUS.UNKNOWN;
      //     switch (utils.escapeStr(_rs.status)) {
      //       case utils.escapeStr('Đang hoạt động (đã được cấp GCN ĐKT)'):
      //         status = PARTNER_COMPANY_STATUS.ACTIVE;
      //         break;
      //       case utils.escapeStr('Không hoạt động tại địa chỉ đã đăng ký'):
      //         status = PARTNER_COMPANY_STATUS.INACTIVE;
      //         break;
      //       case utils.escapeStr('Tạm nghỉ kinh doanh có thời hạn'):
      //         status = PARTNER_COMPANY_STATUS.INACTIVE;
      //         break;
      //       case utils.escapeStr(
      //         'Ngừng hoạt động nhưng chưa hoàn thành thủ tục đóng MST',
      //       ):
      //         status = PARTNER_COMPANY_STATUS.INACTIVE;
      //         break;
      //     }

      //     buyerNNTStatus=status
      //   }
      // }

      res.send(pdf);
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async payment(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { invoiceId } = req.params;
      let { organizationId, organizationDepartmentId } = req.account;
      let { paymentPersonName, paymentDate, paymentMoney, noDatePayment } =
        req.body;

      let invoice = await Invoice.findOne({
        where: {
          invoiceId,
          organizationDepartmentId,
          organizationId,
        },
      });
      if (!invoice) throw new Error(ERROR_INVOICE_NOT_FOUND);
      await InvoiceHistory.create(
        {
          invoiceId,
          paymentPersonName,
          paymentDate,
          paymentMoney,
          noDatePayment,
        },
        { transaction: t },
      );
      // tính số tiền còn nợ, cập nhật ngày nhắc/hạn thanh toán, trạng thái thanh toán
      let expiredPaymentDate = moment(paymentDate).add(noDatePayment, 'days');
      let reminderPaymentDate = moment(paymentDate)
        .add(noDatePayment, 'days')
        .subtract(4, 'days');
      let invoiceHistories = await InvoiceHistory.findAll(
        { where: { invoiceId } },
        { transaction: t },
      );
      let amountPayment = paymentMoney;
      invoiceHistories.forEach(item => {
        amountPayment += item.paymentMoney;
      });
      let debtPayment = invoice.finalAmount - amountPayment;
      let statusPayment = invoice.statusPayment;
      if (debtPayment > 0 && amountPayment > 0) statusPayment = 2;
      if (reminderPaymentDate.toDate() < new Date()) statusPayment = 4;
      if (expiredPaymentDate.toDate() < new Date()) statusPayment = 5;
      if (debtPayment === 0 || debtPayment < 0) statusPayment = 3;

      await invoice.update(
        {
          expiredPaymentDate,
          reminderPaymentDate,
          debtPayment,
          amountPayment,
          statusPayment,
        },
        { transaction: t },
      );
      await t.commit();
      res.send({ result: 'success', invoice });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async returnPayment(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { invoiceId } = req.params;
      let { organizationId, organizationDepartmentId } = req.account;

      let invoice = await Invoice.findOne({
        where: {
          invoiceId,
          organizationDepartmentId,
          organizationId,
          statusPayment: 3,
        },
      });
      if (!invoice) throw new Error(ERROR_INVOICE_NOT_FOUND_PAYMENT);
      await invoice.update(
        {
          dateReturnPayment: new Date(),
          statusPayment: 1,
          debtPayment: invoice.finalAmount,
          amountPayment: 0,
        },
        { transaction: t },
      );
      await InvoiceHistory.destroy(
        {
          where: { invoiceId },
        },
        { transaction: t },
      );
      await t.commit();
      res.send({ result: 'success', invoice });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async accounting(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { invoiceIds } = req.body;
      let { organizationId, organizationDepartmentId } = req.account;
      let { accountingPersonName, accountingDate, noDocument } = req.body;
      if (!accountingDate) throw new Error(ERROR_MISSING_PARAMETERS);
      let invoices = await Invoice.findAll({
        where: {
          invoiceId: { [Op.in]: invoiceIds },
          organizationDepartmentId,
          organizationId,
        },
      });
      if (!invoices) throw new Error(ERROR_INVOICE_NOT_FOUND);

      await Invoice.update(
        {
          accountingPersonName,
          accountingDate,
          noDocument,
        },
        {
          where: {
            invoiceId: { [Op.in]: invoiceIds },
            organizationDepartmentId,
            organizationId,
          },
        },
        { transaction: t },
      );
      await t.commit();
      res.send({ result: 'success', invoices: invoices.length });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async returnAccounting(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { invoiceIds } = req.body;
      let { organizationId, organizationDepartmentId } = req.account;
      let invoices = await Invoice.findAll({
        where: {
          invoiceId: { [Op.in]: invoiceIds },
          organizationDepartmentId,
          organizationId,
        },
      });
      if (!invoices) throw new Error(ERROR_INVOICE_NOT_FOUND);

      await Invoice.update(
        {
          accountingPersonName: null,
          accountingDate: null,
          noDocument: null,
        },
        {
          where: {
            invoiceId: { [Op.in]: invoiceIds },
            organizationDepartmentId,
            organizationId,
          },
        },
        { transaction: t },
      );
      await t.commit();
      res.send({ result: 'success', invoices: invoices.length });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async move(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { invoiceIds } = req.body;
      // let { organizationId, organizationDepartmentId } = req.account;
      let { organizationId, organizationDepartmentId } = req.body;
      let invoices = await Invoice.findAll({
        where: {
          invoiceId: { [Op.in]: invoiceIds },
          organizationDepartmentId: req.account.organizationDepartmentId,
          organizationId: req.account.organizationId,
        },
      });
      if (!invoices) throw new Error(ERROR_INVOICE_NOT_FOUND);

      await Invoice.update(
        {
          organizationId,
          organizationDepartmentId,
        },
        {
          where: {
            invoiceId: { [Op.in]: invoiceIds },
          },
        },
        { transaction: t },
      );
      await t.commit();
      res.send({ result: 'success', invoices: invoices.length });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async upload(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { invoiceId } = req.params;
      if (!req.files) throw new Error(ERROR_MISSING_PARAMETERS);
      let { xmlFile, pdfFile, attachFiles } = req.files;
      let { organizationId, organizationDepartmentId } = req.account;
      let invoice = await Invoice.findOne({
        where: {
          invoiceId,
          organizationDepartmentId,
          organizationId,
        },
      });
      if (!invoice) throw new Error(ERROR_INVOICE_NOT_FOUND);
      if (attachFiles && _.isArray(attachFiles)) {
        for (const key in attachFiles) {
          const attachFile = attachFiles[key];
          const filepath = `${PUBLIC_UPLOAD_DIR}/invoice-attach/${invoice.invoiceId}`;
          const filename = utils.escapeFilename(attachFile.name);
          const size = attachFile.size;

          if (!fs.existsSync(filepath)) fs.mkdirSync(filepath);

          await attachFile.mv(path.join(filepath, filename));

          let url = `resources/invoice-attach/${invoice.invoiceId}/${filename}`;
          await InvoiceAttach.create(
            {
              invoiceId,
              url,
              fileName: filename,
              size,
            },
            { transaction: t },
          );
        }
      }
      if (attachFiles && !_.isArray(attachFiles)) {
        const filepath = `${PUBLIC_UPLOAD_DIR}/invoice-attach/${invoice.invoiceId}`;
        const filename = utils.escapeFilename(attachFiles.name);
        const size = attachFiles.size;

        if (!fs.existsSync(filepath)) fs.mkdirSync(filepath);

        await attachFiles.mv(path.join(filepath, filename));

        let url = `resources/invoice-attach/${invoice.invoiceId}/${filename}`;
        await InvoiceAttach.create(
          {
            invoiceId,
            url,
            fileName: filename,
            size,
          },
          { transaction: t },
        );
      }
      if (pdfFile) {
        if (
          pdfFile.mimetype !== 'application/pdf' &&
          pdfFile.mimetype.split('/')[0] !== 'image'
        ) {
          throw new Error(ERROR_PDF_FILE);
        }
        const filepath = `${UPLOAD_DIR}/invoice-pdf/${invoice.invoiceId}`;
        const filename = utils.escapeFilename(pdfFile.name);

        if (fs.existsSync(filepath)) fs.rmSync(filepath, { recursive: true });
        fs.mkdirSync(filepath);

        await pdfFile.mv(path.join(filepath, filename));

        pdfFile = `uploads/invoice-pdf/${invoice.invoiceId}/${filename}`;
        await invoice.update({ pdfFile }, { transaction: t });
      }
      if (xmlFile) {
        if (_.isArray(xmlFile)) {
          [xmlFile] = xmlFile;
        }

        if (
          xmlFile.mimetype !== 'application/xml' &&
          xmlFile.mimetype !== 'text/xml'
        ) {
          throw new Error(ERROR_XML_FILE);
        }
        let invoiceXML = await invoiceHelper.invoiceFromXml(
          fs.readFileSync(xmlFile.tempFilePath, 'utf-8'),
        );
        if (!invoiceXML) throw new Error(ERROR_XML_INVALID);
        // if (Number(invoiceXML.invoiceNumber) !== Number(invoice.invoiceNumber))
        //   throw new Error(ERROR_XML_NOT_INVOICE);
        if (!invoiceHelper.compareXMLandInvoice(invoiceXML, invoice))
          throw new Error(ERROR_XML_NOT_INVOICE);
        if (
          invoiceHelper.compareXMLandInvoice(invoiceXML, invoice) &&
          invoice.xmlFile === null
        ) {
          const originFilepath = `${UPLOAD_DIR}/invoice-origin-xml/${invoice.invoiceId}`;
          const originFilename = utils.escapeFilename(xmlFile.name);

          if (fs.existsSync(originFilepath)) {
            fs.rmSync(originFilepath, { recursive: true });
          }
          fs.mkdirSync(originFilepath);

          await xmlFile.mv(path.join(originFilepath, originFilename));

          await invoice.update(
            {
              originXmlFile: `uploads/invoice-origin-xml/${invoice.invoiceId}/${originFilename}`,
            },
            { transaction: t },
          );
        }
      }

      await t.commit();

      await invoice.reload({ include: [{ model: InvoiceAttach }] });

      res.send({ result: 'success', invoice });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async deletePDF(req, res, next) {
    try {
      let { invoiceId } = req.params;
      let { organizationDepartmentId, organizationId } = req.account;
      if (!invoiceId) throw new Error(ERROR_MISSING_PARAMETERS);
      let invoice = await Invoice.findOne({
        where: { invoiceId, organizationId, organizationDepartmentId },
      });
      if (!invoice) throw new Error(ERROR_NOT_FOUND);
      const filepath = `${UPLOAD_DIR}/invoice-pdf/${invoice.invoiceId}`;
      if (fs.existsSync(filepath)) fs.rmSync(filepath, { recursive: true });
      invoice.update({ pdfFile: null });
      return res.send({
        result: 'success',
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async analyzeSystem(req, res, next) {
    try {
      const { from, to } = req.query;
      const { organizationId, organizationDepartmentId } = req.account;

      if (!organizationDepartmentId && !organizationId)
        throw new Error(ERROR_PERMISSION_DENIED);
      const conditions = [
        {
          invoiceDate: {
            [Op.between]: [
              from
                ? moment(from).startOf('date')
                : moment().subtract(30, 'day').startOf('date'),
              to ? moment(to).endOf('date') : moment().endOf('date'),
            ],
          },
        },
        true ? { organizationId } : {},
        true ? { organizationDepartmentId } : {},
      ];

      const invoices = await Invoice.findAll({
        where: conditions,
      });

      let totalAmountBeforeVat = 0,
        totalAmountVat = 0,
        totalFinalAmount = 0;
      for (let i of invoices) {
        totalAmountBeforeVat += Number(i.amountBeforeVat);
        totalAmountVat += Number(i.amountVat);
        totalFinalAmount += Number(i.finalAmount);
      }

      const succeededInvoice = invoices;
      const validInvoice = invoices;
      const invalidInvoice = invoices;
      res.send({
        result: 'success',
        succeededInvoices: {
          total: succeededInvoice.length,
          totalAmountBeforeVat,
          totalAmountVat,
          totalFinalAmount,
        },
        validInvoices: {
          total: validInvoice.length,
          totalAmountBeforeVat,
          totalAmountVat,
          totalFinalAmount,
        },
        invalidInvoices: {
          total: invalidInvoice.length,
          totalAmountBeforeVat,
          totalAmountVat,
          totalFinalAmount,
        },
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async analyzeInvoiceDate(req, res, next) {
    try {
      const { from, to } = req.query;
      const { organizationId, organizationDepartmentId } = req.account;

      if (!organizationDepartmentId && !organizationId)
        throw new Error(ERROR_PERMISSION_DENIED);

      const conditions = [
        {
          invoiceDate: {
            [Op.between]: [
              from
                ? moment(from).startOf('date')
                : moment().subtract(30, 'day').startOf('date'),
              to ? moment(to).endOf('date') : moment().endOf('date'),
            ],
          },
        },
        { amountBeforeVat: { [Op.ne]: null } },
        { amountAfterVat: { [Op.ne]: null } },
        { finalAmount: { [Op.ne]: null } },
        // true ? { organizationId } : {},
        // true ?{ organizationDepartmentId } : {},
      ];
      const invoices = await Invoice.findAll({
        where: conditions,
        include: {
          model: InvoiceValidate,
        },
      });

      let invoiceList = {};
      // filter by date
      for (let i of invoices) {
        const date = moment(i.invoiceDate).format(DATE_FORMAT);
        if (invoiceList[`${date}`]) {
          invoiceList[date].push(i);
        } else {
          invoiceList[date] = [];
          invoiceList[date].push(i);
        }
      }

      let invoiceAnalystByDate = {};
      for (const i in invoiceList) {
        invoiceAnalystByDate[i] = {};
        invoiceAnalystByDate[i].total = 0;
        invoiceAnalystByDate[i].totalAmountBeforeVat = 0;
        invoiceAnalystByDate[i].totalAmountVat = 0;
        invoiceAnalystByDate[i].totalFinalAmount = 0;
        for (let invoice of invoiceList[i]) {
          invoiceAnalystByDate[i].total += 1;
          invoiceAnalystByDate[i].totalAmountBeforeVat += Number(
            invoice.amountBeforeVat,
          );
          invoiceAnalystByDate[i].totalAmountVat += Number(invoice.amountVat);
          invoiceAnalystByDate[i].totalFinalAmount += Number(
            invoice.finalAmount,
          );
        }
      }

      res.send({
        result: 'success',
        invoiceAnalystByDate,
      });
    } catch (error) {
      next(error);
    }
  },

  async analyzeInvoiceBuyerName(req, res, next) {
    try {
      const { from, to } = req.query;
      const { organizationId, organizationDepartmentId } = req.account;

      if (!organizationDepartmentId && !organizationId)
        throw new Error(ERROR_PERMISSION_DENIED);

      const conditions = [
        {
          invoiceDate: {
            [Op.between]: [
              from
                ? moment(from).startOf('date')
                : moment().subtract(30, 'day').startOf('date'),
              to ? moment(to).endOf('date') : moment().endOf('date'),
            ],
          },
        },
        { amountBeforeVat: { [Op.ne]: null } },
        { amountAfterVat: { [Op.ne]: null } },
        { finalAmount: { [Op.ne]: null } },
        // true ? { organizationId } : {},
        // true ?{ organizationDepartmentId } : {},
      ];

      const invoices = await Invoice.findAll({
        where: conditions,
        include: {
          model: InvoiceValidate,
        },
      });
      // filter by buyerTaxCode
      let invoiceListByTaxCode = {};
      for (let i of invoices) {
        if (invoiceListByTaxCode[`${i.buyerTaxCode}`]) {
          invoiceListByTaxCode[`${i.buyerTaxCode}`].push(i);
        } else {
          invoiceListByTaxCode[`${i.buyerTaxCode}`] = [];
          invoiceListByTaxCode[`${i.buyerTaxCode}`].push(i);
        }
      }

      let invoiceAnalystByBuyerName = {};
      let total = 0;
      for (const i in invoiceListByTaxCode) {
        invoiceAnalystByBuyerName[i] = {};
        invoiceAnalystByBuyerName[i].totalInvoice = 0;
        invoiceAnalystByBuyerName[i].totalAmountBeforeVat = 0;
        for (let invoice of invoiceListByTaxCode[i]) {
          total += Number(invoice.amountBeforeVat);
          invoiceAnalystByBuyerName[i].buyerName = invoice.buyerName;
          invoiceAnalystByBuyerName[i].totalInvoice += 1;
          invoiceAnalystByBuyerName[i].totalAmountBeforeVat += Number(
            invoice.amountBeforeVat,
          );
        }
      }

      res.send({
        result: 'success',
        total,
        invoiceAnalystByBuyerName,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async validate(req, res, next) {
    try {
      let { invoiceIds } = req.body;
      let { organizationId, organizationDepartmentId } = req.account;

      let invoices = await Invoice.findAll({
        where: {
          invoiceId: { [Op.in]: invoiceIds },
          organizationDepartmentId,
          organizationId,
        },
      });

      if (!invoices) throw new Error(ERROR_INVOICE_NOT_FOUND);

      res.send({ result: 'success' });

      /* KTRA TT BUYER */
      for (const key in invoices) {
        const t = await Sequelize.transaction();

        try {
          let invoice = invoices[key];

          /* enable re-parsed */
          await invoice.update(
            {
              parsedDetailFromCqt: false,
              parsedDetailFromNCC: false,
              parsedFromXml: false,
              xmlCqtNotExist: false,
              detailCqtNotExist: false,
              xmlNCCNotExist: false,
              pdfNCCNotExist: false,
              reduceTaxResolution: null,
              previewPdfFile: null,
              supplierLookupWebsite: null,
            },
            { transaction: t },
          );

          /* get detail from cqt */
          await invoiceHelper.getCqtDetailInvoice(
            invoice,
            {
              organization: req.account.Organization,
              organizationDepartment: req.account.OrganizationDepartment,
            },
            t,
          );

          /* get xml from cqt */
          await invoiceHelper.getCqtXML(
            invoice,
            {
              organization: req.account.Organization,
              organizationDepartment: req.account.OrganizationDepartment,
            },
            t,
          );
          /* from from supplier e.g Vietinvoice */
          await invoiceHelper.mergeWithSupplierInvoice(invoice, t);
          /* merge origin invoice */

          /* merge xml */
          await invoiceHelper.mergeWithXML(invoice, t);

          /* get lookup website */
          if (!invoice.supplierLookupWebsite) {
            let { supplierLookupWebsite } =
              await invoiceHelper.getSupplierReference(invoice);

            if (!supplierLookupWebsite) {
              supplierLookupWebsite =
                await invoiceHelper.findSpecialLookupWebsite(invoice);
            }

            if (supplierLookupWebsite) {
              await invoice.update(
                { supplierLookupWebsite },
                { transaction: t },
              );
            }
          }

          await invoiceHelper.addPreviewPdf(invoice, t);

          await invoiceHelper.validateVatReduce(invoice, t);

          const isCheckCQT = await cqt_validate_invoice(invoice, t);

          // await check_invoice(invoice, t);

          await t.commit();
        } catch (error) {
          await t.rollback();
        }
      }

      await notificationHelper.createNotificationAndSendSyncedEvent([
        {
          targetCode: TARGET_CODE.VALIDATE_INVOICE,
          Accounts: [{ accountId: req.account.accountId }],
          title: `Hoàn tất kiểm tra thông tin hoá đơn`,
          type: NOTIFICATION_TYPE.ACTIVITY,
        },
      ]);
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async isDeleted(req, res, next) {
    try {
      let { organizationId, organizationDepartmentId } = req.account;
      let { isDeleted, invoiceIds } = req.body;
      isDeleted = isDeleted ?? null;
      if (!_.isArray(invoiceIds) || invoiceIds.length === 0)
        throw new Error(ERROR_MISSING_PARAMETERS);
      let invoices = await Invoice.findAll({
        where: {
          invoiceId: {
            [Op.in]: invoiceIds,
          },
          organizationId,
          organizationDepartmentId,
        },
      });
      if (!invoices) throw new Error(ERROR_INVOICE_NOT_FOUND);
      await Invoice.update(
        { isDeleted },
        {
          where: {
            invoiceId: {
              [Op.in]: invoiceIds,
            },
          },
        },
      );
      res.send({
        result: 'success',
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async note(req, res, next) {
    try {
      let { organizationId, organizationDepartmentId } = req.account;
      let { noteInvoice, invoiceIds } = req.body;
      if (!_.isArray(invoiceIds) || invoiceIds.length === 0)
        throw new Error(ERROR_MISSING_PARAMETERS);
      let invoices = await Invoice.findAll({
        where: {
          invoiceId: {
            [Op.in]: invoiceIds,
          },
          organizationId,
          organizationDepartmentId,
        },
      });
      if (!invoices) throw new Error(ERROR_INVOICE_NOT_FOUND);
      await Invoice.update(
        { noteInvoice },
        {
          where: {
            invoiceId: {
              [Op.in]: invoiceIds,
            },
          },
        },
      );
      res.send({
        result: 'success',
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async email(req, res, next) {
    try {
      let { organizationId, organizationDepartmentId } = req.account;
      let { emails, html, subject, invoiceId } = req.body;
      if (!invoiceId || !emails) throw new Error(ERROR_MISSING_PARAMETERS);
      let invoice = await Invoice.findOne({
        where: { invoiceId, organizationId, organizationDepartmentId },
      });
      if (!invoice) throw new Error(ERROR_NOT_FOUND);
      let email = await sendEmail({ emails, html, subject });
      await invoice.update({ isSendMailSupplier: true });
      res.send({
        result: 'success',
        email,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async accept(req, res, next) {
    try {
      let { organizationId, organizationDepartmentId } = req.account;
      let { invoiceId } = req.params;
      let {
        checkResultBuyerName,
        checkResultBuyerTaxCode,
        checkResultBuyerAddress,
        checkResultSellerName,
        checkResultSellerAddress,
        fromDate,
        toDate,
      } = req.body;
      if (!invoiceId) throw new Error(ERROR_MISSING_PARAMETERS);
      let invoice = await Invoice.findOne({
        where: {
          invoiceId,
          organizationId,
          organizationDepartmentId,
        },
      });
      if (!invoice) throw new Error(ERROR_INVOICE_NOT_FOUND);
      let invoiceValidate = await InvoiceValidate.findOne({
        where: {
          invoiceId,
        },
      });
      if (!invoiceValidate) throw new Error(ERROR_INVOICE_NOT_FOUND);
      let update = {};
      if (checkResultBuyerName) {
        update.checkResultBuyerName = checkResultBuyerName;
        update.fromDateAcceptBuyerName = fromDate;
        update.toDateAcceptBuyerName = toDate;
      } else if (checkResultBuyerTaxCode) {
        update.checkResultBuyerTaxCode = checkResultBuyerTaxCode;
        update.fromDateAcceptBuyerTaxCode = fromDate;
        update.toDateAcceptBuyerTaxCode = toDate;
      } else if (checkResultBuyerAddress) {
        update.checkResultBuyerAddress = checkResultBuyerAddress;
        update.fromDateAcceptBuyerAddress = fromDate;
        update.toDateAcceptBuyerAddress = toDate;
      } else if (checkResultSellerName) {
        update.checkResultSellerName = checkResultSellerName;
        update.fromDateAcceptSellerName = fromDate;
        update.toDateAcceptSellerName = toDate;
      } else if (checkResultSellerAddress) {
        update.checkResultSellerAddress = checkResultSellerAddress;
        update.fromDateAcceptSellerAddress = fromDate;
        update.toDateAcceptSellerAddress = toDate;
      }

      await invoiceValidate.update(update);
      res.send({
        result: 'success',
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async export(req, res, next) {
    let { organizationId, organizationDepartmentId } = req.account;
    try {
      let {
        invoiceIds,
        /**
         * Tham số chỉ loại danh sách cần xuất
         */ checkedRadioId,
      } = req.body;

      if (!_.isArray(invoiceIds)) throw new Error(ERROR_MISSING_PARAMETERS);

      if (!organizationDepartmentId && !organizationId) {
        throw new Error(ERROR_USER_NOT_IN_ORGANIZATION);
      }

      let isExporting = false;
      /* check dangExport */
      let organization, organizationDepartment;
      if (organizationId) {
        organization = await Organization.findOne({
          where: { organizationId },
        });

        isExporting = organization.dangExport;
      } else if (organizationDepartmentId) {
        organizationDepartment = await OrganizationDepartment.findOne({
          where: { organizationDepartmentId },
        });

        isExporting = organizationDepartment.dangExport;
      }

      if (isExporting) {
        throw new Error(ERROR_DANG_EXPORT);
      }

      if (organizationId) {
        await Organization.update(
          { dangExport: true },
          { where: { organizationId } },
        );
      } else if (organizationDepartmentId) {
        await OrganizationDepartment.update(
          { dangExport: true },
          { where: { organizationDepartmentId } },
        );
      }

      switch (checkedRadioId) {
        case 'radioInputInvoice_1': {
          let invoices = (
            await Invoice.findAll({
              attributes: [
                'invoiceId',
                'buyerPersonName',
                'buyerAddress',
                'invoiceTypeCode',
                'serial',
                'invoiceNumber',
                'invoiceReceiveDate',
                'sellerTaxCode',
                'invoiceDate',
                'signDate',
                'providedCodeDate',
                'accountingDate',
                'accountingPersonName',
                'sellerName',
                'sellerAddress',
                'finalAmount',
                'paymentCurrency',
                'paymentExchangeRate',
                'finalAmountExchange',
                'statusTax',
                'statusInvoiceText',
                'dateCQTChangeStatus',
                'dateDetectChangeStatus',
                'reminderPaymentDate',
                'expiredPaymentDate',
                'statusPayment',
                'amountPayment',
                'debtPayment',
                'noDocument',
                'noteInvoice',
                'xmlFile',
                'previewPdfFile',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceValidates = (
            await InvoiceValidate.findAll({
              attributes: [
                'invoiceId',
                'invoiceValidateId',
                'checkResultBuyerAddress',
                'checkResultBuyerTaxCode',
                'checkResultBuyerName',
                'checkResultSellerName',
                'checkResultHasInvoiceCode',
                'checkResultSellerAddress',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceValidateDict = invoiceValidates.reduce((prev, curr) => {
            prev[curr.invoiceId] = curr;

            return prev;
          }, {});

          let invoiceHistories = (
            await InvoiceHistory.findAll({
              attributes: [
                'invoiceId',
                'invoiceHistoryId',
                'paymentDate',
                'paymentMoney',
                'paymentPersonName',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceHistoryDict = invoiceHistories.reduce((prev, curr) => {
            if (!prev[curr.invoiceId]) {
              prev[curr.invoiceId] = [];
            }
            prev[curr.invoiceId].push(curr);

            return prev;
          }, {});

          let partnerCompanyDict = (
            await PartnerCompany.findAll({
              where: {
                organizationDepartmentId,
                organizationId,
                [Op.or]: [
                  {
                    taxCode: { [Op.in]: invoices.map(i => i.sellerTaxCode) },
                  },
                ],
              },
            })
          ).reduce((prev, curr) => {
            prev[curr.taxCode] = curr.unitCode;
            return prev;
          }, {});

          let invoiceTags = await InvoiceTag.findAll({
            where: { invoiceId: { [Op.in]: invoiceIds } },
          });

          let tags = await Tag.findAll({
            where: {
              tagId: { [Op.in]: invoiceTags.map(i => i.tagId) },
              accountId: req.account.accountId,
            },
          });

          let tagDict = tags.reduce((prev, curr) => {
            if (!prev[curr.tagId]) {
              prev[curr.tagId] = [];
            }

            prev[curr.tagId] = curr.toJSON();

            return prev;
          }, {});

          let invoiceTagDict = invoiceTags.reduce((prev, curr) => {
            if (!prev[curr.invoiceId]) {
              prev[curr.invoiceId] = [];
            }

            prev[curr.invoiceId].push(...tagDict[curr.tagId]);

            return prev;
          }, {});

          invoices = invoices.map(item => {
            // item.InvoiceValidate = invoiceValidateDict[item.invoiceId];
            // item.InvoiceHistories = invoiceHistoryDict[item.invoiceId] ?? [];
            item.sellerCode = partnerCompanyDict[item.sellerTaxCode];
            item.ketQuaKiemTra = checkResult(
              invoiceValidateDict[item.invoiceId],
            );
            item.thongTinThanhToan = getExportInvoicePaymentInfo({
              InvoiceHistories: invoiceHistoryDict[item.invoiceId] ?? [],
            });
            item.tepHoaDon = getExportInvoiceFileStatus(item);

            item.nhanDan = invoiceTagDict[item.invoiceId]
              ?.map(i => i.name)
              .join('\n');
            return item;
          });

          /* send all as string => reduce size */
          // let dataToSend = '';

          // invoices.forEach((item, index, { length }) => {
          //   let entries = Object.entries(item);

          //   utils.mergeSort(
          //     entries,
          //     0,
          //     entries.length - 1,
          //     ([key]) => attributeIndexDict[key],
          //   );

          //   dataToSend += `${entries.map(([key, value]) => value).join('|')}`;

          //   if (index < length - 1) {
          //     dataToSend += ';;';
          //   }
          // });

          // dataToSend += '';

          if (organizationId) {
            await Organization.update(
              { dangExport: false },
              { where: { organizationId } },
            );
          } else if (organizationDepartmentId) {
            await OrganizationDepartment.update(
              { dangExport: false },
              { where: { organizationDepartmentId } },
            );
          }

          res.send({ result: 'success', invoices: invoices });

          return;
        }
        case 'radioInputInvoice_2': {
          let invoices = (
            await Invoice.findAll({
              attributes: [
                'invoiceId',
                'buyerPersonName',
                'buyerAddress',
                'invoiceTypeCode',
                'serial',
                'invoiceNumber',
                'invoiceDate',
                'providedCodeDate',
                'accountingDate',
                'sellerName',
                'sellerAddress',
                'sellerTaxCode',
                'amountBeforeDiscount',
                'totalDiscountAmount',
                'amountBeforeVat',
                'discountWithoutVAT',
                'amountVat',
                'discountOther',
                'finalAmount',
                'paymentCurrency',
                'paymentExchangeRate',
                'finalAmountExchange',
                'noDocument',
                'accountingPersonName',
                'statusInvoiceText',
                'dateCQTChangeStatus',
                'dateDetectChangeStatus',
                'noteInvoice',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceValidates = (
            await InvoiceValidate.findAll({
              attributes: [
                'invoiceId',
                'invoiceValidateId',
                'checkResultBuyerAddress',
                'checkResultBuyerTaxCode',
                'checkResultBuyerName',
                'checkResultSellerName',
                'checkResultHasInvoiceCode',
                'checkResultSellerAddress',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceValidateDict = invoiceValidates.reduce((prev, curr) => {
            prev[curr.invoiceId] = curr;

            return prev;
          }, {});

          let partnerCompanyDict = (
            await PartnerCompany.findAll({
              where: {
                organizationDepartmentId,
                organizationId,
                [Op.or]: [
                  {
                    taxCode: { [Op.in]: invoices.map(i => i.sellerTaxCode) },
                  },
                ],
              },
            })
          ).reduce((prev, curr) => {
            prev[curr.taxCode] = curr.unitCode;
            return prev;
          }, {});

          let invoiceProducts = await InvoiceProduct.findAll({
            attributes: [
              'invoiceId',
              'invoiceProductId',
              'name',
              'productType',
            ],
            where: { invoiceId: { [Op.in]: invoiceIds } },
          });

          /* parse CKTM */
          invoiceProducts = invoiceProducts.map(item => {
            if (item.productType == PRODUCT_TYPE['CKTM']) {
              item.discountAmount = item.amountTotal;
              item.amountTotal = 0;
              item.price = 0;
            }
            return item;
          });

          let invoiceProductDict = invoiceProducts.reduce((prev, curr) => {
            if (!prev[curr.invoiceId]) {
              prev[curr.invoiceId] = [];
            }

            prev[curr.invoiceId].push(curr.toJSON());

            return prev;
          }, {});

          let invoiceTags = await InvoiceTag.findAll({
            where: { invoiceId: { [Op.in]: invoiceIds } },
          });

          let tags = await Tag.findAll({
            where: {
              tagId: { [Op.in]: invoiceTags.map(i => i.tagId) },
              accountId: req.account.accountId,
            },
          });

          let tagDict = tags.reduce((prev, curr) => {
            if (!prev[curr.tagId]) {
              prev[curr.tagId] = [];
            }

            prev[curr.tagId] = curr.toJSON();

            return prev;
          }, {});

          let invoiceTagDict = invoiceTags.reduce((prev, curr) => {
            if (!prev[curr.invoiceId]) {
              prev[curr.invoiceId] = [];
            }

            prev[curr.invoiceId].push(...tagDict[curr.tagId]);

            return prev;
          }, {});

          invoices = invoices.map(item => {
            // item.InvoiceValidate = invoiceValidateDict[item.invoiceId];
            // item.InvoiceHistories = invoiceHistoryDict[item.invoiceId] ?? [];
            item.sellerCode = partnerCompanyDict[item.sellerTaxCode];
            item.ketQuaKiemTra = checkResult(
              invoiceValidateDict[item.invoiceId],
            );
            item.nhanDan = invoiceTagDict[item.invoiceId]
              ?.map(i => i.name)
              .join('\n');

            item.thueSuat = checKThue(invoiceProductDict[item.invoiceId] ?? []);

            return item;
          });

          /* send all as string => reduce size */
          // let dataToSend = '';

          // invoices.forEach((item, index, { length }) => {
          //   dataToSend += `${Object.values(item).join('|')}`;

          //   if (index < length - 1) {
          //     dataToSend += ';;';
          //   }
          // });

          // dataToSend += '';

          if (organizationId) {
            await Organization.update(
              { dangExport: false },
              { where: { organizationId } },
            );
          } else if (organizationDepartmentId) {
            await OrganizationDepartment.update(
              { dangExport: false },
              { where: { organizationDepartmentId } },
            );
          }

          res.send({ result: 'success', invoices: invoices });

          return;
        }
        case 'radioInputInvoice_3': {
          let invoices = (
            await Invoice.findAll({
              attributes: [
                'invoiceId',
                'buyerName',
                'buyerAddress',
                'invoiceTypeCode',
                'serial',
                'invoiceNumber',
                'invoiceDate',
                'providedCodeDate',
                'accountingDate',
                'sellerCode',
                'sellerName',
                'sellerAddress',
                'sellerTaxCode',
                'discountOther',
                'paymentCurrency',
                'paymentExchangeRate',
                'noDocument',
                'accountingPersonName',
                'noteInvoice',
                'statusInvoiceText',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceDict = invoices.reduce((prev, curr) => {
            prev[curr.invoiceId] = curr;
            return prev;
          }, {});

          let invoiceProducts = await InvoiceProduct.findAll({
            attributes: [
              'invoiceId',
              'invoiceProductId',
              'name',
              'amountTotal',
              'quantity',
              'unit',
              'vat',
              'vatAmount',
              'discountAmount',
              'finalAmount',
              'price',
              'productType',
            ],
            where: { invoiceId: { [Op.in]: invoiceIds } },
          });

          /* parse CKTM */
          invoiceProducts = invoiceProducts.map(item => {
            if (item.productType == PRODUCT_TYPE['CKTM']) {
              item.discountAmount = item.amountTotal;
              item.amountTotal = 0;
              item.price = 0;
            }
            return item;
          });

          let invoiceProductDict = invoiceProducts.reduce((prev, curr) => {
            if (!prev[curr.invoiceId]) {
              prev[curr.invoiceId] = [];
            }

            prev[curr.invoiceId].push(curr.toJSON());

            return prev;
          }, {});

          let invoiceTags = await InvoiceTag.findAll({
            where: { invoiceId: { [Op.in]: invoiceIds } },
          });

          let tags = await Tag.findAll({
            where: {
              tagId: { [Op.in]: invoiceTags.map(i => i.tagId) },
              accountId: req.account.accountId,
            },
          });

          let tagDict = tags.reduce((prev, curr) => {
            if (!prev[curr.tagId]) {
              prev[curr.tagId] = [];
            }

            prev[curr.tagId] = curr.toJSON();

            return prev;
          }, {});

          let invoiceTagDict = invoiceTags.reduce((prev, curr) => {
            if (!prev[curr.invoiceId]) {
              prev[curr.invoiceId] = [];
            }

            prev[curr.invoiceId].push(...tagDict[curr.tagId]);

            return prev;
          }, {});

          let invoiceValidates = (
            await InvoiceValidate.findAll({
              attributes: [
                'invoiceId',
                'invoiceValidateId',
                'checkResultBuyerAddress',
                'checkResultBuyerTaxCode',
                'checkResultBuyerName',
                'checkResultSellerName',
                'checkResultHasInvoiceCode',
                'checkResultSellerAddress',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceValidateDict = invoiceValidates.reduce((prev, curr) => {
            prev[curr.invoiceId] = curr;

            return prev;
          }, {});

          let data = invoiceProducts.map(product => {
            let invoice = invoiceDict[product.invoiceId];
            let item = { ...invoice };

            item.dienGiai = product.name;
            item.tongTienHang = product.amountTotal;
            item.tienChietKhau = product.discountAmount;
            item.doanhSoBanChuaThue =
              product.productType == PRODUCT_TYPE['CKTM']
                ? 0
                : item.tongTienHang - item.tienChietKhau;
            item.thueSuat = product.vat;
            item.thueGtgt = product.vatAmount;
            item.tongTienThanhToan = item.doanhSoBanChuaThue + item.thueGtgt;
            item.tongTienSauThue = product.finalAmount;
            item.soLuong = product.quantity;
            item.donViTinh = product.unit;
            item.donGia = product.price;
            item.nhanDan = invoiceTagDict[item.invoiceId]
              ?.map(i => i.name)
              .join(', ');
            item.ketQuaKiemTra = checkResult(
              invoiceValidateDict[item.invoiceId],
            );

            /* reformat price */
            // item.tongTienHang = item.tongTienHang;
            // item.tienChietKhau = item.tienChietKhau;
            // item.doanhSoBanChuaThue = item.doanhSoBanChuaThue;

            // item.thueGtgt = item.thueGtgt;
            // item.tongTienThanhToan = item.tongTienThanhToan;

            item = utils.deleteEmptyProps(item, '');

            return item;
          });

          // invoices = invoices.map(item => {
          //   // item.InvoiceValidate = invoiceValidateDict[item.invoiceId];
          //   let products = invoiceProductDict[item.invoiceId] ?? [];

          //   item.dienGiai = _.join(
          //     products.map(i => i.name),
          //     '\n',
          //   );
          //   item.tongTienHang = _.sum(products.map(i => i.amountTotal ?? 0));
          //   item.tienChietKhau = _.sum(
          //     products.map(i => i.discountAmount ?? 0),
          //   );
          //   item.doanhSoBanChuaThue = item.tongTienHang - item.tienChietKhau;
          //   item.thueSuat = _.join(
          //     products.map(i => `- ${i.name}: ${i.vat ?? ''}`),
          //     '\n',
          //   );
          //   item.thueGtgt = _.sum(products.map(i => i.vatAmount ?? 0));
          //   item.tongTienThanhToan = item.doanhSoBanChuaThue + item.thueGtgt;
          //   item.tongTienSauThue = _.sum(products.map(i => i.finalAmount ?? 0));
          //   item.soLuong = _.join(
          //     products.map(i => `- ${i.name}: ${i.quantity ?? ''}`),
          //     '\n',
          //   );
          //   item.donViTinh = _.join(
          //     products.map(i => `- ${i.name}: ${i.unit ?? ''}`),
          //     '\n',
          //   );
          //   item.donGia = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${utils.formatCurrency(i.price, false) ?? ''}`,
          //     ),
          //     '\n',
          //   );
          //   item.nhanDan = invoiceTagDict[item.invoiceId]
          //     ?.map(i => i.name)
          //     .join('\n');
          //   item.ketQuaKiemTra = checkResult(
          //     invoiceValidateDict[item.invoiceId],
          //   );

          //   return item;
          // });

          /* send all as string => reduce size */
          // let dataToSend = '';

          // invoices.forEach((item, index, { length }) => {
          //   dataToSend += `${Object.values(item).join('|')}`;

          //   if (index < length - 1) {
          //     dataToSend += ';;';
          //   }
          // });

          // dataToSend += '';

          if (organizationId) {
            await Organization.update(
              { dangExport: false },
              { where: { organizationId } },
            );
          } else if (organizationDepartmentId) {
            await OrganizationDepartment.update(
              { dangExport: false },
              { where: { organizationDepartmentId } },
            );
          }

          res.send({ result: 'success', invoices: data });

          return;
        }
        case 'radioImportAccountantSoftware_1': {
          let invoices = (
            await Invoice.findAll({
              attributes: [
                'invoiceId',
                'paymentMethod',
                'accountingDate',
                'noDocument',
                'sellerTaxCode',
                'sellerName',
                'sellerAddress',
                'paymentCurrency',
                'paymentExchangeRate',
                'invoiceTypeCode',
                'serial',
                'invoiceNumber',
                'invoiceDate',
                'statusInvoiceText',
                'noteInvoice',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceDict = invoices.reduce((prev, curr) => {
            prev[curr.invoiceId] = curr;
            return prev;
          }, {});

          let partnerCompanyDict = (
            await PartnerCompany.findAll({
              where: {
                organizationDepartmentId,
                organizationId,
                [Op.or]: [
                  {
                    taxCode: { [Op.in]: invoices.map(i => i.sellerTaxCode) },
                  },
                ],
              },
            })
          ).reduce((prev, curr) => {
            prev[curr.taxCode] = curr.unitCode;
            return prev;
          }, {});

          let invoiceValidates = (
            await InvoiceValidate.findAll({
              attributes: [
                'invoiceId',
                'invoiceValidateId',
                'checkResultBuyerAddress',
                'checkResultBuyerTaxCode',
                'checkResultBuyerName',
                'checkResultSellerName',
                'checkResultHasInvoiceCode',
                'checkResultSellerAddress',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceValidateDict = invoiceValidates.reduce((prev, curr) => {
            prev[curr.invoiceId] = curr;

            return prev;
          }, {});

          let invoiceProducts = await InvoiceProduct.findAll({
            attributes: [
              'invoiceId',
              'invoiceProductId',
              'name',
              'productType',
              'unit',
              'price',
              'quantity',
              'amountTotal',
              'discount',
              'discountAmount',
              'vat',
              'vatAmount',
            ],
            where: { invoiceId: { [Op.in]: invoiceIds } },
          });

          /* parse CKTM */
          invoiceProducts = invoiceProducts.map(item => {
            if (item.productType == PRODUCT_TYPE['CKTM']) {
              item.discountAmount = item.amountTotal;
              item.amountTotal = 0;
              item.price = 0;
            }
            return item;
          });

          let invoiceProductDict = invoiceProducts.reduce((prev, curr) => {
            if (!prev[curr.invoiceId]) {
              prev[curr.invoiceId] = [];
            }

            prev[curr.invoiceId].push(curr.toJSON());

            return prev;
          }, {});

          let data = invoiceProducts.map(product => {
            let invoice = invoiceDict[product.invoiceId];
            let item = { ...invoice };

            item.ketQuaKiemTra = checkResult(
              invoiceValidateDict[item.invoiceId],
            );
            item.sellerCode = partnerCompanyDict[item.sellerTaxCode];

            item.tinhchat = product.productType;
            item.dvt = product.unit;
            item.soLuong = product.quantity;
            item.donGia = product.price;
            item.thanhTien = product.amountTotal;
            item.tyLeCKPhanTram = product.discount;
            item.phanTramThueGTGT = product.vat;
            item.tienThueGTGT = product.vatAmount;

            item = utils.deleteEmptyProps(item, '');

            return item;
          });

          // invoices = invoices.map(item => {
          //   let products = invoiceProductDict[item.invoiceId] ?? [];

          //   item.ketQuaKiemTra = checkResult(
          //     invoiceValidateDict[item.invoiceId],
          //   );
          //   item.sellerCode = partnerCompanyDict[item.sellerTaxCode];

          //   item.tinhchat = _.join(
          //     products.map(i => `- ${i.name}: ${i.productType}`),
          //     '\n',
          //   );
          //   item.dvt = _.join(
          //     products.map(i => `- ${i.name}: ${i.unit ?? ''}`),
          //     '\n',
          //   );
          //   item.soLuong = _.join(
          //     products.map(i => `- ${i.name}: ${i.quantity ?? ''}`),
          //     '\n',
          //   );
          //   item.donGia = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${utils.formatCurrency(i.price, false) ?? ''}`,
          //     ),
          //     '\n',
          //   );
          //   item.thanhTien = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.amountTotal, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.tyLeCKPhanTram = _.join(
          //     products.map(i => `- ${i.name}: ${i.discount ?? ''}`),
          //     '\n',
          //   );
          //   item.phanTramThueGTGT = _.join(
          //     products.map(i => `- ${i.name}: ${i.vat ?? ''}`),
          //     '\n',
          //   );
          //   item.tienThueGTGT = _.join(
          //     products.map(i => `- ${i.name}: ${i.vatAmount ?? ''}`),
          //     '\n',
          //   );

          //   return item;
          // });

          if (organizationId) {
            await Organization.update(
              { dangExport: false },
              { where: { organizationId } },
            );
          } else if (organizationDepartmentId) {
            await OrganizationDepartment.update(
              { dangExport: false },
              { where: { organizationDepartmentId } },
            );
          }

          res.send({ result: 'success', invoices: data });

          return;
        }
        case 'radioImportAccountantSoftware_2': {
          let invoices = (
            await Invoice.findAll({
              attributes: [
                'invoiceId',
                'paymentMethod',
                'accountingDate',
                'invoiceTypeCode',
                'serial',
                'invoiceNumber',
                'invoiceDate',
                'sellerName',
                'sellerTaxCode',
                'paymentCurrency',
                'paymentExchangeRate',
                'statusInvoiceText',
                'noteInvoice',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceDict = invoices.reduce((prev, curr) => {
            prev[curr.invoiceId] = curr;
            return prev;
          }, {});

          let invoiceProducts = await InvoiceProduct.findAll({
            attributes: [
              'invoiceId',
              'invoiceProductId',
              'name',
              'code',
              'unit',
              'quantity',
              'price',
              'amountTotal',
              'productType',
            ],
            where: { invoiceId: { [Op.in]: invoiceIds } },
          });

          /* parse CKTM */
          invoiceProducts = invoiceProducts.map(item => {
            if (item.productType == PRODUCT_TYPE['CKTM']) {
              item.discountAmount = item.amountTotal;
              item.amountTotal = 0;
              item.price = 0;
            }
            return item;
          });

          let invoiceProductDict = invoiceProducts.reduce((prev, curr) => {
            if (!prev[curr.invoiceId]) {
              prev[curr.invoiceId] = [];
            }

            prev[curr.invoiceId].push(curr.toJSON());

            return prev;
          }, {});

          let invoiceValidates = (
            await InvoiceValidate.findAll({
              attributes: [
                'invoiceId',
                'invoiceValidateId',
                'checkResultBuyerAddress',
                'checkResultBuyerTaxCode',
                'checkResultBuyerName',
                'checkResultSellerName',
                'checkResultHasInvoiceCode',
                'checkResultSellerAddress',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceValidateDict = invoiceValidates.reduce((prev, curr) => {
            prev[curr.invoiceId] = curr;

            return prev;
          }, {});

          let data = invoiceProducts.map(product => {
            let invoice = invoiceDict[product.invoiceId];
            let item = { ...invoice };

            item.tinhchat = product.productType;
            item.dienGiai = product.name;
            item.maHang = product.code;
            item.tenHang = product.name;
            item.dvt = product.unit;
            item.soLuong = product.quantity;
            item.donGia = product.price;
            item.thanhTien = product.amountTotal;

            item.ketQuaKiemTra = checkResult(
              invoiceValidateDict[item.invoiceId],
            );

            item = utils.deleteEmptyProps(item, '');

            return item;
          });

          // invoices = invoices.map(item => {
          //   let products = invoiceProductDict[item.invoiceId] ?? [];

          //   item.tinhchat = _.join(
          //     products.map(i => `- ${i.name}: ${i.productType}`),
          //     '\n',
          //   );
          //   item.dienGiai = _.join(
          //     products.map(i => i.name),
          //     '\n',
          //   );
          //   item.maHang = _.join(
          //     products.map(i => `- ${i.name}: ${i.code ?? ''}`),
          //     '\n',
          //   );
          //   item.tenHang = _.join(
          //     products.map(i => i.name),
          //     '\n',
          //   );
          //   item.dvt = _.join(
          //     products.map(i => `- ${i.name}: ${i.unit ?? ''}`),
          //     '\n',
          //   );
          //   item.soLuong = _.join(
          //     products.map(i => `- ${i.name}: ${i.quantity ?? ''}`),
          //     '\n',
          //   );
          //   item.donGia = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${utils.formatCurrency(i.price, false) ?? ''}`,
          //     ),
          //     '\n',
          //   );
          //   item.thanhTien = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.amountTotal, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );

          //   item.ketQuaKiemTra = checkResult(
          //     invoiceValidateDict[item.invoiceId],
          //   );

          //   return item;
          // });

          if (organizationId) {
            await Organization.update(
              { dangExport: false },
              { where: { organizationId } },
            );
          } else if (organizationDepartmentId) {
            await OrganizationDepartment.update(
              { dangExport: false },
              { where: { organizationDepartmentId } },
            );
          }

          res.send({ result: 'success', invoices: data });
          return;
        }
        case 'radioImportAccountantSoftware_3': {
          let invoices = (
            await Invoice.findAll({
              attributes: [
                'invoiceId',
                'paymentMethod',
                'accountingDate',
                'invoiceTypeCode',
                'serial',
                'invoiceNumber',
                'invoiceDate',
                'sellerName',
                'sellerAddress',
                'sellerTaxCode',
                'paymentCurrency',
                'paymentExchangeRate',
                'statusInvoiceText',
                'noteInvoice',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceDict = invoices.reduce((prev, curr) => {
            prev[curr.invoiceId] = curr;
            return prev;
          }, {});

          let invoiceProducts = await InvoiceProduct.findAll({
            attributes: [
              'invoiceId',
              'invoiceProductId',
              'name',
              'productType',
            ],
            where: { invoiceId: { [Op.in]: invoiceIds } },
          });

          /* parse CKTM */
          invoiceProducts = invoiceProducts.map(item => {
            if (item.productType == PRODUCT_TYPE['CKTM']) {
              item.discountAmount = item.amountTotal;
              item.amountTotal = 0;
              item.price = 0;
            }
            return item;
          });

          let invoiceProductDict = invoiceProducts.reduce((prev, curr) => {
            if (!prev[curr.invoiceId]) {
              prev[curr.invoiceId] = [];
            }

            prev[curr.invoiceId].push(curr.toJSON());

            return prev;
          }, {});

          let invoiceValidates = (
            await InvoiceValidate.findAll({
              attributes: [
                'invoiceId',
                'invoiceValidateId',
                'checkResultBuyerAddress',
                'checkResultBuyerTaxCode',
                'checkResultBuyerName',
                'checkResultSellerName',
                'checkResultHasInvoiceCode',
                'checkResultSellerAddress',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceValidateDict = invoiceValidates.reduce((prev, curr) => {
            prev[curr.invoiceId] = curr;

            return prev;
          }, {});

          let data = invoiceProducts.map(product => {
            let invoice = invoiceDict[product.invoiceId];
            let item = { ...invoice };

            item.dienGiai = product.name;
            item.dienGiaiTrongPhanChiTiet = product.name;
            item.tenHangHoaDichVu = product.name;

            item.ketQuaKiemTra = checkResult(
              invoiceValidateDict[item.invoiceId],
            );

            item = utils.deleteEmptyProps(item, '');

            return item;
          });

          // invoices = invoices.map(item => {
          //   let products = invoiceProductDict[item.invoiceId] ?? [];

          //   item.dienGiai = _.join(
          //     products.map(i => i.name),
          //     '\n',
          //   );
          //   item.dienGiaiTrongPhanChiTiet = _.join(
          //     products.map(i => i.name),
          //     '\n',
          //   );
          //   item.tenHangHoaDichVu = _.join(
          //     products.map(i => i.name),
          //     '\n',
          //   );

          //   item.ketQuaKiemTra = checkResult(
          //     invoiceValidateDict[item.invoiceId],
          //   );

          //   return item;
          // });

          if (organizationId) {
            await Organization.update(
              { dangExport: false },
              { where: { organizationId } },
            );
          } else if (organizationDepartmentId) {
            await OrganizationDepartment.update(
              { dangExport: false },
              { where: { organizationDepartmentId } },
            );
          }

          res.send({ result: 'success', invoices: data });
          return;
        }
        case 'radioImportAccountantSoftware_4': {
          let invoices = (
            await Invoice.findAll({
              attributes: [
                'invoiceId',
                'sellerName',
                'noDocument',
                'invoiceDate',
                'invoiceNumber',
                'serial',
                'paymentExchangeRate',
                'sellerAddress',
                'sellerTaxCode',
                'statusInvoiceText',
                'noteInvoice',
                'buyerPersonName',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceDict = invoices.reduce((prev, curr) => {
            prev[curr.invoiceId] = curr;
            return prev;
          }, {});

          let invoiceProducts = await InvoiceProduct.findAll({
            attributes: [
              'invoiceId',
              'invoiceProductId',
              'name',
              'code',
              'productType',
              'unit',
              'quantity',
              'price',
              'amountTotal',
              'vatAmount',
            ],
            where: { invoiceId: { [Op.in]: invoiceIds } },
          });

          /* parse CKTM */
          invoiceProducts = invoiceProducts.map(item => {
            if (item.productType == PRODUCT_TYPE['CKTM']) {
              item.discountAmount = item.amountTotal;
              item.amountTotal = 0;
              item.price = 0;
            }
            return item;
          });

          let invoiceProductDict = invoiceProducts.reduce((prev, curr) => {
            if (!prev[curr.invoiceId]) {
              prev[curr.invoiceId] = [];
            }

            prev[curr.invoiceId].push(curr.toJSON());

            return prev;
          }, {});

          let partnerCompanyDict = (
            await PartnerCompany.findAll({
              where: {
                organizationDepartmentId,
                organizationId,
                [Op.or]: [
                  {
                    taxCode: { [Op.in]: invoices.map(i => i.sellerTaxCode) },
                  },
                ],
              },
            })
          ).reduce((prev, curr) => {
            prev[curr.taxCode] = curr.unitCode;
            return prev;
          }, {});

          let invoiceValidates = (
            await InvoiceValidate.findAll({
              attributes: [
                'invoiceId',
                'invoiceValidateId',
                'checkResultBuyerAddress',
                'checkResultBuyerTaxCode',
                'checkResultBuyerName',
                'checkResultSellerName',
                'checkResultHasInvoiceCode',
                'checkResultSellerAddress',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceValidateDict = invoiceValidates.reduce((prev, curr) => {
            prev[curr.invoiceId] = curr;

            return prev;
          }, {});

          let data = invoiceProducts.map(product => {
            let invoice = invoiceDict[product.invoiceId];
            let item = { ...invoice };

            item.dienGiai = product.name;
            item.maHang = product.code;
            item.tenMatHang = product.name;
            item.dvt = product.unit;
            item.tinhchat = product.productType;
            item.maKho = product.code;
            item.soLuong = product.quantity;
            item.gia = product.price;
            item.tienHang = product.amountTotal;
            item.tenHangHoaDichVu = product.name;
            item.thue = product.vatAmount;
            item.soLuong = product.quantity;
            item.donViTinh = product.unit;
            item.donGia = product.price;

            item.sellerCode = partnerCompanyDict[item.sellerTaxCode];

            item.ketQuaKiemTra = checkResult(
              invoiceValidateDict[item.invoiceId],
            );

            item = utils.deleteEmptyProps(item, '');

            return item;
          });

          // invoices = invoices.map(item => {
          //   let products = invoiceProductDict[item.invoiceId] ?? [];

          //   item.dienGiai = _.join(
          //     products.map(i => i.name),
          //     '\n',
          //   );
          //   item.maHang = _.join(
          //     products.map(i => `- ${i.name}: ${i.code ?? ''}`),
          //     '\n',
          //   );
          //   item.tenMatHang = _.join(
          //     products.map(i => i.name),
          //     '\n',
          //   );
          //   item.dvt = _.join(
          //     products.map(i => `- ${i.name}: ${i.unit ?? ''}`),
          //     '\n',
          //   );
          //   item.tinhchat = _.join(
          //     products.map(i => `- ${i.name}: ${i.productType}`),
          //     '\n',
          //   );
          //   item.maKho = _.join(
          //     products.map(i => `- ${i.name}: ${i.code ?? ''}`),
          //     '\n',
          //   );
          //   item.soLuong = _.join(
          //     products.map(i => `- ${i.name}: ${i.quantity ?? ''}`),
          //     '\n',
          //   );
          //   item.gia = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${utils.formatCurrency(i.price, false) ?? ''}`,
          //     ),
          //     '\n',
          //   );
          //   item.tienHang = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.amountTotal, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.tenHangHoaDichVu = _.join(
          //     products.map(i => i.name),
          //     '\n',
          //   );
          //   item.thue = _.join(
          //     products.map(i => `- ${i.name}: ${i.vatAmount ?? ''}`),
          //     '\n',
          //   );
          //   item.soLuong = _.join(
          //     products.map(i => `- ${i.name}: ${i.quantity ?? ''}`),
          //     '\n',
          //   );
          //   item.donViTinh = _.join(
          //     products.map(i => `- ${i.name}: ${i.unit ?? ''}`),
          //     '\n',
          //   );
          //   item.donGia = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${utils.formatCurrency(i.price, false) ?? ''}`,
          //     ),
          //     '\n',
          //   );

          //   item.sellerCode = partnerCompanyDict[item.sellerTaxCode];

          //   item.ketQuaKiemTra = checkResult(
          //     invoiceValidateDict[item.invoiceId],
          //   );

          //   return item;
          // });

          if (organizationId) {
            await Organization.update(
              { dangExport: false },
              { where: { organizationId } },
            );
          } else if (organizationDepartmentId) {
            await OrganizationDepartment.update(
              { dangExport: false },
              { where: { organizationDepartmentId } },
            );
          }

          res.send({ result: 'success', invoices: data });
          return;
        }
        case 'OUT_1': {
          let invoices = (
            await Invoice.findAll({
              attributes: [
                'invoiceId',
                'paymentMethod',
                'accountingDate',
                'noDocument',
                'invoiceTypeCode',
                'serial',
                'invoiceNumber',
                'invoiceDate',
                'buyerName',
                'buyerAddress',
                'buyerTaxCode',
                'expiredPaymentDate',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceDict = invoices.reduce((prev, curr) => {
            prev[curr.invoiceId] = curr;
            return prev;
          }, {});

          let invoiceProducts = await InvoiceProduct.findAll({
            attributes: [
              'invoiceId',
              'invoiceProductId',
              'name',
              'code',
              'unit',
              'quantity',
              'amountTotal',
              'finalAmount',
              'discount',
              'discountAmount',
              'vat',
              'vatAmount',
              'productType',
            ],
            where: { invoiceId: { [Op.in]: invoiceIds } },
          });

          /* parse CKTM */
          invoiceProducts = invoiceProducts.map(item => {
            if (item.productType == PRODUCT_TYPE['CKTM']) {
              item.discountAmount = item.amountTotal;
              item.amountTotal = 0;
              item.price = 0;
            }
            return item;
          });

          let invoiceProductDict = invoiceProducts.reduce((prev, curr) => {
            if (!prev[curr.invoiceId]) {
              prev[curr.invoiceId] = [];
            }

            prev[curr.invoiceId].push(curr.toJSON());

            return prev;
          }, {});

          let partnerCompanyDict = (
            await PartnerCompany.findAll({
              where: {
                organizationDepartmentId,
                organizationId,
                [Op.or]: [
                  {
                    taxCode: { [Op.in]: invoices.map(i => i.sellerTaxCode) },
                  },
                  {
                    taxCode: { [Op.in]: invoices.map(i => i.buyerTaxCode) },
                  },
                ],
              },
            })
          ).reduce((prev, curr) => {
            prev[curr.taxCode] = curr.unitCode;
            return prev;
          }, {});

          let data = invoiceProducts.map(product => {
            let invoice = invoiceDict[product.invoiceId];
            let item = { ...invoice };

            item.dienGiai = product.name;
            item.maHang = product.code;
            item.tenHang = product.name;
            item.dvt = product.unit;
            item.soLuong = product.quantity;
            item.donGia = product.amountTotal;
            item.thanhTien = product.finalAmount;
            item.tyLeCKPhanTram = product.discount;
            item.tienChietKhau = product.discountAmount;
            item.phanTramThueGTGT = product.vat;
            item.tienThueGTGT = product.vatAmount;
            item.sellerCode = partnerCompanyDict[item.sellerTaxCode];
            item.buyerCode = partnerCompanyDict[item.buyerTaxCode];

            item = utils.deleteEmptyProps(item, '');

            return item;
          });

          // invoices = invoices.map(item => {
          //   let products = invoiceProductDict[item.invoiceId] ?? [];

          //   item.dienGiai = _.join(
          //     products.map(i => i.name),
          //     '\n',
          //   );
          //   item.maHang = _.join(
          //     products.map(i => `- ${i.name}: ${i.code ?? ''}`),
          //     '\n',
          //   );
          //   item.tenHang = _.join(
          //     products.map(i => i.name),
          //     '\n',
          //   );
          //   item.dvt = _.join(
          //     products.map(i => `- ${i.name}: ${i.unit ?? ''}`),
          //     '\n',
          //   );
          //   item.soLuong = _.join(
          //     products.map(i => `- ${i.name}: ${i.quantity ?? ''}`),
          //     '\n',
          //   );
          //   item.donGia = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.amountTotal, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.thanhTien = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.finalAmount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.tyLeCKPhanTram = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.discount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.tienChietKhau = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.discountAmount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.phanTramThueGTGT = _.join(
          //     products.map(
          //       i => `- ${i.name}: ${utils.formatCurrency(i.vat, false) ?? ''}`,
          //     ),
          //     '\n',
          //   );
          //   item.tienThueGTGT = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.vatAmount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.sellerCode = partnerCompanyDict[item.sellerTaxCode];
          //   item.buyerCode = partnerCompanyDict[item.buyerTaxCode];

          //   return item;
          // });

          if (organizationId) {
            await Organization.update(
              { dangExport: false },
              { where: { organizationId } },
            );
          } else if (organizationDepartmentId) {
            await OrganizationDepartment.update(
              { dangExport: false },
              { where: { organizationDepartmentId } },
            );
          }

          res.send({ result: 'success', invoices: data });
          return;
        }
        case 'OUT_2': {
          let invoices = (
            await Invoice.findAll({
              attributes: [
                'invoiceId',
                'accountingDate',
                'noDocument',
                'invoiceTypeCode',
                'serial',
                'invoiceNumber',
                'invoiceDate',
                'buyerName',
                'buyerAddress',
                'buyerTaxCode',
                'expiredPaymentDate',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceDict = invoices.reduce((prev, curr) => {
            prev[curr.invoiceId] = curr;
            return prev;
          }, {});

          let invoiceProducts = await InvoiceProduct.findAll({
            attributes: [
              'invoiceId',
              'invoiceProductId',
              'name',
              'code',
              'unit',
              'quantity',
              'amountTotal',
              'finalAmount',
              'discount',
              'discountAmount',
              'vat',
              'vatAmount',
              'productType',
            ],
            where: { invoiceId: { [Op.in]: invoiceIds } },
          });

          /* parse CKTM */
          invoiceProducts = invoiceProducts.map(item => {
            if (item.productType == PRODUCT_TYPE['CKTM']) {
              item.discountAmount = item.amountTotal;
              item.amountTotal = 0;
              item.price = 0;
            }
            return item;
          });

          let invoiceProductDict = invoiceProducts.reduce((prev, curr) => {
            if (!prev[curr.invoiceId]) {
              prev[curr.invoiceId] = [];
            }

            prev[curr.invoiceId].push(curr.toJSON());

            return prev;
          }, {});

          let partnerCompanyDict = (
            await PartnerCompany.findAll({
              where: {
                organizationDepartmentId,
                organizationId,
                [Op.or]: [
                  {
                    taxCode: { [Op.in]: invoices.map(i => i.buyerTaxCode) },
                  },
                ],
              },
            })
          ).reduce((prev, curr) => {
            prev[curr.taxCode] = curr.unitCode;
            return prev;
          }, {});

          let data = invoiceProducts.map(product => {
            let invoice = invoiceDict[product.invoiceId];
            let item = { ...invoice };

            item.dienGiai = product.name;
            item.maDichVu = product.code;
            item.tenDichVu = product.name;
            item.dvt = product.unit;
            item.soLuong = product.quantity;
            item.donGia = product.price;
            item.thanhTien = product.finalAmount;
            item.tyLeCKPhanTram = product.discount;
            item.tienChietKhau = product.discountAmount;

            item.phanTramThueGTGT = product.vat;
            item.tienThueGTGT = product.vatAmount;

            item.buyerCode = partnerCompanyDict[item.buyerTaxCode];

            item = utils.deleteEmptyProps(item, '');

            return item;
          });

          // invoices = invoices.map(item => {
          //   let products = invoiceProductDict[item.invoiceId] ?? [];

          //   item.dienGiai = _.join(
          //     products.map(i => i.name),
          //     '\n',
          //   );
          //   item.maDichVu = _.join(
          //     products.map(i => `- ${i.name}: ${i.code ?? ''}`),
          //     '\n',
          //   );
          //   item.tenDichVu = _.join(
          //     products.map(i => i.name),
          //     '\n',
          //   );
          //   item.dvt = _.join(
          //     products.map(i => `- ${i.name}: ${i.unit ?? ''}`),
          //     '\n',
          //   );
          //   item.soLuong = _.join(
          //     products.map(i => `- ${i.name}: ${i.quantity ?? ''}`),
          //     '\n',
          //   );
          //   item.donGia = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.amountTotal, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.thanhTien = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.finalAmount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.tyLeCKPhanTram = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.discount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.tienChietKhau = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.discountAmount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.phanTramThueGTGT = _.join(
          //     products.map(
          //       i => `- ${i.name}: ${utils.formatCurrency(i.vat, false) ?? ''}`,
          //     ),
          //     '\n',
          //   );
          //   item.tienThueGTGT = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.vatAmount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );

          //   item.buyerCode = partnerCompanyDict[item.buyerTaxCode];
          //   return item;
          // });

          if (organizationId) {
            await Organization.update(
              { dangExport: false },
              { where: { organizationId } },
            );
          } else if (organizationDepartmentId) {
            await OrganizationDepartment.update(
              { dangExport: false },
              { where: { organizationDepartmentId } },
            );
          }

          res.send({ result: 'success', invoices: data });
          return;
        }
        case 'OUT_3': {
          let invoices = (
            await Invoice.findAll({
              attributes: [
                'invoiceId',
                'invoiceTypeCode',
                'serial',
                'invoiceNumber',
                'invoiceDate',
                'buyerName',
                'buyerAddress',
                'buyerTaxCode',
                'buyerBankAccount',
                'paymentMethod',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceDict = invoices.reduce((prev, curr) => {
            prev[curr.invoiceId] = curr;
            return prev;
          }, {});

          let invoiceProducts = await InvoiceProduct.findAll({
            attributes: [
              'invoiceId',
              'invoiceProductId',
              'name',
              'code',
              'unit',
              'quantity',
              'amountTotal',
              'finalAmount',
              'discount',
              'discountAmount',
              'vat',
              'vatAmount',
              'productType',
            ],
            where: { invoiceId: { [Op.in]: invoiceIds } },
          });

          /* parse CKTM */
          invoiceProducts = invoiceProducts.map(item => {
            if (item.productType == PRODUCT_TYPE['CKTM']) {
              item.discountAmount = item.amountTotal;
              item.amountTotal = 0;
              item.price = 0;
            }
            return item;
          });

          let invoiceProductDict = invoiceProducts.reduce((prev, curr) => {
            if (!prev[curr.invoiceId]) {
              prev[curr.invoiceId] = [];
            }

            prev[curr.invoiceId].push(curr.toJSON());

            return prev;
          }, {});

          let partnerCompanyDict = (
            await PartnerCompany.findAll({
              where: {
                organizationDepartmentId,
                organizationId,
                [Op.or]: [
                  {
                    taxCode: { [Op.in]: invoices.map(i => i.buyerTaxCode) },
                  },
                ],
              },
            })
          ).reduce((prev, curr) => {
            prev[curr.taxCode] = curr.unitCode;
            return prev;
          }, {});

          let data = invoiceProducts.map(product => {
            let invoice = invoiceDict[product.invoiceId];
            let item = { ...invoice };

            item.maHang = product.code;
            item.tenHang = product.name;
            item.dvt = product.unit;
            item.soLuong = product.quantity;
            item.donGia = product.price;
            item.thanhTien = product.finalAmount;
            item.tyLeCKPhanTram = product.discount;
            item.tienChietKhau = product.discountAmount;

            item.phanTramThueGTGT = product.vat;
            item.tienThueGTGT = product.vatAmount;

            item.buyerCode = partnerCompanyDict[item.buyerTaxCode];

            item = utils.deleteEmptyProps(item, '');

            return item;
          });

          // invoices = invoices.map(item => {
          //   let products = invoiceProductDict[item.invoiceId] ?? [];

          //   item.maHang = _.join(
          //     products.map(i => `- ${i.name}: ${i.code ?? ''}`),
          //     '\n',
          //   );
          //   item.tenHang = _.join(
          //     products.map(i => i.name),
          //     '\n',
          //   );
          //   item.dvt = _.join(
          //     products.map(i => `- ${i.name}: ${i.unit ?? ''}`),
          //     '\n',
          //   );
          //   item.soLuong = _.join(
          //     products.map(i => `- ${i.name}: ${i.quantity ?? ''}`),
          //     '\n',
          //   );
          //   item.donGia = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.amountTotal, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.thanhTien = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.finalAmount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.tyLeCKPhanTram = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.discount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.tienChietKhau = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.discountAmount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.phanTramThueGTGT = _.join(
          //     products.map(
          //       i => `- ${i.name}: ${utils.formatCurrency(i.vat, false) ?? ''}`,
          //     ),
          //     '\n',
          //   );
          //   item.tienThueGTGT = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.vatAmount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );

          //   item.buyerCode = partnerCompanyDict[item.buyerTaxCode];

          //   return item;
          // });

          if (organizationId) {
            await Organization.update(
              { dangExport: false },
              { where: { organizationId } },
            );
          } else if (organizationDepartmentId) {
            await OrganizationDepartment.update(
              { dangExport: false },
              { where: { organizationDepartmentId } },
            );
          }

          res.send({ result: 'success', invoices: data });
          return;
        }
        case 'OUT_4': {
          let invoices = (
            await Invoice.findAll({
              attributes: [
                'invoiceId',
                'invoiceTypeCode',
                'serial',
                'invoiceNumber',
                'invoiceDate',
                'buyerName',
                'buyerAddress',
                'buyerTaxCode',
                'buyerBankAccount',
                'paymentMethod',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceDict = invoices.reduce((prev, curr) => {
            prev[curr.invoiceId] = curr;
            return prev;
          }, {});

          let invoiceProducts = await InvoiceProduct.findAll({
            attributes: [
              'invoiceId',
              'invoiceProductId',
              'name',
              'code',
              'unit',
              'quantity',
              'amountTotal',
              'finalAmount',
              'discount',
              'discountAmount',
              'vat',
              'vatAmount',
              'productType',
            ],
            where: { invoiceId: { [Op.in]: invoiceIds } },
          });

          /* parse CKTM */
          invoiceProducts = invoiceProducts.map(item => {
            if (item.productType == PRODUCT_TYPE['CKTM']) {
              item.discountAmount = item.amountTotal;
              item.amountTotal = 0;
              item.price = 0;
            }
            return item;
          });

          let invoiceProductDict = invoiceProducts.reduce((prev, curr) => {
            if (!prev[curr.invoiceId]) {
              prev[curr.invoiceId] = [];
            }

            prev[curr.invoiceId].push(curr.toJSON());

            return prev;
          }, {});

          let partnerCompanyDict = (
            await PartnerCompany.findAll({
              where: {
                organizationDepartmentId,
                organizationId,
                [Op.or]: [
                  {
                    taxCode: { [Op.in]: invoices.map(i => i.buyerTaxCode) },
                  },
                ],
              },
            })
          ).reduce((prev, curr) => {
            prev[curr.taxCode] = curr.unitCode;
            return prev;
          }, {});

          let data = invoiceProducts.map(product => {
            let invoice = invoiceDict[product.invoiceId];
            let item = { ...invoice };

            item.maHang = product.code;
            item.tenHang = product.name;
            item.dvt = product.unit;
            item.soLuong = product.quantity;
            item.donGia = product.price;
            item.thanhTien = product.finalAmount;
            item.tyLeCKPhanTram = product.discount;
            item.tienChietKhau = product.discountAmount;

            item.phanTramThueGTGT = product.vat;
            item.tienThueGTGT = product.vatAmount;

            item.buyerCode = partnerCompanyDict[item.buyerTaxCode];

            item = utils.deleteEmptyProps(item, '');

            return item;
          });

          // invoices = invoices.map(item => {
          //   let products = invoiceProductDict[item.invoiceId] ?? [];

          //   item.maHang = _.join(
          //     products.map(i => `- ${i.name}: ${i.code ?? ''}`),
          //     '\n',
          //   );
          //   item.tenHang = _.join(
          //     products.map(i => i.name),
          //     '\n',
          //   );
          //   item.dvt = _.join(
          //     products.map(i => `- ${i.name}: ${i.unit ?? ''}`),
          //     '\n',
          //   );
          //   item.soLuong = _.join(
          //     products.map(i => `- ${i.name}: ${i.quantity ?? ''}`),
          //     '\n',
          //   );
          //   item.donGia = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.amountTotal, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.thanhTien = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.finalAmount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.tyLeCKPhanTram = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.discount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.tienChietKhau = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.discountAmount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.phanTramThueGTGT = _.join(
          //     products.map(
          //       i => `- ${i.name}: ${utils.formatCurrency(i.vat, false) ?? ''}`,
          //     ),
          //     '\n',
          //   );
          //   item.tienThueGTGT = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.vatAmount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );

          //   item.buyerCode = partnerCompanyDict[item.buyerTaxCode];

          //   return item;
          // });

          if (organizationId) {
            await Organization.update(
              { dangExport: false },
              { where: { organizationId } },
            );
          } else if (organizationDepartmentId) {
            await OrganizationDepartment.update(
              { dangExport: false },
              { where: { organizationDepartmentId } },
            );
          }

          res.send({ result: 'success', invoices: data });
          return;
        }
        case 'OUT_5': {
          let invoices = (
            await Invoice.findAll({
              attributes: [
                'invoiceId',
                'paymentMethod',
                'accountingDate',
                'noDocument',
                'invoiceNumber',
                'invoiceDate',
                'buyerName',
                'buyerAddress',
                'buyerTaxCode',
              ],
              where: { invoiceId: { [Op.in]: invoiceIds } },
            })
          ).map(item => item.toJSON());

          let invoiceDict = invoices.reduce((prev, curr) => {
            prev[curr.invoiceId] = curr;
            return prev;
          }, {});

          let invoiceProducts = await InvoiceProduct.findAll({
            attributes: [
              'invoiceId',
              'invoiceProductId',
              'name',
              'code',
              'unit',
              'quantity',
              'amountTotal',
              'finalAmount',
              'discount',
              'discountAmount',
              'vat',
              'vatAmount',
              'productType',
            ],
            where: { invoiceId: { [Op.in]: invoiceIds } },
          });

          /* parse CKTM */
          invoiceProducts = invoiceProducts.map(item => {
            if (item.productType == PRODUCT_TYPE['CKTM']) {
              item.discountAmount = item.amountTotal;
              item.amountTotal = 0;
              item.price = 0;
            }
            return item;
          });

          let invoiceProductDict = invoiceProducts.reduce((prev, curr) => {
            if (!prev[curr.invoiceId]) {
              prev[curr.invoiceId] = [];
            }

            prev[curr.invoiceId].push(curr.toJSON());

            return prev;
          }, {});

          let partnerCompanyDict = (
            await PartnerCompany.findAll({
              where: {
                organizationDepartmentId,
                organizationId,
                [Op.or]: [
                  {
                    taxCode: { [Op.in]: invoices.map(i => i.buyerTaxCode) },
                  },
                ],
              },
            })
          ).reduce((prev, curr) => {
            prev[curr.taxCode] = curr.unitCode;
            return prev;
          }, {});

          let data = invoiceProducts.map(product => {
            let invoice = invoiceDict[product.invoiceId];
            let item = { ...invoice };

            item.dienGiai = product.name;
            item.maHang = product.code;
            item.tenHang = product.name;
            item.dvt = product.unit;

            item.soLuong = product.quantity;

            item.donGiaSauThue =
              product.amountTotal ?? 0 + product.vatAmount ?? 0;

            item.donGia = product.amountTotal;
            item.thanhTien = product.finalAmount;
            item.tyLeCKPhanTram = product.discount;
            item.tienChietKhau = product.discountAmount;

            item.phanTramThueGTGT = product.vat;
            item.tienThueGTGT = product.vatAmount;

            item.buyerCode = partnerCompanyDict[item.buyerTaxCode];

            item = utils.deleteEmptyProps(item, '');

            return item;
          });

          // invoices = invoices.map(item => {
          //   let products = invoiceProductDict[item.invoiceId] ?? [];

          //   item.dienGiai = _.join(
          //     products.map(i => i.name),
          //     '\n',
          //   );
          //   item.maHang = _.join(
          //     products.map(i => `- ${i.name}: ${i.code ?? ''}`),
          //     '\n',
          //   );
          //   item.tenHang = _.join(
          //     products.map(i => i.name),
          //     '\n',
          //   );
          //   item.dvt = _.join(
          //     products.map(i => `- ${i.name}: ${i.unit ?? ''}`),
          //     '\n',
          //   );
          //   item.soLuong = _.join(
          //     products.map(i => `- ${i.name}: ${i.quantity ?? ''}`),
          //     '\n',
          //   );

          //   item.donGiaSauThue = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(
          //             i.amountTotal ?? 0 + i.vatAmount ?? 0,
          //             false,
          //           ) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );

          //   item.donGia = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.amountTotal, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.thanhTien = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.finalAmount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.tyLeCKPhanTram = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.discount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.tienChietKhau = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.discountAmount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );
          //   item.phanTramThueGTGT = _.join(
          //     products.map(
          //       i => `- ${i.name}: ${utils.formatCurrency(i.vat, false) ?? ''}`,
          //     ),
          //     '\n',
          //   );
          //   item.tienThueGTGT = _.join(
          //     products.map(
          //       i =>
          //         `- ${i.name}: ${
          //           utils.formatCurrency(i.vatAmount, false) ?? ''
          //         }`,
          //     ),
          //     '\n',
          //   );

          //   item.buyerCode = partnerCompanyDict[item.buyerTaxCode];

          //   return item;
          // });

          if (organizationId) {
            await Organization.update(
              { dangExport: false },
              { where: { organizationId } },
            );
          } else if (organizationDepartmentId) {
            await OrganizationDepartment.update(
              { dangExport: false },
              { where: { organizationDepartmentId } },
            );
          }

          res.send({ result: 'success', invoices: data });
          return;
        }
      }

      let invoices = (
        await Invoice.findAll({
          attributes: ['invoiceId', 'sellerTaxCode'],
          where: {
            organizationId,
            organizationDepartmentId,
            invoiceId: { [Op.in]: invoiceIds },
          },
          include: [
            // { model: InvoiceProduct },
            // {
            //   model: Tag,
            //   where: { accountId: req.account.accountId },
            //   required: false,
            // },
            // { model: InvoiceValidate },
            // { model: TaxReduceValidate },
          ],
        })
      ).map(i => i.toJSON());

      /* fix take too long to fetch */
      // invoices = await invoiceHelper.fetchMultipleFromCqt(invoices, {
      //   organization: req.account.Organization,
      //   organizationDepartment: req.account.OrganizationDepartment,
      // });

      // invoices = await invoiceHelper.fetchMultipleFromSupplier(invoices);

      let partnerCompanyDict = (
        await PartnerCompany.findAll({
          where: {
            organizationDepartmentId,
            organizationId,
            [Op.or]: [
              {
                taxCode: { [Op.in]: invoices.map(i => i.sellerTaxCode) },
              },
              {
                taxCode: { [Op.in]: invoices.map(i => i.buyerTaxCode) },
              },
            ],
          },
        })
      ).reduce((prev, curr) => {
        prev[curr.taxCode] = curr.unitCode;
        return prev;
      }, {});

      invoices = invoices.map(item => {
        item.sellerCode = partnerCompanyDict[item.sellerTaxCode];
        item.buyerCode = partnerCompanyDict[item.buyerTaxCode];

        return item;
      });

      if (organizationId) {
        await Organization.update(
          { dangExport: false },
          { where: { organizationId } },
        );
      } else if (organizationDepartmentId) {
        await OrganizationDepartment.update(
          { dangExport: false },
          { where: { organizationDepartmentId } },
        );
      }

      res.send({ result: 'success', invoices });
    } catch (error) {
      if (organizationId) {
        await Organization.update(
          { dangExport: false },
          { where: { organizationId } },
        );
      } else if (organizationDepartmentId) {
        await OrganizationDepartment.update(
          { dangExport: false },
          { where: { organizationDepartmentId } },
        );
      }
      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async acceptTaxReduce(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { organizationDepartmentId, organizationId } = req.account;
      let { productName, isTaxReduce } = req.body;

      if (!productName) {
        throw new Error(ERROR_MISSING_PARAMETERS);
      }

      productName = _.trim(productName);

      isTaxReduce = !!utils.parseBoolean(isTaxReduce);

      let [acceptTaxReduce, createdNew] = await AcceptTaxReduce.upsert(
        {
          acceptBy: req.account.accountId,
          organizationDepartmentId,
          organizationId,
          isTaxReduce,
          productName,
        },
        { transaction: t },
      );

      try {
        let products = await InvoiceProduct.findAll({
          where: { name: { [Op.like]: `%${productName}%` } },
          include: [
            {
              model: Invoice,
              where: { organizationId, organizationDepartmentId },
              attributes: [],
              required: true,
            },
          ],
          transaction: t,
        });

        let invoiceIds = products.map(item => item.invoiceId);

        await Invoice.update(
          {
            reduceTaxResolution: null,
            statusTax: TAX_STATUS_TEXT['NOT_CHECKED'],
          },
          { where: { invoiceId: { [Op.in]: invoiceIds } }, transaction: t },
        );
      } catch (error) {
        console.warn(error);
      }

      await t.commit();

      res.send({ result: 'success', acceptTaxReduce });
    } catch (error) {
      await t.rollback();

      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async manualCreateFile(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      const {
        organizationDepartmentId,
        organizationId,
        Organization: organization,
        OrganizationDepartment: organizationDepartment,
        taxCode,
      } = req.account;

      let { id } = req.body;
      let file = req.files?.['file'];

      if (!file) {
        throw new Error(ERROR_MISSING_PARAMETERS);
      }

      if (_.isArray(file)) {
        file = file[0];
      }

      if (
        file.mimetype != 'application/xml' &&
        file.mimetype != 'text/xml' &&
        file.mimetype != 'application/pdf'
      ) {
        res.status(REQ_ERROR_CODE).send({
          result: 'failed',
          name: file.name,
          reason: ERROR_CANNOT_READ_INVOICE_FROM_FILE,
          id,
        });
        return;
      }

      let xmlContent = fs.readFileSync(file.tempFilePath, 'utf-8');

      let readInvoice;
      if (file.mimetype == 'application/xml' || file.mimetype == 'text/xml') {
        readInvoice = await invoiceHelper
          .invoiceFromXml(xmlContent)
          .catch(err => null);
      } else if (file.mimetype == 'application/pdf') {
      }

      if (!readInvoice) {
        res.status(REQ_ERROR_CODE).send({
          result: 'failed',
          name: file.name,
          reason: ERROR_CANNOT_READ_INVOICE_FROM_FILE,
          id,
        });
        return;
      }

      /* invalid invoice */
      if (invoiceHelper.isMalformedInvoice(readInvoice)) {
        await t.commit();

        res.status(REQ_ERROR_CODE).send({
          result: 'failed',
          name: file.name,
          reason: ERROR_MALFORM_INVOICE,
          id,
        });
        return;
      }

      if (!INVOICE_TYPE_CODE[readInvoice.invoiceTypeCode]) {
        await t.commit();

        res.status(REQ_ERROR_CODE).send({
          result: 'failed',
          name: file.name,
          reason: ERROR_INVALID_INVOICE_TYPE_CODE,
          id,
        });
        return;
      }

      /* check valid INPUT/OUTPUT */
      // if (
      //   (invoiceXml.sellerTaxCode != taxCode &&
      //     invoiceXml.buyerTaxCode != taxCode) ||
      //   invoiceXml.buyerTaxCode == invoiceXml.sellerTaxCode
      // ) {
      //   throw new Error(ERROR_INVALID_INVOICE_TAXCODE);
      // }

      if (
        !utils.isValidTaxCode(readInvoice.sellerTaxCode) ||
        !utils.isValidTaxCode(readInvoice.buyerTaxCode)
      ) {
        await t.commit();

        res.status(REQ_ERROR_CODE).send({
          result: 'failed',
          name: file.name,
          reason: ERROR_INVALID_INVOICE_TAXCODE,
          id,
        });
        return;
      }

      /* CHECK duplicate */
      let duplicateInvoice = await invoiceHelper.findDuplicateInvoice(
        readInvoice,
        { organizationDepartmentId, organizationId },
        t,
      );

      if (duplicateInvoice) {
        await t.commit();

        res.send({
          result: 'failed',
          status: 201,
          duplicateInvoiceId: duplicateInvoice.invoiceId,
          name: file.name,
          invoiceId: duplicateInvoice.invoiceId,
          id,
        });
        return;
      }

      // readInvoice.isManualInput = true;
      // readInvoice.statusInvoiceText = null;
      // readInvoice.statusInvoice = null;
      readInvoice.typeInvoiceReceive = TYPE_INVOICE_RECEIVE[1];

      /* ktra tai nguyen */
      let canSave = false;
      /**
       * SL cos theer trừ
       */
      let maxEntries = await companyHelper.getMaxInvoiceEntries(
        req.account.companyId,
        t,
      );

      let remainResource = await companyHelper.getRemainResource(
        req.account.companyId,
        t,
      );

      if (!maxEntries.length) {
        throw new Error(ERROR_OUT_OF_INVOICES);
      }

      /*  */
      /* 31/10: không tính thời hạn riêng từng lần cấp. Cộng dồn SL, thời hạn */
      let resourceHistories = await ResourceHistory.findAll({
        where: {
          companyId: req.account.companyId,
          left: { [Op.gt]: 0 },
          packageType: PACKAGE_TYPE.QUANTITY,
        },
        transaction: t,
      });

      let [invoice] = await invoiceHelper.saveToInvoice(
        [readInvoice],
        { organizationDepartmentId, organizationId, taxCode, maxEntries },
        t,
      );

      /* reduce left in resource history */
      if (invoice) {
        let total = [invoice].length;
        /* first use promotion */
        if (remainResource.promotionRemain) {
          let used = Math.min(total, remainResource.promotionRemain);
          total -= used;
          await Company.update(
            { promotionRemain: remainResource.promotionRemain - used },
            { where: { companyId: req.account.companyId }, transaction: t },
          );
        }
        /* then use resources */
        let useQuantityRemain = total;
        for (let i = 0; i < resourceHistories.length && total > 0; i++) {
          let used = Math.min(total, resourceHistories[i].left);
          total -= used;
          await resourceHistories[i].update(
            { left: resourceHistories[i].left - used },
            { transaction: t },
          );
        }
        await Company.update(
          {
            quantityRemain: remainResource.quantityRemain - useQuantityRemain,
          },
          { where: { companyId: req.account.companyId }, transaction: t },
        );
      }

      if (file.mimetype == 'application/xml' || file.mimetype == 'text/xml') {
        let originFilepath = `${UPLOAD_DIR}/invoice-origin-xml/${invoice.invoiceId}`;
        let originfFilename = `origin-invoice.xml`;

        if (fs.existsSync(originFilepath)) {
          fs.rmSync(originFilepath, { recursive: true });
        }

        fs.mkdirSync(originFilepath);

        await file.mv(`${originFilepath}/${originfFilename}`);

        await invoice.update(
          {
            originXmlFile: `uploads/invoice-origin-xml/${invoice.invoiceId}/${originfFilename}`,
          },
          { transaction: t },
        );

        /* read cert */
        if (readInvoice.cqtCKS || readInvoice.sellerCKS) {
          let { cqtCTS, sellerCTS } = invoiceHelper.readX509Cert(readInvoice);

          await invoice.update(
            { cqtCKS: cqtCTS, sellerCKS: sellerCTS },
            { transaction: t },
          );
        }
      }

      if (readInvoice.InvoiceProducts) {
        await InvoiceProduct.destroy({
          where: { invoiceId: invoice.invoiceId },
          transaction: t,
        });

        await InvoiceProduct.bulkCreate(
          readInvoice.InvoiceProducts.map(item => {
            item.invoiceId = invoice.invoiceId;

            return item;
          }),
          { transaction: t },
        );
      }

      /* get lookup website */
      if (true) {
        let { supplierLookupWebsite } =
          await invoiceHelper.getSupplierReference(invoice);

        if (!supplierLookupWebsite) {
          supplierLookupWebsite = await invoiceHelper.findSpecialLookupWebsite(
            invoice,
          );
        }

        if (supplierLookupWebsite) {
          await invoice.update({ supplierLookupWebsite }, { transaction: t });
        }
      }

      await cqt_validate_invoice(invoice, t);

      await t.commit();

      await invoice.reload({
        include: [
          { model: InvoiceValidate },
          { model: InvoiceAttach },
          { model: InvoiceProduct },
          { model: InvoiceHistory },
          { model: TaxReduceValidate },
        ],
      });

      if (!invoice.invoiceCategory) {
        res.send({
          result: 'failed',
          status: 202,
          invoice,
          invoiceId: invoice.invoiceId,
          reason: ERROR_INVALID_INVOICE_TAXCODE,
          name: file.name,
          id,
        });
        return;
      }

      res.send({
        result: 'success',
        invoice,
        invoiceId: invoice.invoiceId,
        name: file.name,
        id,
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async manualCreateInput(req, res, next) {
    const t = await Sequelize.transaction();
    let name;

    try {
      const { organizationDepartmentId, organizationId, taxCode } = req.account;
      let {
        sellerTaxCode,
        buyerTaxCode,
        buyerName,
        sellerName,
        sellerAddress,
        buyerAddress,
        invoiceDate,
        serial,
        invoiceTypeCode,
        InvoiceProducts,
        amountBeforeVat,
        amountVat,
        finalAmount,
        invoiceNumber,
        statusInvoice,
      } = req.body;

      let imgFile = req.files?.['imgFile'];

      let pdfFile = req.files?.['pdfFile'];

      let xmlFile = req.files?.['xmlFile'];

      // if (!imgFile && !pdfFile) {
      //   throw new Error(ERROR_MISSING_INVOICE_FILES);
      // }

      if (_.isArray(pdfFile)) {
        pdfFile = pdfFile[0];
      }

      if (_.isArray(imgFile)) {
        imgFile = imgFile[0];
      }

      if (_.isArray(xmlFile)) {
        xmlFile = xmlFile[0];
      }

      if (pdfFile) {
        name = pdfFile.name;
      }

      if (imgFile) {
        name = imgFile.name;
      }

      if (xmlFile) {
        name = xmlFile.name;
      }

      if (
        !utils.isValidTaxCode(sellerTaxCode) ||
        !utils.isValidTaxCode(buyerTaxCode)
      ) {
        throw new Error(ERROR_INVALID_INVOICE_TAXCODE);
      }

      // if (
      //   (sellerTaxCode != taxCode && buyerTaxCode != taxCode) ||
      //   buyerTaxCode == sellerTaxCode
      // ) {
      //   throw new Error(ERROR_INVALID_INVOICE_TAXCODE);
      // }

      if (
        !sellerName ||
        !sellerAddress ||
        !buyerName ||
        !buyerAddress ||
        !utils.isValidDate(invoiceDate) ||
        !isValidNumber(invoiceNumber)
      ) {
        throw new Error(ERROR_INVALID_PARAMETER);
      }

      if (!_.isArray(InvoiceProducts)) {
        throw new Error(ERROR_INVALID_PARAMETER);
      }

      // if (!INVOICE_TYPE_CODE[invoiceTypeCode]) {
      //   throw new Error(ERROR_INVALID_INVOICE_TYPE_CODE);
      // }

      // if (!serial || ['C', 'K'].every(char => serial.startsWith(char))) {
      //   throw new Error(ERROR_INVALID_INVOICE_SERIAL);
      // }

      if (
        invoiceHelper.isMalformedInvoice({
          sellerTaxCode,
          buyerTaxCode,
          serial,
          invoiceNumber,
          invoiceTypeCode,
        })
      ) {
        throw new Error(ERROR_MALFORM_INVOICE);
      }

      /*  */
      if (!isValidNumber(finalAmount)) {
        finalAmount = 0;
      }
      if (!isValidNumber(amountVat)) {
        amountVat = 0;
      }
      if (!isValidNumber(amountBeforeVat)) {
        amountBeforeVat = 0;
      }

      if (!InvoiceProducts.length) {
        throw new Error(ERROR_MISSING_INVOICE_PRODUCTS);
      }

      if (
        InvoiceProducts.some(item => {
          if (!item.name) return true;

          if (item.vat != null && item.vat != undefined) {
            if (!VAT_TYPE[item.vat]) {
              return true;
            }
          }

          if (item.discount !== undefined) {
            if (!isValidNumber(item.discount)) {
              return true;
            }
          } else {
            item.discount = undefined;
          }

          if (item.discountAmount !== undefined) {
            if (!isValidNumber(item.discount)) {
              return true;
            }
          } else {
            item.discountAmount = undefined;
          }

          if (!item.quantity) {
            item.quantity = 0;
          }

          if (!item.price) {
            item.price = 0;
          }

          return false;
        })
      ) {
        throw new Error(ERROR_INVALID_INVOICE_PRODUCTS);
      }

      /* CHECK duplicate */
      let duplicateInvoice = await invoiceHelper.findDuplicateInvoice(
        {
          organizationDepartmentId,
          organizationId,
          sellerTaxCode,
          serial,
          invoiceNumber,
          invoiceTypeCode,
        },
        { organizationDepartmentId, organizationId },
        t,
      );

      if (duplicateInvoice) {
        await t.commit();

        res.send({
          result: 'failed',
          status: 201,
          duplicateInvoiceId: duplicateInvoice.invoiceId,
          name: name,
          invoiceId: duplicateInvoice.invoiceId,
        });
        return;
      }

      let [invoice] = await invoiceHelper.saveToInvoice(
        [
          {
            invoiceNumber: Number(invoiceNumber),
            sellerTaxCode,
            buyerTaxCode,
            buyerName,
            sellerName,
            sellerAddress,
            buyerAddress,
            invoiceDate,
            serial,
            invoiceTypeCode,
            amountBeforeVat,
            amountVat,
            finalAmount,
            isManualInput: true,
            organizationDepartmentId,
            organizationId,
            pdfFile: null,
            imageFile: null,
            statusInvoice: null,
            statusInvoiceText: null,
            typeInvoiceReceive: TYPE_INVOICE_RECEIVE[4],
          },
        ],
        { organizationDepartmentId, organizationId, taxCode },
        t,
      );

      if (statusInvoice == 8) {
        await invoice.update(
          {
            statusInvoice: INVOICE_STATUS[8],
            statusInvoiceText: INVOICE_STATUS_TEXT[8],
          },
          { transaction: t },
        );
      }

      if (InvoiceProducts.length) {
        await InvoiceProduct.destroy({
          where: { invoiceId: invoice.invoiceId },
          transaction: t,
        });

        await InvoiceProduct.bulkCreate(
          InvoiceProducts.map(item => {
            item.invoiceId = invoice.invoiceId;

            return item;
          }),
          { transaction: t },
        );
      }

      if (invoice.statusInvoice !== INVOICE_STATUS[8]) {
        await cqt_validate_invoice(invoice, t);
      }

      if (pdfFile) {
        let filepath = `${UPLOAD_DIR}/invoice-pdf/${invoice.invoiceId}`;
        let filename = `invoice.pdf`;

        await pdfFile.mv(`${filepath}/${filename}`);

        await invoice.update(
          { pdfFile: `uploads/invoice-pdf/${invoice.invoiceId}/${filename}` },
          { transaction: t },
        );
      }

      if (imgFile) {
        let filepath = `${UPLOAD_DIR}/invoice-img/${invoice.invoiceId}`;
        let filename = `${utils.escapeFilename(imgFile.name)}`;

        await imgFile.mv(`${filepath}/${filename}`);

        await invoice.update(
          { imageFile: `uploads/invoice-img/${invoice.invoiceId}/${filename}` },
          { transaction: t },
        );
      }

      await t.commit();

      if (!invoice.invoiceCategory) {
        res.send({
          result: 'failed',
          status: 202,
          invoice,
          invoiceId: invoice.invoiceId,
          reason: ERROR_INVALID_INVOICE_TAXCODE,
          name: name,
        });
        return;
      }

      res.send({
        result: 'success',
        invoice,
        invoiceId: invoice.invoiceId,
        name,
      });
    } catch (error) {
      await t.rollback();
      // next(error);
      res.status(REQ_ERROR_CODE).send({
        result: 'failed',
        name: name,
        reason: error.message,
      });
      return;
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async updateManualInvoiceInput(req, res, next) {
    const t = await Sequelize.transaction();

    try {
      const { invoiceId } = req.params;
      const { organizationDepartmentId, organizationId, taxCode } = req.account;
      let {
        sellerTaxCode,
        buyerTaxCode,
        buyerName,
        sellerName,
        sellerAddress,
        buyerAddress,
        invoiceDate,
        serial,
        invoiceTypeCode,
        InvoiceProducts,
        amountBeforeVat,
        amountVat,
        finalAmount,
        invoiceNumber,
        statusInvoice,
      } = req.body;

      let invoice = await Invoice.findOne({
        where: {
          invoiceId,
          organizationId,
          organizationDepartmentId,
          isManualInput: true,
          // statusInvoice: INVOICE_STATUS[8],
        },
        transaction: t,
      });

      if (!invoice) {
        throw new Error(ERROR_INVOICE_NOT_FOUND);
      }

      if (
        (sellerTaxCode && !utils.isValidTaxCode(sellerTaxCode)) ||
        (buyerTaxCode && !utils.isValidTaxCode(buyerTaxCode))
      ) {
        throw new Error(ERROR_INVALID_INVOICE_TAXCODE);
      }

      if (InvoiceProducts && !_.isArray(InvoiceProducts)) {
        throw new Error(ERROR_INVALID_PARAMETER);
      }

      if (
        invoiceTypeCode != null &&
        invoiceTypeCode != undefined &&
        !INVOICE_TYPE_CODE[invoiceTypeCode]
      ) {
        throw new Error(ERROR_INVALID_INVOICE_TYPE_CODE);
      } else {
        invoiceTypeCode = undefined;
      }

      if (serial && !invoiceHelper.isValidSerial(serial)) {
        throw new Error(ERROR_INVALID_INVOICE_SERIAL);
      }

      /*  */
      if (
        finalAmount != null &&
        finalAmount != undefined &&
        !isValidNumber(finalAmount)
      ) {
        finalAmount = 0;
      } else {
        finalAmount = undefined;
      }

      if (
        amountVat != undefined &&
        amountVat != null &&
        !isValidNumber(amountVat)
      ) {
        amountVat = 0;
      } else {
        amountVat = undefined;
      }

      if (
        amountBeforeVat != undefined &&
        amountBeforeVat != null &&
        !isValidNumber(amountBeforeVat)
      ) {
        amountBeforeVat = 0;
      } else {
        amountBeforeVat = undefined;
      }

      if (
        _.isArray(InvoiceProducts) &&
        InvoiceProducts.some(item => {
          if (!item.name) return true;

          if (item.vat != null && item.vat != undefined) {
            if (!VAT_TYPE[item.vat]) {
              return true;
            }
          }

          if (item.discount !== undefined) {
            if (!isValidNumber(item.discount)) {
              return true;
            }
          } else {
            item.discount = undefined;
          }

          if (item.discountAmount !== undefined) {
            if (!isValidNumber(item.discount)) {
              return true;
            }
          } else {
            item.discountAmount = undefined;
          }

          if (!item.quantity) {
            item.quantity = 0;
          }

          if (!item.price) {
            item.price = 0;
          }

          return false;
        })
      ) {
        throw new Error(ERROR_INVALID_INVOICE_PRODUCTS);
      }

      /* CHECK duplicate */
      let duplicateInvoice = await Invoice.findOne({
        where: {
          organizationDepartmentId,
          organizationId,
          invoiceNumber: Number(invoiceNumber ?? invoice.invoiceNumber),
          sellerTaxCode: sellerTaxCode ?? invoice.sellerTaxCode,
          invoiceTypeCode: invoiceTypeCode ?? invoice.invoiceTypeCode,
          serial: serial ?? invoice.serial,
          [Op.or]: [{ sellerTaxCode: taxCode }, { buyerTaxCode: taxCode }],
          invoiceId: { [Op.ne]: invoiceId },
        },
        transaction: t,
      });

      if (duplicateInvoice) {
        throw new Error(ERROR_DUPLICATE_INVOICE);
      }

      await invoice.update(
        {
          invoiceNumber: Number(invoiceNumber),
          sellerTaxCode,
          buyerTaxCode,
          buyerName,
          sellerName,
          sellerAddress,
          buyerAddress,
          invoiceDate,
          serial,
          invoiceTypeCode,
          amountBeforeVat,
          amountVat,
          finalAmount,
        },
        { transaction: t },
      );

      /* Luu lai, ko luu nhap nua */
      if (statusInvoice != 8) {
        await invoice.update(
          {
            statusInvoice: null,
            statusInvoiceText: null,
          },
          { transaction: t },
        );
      }

      if (_.isArray(InvoiceProducts) && InvoiceProducts.length) {
        await InvoiceProduct.destroy({
          where: { invoiceId: invoice.invoiceId },
          transaction: t,
        });

        await InvoiceProduct.bulkCreate(
          InvoiceProducts.map(item => {
            item.invoiceId = invoice.invoiceId;

            return item;
          }),
          { transaction: t },
        );
      }

      if (invoice.statusInvoice !== INVOICE_STATUS[8]) {
        await cqt_validate_invoice(invoice, t);
      }

      await t.commit();

      res.send({
        result: 'success',
        invoice,
        invoiceId: invoice.invoiceId,
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
};

/**
 *
 * @param {{InvoiceHistories: import('../types').InvoiceHistory[]}} row
 * @returns
 */
function getExportInvoicePaymentInfo(row) {
  const arrRet = [];
  const invoiceHistories = row?.InvoiceHistories;
  if (invoiceHistories) {
    const histories = invoiceHistories?.map(item => {
      return {
        '- Ngày thanh toán': item?.paymentDate
          ? moment(item?.paymentDate).format('YYYY-MM-DD')
          : '',
        '- Người thanh toán': item?.paymentPersonName ?? '',
        '- Số tiền thanh toán': item?.paymentMoney ?? '',
      };
    });

    histories?.forEach(item => {
      const arrStr = [];
      const keys = Object.keys(item);
      keys.forEach(key => {
        const value = item[key];
        arrStr.push(`${key}: ${value}`);
      });
      arrRet.push(arrStr.join('\n'));
    });
  }

  return arrRet.join('\n');
}

/**
 *
 * @param {import('../types').Invoice} row
 * @returns
 */
const getExportInvoiceFileStatus = row => {
  const arrRet = [];
  arrRet.push(row?.xmlFile ? 'Có XML' : 'Không có XML');
  arrRet.push(
    row?.previewPdfFile ? 'Có bản thể hiện' : 'Không có bản thể hiện',
  );
  return arrRet.join(', ');
};

/**
 *
 * @param {import('../types').InvoiceValidate} e
 * @returns
 */
const checkResult = e => {
  let result = '';
  if (e?.checkResultBuyerAddress === false) {
    result += 'Sai địa chỉ người mua';
  }
  if (e?.checkResultBuyerTaxCode === false) {
    if (result !== '') {
      result += ', Sai mã số thuế người mua';
    } else {
      result += 'Sai mã số thuế người mua';
    }
  }
  if (e?.checkResultBuyerName === false) {
    if (result !== '') {
      result += ', Sai tên người mua';
    } else {
      result += 'Sai tên người mua';
    }
  }
  if (e?.checkResultSellerAddress === false) {
    if (result !== '') {
      result += ', Sai địa chỉ người bán';
    } else {
      result += 'Sai địa chỉ người bán';
    }
  }
  if (e?.checkResultSellerName === false) {
    if (result !== '') {
      result += ', Sai tên người bán';
    } else {
      result += 'Sai tên người bán';
    }
  }
  if (e?.checkResultHasInvoiceCode === false) {
    if (result !== '') {
      result += ', Hóa đơn chưa tồn tại trên CQT';
    } else {
      result += 'Hóa đơn chưa tồn tại trên CQT';
    }
  }
  if (e?.checkResultHasInvoiceCode === false) {
    if (result !== '') {
      result += ', Chưa cấp mã hóa đơn';
    } else {
      result += 'Chưa cấp mã hóa đơn';
    }
  }

  if (e?.checkResultBuyerName === null) {
    if (result !== '') {
      result += ', Không có tên người mua';
    } else {
      result += 'Không có tên người mua';
    }
  }
  if (e?.checkResultBuyerAddress === null) {
    if (result !== '') {
      result += ', Không có địa chỉ người mua';
    } else {
      result += 'Không có địa chỉ người mua';
    }
  }
  if (e?.checkResultSellerAddress === null) {
    if (result !== '') {
      result += ', Không có địa chỉ người bán';
    } else {
      result += 'Không có địa chỉ người bán';
    }
  }
  if (e?.checkResultBuyerTaxCode === null) {
    if (result !== '') {
      result += ', Không có mã số thuế người mua';
    } else {
      result += 'Không có mã số thuế người mua';
    }
  }
  if (e?.checkResultSellerName === null) {
    if (result !== '') {
      result += ', Không có tên người bán';
    } else {
      result += 'Không có tên người bán';
    }
  }
  if (e?.checkResultSignatureNCC === null) {
    if (result !== '') {
      result += ', Không có chữ ký nhà cung cấp';
    } else {
      result += 'Không có chữ ký nhà cung cấp';
    }
  }
  if (
    e?.checkResultBuyerAddress &&
    e?.checkResultBuyerTaxCode &&
    e?.checkResultBuyerName &&
    e?.checkResultSellerName &&
    e?.checkResultHasInvoiceCode &&
    e?.checkResultHasInvoiceCode &&
    e?.checkResultSellerAddress
  ) {
    result += 'Hóa đơn hợp lệ';
  }

  return result;
};

/**
 *
 * @param {import('../types').InvoiceProduct[]} e
 * @returns
 */
const checKThue = e => {
  let arrThue = [];
  for (let i = 0; i < e?.length; i++) {
    if (e?.[i]?.vat && !arrThue?.includes(e?.[i]?.vat)) {
      arrThue.push(e?.[i]?.vat);
    }
  }

  return arrThue?.length > 0 ? arrThue.map(x => x).join(',') : 0;
};
