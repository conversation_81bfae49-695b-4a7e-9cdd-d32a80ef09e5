const { Op } = require('sequelize');
const { ERROR_INVALID_DATE } = require('../configs/error.vi');
const { AuthLog, Sequelize } = require('../models');
const { isValidNumber, isValidDate } = require('../utils');
const _ = require('lodash');
const moment = require('moment');
const { AUTH_LOG_STATUS } = require('../configs/constants/auth-log.constanst');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async mine(req, res, next) {
    try {
      let { limit, page, from, to, q } = req.query;

      q = q ?? '';

      if (from && !isValidDate(from)) throw new Error(ERROR_INVALID_DATE);

      if (to && !isValidDate(to)) throw new Error(ERROR_INVALID_DATE);

      /**
       * @type {import('sequelize').WhereOptions<import('../types').AuthLog>}
       */
      let conditions = [
        { accountId: req.account.accountId },
        from ? { createdAt: { [Op.gte]: moment(from).startOf('date') } } : {},
        to ? { createdAt: { [Op.lte]: moment(to).endOf('date') } } : {},
        {
          [Op.or]: [
            { device: { [Op.like]: `%${q}%` } },
            { browser: { [Op.like]: `%${q}%` } },
            { ipAddress: { [Op.like]: `%${q}%` } },
          ],
        },
      ];

      if (!isValidNumber(limit) || !isValidNumber(page)) {
        let rs = await AuthLog.findAll({
          where: conditions,
        });

        res.send({ result: 'success', authLogs: rs, total: rs.length });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        let rs = await AuthLog.findAndCountAll({
          limit,
          offset: limit * page,
          where: conditions,
        });

        res.send({
          result: 'success',
          total: rs.count,
          count: rs.rows.length,
          authLogs: rs.rows,
        });
      }
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async mineDevices(req, res, next) {
    try {
      let { page, limit, from, to, q } = req.query;

      q = q ?? '';

      if (from && !isValidDate(from)) throw new Error(ERROR_INVALID_DATE);

      if (to && !isValidDate(to)) throw new Error(ERROR_INVALID_DATE);

      /**
       * @type {import('sequelize').WhereOptions<import('../types').AuthLog>}
       */
      let conditions = [
        { accountId: req.account.accountId },
        { status: AUTH_LOG_STATUS.SUCCESS },
        from ? { createdAt: { [Op.gte]: moment(from).startOf('date') } } : {},
        to ? { createdAt: { [Op.lte]: moment(to).endOf('date') } } : {},
        {
          [Op.or]: [
            { device: { [Op.like]: `%${q}%` } },
            { browser: { [Op.like]: `%${q}%` } },
            { ipAddress: { [Op.like]: `%${q}%` } },
          ],
        },
      ];

      if (!isValidNumber(page) || !isValidNumber(limit)) {
        let rs = await AuthLog.findAll({
          attributes: [
            'device',
            'ipAddress',
            'browser',
            [Sequelize.fn('MAX', Sequelize.col('createdAt')), 'createdAt'],
          ],
          where: conditions,
          order: [['createdAt', 'DESC']],
          group: ['device', 'ipAddress', 'browser'],
        });

        res.send({ result: 'success', devices: rs, total: rs.length });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        let rs = await AuthLog.findAndCountAll({
          limit,
          offset: limit * page,
          attributes: [
            'device',
            'ipAddress',
            'browser',
            [Sequelize.fn('MAX', Sequelize.col('createdAt')), 'createdAt'],
          ],
          where: conditions,
          order: [['createdAt', 'DESC']],
          group: ['device', 'ipAddress', 'browser'],
          distinct: true,
        });

        res.send({
          result: 'success',
          total: rs.count.length,
          count: rs.rows.length,
          devices: rs.rows,
        });
      }
    } catch (error) {
      next(error);
    }
  },
};
