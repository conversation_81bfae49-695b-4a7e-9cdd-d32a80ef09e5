const _ = require('lodash');
const utils = require('../utils');
const {
  Sequelize,
  Package,
  Config,
  Account,
  OrderItem,
  Order,
} = require('../models');
const { Op } = require('sequelize');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_NOT_FOUND,
  ERROR_PACKAGE_CODE_EXISTED,
  ERROR_INVALID_PARAMETER,
} = require('../configs/error.vi');
const { SERVICE } = require('../configs/constants/account.constant');
const { PACKAGE_TYPE } = require('../configs/constants/package.constant');
const { STATUS_ORDER } = require('../configs/constants/order.constant');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let {
        q,
        limit,
        page,
        packageType,
        dealerPackage = false,
        service,
        grouping = false,
      } = req.query;
      q = q ?? '';

      /**
       * @type {import('sequelize').WhereOptions<import('../types').Package>}
       */

      let conditions = [
        dealerPackage === 'all'
          ? {}
          : { dealerPackage: utils.parseBoolean(dealerPackage) },
        {
          [Op.or]: [
            { packageCode: { [Op.like]: `%${q}%` } },
            { packageCost: { [Op.like]: `%${q}%` } },
          ],
        },
      ];
      if (packageType && packageType !== '') {
        conditions.push({ packageType: packageType });
      }
      if (service && Object.values(SERVICE).includes(service)) {
        conditions.push({ service: service });
      }

      /* ko hien TRIAL neu goi tu QLHD */
      if (req.headers.referer) {
        conditions.push({
          [Op.or]: [
            { kmCode: { [Op.is]: null } },
            { kmCode: { [Op.notLike]: `%TRIAL%` } },
          ],
        });
      }

      let licensePackageOffConfig = await Config.findOne({
        where: { key: 'LICENSE_OFF' },
      });

      if (
        licensePackageOffConfig &&
        utils.parseBoolean(licensePackageOffConfig.value)
      ) {
        conditions.push({ packageType: { [Op.ne]: PACKAGE_TYPE.LICENSE } });
      }

      let packages;
      limit = _.toNumber(limit);
      page = _.toNumber(page);

      let KHUYEN_MAI_I_PRO = utils.parseBoolean(
        (await Config.findOne({ where: { key: 'KHUYEN_MAI_I_PRO' } }))?.value,
      );

      if (!utils.isValidNumber(limit) || !utils.isValidNumber(page)) {
        page = undefined;
        limit = undefined;
        packages = await Package.findAndCountAll({
          where: conditions,
          order: [
            [
              Sequelize.literal(
                `case when packageType = '${PACKAGE_TYPE.LICENSE}' then 1 when packageType = '%${PACKAGE_TYPE.DURATION}%' then 2 else 3 end`,
              ),
            ],
            ['quantity', 'ASC'],
            ['duration', 'ASC'],
          ],
          logging: console.log,
        });

        let promotionPackages = [];

        if (utils.parseBoolean(grouping)) {
          if (KHUYEN_MAI_I_PRO && !packageType) {
            let i_pro_trial_10000 = packages.rows.find(
              i => i.kmCode == 'TRIAL-10000',
            );
            let i_pro_trial_20000 = packages.rows.find(
              i => i.kmCode == 'TRIAL-20000',
            );
            let i_pro_trial_1t = packages.rows.find(
              i => i.kmCode == 'TRIAL-1T',
            );
            let i_pro_trial_6t = packages.rows.find(
              i => i.kmCode == 'TRIAL-6T',
            );

            if (i_pro_trial_10000 && i_pro_trial_1t) {
              /**
               * @type {import('../types').Package}
               */
              let promotion1 = {
                packageType: 'GROUP-PROMOTION',
                packages: [i_pro_trial_10000, i_pro_trial_1t],
                packageCode: [
                  i_pro_trial_10000.packageCode,
                  i_pro_trial_1t.packageCode,
                ].toString(),
                packageCost:
                  i_pro_trial_10000.packageCost + i_pro_trial_1t.packageCost,
                service: SERVICE.I_PRO,
                description: 'Gói khuyên mãi cho khách hàng dùng thử 1',
                duration: null,
                quantity: null,
              };

              promotionPackages.push(promotion1);
            }

            if (i_pro_trial_20000 && i_pro_trial_6t) {
              /**
               * @type {import('../types').Package}
               */
              let promotion2 = {
                packageType: 'GROUP-PROMOTION',
                packages: [i_pro_trial_20000, i_pro_trial_6t],
                packageCode: [
                  i_pro_trial_20000.packageCode,
                  i_pro_trial_6t.packageCode,
                ].toString(),
                packageCost:
                  i_pro_trial_20000.packageCost + i_pro_trial_6t.packageCost,
                service: SERVICE.I_PRO,
                description: 'Gói khuyên mãi cho khách hàng dùng thử 2',
                duration: null,
                quantity: null,
              };

              promotionPackages.push(promotion2);
            }
          }

          packages.rows = _.concat(
            promotionPackages,
            packages.rows.filter(i => !i.kmCode.startsWith('TRIAL')),
          );

          packages.count = packages.rows.length;
        }
      } else {
        packages = await Package.findAndCountAll({
          where: conditions,
          limit,
          offset: limit * page,
          logging: console.log,
          order: [
            [
              Sequelize.literal(
                `case when packageType = '${PACKAGE_TYPE.LICENSE}' then 1 when packageType = '%${PACKAGE_TYPE.DURATION}%' then 2 else 3 end`,
              ),
            ],
            ['quantity', 'ASC'],
            ['duration', 'ASC'],
          ],
        });
      }

      res.send({
        result: 'success',
        page,
        total: packages.count,
        count: packages.rows.length,
        packages: packages.rows,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      let { packageId } = req.params;
      if (!packageId) {
        throw new Error(ERROR_MISSING_PARAMETERS);
      }
      let package = (
        await Package.findOne({
          where: {
            packageId,
          },
        })
      )?.toJSON();
      if (!package) {
        throw new Error(ERROR_NOT_FOUND);
      }
      res.send({
        result: 'success',
        package,
      });
    } catch (error) {
      next(error);
    }
  },
  // /**
  //  * @type {import('../types').RequestHandler}
  //  */
  // async detailByPackageCode(req, res, next) {
  //   try {
  //     let { packageCode } = req.query;
  //     let package = (
  //       await Package.findOne({
  //         where: {
  //           packageCode,
  //         },
  //       })
  //     )?.toJSON();
  //     if (!package) {
  //       throw new Error(ERROR_NOT_FOUND);
  //     }
  //     res.send({
  //       result: 'success',
  //       package,
  //     });
  //   } catch (error) {
  //     next(error);
  //   }
  // },
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    const t = await Sequelize.transaction();

    try {
      let {
        packageCode,
        packageCost,
        unit,
        packageType,
        quantity,
        duration,
        description,
        recommend,
        dealerPackage,
        service,
        metaAccountCode,
      } = req.body;

      if (!packageCode || !packageCost || !unit)
        throw new Error(ERROR_MISSING_PARAMETERS);
      if (recommend !== undefined) {
        recommend = utils.parseBoolean(recommend);
      }

      // kiểm tra mã đã tồn tại
      const checkPackageCode = await Package.findOne({
        where: { packageCode },
      });
      if (checkPackageCode) {
        throw new Error(ERROR_PACKAGE_CODE_EXISTED);
      }

      if (!Object.values(SERVICE).includes(service)) {
        service = undefined;
      }

      if (packageType && !PACKAGE_TYPE[packageType]) {
        throw new Error(ERROR_INVALID_PARAMETER);
      }

      if (packageType == PACKAGE_TYPE.QUANTITY) {
        if (!utils.isValidNumber(quantity) || !Number(quantity)) {
          throw new Error(ERROR_MISSING_PARAMETERS);
        }
      } else if (packageType == PACKAGE_TYPE.DURATION) {
        if (!utils.isValidNumber(duration) || !Number(duration)) {
          throw new Error(ERROR_MISSING_PARAMETERS);
        }
      }

      if (!utils.parseBoolean(dealerPackage)) {
        if (!packageType) throw new Error(ERROR_MISSING_PARAMETERS);
      }

      const package = await Package.create(
        {
          packageCode,
          packageCost,
          unit,
          packageType,
          quantity: packageType == PACKAGE_TYPE.QUANTITY ? quantity : undefined,
          duration:
            packageType == PACKAGE_TYPE.LICENSE ||
            packageType == PACKAGE_TYPE.DURATION
              ? duration
              : undefined,
          description,
          recommend,
          dealerPackage: utils.parseBoolean(dealerPackage),
          service,
          dealerMetaAccountCode: metaAccountCode,
        },
        { transaction: t },
      );

      await t.commit();

      res.send({
        result: 'success',
        package,
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },

  async update(req, res, next) {
    const t = await Sequelize.transaction();

    try {
      let { packageId } = req.params;
      let {
        packageCode,
        packageCost,
        unit,
        packageType,
        quantity,
        duration,
        description,
        recommend,
        service,
        metaAccountCode,
      } = req.body;

      let package = await Package.findOne({
        where: {
          packageId,
          [Op.and]: [
            metaAccountCode ? { dealerMetaAccountCode: metaAccountCode } : {},
          ],
        },
      });

      if (!package) {
        throw new Error(ERROR_NOT_FOUND);
      }

      /**
       * @type {import('../types').Package}
       */
      let update = {};
      if (packageCode) {
        // kiểm tra mã đã tồn tại
        const checkPackageCode = await Package.findOne({
          where: { packageCode, packageId: { [Op.ne]: packageId } },
        });
        if (checkPackageCode) {
          throw new Error(ERROR_PACKAGE_CODE_EXISTED);
        }

        update.packageCode = packageCode;
      }
      if (packageCost) update.packageCost = packageCost;
      if (unit) update.unit = unit;
      if (packageType) {
        if (!PACKAGE_TYPE[packageType]) {
          throw new Error(ERROR_INVALID_PARAMETER);
        }

        update.packageType = packageType;
      }
      if (quantity) {
        if (!utils.isValidNumber(quantity)) {
          throw new Error(ERROR_INVALID_PARAMETER);
        }
        update.quantity = quantity;
      }
      if (duration) {
        if (!utils.isValidNumber(duration)) {
          throw new Error(ERROR_INVALID_PARAMETER);
        }
        update.duration = duration;
      }
      if (description) update.description = description;
      if (recommend !== undefined)
        update.recommend = utils.parseBoolean(recommend);
      if (service && SERVICE[service]) {
        update.service = service;
      }

      await package.update(update, { transaction: t });
      await t.commit();

      res.send({
        result: 'success',
        package,
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    try {
      const { packageIds } = req.body;
      if (!_.isArray(packageIds)) throw new Error(ERROR_INVALID_PARAMETER);

      let deleteCount = await Package.destroy({
        where: {
          packageId: { [Op.in]: packageIds },
        },
      });

      return res.send({
        result: 'success',
        deleteCount,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @deprecated
   * Lấy d/sách các gói mà đại lý đã tạo
   * @type {import('../types').RequestHandler}
   */
  async findSellDealerPackage(req, res, next) {
    try {
      let { limit, page, metaAccountCode, packageType } = req.query;

      if (!metaAccountCode) throw new Error(ERROR_MISSING_PARAMETERS);

      let packages = await Package.findAll({
        where: {
          // dealerMetaAccountCode: metaAccountCode,
          dealerPackage: false,
          [Op.and]: [packageType ? { packageType } : {}],
        },
      });

      let total = packages.length;

      if (utils.isValidNumber(limit) && utils.isValidNumber(page)) {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        packages = packages.slice(limit * page, limit * page + limit);
      } else {
      }

      res.send({
        result: 'success',
        packages,
        page,
        total,
        count: packages.length,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @deprecated
   * Lấy danh sách các gói SLL mà một DL có thể mua
   * @type {import('../types').RequestHandler}
   */
  async findBuyDealerPackage(req, res, next) {
    try {
      let { limit, page, metaAccountCode, packageType } = req.query;

      let packages = await Package.findAll({
        where: {
          dealerPackage: true,
          [Op.and]: [
            packageType ? { packageType } : {},
            // metaAccountCode
            //   ? { dealerMetaAccountCode: metaAccountCode }
            //   : { dealerMetaAccountCode: { [Op.is]: null } },
          ],
        },
      });

      let total = packages.length;

      if (utils.isValidNumber(limit) && utils.isValidNumber(page)) {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        packages = packages.slice(limit * page, limit * page + limit);
      } else {
      }

      res.send({
        result: 'success',
        packages,
        page,
        total,
        count: packages.length,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async history(req, res, next) {
    try {
      let { metaAccountCode } = req.params;

      let account = await Account.findOne({ where: { metaAccountCode } });

      if (!account) throw new Error(ERROR_MISSING_PARAMETERS);

      let orderItems = await OrderItem.findAll({
        include: [
          {
            model: Order,
            where: {
              accountId: account.accountId,
              statusOrder: STATUS_ORDER.COMPLETE,
            },
          },
        ],
      });

      let packages = _.concat(
        ...orderItems.map(item => {
          let arr = [];

          for (let i = 0; i < item.quantity; i++) {
            arr.push(item.package);
          }

          return arr;
        }),
      );

      res.send({ result: 'success', packages });
    } catch (error) {
      next(error);
    }
  },
};
