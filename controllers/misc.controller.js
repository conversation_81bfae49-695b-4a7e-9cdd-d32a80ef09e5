const { default: axios } = require('axios');
const _ = require('lodash');
const fs = require('fs');
const path = require('path');
const {
  ERROR_NOT_FOUND,
  ERROR_MISSING_PARAMETERS,
  ERROR_INVALID_PARAMETER,
  ERROR_INVALID_TAXCODE,
  ERROR_COMPANY_NOT_EXISTED,
  ERROR_INVALID_TAXCODE_OR_CCCD,
  ERROR_USER_NOT_FOUND,
} = require('../configs/error.vi');
const { isValidTaxCode } = require('../utils');
const { Company, Account } = require('../models');
const qlbhHelper = require('../utils/qlbh.helper');
const { PUBLIC_UPLOAD_DIR } = require('../configs/env');
const utils = require('../utils');
const nntHelper = require('../utils/nnt.helper');
const { ACCOUNT_LEVEL } = require('../configs/constants/account.constant');
const { getPublicAccountAttributes } = require('../utils/account.helper');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async findCompanyByTaxCode(req, res, next) {
    try {
      let { q } = req.query;

      q = q ?? '';

      if (!isValidTaxCode(q)) throw new Error(ERROR_INVALID_TAXCODE);

      let company = await nntHelper.getCompany(q);

      res.send({ result: 'success', company });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async findCompanyByQueries(req, res, next) {
    try {
      let { queries } = req.query;

      if (!_.isArray(queries)) {
        throw new Error(ERROR_INVALID_PARAMETER);
      }

      if (queries.some(q => !isValidTaxCode(q) && !utils.isValidCCCD(q))) {
        throw new Error(ERROR_INVALID_TAXCODE_OR_CCCD);
      }

      let companies = await Promise.all(
        queries.map(q => nntHelper.getCompany(q).catch(console.log)),
      );

      companies = companies.filter(_.isObject);

      res.send({ result: 'success', companies });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async findVoucherByCode(req, res, next) {
    try {
      let { voucherCode } = req.query;

      let { result, reason, voucher } =
        await qlbhHelper.clientCodeGetVoucherByCode(voucherCode);

      if (result != 'success') throw new Error(reason);

      res.send({ result: 'success', voucher });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async pushFile(req, res, next) {
    try {
      if (!req.files?.['file']) throw new Error(ERROR_MISSING_PARAMETERS);

      let file = req.files['file'];
      if (_.isArray(file)) file = file[0];

      const filepath = `${PUBLIC_UPLOAD_DIR}/common-file`;
      const filename = `${Date.now()}-` + utils.escapeFilename(file.name);

      await file.mv(path.join(filepath, filename));

      res.send({
        result: 'success',
        url: 'resources/common-file/' + filename,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async findVoucher(req, res, next) {
    try {
      let { result, reason, vouchers } =
        await qlbhHelper.clientCodeFindVoucher();

      if (result != 'success') {
        throw new Error(reason);
      }

      res.send({ result: 'success', vouchers });
    } catch (error) {
      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async findByMetaAccountCode(req, res, next) {
    try {
      let { metaAccountCode } = req.params;

      let account = await Account.findOne({
        attributes: getPublicAccountAttributes(),
        where: { metaAccountCode, accountLevel: ACCOUNT_LEVEL.COMPANY_ADMIN },
        include: [{ model: Company }],
      });

      if (!account) {
        throw new Error(ERROR_USER_NOT_FOUND);
      }

      res.send({ result: 'success', account });
    } catch (error) {
      next(error);
    }
  },
};
