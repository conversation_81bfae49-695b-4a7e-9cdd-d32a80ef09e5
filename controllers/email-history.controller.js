const { Op } = require('sequelize');
const {
  ERROR_NOT_FOUND,
  ERROR_INVALID_PARAMETER,
} = require('../configs/error.vi');
const { EmailHistory, Account, Sequelize, Company } = require('../models');
const { sendEmail } = require('../utils/nodemailer.helper');
const { isValidNumber, isValidDate } = require('../utils');
const _ = require('lodash');
const { DATETIME_FORMAT } = require('../configs/constants/other.constant');
const moment = require('moment');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async resend(req, res, next) {
    try {
      let { emailHistoryIds } = req.body;

      if (!_.isArray(emailHistoryIds)) {
        throw new Error(ERROR_INVALID_PARAMETER);
      }

      let emailHistories = await <PERSON><PERSON><PERSON>istory.findAll({
        where: { emailHistoryId: { [Op.in]: emailHistoryIds } },
      });

      res.send({ result: 'success' });

      for (let emailHistory of emailHistories) {
        try {
          sendEmail({
            subject: emailHistory.emailTitle,
            html: emailHistory.emailContent,
            recipients: emailHistory.emailRecipients,
            saveToHistory: false,
          });
        } catch (error) {
          console.warn(error);
        }
      }
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let {
        limit,
        page,
        q,
        status,
        emailType,
        loaiKhachHang,
        metaAccountCodes,
        startDate,
        endDate,
      } = req.method == 'GET' ? req.query : req.body;

      if (metaAccountCodes !== undefined) {
        metaAccountCodes = String(metaAccountCodes)
          .split(',')
          .map(i => i.trim());
      }

      /**
       * @type {import('sequelize').WhereOptions<import('../types').EmailHistory>}
       */
      let conditions = [
        q
          ? {
              [Op.or]: [
                // {
                //   emailTitle: { [Op.like]: `%${q}%` },
                // },
                // Sequelize.literal(`emailRecipients LIKE '%${q}%'`),
                {
                  '$Account.email$': q,
                },
                {
                  '$Account.Company.taxCode$': q,
                },
              ],
            }
          : {},
        status ? { status } : {},
        emailType ? { emailType } : {},
        // loaiKhachHang ? { loaiKhachHang } : {},
        startDate && isValidDate(startDate)
          ? {
              sentTime: {
                [Op.gte]: moment(startDate).format(DATETIME_FORMAT),
              },
            }
          : {},
        endDate && isValidDate(endDate)
          ? {
              sentTime: {
                [Op.lte]: moment(endDate).format(DATETIME_FORMAT),
              },
            }
          : {},
      ];

      if (!isValidNumber(page)) {
        page = 0;
      }
      if (!isValidNumber(limit)) {
        limit = 10;
      }

      if (_.isArray(metaAccountCodes)) {
        let accounts = await Account.findAll({
          where: { metaAccountCode: { [Op.in]: metaAccountCodes } },
        });
        conditions.push({
          accountId: { [Op.in]: accounts.map(i => i.accountId) },
        });
      }

      page = _.toNumber(page);
      limit = _.toNumber(limit);

      let rs = await EmailHistory.findAndCountAll({
        limit: limit,
        offset: limit * page,
        where: conditions,
        order: [['sentTime', 'DESC']],
        include: [{ model: Account, include: [{ model: Company }] }],
        logging: console.log,
      });

      res.send({
        result: 'success',
        page,
        total: rs.count,
        count: rs.rows.length,
        emails: rs.rows,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    try {
      let { emailHistoryIds } = req.body;

      if (!_.isArray(emailHistoryIds)) {
        throw new Error(ERROR_INVALID_PARAMETER);
      }
      let deleteCount = await EmailHistory.destroy({
        where: { emailHistoryId: { [Op.in]: emailHistoryIds } },
      });

      res.send({ result: 'success', deleteCount });
    } catch (error) {
      next(error);
    }
  },
};
