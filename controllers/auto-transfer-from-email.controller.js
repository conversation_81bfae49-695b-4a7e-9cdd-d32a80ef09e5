const { Op } = require('sequelize');
const {
  ERROR_NOT_FOUND,
  ERROR_INVALID_PARAMETER,
  ERROR_MISSING_PARAMETERS,
} = require('../configs/error.vi');
const {
  Organization,
  OrganizationDepartment,
  AutoTransferInvoiceFromEmail,
  Sequelize,
} = require('../models');
const _ = require('lodash');
module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      const { organizationBIds, organizationDepartmentBIds } = req.body;
      const { organizationId, organizationDepartmentId } = req.account;

      if (!organizationBIds && !organizationDepartmentBIds)
        throw new Error(ERROR_MISSING_PARAMETERS);

      await AutoTransferInvoiceFromEmail.destroy({
        where: {
          organizationId,
          organizationDepartmentId,
        },
        transaction: t,
      });

      if (organizationBIds) {
        if (!_.isArray(organizationBIds))
          throw new Error(ERROR_INVALID_PARAMETER);

        for (let i = 0; i < organizationBIds.length; i++) {
          const organizationB = await Organization.findOne({
            where: {
              organizationId: organizationBIds[i],
            },
          });
          if (!organizationB) throw new Error(ERROR_NOT_FOUND);
        }

        await AutoTransferInvoiceFromEmail.bulkCreate(
          organizationBIds.map(
            /**
             *
             * @param {import('../types').AutoTransferInvoiceFromEmail} invoice
             * @returns
             */
            bId => ({
              organizationId: organizationId,
              organizationDepartmentId: organizationDepartmentId,
              organizationBId: bId,
            }),
          ),
          {
            transaction: t,
          },
        );
      }
      if (organizationDepartmentBIds) {
        if (!_.isArray(organizationDepartmentBIds))
          throw new Error(ERROR_INVALID_PARAMETER);

        for (let i = 0; i < organizationDepartmentBIds.length; i++) {
          const organizationDepartmentB = await OrganizationDepartment.findOne({
            where: {
              organizationDepartmentId: organizationDepartmentBIds[i],
            },
          });
          if (!organizationDepartmentB) throw new Error(ERROR_NOT_FOUND);
        }

        await AutoTransferInvoiceFromEmail.bulkCreate(
          organizationDepartmentBIds.map(
            /**
             *
             * @param {import('../types').AutoTransferInvoiceFromEmail} invoice
             * @returns
             */
            bId => ({
              organizationId,
              organizationDepartmentId,
              organizationDepartmentBId: bId,
            }),
          ),
          {
            transaction: t,
          },
        );
      }
      await t.commit();
      res.send({
        result: 'success',
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      const { organizationId, organizationDepartmentId } = req.account;
      const conditions = {
        [Op.and]: [
          true ? { organizationDepartmentId } : {},
          true ? { organizationId } : {},
        ],
      };

      const autoTransferInvoiceFromEmails =
        await AutoTransferInvoiceFromEmail.findAll({
          where: conditions,
          include: [
            {
              model: Organization,
            },
            {
              model: OrganizationDepartment,
              include: {
                model: Organization,
                as: 'OrganizationBranch',
              },
            },
          ],
        });

      for (let autoTransferInvoiceFromEmail of autoTransferInvoiceFromEmails) {
        if (autoTransferInvoiceFromEmail.OrganizationDepartment) {
          autoTransferInvoiceFromEmail.taxCode =
            autoTransferInvoiceFromEmail.OrganizationDepartment.OrganizationBranch.taxCode;
          autoTransferInvoiceFromEmail.OrganizationDepartment.taxCode =
            autoTransferInvoiceFromEmail.OrganizationDepartment.OrganizationBranch.taxCode;
          autoTransferInvoiceFromEmail.OrganizationDepartment.setDataValue(
            'taxCode',
            autoTransferInvoiceFromEmail.OrganizationDepartment.taxCode,
          );
          autoTransferInvoiceFromEmail.setDataValue(
            'taxCode',
            autoTransferInvoiceFromEmail.taxCode,
          );
        } else if (autoTransferInvoiceFromEmail.Organization) {
          autoTransferInvoiceFromEmail.taxCode =
            autoTransferInvoiceFromEmail.Organization.taxCode;
          autoTransferInvoiceFromEmail.setDataValue(
            'taxCode',
            autoTransferInvoiceFromEmail.taxCode,
          );
        }
      }
      res.send({
        result: 'success',
        autoTransferInvoiceFromEmails,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
    } catch (error) {
      next(error);
    }
  },
};
