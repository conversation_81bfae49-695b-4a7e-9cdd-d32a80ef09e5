const _ = require('lodash');
const {
  SupplierNotDownloadInvoice,
  Company,
  PartnerCompany,
  Sequelize,
} = require('../models');
const { ERROR_INVALID_PARAMETER } = require('../configs/error.vi');
const { isValidNumber } = require('../utils');
const { Op } = require('sequelize');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    let t = await Sequelize.transaction();
    try {
      const { organizationId, organizationDepartmentId } = req.account;
      const { partnerCompanyIds } = req.body;

      if (!_.isArray(partnerCompanyIds))
        throw new Error(ERROR_INVALID_PARAMETER);

      await SupplierNotDownloadInvoice.destroy({
        where: {
          organizationId,
          organizationDepartmentId,
          // partnerCompanyId: { [Op.in]: [partnerCompanyIds] },
        },
        transaction: t,
      });

      for (let i = 0; i < partnerCompanyIds.length; i++) {
        const findPartnerCompany = await PartnerCompany.findOne({
          where: { partnerCompanyId: partnerCompanyIds[i] },
        });
        if (!findPartnerCompany) continue;
        await SupplierNotDownloadInvoice.create(
          {
            organizationDepartmentId,
            organizationId,
            partnerCompanyId: partnerCompanyIds[i],
          },
          {
            transaction: t,
          },
        );
      }

      await t.commit();
      res.send({
        result: 'success',
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let { q, limit, page, orderField, orderMode } = req.query;
      q = q ?? '';
      orderField = orderField ?? 'updatedAt';
      orderMode = orderMode ?? 'DESC';

      let { organizationDepartmentId, organizationId } = req.account;

      /**
       * @type {import('sequelize').WhereOptions<import('../types').SupplierNotDownloadInvoice>}
       */
      let conditions = {
        [Op.and]: [
          {
            [Op.or]: [
              {
                '$PartnerCompany.partnerCompanyName$': { [Op.like]: `%${q}%` },
              },
              { '$PartnerCompany.address$': { [Op.like]: `%${q}%` } },
              { '$PartnerCompany.taxCode$': { [Op.like]: `%${q}%` } },
            ],
          },
          { organizationDepartmentId },
          { organizationId },
        ],
      };

      if (!isValidNumber(limit) || !isValidNumber(page)) {
        let sndis = (
          await SupplierNotDownloadInvoice.findAll({
            where: conditions,
            order: [[orderField, orderMode]],
            include: {
              model: PartnerCompany,
            },
          })
        ).map(tag => tag.toJSON());

        res.send({ result: 'success', supplierNotDownloadInvoices: sndis });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        let sndis = await SupplierNotDownloadInvoice.findAndCountAll({
          where: conditions,
          include: {
            model: PartnerCompany,
          },
          limit,
          offset: limit * page,
          order: [[orderField, orderMode]],
        });

        res.send({
          result: 'success',
          page,
          total: sndis.count,
          count: sndis.rows.length,
          supplierNotDownloadInvoices: sndis.rows,
        });
      }
    } catch (error) {
      next(error);
    }
  },
};
