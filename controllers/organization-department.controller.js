const { Op } = require('sequelize');
const { ORGANIZATION_TYPE } = require('../configs/constants/company.constant');
const { INVOICE_MAILBOX_DOMAIN } = require('../configs/env');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_INVALID_EMAIL,
  ERROR_INVALID_INVOICE_MAILBOX,
  ERROR_ORGANIZATION_BRANCH_NOT_FOUND,
  ERROR_NOT_FOUND,
  ERROR_INVALID_PARAMETER,
  ERROR_MOVE_INVOICE_BEFORE_DELETE,
  ERROR_PERMISSION_DENIED,
  ERROR_INVALID_PHONE,
  ERROR_ORGANIZATION_NOT_GRANTED,
} = require('../configs/error.vi');
const {
  Organization,
  OrganizationDepartment,
  Sequelize,
  Invoice,
  AcceptPartnerCompanyDetail,
  AccountOrganizationAccess,
} = require('../models');
const {
  isValidEmail,
  replaceInString,
  isValidDate,
  isValidPhone,
} = require('../utils');
const { checkInvoiceMailboxUsed } = require('../utils/company.helper');
const _ = require('lodash');
const { getPublicAccountAttributes } = require('../utils/account.helper');
const { ACCOUNT_LEVEL } = require('../configs/constants/account.constant');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let {
        organizationId,
        departmentName,
        invoiceMailbox,
        representativeEmail,
        representativeJobTitle,
        representativeName,
        representativePhone,
      } = req.body;

      /* TODO: check if is first child */

      if (!organizationId || !departmentName || !invoiceMailbox)
        throw new Error(ERROR_MISSING_PARAMETERS);

      if (!isValidEmail(invoiceMailbox)) throw new Error(ERROR_INVALID_EMAIL);

      if (representativeEmail && !isValidEmail(representativeEmail))
        throw new Error(ERROR_INVALID_EMAIL);

      // if (representativePhone && !isValidPhone(representativePhone))
      //   throw new Error(ERROR_INVALID_PHONE);

      if (!invoiceMailbox.endsWith(INVOICE_MAILBOX_DOMAIN)) {
        throw new Error(
          replaceInString(ERROR_INVALID_INVOICE_MAILBOX, {
            domain: INVOICE_MAILBOX_DOMAIN,
          }),
        );
      }

      let organization = await Organization.findOne({
        where: {
          organizationId,
          organizationType: ORGANIZATION_TYPE.BRANCH,
          companyId: req.account.companyId,
        },
      });

      if (!organization) throw new Error(ERROR_ORGANIZATION_BRANCH_NOT_FOUND);

      if (req.account.accountLevel != ACCOUNT_LEVEL.COMPANY_ADMIN) {
        let access = await AccountOrganizationAccess.findOne({
          where: {
            organizationId: organizationId,
            accountId: req.account.accountId,
          },
        });

        if (!access) {
          throw new Error(ERROR_ORGANIZATION_NOT_GRANTED);
        }
      }
      await checkInvoiceMailboxUsed(invoiceMailbox, req.account.companyId);

      let organizationDepartment = await OrganizationDepartment.create(
        {
          organizationId,
          departmentName,
          invoiceMailbox,
          representativeEmail,
          representativeJobTitle,
          representativeName,
          representativePhone,
        },
        { transaction: t },
      );

      await t.commit();

      /* auto grant access */
      if (req.account.accountLevel != ACCOUNT_LEVEL.COMPANY_ADMIN) {
        await AccountOrganizationAccess.create({
          accountId: req.account.accountId,
          organizationId: organization.organizationId,
        });
      }

      res.send({ result: 'success', organizationDepartment });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { organizationDepartmentId } = req.params;

      let organizationDepartment = await OrganizationDepartment.findOne({
        where: { organizationDepartmentId },
        include: [
          {
            association: 'OrganizationBranch',
            where: { companyId: req.account.companyId },
          },
        ],
        transaction: t,
      });

      if (!organizationDepartment) throw new Error(ERROR_NOT_FOUND);

      let {
        departmentName,
        representativeEmail,
        representativeJobTitle,
        representativeName,
        representativePhone,
      } = req.body;

      if (representativeEmail && !isValidEmail(representativeEmail))
        throw new Error(ERROR_INVALID_EMAIL);

      if (representativePhone && !isValidPhone(representativePhone))
        throw new Error(ERROR_INVALID_PHONE);

      await organizationDepartment.update(
        {
          departmentName,
          representativeEmail,
          representativeJobTitle,
          representativeName,
          representativePhone,
        },
        { transaction: t },
      );

      let { acceptPartnerCompanyDetails } = req.body;

      if (_.isArray(acceptPartnerCompanyDetails)) {
        if (
          acceptPartnerCompanyDetails.some(item => {
            let { acceptName, acceptAddress, from, to } = item;

            if (!acceptName && !acceptAddress) return true;

            if (!isValidDate(from) || !isValidDate(to)) return true;

            return false;
          })
        ) {
          throw new Error(ERROR_MISSING_PARAMETERS);
        }

        await AcceptPartnerCompanyDetail.destroy({
          where: {
            taxCode: organizationDepartment.OrganizationBranch.taxCode,
            organizationDepartmentId: req.account.organizationDepartmentId,
            organizationId: req.account.organizationId,
          },
          transaction: t,
        });

        let _acceptPartnerCompanyDetails =
          await AcceptPartnerCompanyDetail.bulkCreate(
            acceptPartnerCompanyDetails.map(item => {
              return {
                taxCode: organizationDepartment.OrganizationBranch.taxCode,
                acceptBy: req.account.accountId,
                acceptAddress: item.acceptAddress,
                acceptName: item.acceptName,
                from: item.from,
                to: item.to,
                organizationDepartmentId: req.account.organizationDepartmentId,
                organizationId: req.account.organizationId,
              };
            }),
            {
              transaction: t,
            },
          );

        _acceptPartnerCompanyDetails = _acceptPartnerCompanyDetails.map(
          item => {
            item.Account = _.pick(req.account, getPublicAccountAttributes());

            return item;
          },
        );

        organizationDepartment.setDataValue(
          'acceptPartnerCompanyDetails',
          _acceptPartnerCompanyDetails,
        );
      }

      await t.commit();

      res.send({ result: 'success', organizationDepartment });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { organizationDepartmentIds } = req.body;

      if (!_.isArray(organizationDepartmentIds))
        throw new Error(ERROR_INVALID_PARAMETER);

      /* TODO: check invoice before deletion */
      if (
        await Invoice.findOne({
          where: {
            organizationDepartmentId: { [Op.in]: organizationDepartmentIds },
            invoiceCategory: { [Op.not]: null },
          },
        })
      ) {
        throw new Error(ERROR_MOVE_INVOICE_BEFORE_DELETE);
      }

      let organizationDepartments = await OrganizationDepartment.findAll({
        where: {
          organizationDepartmentId: { [Op.in]: organizationDepartmentIds },
        },
        include: [
          {
            association: 'OrganizationBranch',

            where: { companyId: req.account.companyId },
          },
        ],
      });

      /* TODO: check invoice before deletion */
      organizationDepartmentIds = organizationDepartments.map(
        item => item.organizationDepartmentId,
      );

      let deleteCount = await OrganizationDepartment.destroy({
        where: {
          organizationDepartmentId: { [Op.in]: organizationDepartmentIds },
        },
        transaction: t,
      });

      await t.commit();

      res.send({ result: 'success', deleteCount });
    } catch (error) {
      await t.rollback();

      next(error);
    }
  },
};
