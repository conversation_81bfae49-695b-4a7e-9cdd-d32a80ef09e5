const { Op, or } = require('sequelize');
const _ = require('lodash');
const utils = require('../utils');
const moment = require('moment');
const {
  Order,
  OrderItem,
  Sequelize,
  Package,
  Company,
  Account,
} = require('../models');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_NOT_FOUND,
  ERROR_ORDER_NOT_PAYMENT,
  ERROR_ORDER_ITEM_NOT_FOUND,
  ERROR_INVALID_PARAMETER,
  ERROR_ORDER_CLIENT_ORDER_CODE_EXISTS,
  ERROR_ORDER_NOT_PENDING,
  ERROR_CANNOT_PURCHASE_OTHER_SERVICE,
  ERROR_CANNOT_PURCHASE_MULTIPLE_SERVICE,
} = require('../configs/error.vi');
const {
  STATUS_ORDER,
  TYPE_PAYMENT,
} = require('../configs/constants/order.constant');
//const {} = require('../configs/constants/package.constant');
const qlbhHelper = require('../utils/qlbh.helper');
const { PACKAGE_TYPE } = require('../configs/constants/package.constant');
const { completeOrder } = require('../utils/order.helper');
const orderHelper = require('../utils/order.helper');
const { VNPAY_HASH_SECRET } = require('../configs/env');
const { emitSocketEvent } = require('../utils/websocket.helper');
const { EMAIL_TYPE } = require('../configs/constants/other.constant');
const { parseFromHTMLTemplate } = require('../utils/email.helper');
const { sendEmail } = require('../utils/nodemailer.helper');
const {
  TEMPLATE_INFO_ACCOUNT,
  TEMPLATE_ORDER_PAYMENT,
  TEMPLATE_ORDER_PAYMENT_SUCCESS,
  TEMPLATE_ORDER_PAYMENT_FAILED,
} = require('../configs/constants/local-path.constant');
const {
  SUBJECT_INFO_ACCOUNT,
  SUBJECT_ORDER_PAYMENT,
} = require('../configs/message.vi');
const { ACCOUNT_LEVEL } = require('../configs/constants/account.constant');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let { q, limit, page, statusOrder, typePayment } = req.query;
      q = q ?? '';

      /**
       * @type {import('sequelize').WhereOptions<import('../types').Order>}
       */

      let conditions = [
        {
          [Op.or]: [
            { invoiceCustomerName: { [Op.like]: `%${q}%` } },
            { invoiceCustomerEmail: { [Op.like]: `%${q}%` } },
            { invoiceCustomerPhone: { [Op.like]: `%${q}%` } },
            { orderPhone: { [Op.like]: `%${q}%` } },
          ],
        },
      ];
      if (Object.values(STATUS_ORDER).includes(statusOrder)) {
        conditions.push({ statusOrder });
      }
      if (Object.values(TYPE_PAYMENT).includes(typePayment)) {
        conditions.push({ typePayment });
      }
      let orders;

      if (!utils.isValidNumber(limit) || !utils.isValidNumber(page)) {
        page = undefined;
        limit = undefined;
        orders = await Order.findAndCountAll({
          where: conditions,
          include: [{ model: OrderItem }],
          order: [['createdAt', 'DESC']],
          distinct: true,
        });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        orders = await Order.findAndCountAll({
          where: conditions,
          include: [{ model: OrderItem }],
          limit,
          offset: limit * page,
          order: [['createdAt', 'DESC']],
          distinct: true,
        });
      }

      res.send({
        result: 'success',
        page,
        total: orders.count,
        count: orders.rows.length,
        orders: orders.rows,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      let { orderId } = req.params;
      if (!orderId) {
        throw new Error(ERROR_MISSING_PARAMETERS);
      }
      let order = (
        await Order.findOne({
          where: {
            orderId,
          },
          include: [{ model: OrderItem }],
        })
      )?.toJSON();
      if (!order) {
        throw new Error(ERROR_NOT_FOUND);
      }
      res.send({
        result: 'success',
        order,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    const t = await Sequelize.transaction();

    try {
      let {
        taxCode,
        companyName,
        address,
        orderPhone,
        orderEmail,
        orderName,
        orderPosition,
        invoiceCustomerName,
        invoiceCustomerPhone,
        invoiceCustomerEmail,
        invoiceCustomerAddress,
        voucherCode,
        typePayment,
        orderItems,
        clientOrderCode,
      } = req.body;
      let { accountId, companyId } = req.account;
      if (
        !taxCode ||
        !companyName ||
        !address ||
        !orderName ||
        !orderEmail ||
        !orderPhone ||
        !orderPosition ||
        !invoiceCustomerName ||
        !invoiceCustomerPhone ||
        !invoiceCustomerEmail ||
        !invoiceCustomerAddress
      )
        throw new Error(ERROR_MISSING_PARAMETERS);
      if (typePayment === undefined) throw new Error(ERROR_ORDER_NOT_PAYMENT);
      if (!orderItems || orderItems.length === 0)
        throw new Error(ERROR_ORDER_ITEM_NOT_FOUND);

      if (clientOrderCode) {
        let isClientOrderCode = await Order.findOne({
          where: { clientOrderCode },
        });
        if (isClientOrderCode)
          throw new Error(ERROR_ORDER_CLIENT_ORDER_CODE_EXISTS);
      }

      let company = await Company.findOne({
        where: { companyId },
        transaction: t,
      });

      let order = await Order.create({
        accountId,
        companyId,
        address,
        taxCode,
        companyName,
        orderName,
        orderPhone,
        orderEmail,
        orderPosition,
        invoiceCustomerName,
        invoiceCustomerPhone,
        invoiceCustomerEmail,
        invoiceCustomerAddress,
        voucherCode,
        typePayment,
        statusOrder: STATUS_ORDER.PENDING,
        orderCode: orderHelper.generateOrderCode(),
        clientOrderCode,
      });
      let totalCost = 0,
        service;
      for (let index = 0; index < orderItems.length; index++) {
        const element = orderItems[index];

        if (!element.package?.packageId) {
          throw new Error(ERROR_MISSING_PARAMETERS);
        }

        if (!utils.isValidNumber(element.quantity)) {
          element.quantity = 1;
        }

        let package = await Package.findOne({
          where: { packageId: element.package.packageId },
        });

        if (!package) throw new Error(ERROR_NOT_FOUND);

        if (!service) {
          service = package.service;
        } else {
          if (service != package.service) {
            throw new Error(ERROR_CANNOT_PURCHASE_MULTIPLE_SERVICE);
          }
        }

        if (company.service) {
          if (company.service != package.service) {
            throw new Error(ERROR_CANNOT_PURCHASE_OTHER_SERVICE);
          }
        }

        element.package = package.toJSON();

        await OrderItem.create(
          {
            orderId: order.orderId,
            quantity: Number(element.package.quantity),
            price: Number(element.package.packageCost),
            cost:
              Number(element.package.packageCost) * Number(element.quantity),
            package: element.package,
          },
          { transaction: t },
        );
        totalCost +=
          Number(element.package.packageCost) * Number(element.quantity);
      }
      let promotion = 0;
      if (voucherCode) {
        let { result, reason, voucher } =
          await qlbhHelper.clientCodeGetVoucherByCode(voucherCode);
        if (result != 'success') throw new Error(reason);
        if (voucher.discountOnPercent)
          promotion += (totalCost * voucher.discountOnPercent) / 100;
        if (voucher.discountOnTotal) promotion += voucher.discountOnTotal;

        if (voucher.maximumDiscount) {
          promotion = Math.min(voucher.maximumDiscount, promotion);
        }

        order.set('voucher', voucher);

        let { result: _result, reason: _reason } =
          await qlbhHelper.clientCodeUseVoucher(voucherCode);

        if (_result != 'success') {
          throw new Error(_reason);
        }
      }
      // tính lại tổng tiền gốc, tiền cần thanh toán
      await order.update(
        {
          totalCost,
          promotion,
          totalPayment: Math.max(0, totalCost - promotion),
        },
        { transaction: t },
      );

      /* VNPAY url */
      let vnpayUrl = undefined;
      if (typePayment == TYPE_PAYMENT.VNPAY) {
        vnpayUrl = orderHelper.createVNPAYPaymentUrl(
          order.totalPayment,
          order.clientOrderCode ?? order.orderCode,
          req.ip,
        );

        orderHelper.logVNPAY('SERVER', {
          request: JSON.stringify({
            ...order.toJSON(),
          }),
        });
      }

      if (!company.service) {
        await company.update(
          { service: orderItems[0].package.service },
          { transaction: t },
        );
      }

      await t.commit();

      // order = await Order.findOne({
      //   where: { orderId: order.orderId },
      //   include: [{ model: OrderItem }],
      // });
      // await order.reload({ include: [{ model: OrderItem }] });
      order.setDataValue('OrderItems', orderItems);

      // send email order in creating
      await sendEmail({
        emailType: EMAIL_TYPE.OTHER,
        html: await parseFromHTMLTemplate(TEMPLATE_ORDER_PAYMENT, {
          account: req.account,
          recipient: {
            email: req.account.email,
            name: req.account.fullname,
            taxCode: req.account.taxCode,
          },
          orderItems: orderItems.map(i => {
            i.price = Intl.NumberFormat('de-DE').format(i.price ?? 0);
            i.package.packageType =
              i.package.packageType == PACKAGE_TYPE.DURATION
                ? 'Thời hạn'
                : i.package.packageType == PACKAGE_TYPE.LICENSE
                ? 'Giấy phép'
                : 'Số lượng';
            return i;
          }),
          order,
          // totalPayment: Intl.NumberFormat('de-DE').format(totalPayment ?? 0),
        }),
        recipients: [
          {
            email: req.account.email,
            name: req.account.fullname,
          },
        ],

        subject: SUBJECT_ORDER_PAYMENT,

        accountId: order.accountId,
      });

      res.send({
        result: 'success',
        order,
        vnpayUrl,
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * Không đổi thông tin đơn hàng được ngoài trạng thái
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    const t = await Sequelize.transaction();

    try {
      let {
        taxCode,
        companyName,
        address,
        orderPhone,
        orderEmail,
        orderName,
        orderPosition,
        invoiceCustomerName,
        invoiceCustomerPhone,
        invoiceCustomerEmail,
        invoiceCustomerAddress,
        voucherCode,
        typePayment,
        statusOrder,
        orderItems,
      } = req.body;
      // let { accountId, companyId } = req.account;
      let { orderId } = req.params;

      // check orderItems
      // if (orderItems && orderItems.length === 0)
      //   throw new Error(ERROR_ORDER_ITEM_NOT_FOUND);

      // check orderId&&accountId
      let order = await Order.findOne({
        where: [
          { orderId },
          req.account ? { companyId: req.account.companyId } : {},
        ],
        include: [{ model: OrderItem }, { model: Company }],
      });
      if (!order) throw new Error(ERROR_NOT_FOUND);

      let update = {};
      // if (taxCode) update.taxCode = taxCode;
      // if (companyName) update.companyName = companyName;
      // if (address) update.address = address;
      // if (orderEmail) update.orderEmail = orderEmail;
      // if (orderName) update.orderName = orderName;
      // if (orderPhone) update.orderPhone = orderPhone;
      // if (orderPosition) update.orderPosition = orderPosition;
      // if (invoiceCustomerName) update.invoiceCustomerName = invoiceCustomerName;
      // if (invoiceCustomerPhone)
      //   update.invoiceCustomerPhone = invoiceCustomerPhone;
      // if (invoiceCustomerEmail)
      //   update.invoiceCustomerEmail = invoiceCustomerEmail;
      // if (invoiceCustomerAddress)
      //   update.invoiceCustomerAddress = invoiceCustomerAddress;
      // if (voucherCode) update.voucherCode = voucherCode;
      // if (Object.values(TYPE_PAYMENT).includes(typePayment))
      //   update.typePayment = typePayment;
      if (Object.values(STATUS_ORDER).includes(statusOrder)) {
        // chi chuyen status neu dang pending
        if (order.statusOrder == STATUS_ORDER.PENDING) {
          update.statusOrder = statusOrder;
        }
      }

      // let totalCost = 0;
      // if (orderItems === undefined) {
      //   totalCost = order.totalCost;
      // } else {
      //   await OrderItem.destroy({ where: { orderId } }, { transaction: t });
      //   for (let index = 0; index < orderItems.length; index++) {
      //     const element = orderItems[index];
      //     const package = await Package.findOne(
      //       {
      //         where: {
      //           packageId: element.packageId,
      //         },
      //       },
      //       { transaction: t },
      //     );
      //     await OrderItem.create(
      //       {
      //         orderId: order.orderId,
      //         packageId: element.packageId,
      //         packageCode: package.packageCode,
      //         quantity: Number(element.quantity),
      //         price: Number(element.price),
      //         cost: Number(element.quantity) * Number(element.price),
      //       },
      //       { transaction: t },
      //     );
      //     totalCost += Number(element.quantity) * Number(element.price);
      //   }
      // }

      // let promotion = 0;
      // if (voucherCode || order.voucherCode) {
      //   let { result, reason, voucher } =
      //     await qlbhHelper.clientCodeGetVoucherByCode(
      //       voucherCode ?? order.voucherCode,
      //     );
      //   if (result != 'success') throw new Error(reason);
      //   if (voucher.discountOnPercent)
      //     promotion += (totalCost * voucher.discountOnPercent) / 100;
      //   if (voucher.discountOnTotal) promotion += voucher.discountOnTotal;
      // }
      // tính tổng tiền gốc và tiền phải thanh toán
      // update.totalCost = totalCost;
      // update.promotion = promotion;
      // update.totalPayment = totalCost - promotion;

      if (
        statusOrder == STATUS_ORDER.COMPLETE &&
        order.statusOrder == STATUS_ORDER.PENDING
      ) {
        await completeOrder(order, order.companyId, t);
      }

      await order.update(update, { transaction: t });
      await t.commit();

      // order = await Order.findOne({
      //   where: { orderId: order.orderId },
      //   include: [{ model: OrderItem }],
      // });

      // nếu chuyển trạng thái sáng hoàn thành thì tăng số lượng đơn, thời gian cho công ty

      res.send({
        result: 'success',
        order,
      });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    try {
      const { orderIds } = req.body;

      if (!_.isArray(orderIds)) throw new Error(ERROR_INVALID_PARAMETER);
      let orderCancels = await Order.findAll({
        where: {
          orderId: { [Op.in]: orderIds },
          statusOrder: STATUS_ORDER.CANCEL,
        },
      });
      let orderIdCancels = _.map(orderCancels, 'orderId');
      await OrderItem.destroy({
        where: { orderId: { [Op.in]: orderIdCancels } },
      });
      const deleteCount = await Order.destroy({
        where: { orderId: { [Op.in]: orderIdCancels } },
      });

      res.send({
        result: 'success',
        deleteCount,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async history(req, res, next) {
    try {
      let { q, limit, page } = req.query;
      q = q ?? '';
      let { companyId } = req.account;
      /**
       * @type {import('sequelize').WhereOptions<import('../types').Order>}
       */

      let conditions = [
        {
          companyId,
        },
      ];

      let orders;

      // console.log(limit, page);
      if (!utils.isValidNumber(limit) || !utils.isValidNumber(page)) {
        page = undefined;
        limit = undefined;
        orders = await Order.findAndCountAll({
          where: conditions,
          include: [{ model: OrderItem }],
          order: [['createdAt', 'DESC']],
          distinct: true,
        });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        orders = await Order.findAndCountAll({
          where: conditions,
          include: [{ model: OrderItem }],
          limit,
          offset: limit * page,
          order: [['createdAt', 'DESC']],
          distinct: true,
        });
      }
      res.send({
        result: 'success',
        page,
        total: orders.count,
        count: orders.rows.length,
        orders: orders.rows,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async vnPayReturn(req, res, next) {
    try {
      res.send('OK');
    } catch (error) {}
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async vnPayIpn(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      /**
       * @type {import('../types').VNPAY_ReturnParams}
       */
      var vnp_Params = req.query;
      var secureHash = vnp_Params['vnp_SecureHash'];

      delete vnp_Params['vnp_SecureHash'];
      delete vnp_Params['vnp_SecureHashType'];

      vnp_Params = sortObject(vnp_Params);
      var secretKey = VNPAY_HASH_SECRET;
      var querystring = require('qs');
      var signData = querystring.stringify(vnp_Params, { encode: false });
      var crypto = require('crypto');
      var hmac = crypto.createHmac('sha512', secretKey);
      var signed = hmac.update(Buffer.from(signData, 'utf-8')).digest('hex');

      // response for vnpay
      let RspCode, Message;

      if (secureHash === signed) {
        var vnp_TxnRef = vnp_Params['vnp_TxnRef'];
        var rspCode = vnp_Params['vnp_ResponseCode'];

        vnp_TxnRef = decodeURIComponent(vnp_TxnRef).replace(/\+/g, ' ');
        let orderCode = vnp_TxnRef.split(' ')[0];

        let order = await Order.findOne({
          where: {
            [Op.or]: [{ clientOrderCode: orderCode }, { orderCode: orderCode }],
            typePayment: TYPE_PAYMENT.VNPAY,
            // paymentStatus: PAYMENT_STATUS.PENDING,
          },
          include: [{ model: OrderItem }],
          transaction: t,
        });

        if (!order) {
          //Kiem tra du lieu co hop le khong, cap nhat trang thai don hang va gui ket qua cho VNPAY theo dinh dang duoi
          RspCode = '01';
          Message = 'Order not found';
        } else {
          let totalPrice = _.sum([order.totalPayment]);

          if (order.getDataValue('statusOrder') == STATUS_ORDER.PENDING) {
            if (vnp_Params.vnp_Amount / 100 != totalPrice) {
              //Kiem tra du lieu co hop le khong, cap nhat trang thai don hang va gui ket qua cho VNPAY theo dinh dang duoi
              RspCode = '04';
              Message = 'Invalid amount';
            } else {
              if (
                vnp_Params.vnp_ResponseCode == '00' ||
                vnp_Params.vnp_ResponseCode == '07'
              ) {
                await orderHelper.completeOrder(
                  order.toJSON(),
                  order.companyId,
                  t,
                );

                await order.update(
                  {
                    statusOrder: STATUS_ORDER.COMPLETE,
                    transactionNo: vnp_Params.vnp_TransactionNo,
                    bankTranNo: vnp_Params.vnp_BankTranNo,
                  },
                  { transaction: t },
                );

                /* send result email */
              } else {
                await order.update(
                  {
                    statusOrder: STATUS_ORDER.FAILED,
                    transactionNo: vnp_Params.vnp_TransactionNo,
                    bankTranNo: vnp_Params.vnp_BankTranNo,
                  },
                  { transaction: t },
                );

                /* send result email */
              }

              /* Send to ws */

              //Kiem tra du lieu co hop le khong, cap nhat trang thai don hang va gui ket qua cho VNPAY theo dinh dang duoi
              RspCode = '00';
              Message = 'success';
            }
          } else {
            if (vnp_Params.vnp_Amount / 100 != totalPrice) {
              //Kiem tra du lieu co hop le khong, cap nhat trang thai don hang va gui ket qua cho VNPAY theo dinh dang duoi
              RspCode = '04';
              Message = 'Invalid amount';
            } else {
              //Kiem tra du lieu co hop le khong, cap nhat trang thai don hang va gui ket qua cho VNPAY theo dinh dang duoi
              RspCode = '02';
              Message = 'Order already confirmed';
            }
          }
        }
      } else {
        RspCode = '97';
        Message = 'Fail checksum';
      }

      await t.commit();
      res.status(200).json({ RspCode: RspCode, Message: Message });

      orderHelper.logVNPAY('VNPAY', {
        response: { ...vnp_Params, RspCode, Message },
        ipAddr: req.ip,
      });
    } catch (error) {
      await t.rollback();
      orderHelper.logVNPAY('VNPAY', {
        response: { RspCode: '99', Message: 'Unknown error' },
        ipAddr: req.ip,
      });
      console.warn(error);

      res.status(200).json({ RspCode: '99', Message: 'Unknown error' });
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async repayment(req, res, next) {
    try {
      let { orderId } = req.params;

      let order = await Order.findOne({
        where: { orderId, companyId: req.account.companyId },
      });

      if (!order) {
        throw new Error(ERROR_NOT_FOUND);
      }

      if (order.statusOrder != STATUS_ORDER.PENDING) {
        throw new Error(ERROR_ORDER_NOT_PENDING);
      }

      let vnpayUrl;
      if (order.typePayment == TYPE_PAYMENT.VNPAY) {
        vnpayUrl = orderHelper.createVNPAYPaymentUrl(
          order.totalPayment,
          order.clientOrderCode ?? order.orderCode,
          req.ip,
        );

        orderHelper.logVNPAY('SERVER', {
          request: JSON.stringify({
            ...order.toJSON(),
          }),
        });
      }

      res.send({ result: 'success', order, vnpayUrl });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async emailPayment(req, res, next) {
    try {
      const { accountId, companyId } = req.account;
      const { orderId } = req.params;

      const order = await Order.findOne({
        where: {
          orderId,
          companyId,
        },
      });
      if (!order) throw new Error(ERROR_NOT_FOUND);

      const orderItems = await OrderItem.findAll({
        where: {
          orderId,
        },
      });

      res.send({
        result: 'success',
      });

      // let totalPayment = 0;
      // orderItems.forEach(
      //   orderItem =>
      //     (totalPayment +=
      //       Number(orderItem.price) * Number(orderItem.quantity ?? 1)),
      // );

      await sendEmail({
        emailType: EMAIL_TYPE.OTHER,
        html: await parseFromHTMLTemplate(TEMPLATE_ORDER_PAYMENT, {
          account: req.account,
          recipient: {
            email: req.account.email,
            name: req.account.fullname,
            taxCode: req.account.taxCode,
          },
          orderItems: orderItems.map(i => {
            i.price = Intl.NumberFormat('de-DE').format(i.price ?? 0);
            i.package.packageType =
              i.package.packageType == PACKAGE_TYPE.DURATION
                ? 'Thời hạn'
                : i.package.packageType == PACKAGE_TYPE.LICENSE
                ? 'Giấy phép'
                : 'Số lượng';
            return i.toJSON();
          }),
          order,
          // totalPayment: Intl.NumberFormat('de-DE').format(totalPayment ?? 0),
        }),
        recipients: [
          {
            email: req.account.email,
            name: req.account.fullname,
          },
          // {
          //   email: order.orderEmail,
          // },
        ],

        subject: SUBJECT_ORDER_PAYMENT,

        accountId: order.accountId,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async historyMetaAccountCode(req, res, next) {
    try {
      let { q, limit, page, statusOrder, typePayment } = req.query;
      q = q ?? '';
      let { metaAccountCode } = req.params;

      let accounts = await Account.findAll({
        where: {
          accountLevel: ACCOUNT_LEVEL.COMPANY_ADMIN,
          [Op.and]: [metaAccountCode ? { metaAccountCode } : {}],
        },
      });

      if (!accounts.length) throw new Error(ERROR_NOT_FOUND);

      /**
       * @type {import('sequelize').WhereOptions<import('../types').Order>}
       */

      let conditions = [
        {
          companyId: { [Op.in]: accounts.map(item => item.companyId) },
          [Op.and]: [
            statusOrder ? { statusOrder } : {},
            typePayment ? { typePayment } : {},
          ],
          [Op.or]: [
            { orderCode: { [Op.like]: `%${q}%` } },
            { clientOrderCode: { [Op.like]: `%${q}%` } },
          ],
        },
      ];

      let orders;

      // console.log(limit, page);
      if (!utils.isValidNumber(limit) || !utils.isValidNumber(page)) {
        page = undefined;
        limit = undefined;
        orders = await Order.findAndCountAll({
          where: conditions,
          include: [{ model: OrderItem }, { model: Company }],
          order: [['createdAt', 'DESC']],
          distinct: true,
        });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        orders = await Order.findAndCountAll({
          where: conditions,
          include: [{ model: OrderItem }, { model: Company }],
          limit,
          offset: limit * page,
          order: [['createdAt', 'DESC']],
          distinct: true,
        });
      }
      res.send({
        result: 'success',
        page,
        total: orders.count,
        count: orders.rows.length,
        orders: orders.rows,
      });
    } catch (error) {
      next(error);
    }
  },
};

/**
 * sort for vnpay
 * @param {any} obj
 * @returns
 */
function sortObject(obj) {
  var sorted = {};
  var str = [];
  var key;
  for (key in obj) {
    // eslint-disable-next-line no-prototype-builtins
    if (obj.hasOwnProperty(key)) {
      str.push(encodeURIComponent(key));
    }
  }
  str.sort();
  for (key = 0; key < str.length; key++) {
    sorted[str[key]] = encodeURIComponent(obj[str[key]]).replace(/%20/g, '+');
  }
  return sorted;
}
