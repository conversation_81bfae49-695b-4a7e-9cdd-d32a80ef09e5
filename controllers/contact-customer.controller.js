const _ = require('lodash');
const utils = require('../utils');
const { Sequelize } = require('../models');
const { Op } = require('sequelize');
const {
  CONTACT_STATUS,
} = require('../configs/constants/contact-status.constant');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_NOT_FOUND,
  ERROR_INVALID_EMAIL,
  ERROR_INVALID_PHONE,
  ERROR_INVALID_PARAMETER,
} = require('../configs/error.vi');
const {
  findContactCustomer,
  detailContactCustomer,
  createContactCustomer,
  updateContactCustomer,
  summaryContactCustomer,
} = require('../utils/contact-customer.helper');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      const { result, reason, total, count, contacts, page } =
        await findContactCustomer(req.query);

      if (result !== 'success') throw new Error(reason);
      res.send({ result: 'success', total, count, contacts, page });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      let { contactId } = req.params;
      if (!contactId) {
        throw new Error(ERROR_MISSING_PARAMETERS);
      }
      const { result, reason, contact } = await detailContactCustomer(
        contactId,
      );

      if (result !== 'success') throw new Error(reason);
      res.send({ result: 'success', contact });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    const t = await Sequelize.transaction();

    try {
      const { result, reason, contact } = await createContactCustomer(req.body);

      if (result !== 'success') throw new Error(reason);

      await t.commit();
      res.send({ result: 'success', contact });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    const t = await Sequelize.transaction();

    try {
      let { contactId } = req.params;

      const { result, reason, contact } = await updateContactCustomer(
        contactId,
        req.body,
      );

      if (result !== 'success') throw new Error(reason);

      await t.commit();

      res.send({ result: 'success', contact });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async summary(req, res, next) {
    try {
      const { result, reason, data } = await summaryContactCustomer();

      if (result !== 'success') throw new Error(reason);
      res.send({ result: 'success', data });
    } catch (error) {
      next(error);
    }
  },
};
