const { Op } = require('sequelize');
const { InstructionCategory, Instruction } = require('../models');
const { isValidNumber } = require('../utils');
const _ = require('lodash');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_NOT_FOUND,
  ERROR_INSTRUCTION_RESTRICTED,
} = require('../configs/error.vi');
module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    try {
      let { title, description } = req.body;

      if (!title) throw new Error(ERROR_MISSING_PARAMETERS);

      let instructionCategory = await InstructionCategory.create({
        title,
        description,
      });

      res.send({ result: 'success', instructionCategory });
    } catch (error) {
      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      const { instructionCategoryId } = req.params;
      if (!instructionCategoryId) throw new Error(ERROR_MISSING_PARAMETERS);
      const instructionCategory = await InstructionCategory.findOne({
        where: { instructionCategoryId },
      });
      if (!instructionCategory) throw new Error(ERROR_NOT_FOUND);
      return res.send({
        result: 'success',
        instructionCategory,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let { q, limit, page, orderField, orderMode, instructionCategoryId } =
        req.query;

      q = q ?? '';
      orderField = orderField ?? 'instructionCategoryId';
      orderMode = orderMode ?? 'ASC';

      /**
       * @type {sequelize.WhereOptions<import('../types').InstructionCategory>}
       */
      let conditions = [
        {
          [Op.or]: [
            { title: { [Op.like]: `%${q}%` } },
            { description: { [Op.like]: `%${q}%` } },
          ],
        },
        instructionCategoryId ? { instructionCategoryId } : {},
      ];
      if (!isValidNumber(limit) || !isValidNumber(page)) {
        let rs = (
          await InstructionCategory.findAll({
            where: conditions,
            order: [[orderField, orderMode]],
          })
        ).map(r => r.toJSON());

        res.send({ result: 'success', instructionCategories: rs });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        let rs = await InstructionCategory.findAndCountAll({
          where: conditions,
          limit,
          offset: limit * page,
          order: [[orderField, orderMode]],
        });

        res.send({
          result: 'success',
          page,
          total: rs.count,
          count: rs.rows.length,
          instructionCategories: rs.rows,
        });
      }
    } catch (error) {
      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    try {
      let { instructionCategoryId } = req.params;

      let instructionCategory = await InstructionCategory.findOne({
        where: { instructionCategoryId },
      });

      if (!instructionCategory) throw new Error(ERROR_NOT_FOUND);

      let { title, description } = req.body;

      await instructionCategory.update({ title, description });

      res.send({ result: 'success', instructionCategory });
    } catch (error) {
      next(error);
    }
  },

  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    try {
      let { instructionCategoryIds } = req.body;
      let instruction = await Instruction.findOne({
        where: { instructionCategoryId: { [Op.in]: instructionCategoryIds } },
      });

      if (instruction) throw new Error(ERROR_INSTRUCTION_RESTRICTED);

      let deleteInstructionCategory = await InstructionCategory.destroy({
        where: { instructionCategoryId: { [Op.in]: instructionCategoryIds } },
      });

      res.send({ result: 'success', deleteInstructionCategory });
    } catch (error) {
      next(error);
    }
  },
};
