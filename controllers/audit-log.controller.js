const _ = require('lodash');
const { isValidNumber } = require('../utils');
const { AuditLog, Account } = require('../models');
const { Op } = require('sequelize');
const utils = require('../utils');
const moment = require('moment');
const { getPublicAccountAttributes } = require('../utils/account.helper');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let { q, limit, page, orderField, orderMode, from, to } = req.query;

      q = q ?? '';
      orderField = orderField ?? 'createdAt';
      orderMode = orderMode ?? 'DESC';

      if (!utils.isValidDate(from)) from = undefined;
      if (!utils.isValidDate(to)) to = undefined;

      let conditions = {
        [Op.or]: [
          {
            action: { [Op.like]: `%${q}%` },
          },
          { description: { [Op.like]: `%${q}%` } },
          { reference: { [Op.like]: `%${q}%` } },
          { organization: { [Op.like]: `%${q}%` } },
          { '$Account.fullname$': { [Op.like]: `%${q}%` } },
        ],
        [Op.and]: [
          from
            ? {
                createdAt: {
                  [Op.gte]: moment(from).startOf('date').toString(),
                },
              }
            : {},
          to
            ? { createdAt: { [Op.lte]: moment(to).endOf('date').toString() } }
            : {},
        ],
      };

      if (!isValidNumber(limit) || !isValidNumber(page)) {
        let als = (
          await AuditLog.findAll({
            where: conditions,
            order: [[orderField, orderMode]],
            include: [
              {
                model: Account,
                //   foreignKey: 'accountId',
                attributes: getPublicAccountAttributes(),
                where: {
                  companyId: req.account.companyId,
                },
              },
            ],
          })
        ).map(al => al.toJSON());

        res.send({ result: 'success', auditLogs: als });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        let al = await AuditLog.findAndCountAll({
          where: conditions,
          limit,
          offset: limit * page,
          order: [[orderField, orderMode]],
          include: [
            {
              model: Account,
              //foreignKey: 'accountId',
              attributes: getPublicAccountAttributes(),
              where: {
                companyId: req.account.companyId,
              },
            },
          ],
        });

        res.send({
          result: 'success',
          page,
          total: al.count,
          count: al.rows.length,
          auditLogs: al.rows,
        });
      }
    } catch (error) {
      next(error);
    }
  },
};
