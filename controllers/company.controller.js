const { Op } = require('sequelize');
const { isValidPhone, isValidDate, nonAccentVietnamese } = require('../utils');
const {
  Company,
  Sequelize,
  SupplierNotDownloadInvoice,
  Package,
  Account,
  ResourceHistory,
} = require('../models');
const {
  ERROR_INVALID_PHONE,
  ERROR_INVALID_EMAIL,
  ERROR_INVALID_DATE,
  ERROR_MISSING_PARAMETERS,
  ERROR_INVALID_TAXCODE,
  ERROR_COMPANY_NOT_EXISTED,
  ERROR_NOT_FOUND,
  ERROR_INVALID_PARAMETER,
  ERROR_INVALID_GENDER,
  ERROR_USER_NOT_FOUND,
} = require('../configs/error.vi');
const { PUBLIC_UPLOAD_DIR } = require('../configs/env');
const _ = require('lodash');
const moment = require('moment');
const path = require('path');
const fs = require('fs');
const utils = require('../utils');
const { isValidGender } = require('../utils/account.helper');
const { SERVICE } = require('../configs/constants/account.constant');
const { PACKAGE_TYPE } = require('../configs/constants/package.constant');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      let company = await Company.findOne({
        where: { companyId: req.account.companyId },
      });

      res.send({ result: 'success', company });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let {
        companyName,
        companyAddress,
        companyPhone,
        companyLogo,
        companyEmail,
        businessPermitAddress,
        businessPermitDate,
        bankAccountNumber,
        bankName,
        contactPersonName,
        contactPersonEmail,
        contactPersonPhone,
        contactPersonPosition,
        fax,
        website,
        districtCode,
        provinceCode,
        taxAuthority,
      } = req.body;

      // if (companyPhone && !isValidPhone(companyPhone)) {
      //   throw new Error(ERROR_INVALID_PHONE);
      // }

      if (companyEmail && !utils.isValidEmail(companyEmail)) {
        throw new Error(ERROR_INVALID_EMAIL);
      }

      if (businessPermitDate && !isValidDate(businessPermitDate)) {
        throw new Error(ERROR_INVALID_DATE);
      }

      if (contactPersonEmail && !utils.isValidEmail(contactPersonEmail)) {
        throw new Error(ERROR_INVALID_EMAIL);
      }

      // if (contactPersonPhone && !isValidPhone(contactPersonPhone)) {
      //   throw new Error(ERROR_INVALID_PHONE);
      // }

      let company = await Company.findOne({
        where: { companyId: req.account.companyId },
      });

      if (req.files?.['companyLogo']) {
        let file = req.files['companyLogo'];

        if (_.isArray(file)) file = file[0];

        let filepath = `${PUBLIC_UPLOAD_DIR}/company-logo/${company.companyId}`;
        let filename = `${file.name}`;

        filename = utils.escapeFilename(filename);

        if (fs.existsSync(filepath)) fs.rmSync(filepath, { recursive: true });
        fs.mkdirSync(filepath);

        await file.mv(path.join(filepath, filename));

        companyLogo = `resources/company-logo/${company.companyId}/${filename}`;
      }

      await company.update(
        {
          companyName,
          companyAddress,
          companyPhone,
          companyEmail,
          businessPermitAddress,
          businessPermitDate,
          companyLogo,
          bankAccountNumber,
          bankName,
          contactPersonEmail,
          contactPersonName,
          contactPersonPhone,
          contactPersonPosition,
          fax,
          website,
          districtCode,
          provinceCode,
          taxAuthority,
        },
        { transaction: t },
      );

      await t.commit();

      res.send({ result: 'success', company });
    } catch (error) {
      await t.rollback();

      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async findCompanyByTaxCode(req, res, next) {
    try {
      let { taxCode } = req.query;

      if (!taxCode) throw new Error(ERROR_MISSING_PARAMETERS);

      if (!utils.isValidTaxCode(taxCode))
        throw new Error(ERROR_INVALID_TAXCODE);

      let company = await Company.findOne({ where: { taxCode } });

      if (!company) throw new Error(ERROR_COMPANY_NOT_EXISTED);

      res.send({ result: 'success', company });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async findCompanyByTaxCodes(req, res, next) {
    try {
      let { taxCodes } = req.method == 'GET' ? req.query : req.body;

      if (!_.isArray(taxCodes)) throw new Error(ERROR_MISSING_PARAMETERS);

      let companies = await Company.findAll({
        where: { taxCode: { [Op.in]: taxCodes } },
      });

      res.send({ result: 'success', companies });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async updateCompanyByTaxCode(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { taxCode } = req.params;

      let company = await Company.findOne({ where: { taxCode } });

      if (!company) throw new Error(ERROR_COMPANY_NOT_EXISTED);

      let {
        companyLegalRepresentativeName,
        companyLegalRepresentativeTitle,
        companyLegalRepresentativeDoB,
        companyLegalRepresentativeGender,
        companyAddress,
        companyPhone,
        companyName,
        companyLegalRepresentativeEmail,
        companyLegalRepresentativePhone,
        companyServiceType,
      } = req.body;

      // if (companyPhone && !isValidPhone(companyPhone)) {
      //   throw new Error(ERROR_INVALID_PHONE);
      // }

      if (
        companyLegalRepresentativeGender &&
        !isValidGender(companyLegalRepresentativeGender)
      ) {
        throw new Error(ERROR_INVALID_GENDER);
      }

      if (
        companyLegalRepresentativeDoB &&
        !isValidDate(companyLegalRepresentativeDoB)
      ) {
        throw new Error(ERROR_INVALID_DATE);
      }

      if (companyServiceType && !SERVICE[companyServiceType]) {
        throw new Error(ERROR_INVALID_PARAMETER);
      } else if (companyServiceType === null || companyServiceType === '') {
        companyServiceType = null;
      } else if (!companyServiceType) {
        companyServiceType = undefined;
      }

      await company.update(
        {
          companyAddress,
          companyPhone,
          companyName,
          contactPersonDoB: companyLegalRepresentativeDoB,
          contactPersonName: companyLegalRepresentativeName,
          contactPersonPosition: companyLegalRepresentativeTitle,
          contactPersonGender: companyLegalRepresentativeGender,
          contactPersonEmail: companyLegalRepresentativeEmail,
          contactPersonPhone: companyLegalRepresentativePhone,
          service: companyServiceType,
        },
        { transaction: t },
      );

      await t.commit();

      res.send({ result: 'success', company });
    } catch (error) {
      await t.rollback();

      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async topUpToCompany(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      // packages = {packageCode, quantity}[]
      const { packages, taxCode } = req.body;
      if (!taxCode || !packages) throw new Error(ERROR_MISSING_PARAMETERS);
      if (!_.isArray(packages)) throw new Error(ERROR_INVALID_PARAMETER);

      const company = await Company.findOne({ where: { taxCode } });

      if (!company) throw new Error(ERROR_NOT_FOUND);

      let successfulPackages = [];
      let quantity = 0;
      let duration = 0;

      for (let package of packages) {
        if (!package?.packageCode || !package.quantity) continue;
        const _package = await Package.findOne({
          where: { packageCode: package.packageCode },
        });

        if (!_package) continue;
        successfulPackages.push(package);
        if (_package.packageType === PACKAGE_TYPE.DURATION) {
          duration += Number(package.duration);
        }
        if (_package.packageType === PACKAGE_TYPE.LICENSE) {
          duration += Number(package.duration);
        }
        if (_package.packageType === PACKAGE_TYPE.QUANTITY) {
          quantity += Number(package.quantity);
        }
      }

      let quantityRemain = (company.quantityRemain ?? 0) + quantity;
      let quantityPurchased = (company.quantityPurchased ?? 0) + quantity;
      let dateExpiredPackage = moment(
        company.dateExpiredPackage,
      ).isSameOrBefore()
        ? new Date()
        : moment(company.dateExpiredPackage).toDate();

      if (duration === 0) {
        dateExpiredPackage = undefined;
      } else {
        dateExpiredPackage = moment(dateExpiredPackage)
          .add(duration, 'days')
          .toDate();
      }

      await company.update(
        {
          quantityRemain,
          quantityPurchased,
          dateExpiredPackage,
        },
        { transaction: t },
      );

      await t.commit();
      res.send({ result: 'success', successfulPackages, company });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async khachHangKyHopDongNCC(req, res, next) {
    try {
      let { metaAccountCode, linkKyHopDong, daKyHopDongNCC } = req.body;

      let account = await Account.findOne({ where: { metaAccountCode } });

      if (!account) {
        throw new Error(ERROR_USER_NOT_FOUND);
      }

      let company = await Company.findOne({
        where: { companyId: account.companyId },
      });

      await company.update({
        linkKyHopDong,
        daKyHopDongNCC: utils.parseBoolean(daKyHopDongNCC),
      });

      if (company.daKyHopDongNCC) {
        await ResourceHistory.update(
          { active: true },
          { where: { companyId: company.companyId } },
        );
      }

      res.send({ result: 'success' });
    } catch (error) {
      next(error);
    }
  },
};
