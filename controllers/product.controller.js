const {
  SHEET_NAME_FULL_PRODUCT,
  SHEET_NAME_NOT_REDUCE_TAX,
} = require('../configs/constants/local-path.constant');
const { ERROR_MISSING_PARAMETERS } = require('../configs/error.vi');
const XLSX = require('xlsx');
const _ = require('lodash');
const { Product } = require('../models');
const { isValidNumber, parseBoolean } = require('../utils');
const { Op } = require('sequelize');
const utils = require('../utils');
module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async import(req, res, next) {
    try {
      let { file } = req.files;
      if (!req.files['file']) throw new Error(ERROR_MISSING_PARAMETERS);

      let importFile = req.files['file'];
      if (_.isArray(file)) importFile = file[0];

      file = XLSX.readFile(importFile.tempFilePath);

      let json = XLSX.utils.sheet_to_json(
        file.Sheets[SHEET_NAME_FULL_PRODUCT],
        {
          header: 1,
          blankrows: false,
        },
      );

      let json_ = [];
      let c = [null, null, null, null, null, null, null, null];
      for (let index = 1; index < json.length; index++) {
        if (!json[index][7]) continue;
        let productCode = '';

        for (let i = 6; i >= 0; i--) {
          if (json[index][i]) {
            if (json[index][i].startsWith('0')) c[i] = json[index][i].substr(1);
            else {
              c[i] = json[index][i];
            }
          } else {
            c[i] = null;
          }

          if (i != 0 && !json[index][i - 1] && json[index][i]) break;
        }

        for (let i of c.filter(_.isString)) {
          productCode += i;
        }

        json_.push({
          productCode,
          productName: json[index][7],
          content: json[index][8],
          note: json[index][9],
          reduceTax: true,
        });
      }

      let rs = await Product.bulkCreate(json_, {
        updateOnDuplicate: ['content', 'note', 'productName', 'reduceTax'],
      });

      res.send({
        result: 'success',
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async reduceTax(req, res, next) {
    try {
      let { file } = req.files;
      if (!req.files['file']) throw new Error(ERROR_MISSING_PARAMETERS);

      let importFile = req.files['file'];
      if (_.isArray(file)) importFile = file[0];

      file = XLSX.readFile(importFile.tempFilePath);

      let json = XLSX.utils.sheet_to_json(
        file.Sheets[SHEET_NAME_NOT_REDUCE_TAX],
        {
          header: 1,
          blankrows: false,
        },
      );

      let json_ = [];
      let c = [null, null, null, null, null, null, null, null];

      // let hsCode = '';
      // let productName = '';
      // let productContent = '';

      for (let index = json.length - 1; index >= 6; index--) {
        if (
          !json[index][0] &&
          !json[index][1] &&
          !json[index][2] &&
          !json[index][3] &&
          !json[index][4] &&
          !json[index][5] &&
          !json[index][6] &&
          json[index][7]
        ) {
          json[index - 1][7] =
            (json[index - 1][7] ? json[index - 1][7] : '') +
            ' ' +
            (json[index][7] ? json[index][7] : '');
          json[index - 1][8] =
            (json[index - 1][8] ? json[index - 1][8] : '') +
            ' ' +
            (json[index][8] ? json[index][8] : '');
          json[index - 1][9] =
            (json[index - 1][9] ? json[index - 1][9] : '') +
            ' ' +
            (json[index][9] ? json[index][9] : '');
        }

        if (
          !json[index][0] &&
          !json[index][1] &&
          !json[index][2] &&
          !json[index][3] &&
          !json[index][4] &&
          !json[index][5] &&
          !json[index][6] &&
          !json[index][7] &&
          json[index][8]
        ) {
          json[index - 1][8] =
            (json[index - 1][8] ? json[index - 1][8] : '') +
            ' ' +
            (json[index][8] ? json[index][8] : '');
          json[index - 1][9] =
            (json[index - 1][9] ? json[index - 1][9] : '') +
            ' ' +
            (json[index][9] ? json[index][9] : '');
        }

        if (
          !json[index][0] &&
          !json[index][1] &&
          !json[index][2] &&
          !json[index][3] &&
          !json[index][4] &&
          !json[index][5] &&
          !json[index][6] &&
          !json[index][7] &&
          !json[index][8] &&
          json[index][9]
        ) {
          json[index - 1][9] =
            (json[index - 1][9] ? json[index - 1][9] : '') +
            ' ' +
            (json[index][9] ? json[index][9] : '');
        }
      }

      for (let index = 7; index < json.length; index++) {
        if (
          !json[index][0] &&
          !json[index][1] &&
          !json[index][2] &&
          !json[index][3] &&
          !json[index][4] &&
          !json[index][5] &&
          !json[index][6]
        )
          continue;

        let productCode = '';

        for (let i = 6; i >= 0; i--) {
          if (json[index][i]) {
            if (json[index][i].startsWith('0')) c[i] = json[index][i].substr(1);
            else {
              c[i] = json[index][i];
            }
          } else {
            c[i] = null;
          }

          if (i != 0 && !json[index][i - 1] && json[index][i]) break;
        }

        for (let i of c.filter(_.isString)) {
          productCode += i;
        }

        json_.push({
          productCode,
          productName: json[index][7],
          content: json[index][8],
          hsCode: json[index][9],
          reduceTax: false,
        });

        // hsCode = '';
        // productName = '';
        // productContent = '';
      }

      let rs = await Product.bulkCreate(json_, {
        updateOnDuplicate: [
          'content',
          'note',
          'productName',
          'hsCode',
          'reduceTax',
        ],
      });

      res.send({
        result: 'success',
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let { q, limit, page, orderField, orderMode, reduceTax } = req.query;
      q = q ?? '';
      orderField = orderField ?? 'updatedAt';
      orderMode = orderMode ?? 'DESC';

      /**
       * @type {import('sequelize').WhereOptions<import('../types').Product>}
       */
      let conditions = {
        [Op.and]: [
          // {
          //   [Op.or]: [
          //     { productName: { [Op.like]: `%${q}%` } },
          //     { content: { [Op.like]: `%${q}%` } },
          //     { note: { [Op.like]: `%${q}%` } },
          //     { hsCode: { [Op.like]: `%${q}%` } },
          //     { productCode: { [Op.like]: `%${q}%` } },
          //   ],
          // },
          parseBoolean(reduceTax) !== undefined
            ? { reduceTax: !!reduceTax }
            : {},
        ],
      };

      let products = (await Product.findAll({ where: conditions })).map(item =>
        item.toJSON(),
      );

      if (q) {
        let arrToSort = products.map(item => [
          item,
          utils.similarity(item.productName, q),
        ]);

        utils.mergeSort(arrToSort, 0, arrToSort.length - 1, item => item[1]);

        arrToSort = arrToSort.reverse();

        arrToSort = arrToSort.filter(
          item =>
            utils.matchSearch(item[0].content, q) ||
            utils.matchSearch(item[0].productName, q) ||
            utils.matchSearch(item[0].note, q) ||
            utils.matchSearch(item[0].hsCode, q) ||
            utils.matchSearch(item[0].productCode, q) ||
            item[1] >= 0.5,
        );

        products = arrToSort.map(item => item[0]);
      }

      let total = products.length;

      if (!isValidNumber(limit) || !isValidNumber(page)) {
        let ps = products;

        res.send({ result: 'success', products: ps });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        let ps = products.slice(limit * page, limit * page + limit);

        res.send({
          result: 'success',
          page,
          total: total,
          count: ps.length,
          products: ps,
        });
      }
    } catch (error) {
      next(error);
    }
  },
};
