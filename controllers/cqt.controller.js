const {
  ERROR_ORGANIZATION_HQ_NOT_FOUND,
  ERROR_ORGANIZATION_DEPARTMENT_NOT_FOUND,
  ERROR_NOT_FOUND,
  ERROR_MISSING_PARAMETERS,
  ERROR_USERNAME_NOTSAME_TAXCODE,
  ERROR_ORGANIZATION_NOT_GRANTED,
  ERROR_CONNECT_CQT_FAIL,
  ERROR_USER_NOT_IN_ORGANIZATION,
  ERROR_DANG_DONG_BO,
} = require('../configs/error.vi');
const {
  Organization,
  OrganizationDepartment,
  AccountOrganizationAccess,
  Sequelize,
  Account,
} = require('../models');
const {
  cqt_authenticate,
  cqt_getHDDV,
  cqt_getHDDR,
  cqt_getHDDV_MTT,
  cqt_getHDDR_MTT,
} = require('../utils/cqt.helper');
const moment = require('moment');
const notificationHelper = require('../utils/notification.helper');
const {
  NOTIFICATION_TYPE,
  TARGET_CODE,
} = require('../configs/constants/notification.constant');
const nntHelper = require('../utils/nnt.helper');
const { ORGANIZATION_TYPE } = require('../configs/constants/company.constant');
const { INVOICE_MAILBOX_DOMAIN, JWT_SECRET } = require('../configs/env');
const { ACCOUNT_LEVEL } = require('../configs/constants/account.constant');
const { checkInvoiceMailboxUsed } = require('../utils/company.helper');
const jwt = require('jsonwebtoken');
const _ = require('lodash');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async connection(req, res, next) {
    let { username, password, linkWebsite, isInput, isOutput } = req.body;

    try {
      let { organizationDepartmentId, organizationId } = req.account;

      if (!organizationDepartmentId && !organizationId) {
        return module.exports.createNewOrganization(req, res, next);
      }

      let taxCode = req.account.taxCode;
      // console.log(req.account, taxCode);
      username = _.trim(username);

      if (username != taxCode) throw new Error(ERROR_USERNAME_NOTSAME_TAXCODE);
      let { token } = await cqt_authenticate(username, password);
      if (!token) throw new Error(ERROR_CONNECT_CQT_FAIL);

      if (organizationDepartmentId) {
        let organizationDepartment = await OrganizationDepartment.findOne({
          where: { organizationDepartmentId },
          include: [
            {
              association: 'OrganizationBranch',
              where: { companyId: req.account.companyId },
            },
          ],
        });
        if (!organizationDepartment)
          throw new Error(ERROR_ORGANIZATION_DEPARTMENT_NOT_FOUND);

        await organizationDepartment.update({
          username,
          password,
          linkWebsite: linkWebsite ?? process.env.LINK_WEBSITE_SYNC,
          isInput,
          isOutput,
          usernameOld: organizationDepartment.usernameOld
            ? organizationDepartment.username
            : username,
          cqtToken: token,
          cqtTokenExpiration: moment(new Date()).add(1, 'days'),
        });
      } else if (organizationId) {
        let organization = await Organization.findOne({
          where: { organizationId, companyId: req.account.companyId },
        });
        if (!organization) throw new Error(ERROR_ORGANIZATION_HQ_NOT_FOUND);
        await organization.update({
          username,
          password,
          linkWebsite: linkWebsite ?? process.env.LINK_WEBSITE_SYNC,
          isInput,
          isOutput,
          usernameOld: organization.usernameOld
            ? organization.username
            : username,
          cqtToken: token,
          cqtTokenExpiration: moment(new Date()).add(1, 'days'),
        });
      }

      res.send({ result: 'success' });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async disconnection(req, res, next) {
    try {
      let { isInput, isOutput } = req.body;
      let update;
      let organizationId = req.account.Organization?.organizationId;
      let organizationDepartmentId =
        req.account.OrganizationDepartment?.organizationDepartmentId;
      if (organizationDepartmentId) {
        let organizationDepartment = await OrganizationDepartment.findOne({
          where: { organizationDepartmentId },
        });
        if (!organizationDepartment)
          throw new Error(ERROR_ORGANIZATION_DEPARTMENT_NOT_FOUND);
        if (!isInput && !isOutput) {
          update = {
            username: null,
            password: null,
            linkWebsite: null,
            isInput: null,
            isOutput: null,
            cqtToken: null,
            cqtTokenExpiration: null,
          };
        } else {
          update = {
            isInput,
            isOutput,
          };
        }
        await organizationDepartment.update(update);
      } else if (organizationId) {
        let organization = await Organization.findOne({
          where: { organizationId },
        });
        if (!organization) throw new Error(ERROR_ORGANIZATION_HQ_NOT_FOUND);
        if (!isInput && !isOutput) {
          update = {
            username: null,
            password: null,
            linkWebsite: null,
            isInput: null,
            isOutput: null,
            cqtToken: null,
            cqtTokenExpiration: null,
          };
        } else {
          update = {
            isInput,
            isOutput,
          };
        }
        await organization.update(update);
      }

      res.send({ result: 'success' });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async reconnection(req, res, next) {
    try {
      let { username, password, linkWebsite, isInput, isOutput } = req.body;
      let taxCode =
        req.account.OrganizationDepartment?.OrganizationBranch.taxCode ??
        req.account.Organization?.taxCode;
      if (username != taxCode) throw new Error(ERROR_USERNAME_NOTSAME_TAXCODE);
      let { token } = await cqt_authenticate(username, password);
      if (!token) throw new Error(ERROR_NOT_FOUND);
      let conditions = [];
      let organizationId = req.account.Organization?.organizationId;
      let organizationDepartmentId =
        req.account.OrganizationDepartment?.organizationDepartmentId;
      if (organizationDepartmentId) {
        let organizationDepartment = await OrganizationDepartment.findOne({
          where: { organizationDepartmentId },
        });
        if (!organizationDepartment)
          throw new Error(ERROR_ORGANIZATION_DEPARTMENT_NOT_FOUND);
        conditions.push({
          username,
          password,
          linkWebsite: linkWebsite ?? process.env.LINK_WEBSITE_SYNC,
          isInput,
          isOutput,
          usernameOld: organizationDepartment.usernameOld
            ? organizationDepartment.username
            : username,
          cqtToken: token,
          cqtTokenExpiration: moment(new Date()).add(1, 'days'),
        });

        await organizationDepartment.update(conditions);
      } else if (organizationId) {
        let organization = await Organization.findOne({
          where: { organizationId },
        });
        if (!organization) throw new Error(ERROR_ORGANIZATION_HQ_NOT_FOUND);

        conditions.push({
          username,
          password,
          linkWebsite: linkWebsite ?? process.env.LINK_WEBSITE_SYNC,
          isInput,
          isOutput,
          usernameOld: organization.usernameOld
            ? organization.username
            : username,
          cqtToken: token,
          cqtTokenExpiration: moment(new Date()).add(1, 'days'),
        });

        await organization.update(conditions);
      }

      res.send({ result: 'success' });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async synchronization(req, res, next) {
    let { sort, size, from, to } = req.body;
    let { organizationId, organizationDepartmentId } = req.account;

    /* admin can sync across multiple organizations */
    if (req.body.organizationId) {
      if (req.account.accountLevel == ACCOUNT_LEVEL.COMPANY_ADMIN) {
        if (
          await Organization.findOne({
            where: { organizationId, companyId: req.account.companyId },
          })
        ) {
          organizationId = req.body.organizationId;
          organizationDepartmentId = null;
        }
      }
    }
    try {
      let taxCode =
        req.account.OrganizationDepartment?.OrganizationBranch.taxCode ??
        req.account.Organization?.taxCode;
      if (!from || !to) throw new Error(ERROR_MISSING_PARAMETERS);

      let token;
      let isInput;
      let isOutput;
      let username;
      let password;
      let data;
      let totalHDDV = 0;
      let totalHDDR = 0;
      let organizationDepartment;
      let organization;
      let dangDongBo;
      if (organizationDepartmentId) {
        organizationDepartment = await OrganizationDepartment.findOne({
          where: {
            organizationDepartmentId,
          },
        });
        if (!organizationDepartment)
          throw new Error(ERROR_ORGANIZATION_DEPARTMENT_NOT_FOUND);
        token = organizationDepartment.cqtToken;
        isInput = organizationDepartment.isInput;
        isOutput = organizationDepartment.isOutput;
        username = organizationDepartment.username;
        password = organizationDepartment.password;

        dangDongBo = organizationDepartment.dangDongBo;
      } else if (organizationId) {
        organization = await Organization.findOne({
          where: {
            organizationId,
          },
        });
        if (!organization)
          throw new Error(ERROR_ORGANIZATION_DEPARTMENT_NOT_FOUND);
        token = organization.cqtToken;
        isInput = organization.isInput;
        isOutput = organization.isOutput;
        username = organization.username;
        password = organization.password;

        dangDongBo = organization.dangDongBo;
      }

      if (organizationDepartmentId) {
        dangDongBo = (
          await OrganizationDepartment.findOne({
            where: { organizationDepartmentId },
          })
        )?.dangDongBo;
      } else {
        dangDongBo = (await Organization.findOne({ where: { organizationId } }))
          ?.dangDongBo;
      }

      if (dangDongBo) {
        throw new Error(ERROR_DANG_DONG_BO);
      }

      /* set dangDongBo = true */
      if (organizationId) {
        await Organization.update(
          { dangDongBo: true },
          { where: { organizationId } },
        );
      } else if (organizationDepartmentId) {
        await OrganizationDepartment.update(
          { dangDongBo: true },
          { where: { organizationDepartmentId } },
        );
      }

      let decode = jwt.decode(token, {});
      if (parseInt(decode.exp * 1000) < Date.now()) {
        token = (await cqt_authenticate(username, password)).token;
        if (!token) throw new Error(ERROR_CONNECT_CQT_FAIL);
        if (organizationDepartment)
          await organizationDepartment.update({
            cqtToken: token,
            cqtTokenExpiration: moment(new Date()).add(1, 'days'),
          });
        if (organization)
          await organization.update({
            cqtToken: token,
            cqtTokenExpiration: moment(new Date()).add(1, 'days'),
          });
      }
      res.send({
        result: 'success',
      });

      let date = moment(new Date(req.body.from)).add(1, 'M').toDate();
      let arrDate = [];
      while (date < new Date(req.body.to) && date < new Date()) {
        arrDate.push({ from: new Date(from), to: date });
        from = date;
        date = moment(date).add(1, 'M').toDate();
      }
      arrDate.push({
        from: new Date(from),
        to: new Date(to) <= new Date() ? new Date(to) : new Date(),
      });
      // console.log(arrDate);
      for (let key in arrDate) {
        let itemDate = arrDate[key];
        from = moment(itemDate.from)
          .startOf('date')
          .format('DD/MM/yyyyTHH:mm:ss');
        to = moment(itemDate.to).endOf('date').format('DD/MM/yyyyTHH:mm:ss');
        let search = `tdlap=ge=${from};tdlap=le=${to}`;
        let config = {
          params: {
            sort,
            size: 50,
            search,
          },
          headers: { Authorization: `Bearer ${token}` },
        };
        if (isInput) {
          try {
            let stateCQT = 'start';
            while (stateCQT !== null) {
              if (stateCQT !== 'start') config.params.state = stateCQT;
              else {
                delete config.params.state;

                // config.params.state = undefined;
              }

              let { datas, state } = await cqt_getHDDV(
                config,
                organizationId,
                organizationDepartmentId,
                taxCode,
                // itemDate.from,
                // itemDate.to,
              );
              totalHDDV += datas?.length;
              stateCQT = state;
            }
          } catch (error) {
            // console.log(error);
          }
        }

        // kiem tra cấu hình nếu downloadAllOutputInvoice ko bật thì không chạy cqt_getHDDR cqt_getHDDR_MTT
        const isDownloadAllOutputInvoice =
          !!organization?.downloadAllOutputInvoice ||
          !!organizationDepartment?.downloadAllOutputInvoice;

        if (isOutput) {
          try {
            let stateCQT = 'start';
            while (stateCQT !== null) {
              if (stateCQT !== 'start') config.params.state = stateCQT;
              else {
                delete config.params.state;

                // config.params.state = undefined;
              }
              let { datas, state } = await cqt_getHDDR(
                config,
                organizationId,
                organizationDepartmentId,
                taxCode,
                // itemDate.from,
                // itemDate.to,
              );
              totalHDDR += datas?.length;
              stateCQT = state;
            }
          } catch (error) {
            // console.log(error);
          }
        }

        /* MTT */
        if (isInput) {
          try {
            let stateCQT = 'start';
            while (stateCQT !== null) {
              if (stateCQT !== 'start') config.params.state = stateCQT;
              else {
                delete config.params.state;

                // config.params.state = undefined;
              }
              let { datas, state } = await cqt_getHDDV_MTT(
                config,
                organizationId,
                organizationDepartmentId,
                taxCode,
                // itemDate.from,
                // itemDate.to,
              );
              totalHDDV += datas?.length;
              stateCQT = state;
            }
          } catch (error) {
            // console.log(error);
          }
        }

        if (isOutput && !!isDownloadAllOutputInvoice) {
          try {
            let stateCQT = 'start';
            while (stateCQT !== null) {
              if (stateCQT !== 'start') config.params.state = stateCQT;
              else {
                delete config.params.state;

                // config.params.state = undefined;
              }
              let { datas, state } = await cqt_getHDDR_MTT(
                config,
                organizationId,
                organizationDepartmentId,
                taxCode,
                // itemDate.from,
                // itemDate.to,
              );
              totalHDDR += datas?.length;
              stateCQT = state;
            }
          } catch (error) {
            // console.log(error);
          }
        }
      }

      // create notification
      let [noti] =
        await notificationHelper.createNotificationAndSendSyncedEvent([
          {
            type: NOTIFICATION_TYPE.ACTIVITY,
            targetCode: TARGET_CODE.CQT_SYNC,
            title: `Hoàn tất đồng bộ danh sách hoá đơn từ CQT từ ngày ${moment(
              req.body.from,
            ).format('DD/MM/YYYY')} đến ngày ${moment(req.body.to).format(
              'DD/MM/YYYY',
            )}`,
            Accounts: [{ accountId: req.account.accountId }],
          },
        ]);

      /* set dangDongBo => false */
      if (organizationId) {
        await Organization.update(
          { dangDongBo: false },
          { where: { organizationId } },
        );
      } else if (organizationDepartmentId) {
        await OrganizationDepartment.update(
          { dangDongBo: false },
          { where: { organizationDepartmentId } },
        );
      }
    } catch (error) {
      /* set dangDongBo => false */
      if (organizationId) {
        await Organization.update(
          { dangDongBo: false },
          { where: { organizationId } },
        );
      } else if (organizationDepartmentId) {
        await OrganizationDepartment.update(
          { dangDongBo: false },
          { where: { organizationDepartmentId } },
        );
      }
      console.log(error);
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async createNewOrganization(req, res, next) {
    const t = await Sequelize.transaction();
    try {
      let { username, password, linkWebsite, isInput, isOutput } = req.body;

      if (!username || !password) throw new Error(ERROR_MISSING_PARAMETERS);

      username = _.trim(username);

      await checkInvoiceMailboxUsed(
        `${username.replace(/-/g, '_')}${INVOICE_MAILBOX_DOMAIN}`,
        req.account.companyId,
      );

      /* create new organization */
      let _company = await nntHelper.getCompany(username).catch(() => null);

      let organization = await Organization.create(
        {
          organizationName: _company?.companyName ?? username,
          taxCode: username,
          organizationType: ORGANIZATION_TYPE.HQ,
          businessPermitAddress: _company?.businessPermitAddress,
          businessPermitDate: _company?.businessPermitDate,
          invoiceMailbox: `${username.replace(
            /-/g,
            '_',
          )}${INVOICE_MAILBOX_DOMAIN}`,
          companyId: req.account.companyId,
        },
        { transaction: t },
      );

      let { token } = await cqt_authenticate(username, password);
      if (!token) throw new Error(ERROR_CONNECT_CQT_FAIL);

      await organization.update(
        {
          username,
          password,
          linkWebsite: linkWebsite ?? process.env.LINK_WEBSITE_SYNC,
          isInput,
          isOutput,
          usernameOld: organization.usernameOld
            ? organization.username
            : username,
          cqtToken: token,
          cqtTokenExpiration: moment(new Date()).add(1, 'days'),
        },
        { transaction: t },
      );

      if (
        !req.account.organizationId &&
        !req.account.organizationDepartmentId
      ) {
        await Account.update(
          { organizationId: organization.organizationId },
          { transaction: t, where: { accountId: req.account.accountId } },
        );
      }

      await t.commit();

      res.send({ result: 'success', organization });
    } catch (error) {
      await t.rollback();
      next(error);
    }
  },
};
