const {
  ERROR_NOT_FOUND,
  ERROR_MISSING_FILE,
  ERROR_MISSING_PARAMETERS,
} = require('../configs/error.vi');
const _ = require('lodash');

const fs = require('fs');
module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async get(req, res, next) {
    try {
      const screenCodes = require('../assets/json/screen_code.json');
      if (!screenCodes) throw new Error(ERROR_NOT_FOUND);
      return res.send({
        result: 'success',
        screenCodes,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    try {
      const { screenCodes } = req.body;
      if (!screenCodes) throw new Error(ERROR_MISSING_PARAMETERS);

      let path = './assets/json/screen_code.json';
      fs.writeFileSync(path, JSON.stringify(screenCodes));
      // const screen_code = require(path);
      res.send({ result: 'success', screenCodes });
    } catch (error) {
      next(error);
    }
  },
};
