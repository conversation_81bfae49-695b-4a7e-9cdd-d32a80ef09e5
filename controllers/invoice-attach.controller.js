const { InvoiceAttach } = require('../models');
const { ERROR_NOT_FOUND } = require('../configs/error.vi');
const { PUBLIC_UPLOAD_DIR } = require('../configs/env');
const fs = require('fs');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    try {
      let { invoiceAttachId } = req.body;
      let invoiceAttach = await InvoiceAttach.findOne({
        where: { invoiceAttachId },
      });
      if (!invoiceAttach) throw new Error(ERROR_NOT_FOUND);
      let invoiceAttachs = await InvoiceAttach.findAll({
        where: { invoiceId: invoiceAttach.invoiceId, url: invoiceAttach.url },
      });
      if (invoiceAttachs.length === 1) {
        // xoá ảnh trong resouces
        const filePath = invoiceAttach.url.replace(
          'resources',
          PUBLIC_UPLOAD_DIR,
        );
        fs.rmSync(filePath, { recursive: true });
      }
      await InvoiceAttach.destroy({
        where: { invoiceAttachId, invoiceId: invoiceAttach.invoiceId },
      });

      res.send({ result: 'success' });
    } catch (error) {
      next(error);
    }
  },
};
