const { Op } = require('sequelize');
const {
  ERROR_PERMISSION_DENIED,
  ERROR_MISSING_PARAMETERS,
  ERROR_USER_NOT_IN_ORGANIZATION,
} = require('../configs/error.vi');
const moment = require('moment');
const { Invoice, InvoiceValidate } = require('../models');
const { DATE_FORMAT } = require('../configs/constants/other.constant');
const {
  INVOICE_CATEGORY,
  DATE_TYPE,
} = require('../configs/constants/invoice.constant');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async analyzeSystem(req, res, next) {
    try {
      const { from, to } = req.query;
      const { organizationId, organizationDepartmentId } = req.account;

      if (!from) throw new Error(ERROR_MISSING_PARAMETERS);

      if (!organizationDepartmentId && !organizationId) {
        let analyze = {
          invalidInvoices: {
            total: 0,
            totalAmountBeforeVat: 0,
            totalAmountVat: 0,
            totalFinalAmount: 0,
          },
          validInvoices: {
            total: 0,
            totalAmountBeforeVat: 0,
            totalAmountVat: 0,
            totalFinalAmount: 0,
          },
          succeededInvoices: {
            total: 0,
            totalAmountBeforeVat: 0,
            totalAmountVat: 0,
            totalFinalAmount: 0,
          },
        };

        res.send({
          result: 'success',
          succeededInvoices: analyze.succeededInvoices,
          validInvoices: analyze.validInvoices,
          invalidInvoices: analyze.invalidInvoices,
          // ta,
          // ti,
          // tv,
        });

        return;
      }

      const conditions = [
        // { invoiceCheckStatus: 2 },
        {
          invoiceDate: {
            [Op.between]: [
              from
                ? moment(from).startOf('date')
                : moment().subtract(30, 'day').startOf('date'),
              to ? moment(to).endOf('date') : moment().endOf('date'),
            ],
          },
        },
        { amountBeforeVat: { [Op.ne]: null } },
        { amountAfterVat: { [Op.ne]: null } },
        { finalAmount: { [Op.ne]: null } },
        { organizationId },
        { organizationDepartmentId },
        { invoiceCategory: { [Op.ne]: null } },
        {
          [Op.or]: [
            {
              isDeleted: {
                [Op.eq]: null,
              },
            },
            {
              isDeleted: {
                [Op.eq]: false,
              },
            },
          ],
        },

        {
          invoiceCategory: {
            [Op.or]: [
              INVOICE_CATEGORY.INPUT_INVOICE,
              INVOICE_CATEGORY.OUTPUT_INVOICE,
            ],
          },
        },
      ];

      const invoices = await Invoice.findAll({
        where: conditions,
        include: {
          model: InvoiceValidate,
        },
      });

      let analyze = {
        invalidInvoices: {
          total: 0,
          totalAmountBeforeVat: 0,
          totalAmountVat: 0,
          totalFinalAmount: 0,
        },
        validInvoices: {
          total: 0,
          totalAmountBeforeVat: 0,
          totalAmountVat: 0,
          totalFinalAmount: 0,
        },
        succeededInvoices: {
          total: 0,
          totalAmountBeforeVat: 0,
          totalAmountVat: 0,
          totalFinalAmount: 0,
        },
      };

      for (let i of invoices) {
        if (i.InvoiceValidate) {
          //  processedInvoice.push(i);
          if (
            !i.InvoiceValidate.checkResultBuyerName ||
            !i.InvoiceValidate.checkResultBuyerTaxCode ||
            !i.InvoiceValidate.checkResultBuyerAddress ||
            !i.InvoiceValidate.checkResultSellerName ||
            !i.InvoiceValidate.checkResultSellerAddress ||
            !i.InvoiceValidate.checkResultSignatureNCC ||
            !i.InvoiceValidate.checkResultSignatureCQT ||
            !i.InvoiceValidate.checkResultHasInvoiceCode ||
            !i.InvoiceValidate.checkResultHasCQTRecord ||
            !i.InvoiceValidate.checkDate
          ) {
            // invalidInvoice.push(i);
            analyze.invalidInvoices.total += 1;
            analyze.invalidInvoices.totalAmountBeforeVat += Number(
              i.amountBeforeVat,
            );
            analyze.invalidInvoices.totalAmountVat += Number(i.amountVat);
            analyze.invalidInvoices.totalFinalAmount += Number(i.finalAmount);
          } else {
            // validInvoice.push(i);
            analyze.validInvoices.total += 1;
            analyze.validInvoices.totalAmountBeforeVat += Number(
              i.amountBeforeVat,
            );
            analyze.validInvoices.totalAmountVat += Number(i.amountVat);
            analyze.validInvoices.totalFinalAmount += Number(i.finalAmount);
          }
        }
      }

      analyze.succeededInvoices = {
        total: analyze.invalidInvoices.total + analyze.validInvoices.total,
        totalAmountBeforeVat:
          analyze.invalidInvoices.totalAmountBeforeVat +
          analyze.validInvoices.totalAmountBeforeVat,
        totalAmountVat:
          analyze.invalidInvoices.totalAmountVat +
          analyze.validInvoices.totalAmountVat,
        totalFinalAmount:
          analyze.invalidInvoices.totalFinalAmount +
          analyze.validInvoices.totalFinalAmount,
      };

      res.send({
        result: 'success',
        succeededInvoices: analyze.succeededInvoices,
        validInvoices: analyze.validInvoices,
        invalidInvoices: analyze.invalidInvoices,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async analyzeInvoiceDate(req, res, next) {
    try {
      const { from, to } = req.query;
      const { organizationId, organizationDepartmentId } = req.account;

      if (!organizationDepartmentId && !organizationId) {
        res.send({
          result: 'success',
          invoiceAnalystByDate: {},
        });
        return;
      }

      const conditions = [
        // { invoiceCheckStatus: 2 },
        {
          invoiceDate: {
            [Op.between]: [
              from
                ? moment(from).startOf('date')
                : moment().subtract(30, 'day').startOf('date'),
              to ? moment(to).endOf('date') : moment().endOf('date'),
            ],
          },
        },
        { amountBeforeVat: { [Op.ne]: null } },
        { amountAfterVat: { [Op.ne]: null } },
        { finalAmount: { [Op.ne]: null } },
        true ? { organizationId } : {},
        true ? { organizationDepartmentId } : {},
        { invoiceCategory: INVOICE_CATEGORY.INPUT_INVOICE },
      ];
      const invoices = await Invoice.findAll({
        where: conditions,
        include: {
          model: InvoiceValidate,
        },
        order: [['invoiceDate']],
      });

      let invoiceList = {};
      // filter by date
      for (let i of invoices) {
        if (i.InvoiceValidate) {
          const date = moment(i.invoiceDate).format(DATE_FORMAT);
          if (invoiceList[date]) {
            invoiceList[date].push(i);
          } else {
            invoiceList[date] = [];
            invoiceList[date].push(i);
          }
        }
      }

      let invoiceAnalystByDate = {};
      for (const i in invoiceList) {
        invoiceAnalystByDate[i] = {};
        invoiceAnalystByDate[i].total = 0;
        invoiceAnalystByDate[i].totalAmountBeforeVat = 0;
        invoiceAnalystByDate[i].totalAmountVat = 0;
        invoiceAnalystByDate[i].totalFinalAmount = 0;
        for (let invoice of invoiceList[i]) {
          invoiceAnalystByDate[i].total += 1;
          invoiceAnalystByDate[i].totalAmountBeforeVat += Number(
            invoice.amountBeforeVat,
          );
          invoiceAnalystByDate[i].totalAmountVat += Number(invoice.amountVat);
          invoiceAnalystByDate[i].totalFinalAmount += Number(
            invoice.finalAmount,
          );
        }
      }

      res.send({
        result: 'success',
        invoiceAnalystByDate,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async analyzeInvoiceBuyerName(req, res, next) {
    try {
      const { from, to } = req.query;
      const { organizationId, organizationDepartmentId } = req.account;

      if (!organizationDepartmentId && !organizationId) {
        res.send({
          result: 'success',
          total: 0,
          invoiceAnalystTotalValue: {},
        });

        return;
      }

      const conditions = [
        // { invoiceCheckStatus: 2 },
        {
          invoiceDate: {
            [Op.between]: [
              from
                ? moment(from).startOf('date')
                : moment().subtract(30, 'day').startOf('date'),
              to ? moment(to).endOf('date') : moment().endOf('date'),
            ],
          },
        },
        { amountBeforeVat: { [Op.ne]: null } },
        { amountAfterVat: { [Op.ne]: null } },
        { finalAmount: { [Op.ne]: null } },
        true ? { organizationId } : {},
        true ? { organizationDepartmentId } : {},
        { invoiceCategory: INVOICE_CATEGORY.INPUT_INVOICE },
      ];

      const invoices = await Invoice.findAll({
        where: conditions,
        include: {
          model: InvoiceValidate,
        },
      });

      // filter by sellerTaxCode
      let invoiceListByTaxCode = {};
      for (let i of invoices) {
        if (i.InvoiceValidate)
          if (invoiceListByTaxCode[`${i.sellerTaxCode}`]) {
            invoiceListByTaxCode[`${i.sellerTaxCode}`].push(i);
          } else {
            invoiceListByTaxCode[`${i.sellerTaxCode}`] = [];
            invoiceListByTaxCode[`${i.sellerTaxCode}`].push(i);
          }
      }

      let invoiceAnalystTotalValue = {};
      let total = 0;
      for (const i in invoiceListByTaxCode) {
        invoiceAnalystTotalValue[i] = {};
        invoiceAnalystTotalValue[i].totalInvoice = 0;
        invoiceAnalystTotalValue[i].totalAmountBeforeVat = 0;
        for (let invoice of invoiceListByTaxCode[i]) {
          total += Number(invoice.amountBeforeVat);
          invoiceAnalystTotalValue[i].sellerName = invoice.sellerName;
          invoiceAnalystTotalValue[i].totalInvoice += 1;
          invoiceAnalystTotalValue[i].totalAmountBeforeVat += Number(
            invoice.amountBeforeVat,
          );
        }
      }
      // let invoiceAnalystTotalValues = [];
      // for (let index = 0; index < sortInvoice.length; index++) {
      //   if (index <= 5) {
      //     invoiceAnalystTotalValues.push(sortInvoice[index]);
      //   } else {
      //     invoiceAnalystTotalValue[6].sellerName = 'Các nhà cung cấp khác';
      //     invoiceAnalystTotalValue[6].totalAmountBeforeVat +=
      //       sortInvoice[index].totalAmountBeforeVat;
      //     invoiceAnalystTotalValue[6].totalInvoice += 1;
      //   }
      // }

      res.send({
        result: 'success',
        total,
        invoiceAnalystTotalValue,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async reportTotalInvoice(req, res, next) {
    try {
      const { from, to } = req.query;
      const { organizationId, organizationDepartmentId } = req.account;

      if (!organizationDepartmentId && !organizationId) {
        res.send({
          result: 'success',
          invoices: 0,
          inputInvoices: 0,
          outputInvoices: 0,
        });
        return;
      }

      const conditions = [
        // { invoiceCheckStatus: 2 },
        {
          invoiceDate: {
            [Op.between]: [
              from
                ? moment(from).startOf('date')
                : moment().subtract(30, 'day').startOf('date'),
              to ? moment(to).endOf('date') : moment().endOf('date'),
            ],
          },
        },
        { amountBeforeVat: { [Op.ne]: null } },
        { amountAfterVat: { [Op.ne]: null } },
        { finalAmount: { [Op.ne]: null } },
        { organizationId },
        { organizationDepartmentId },
        // { invoiceCategory: { [Op.not]: null } },
        // {
        //   [Op.or]: [
        //     {
        //       isDeleted: {
        //         [Op.eq]: null,
        //       },
        //     },
        //     {
        //       isDeleted: {
        //         [Op.eq]: false,
        //       },
        //     },
        //   ],
        // },
      ];

      const invoices = await Invoice.findAll({
        where: conditions,
        include: {
          model: InvoiceValidate,
        },
      });

      let inputInvoices = {
        totalAmountBeforeVat: 0,
        totalAmountVat: 0,
        totalFinalAmount: 0,
        total: 0,
      };
      let outputInvoices = {
        totalAmountBeforeVat: 0,
        totalAmountVat: 0,
        totalFinalAmount: 0,
        total: 0,
      };

      for (let invoice of invoices) {
        if (invoice.invoiceCategory == INVOICE_CATEGORY.INPUT_INVOICE) {
          inputInvoices.totalAmountBeforeVat += Number(invoice.amountBeforeVat);
          inputInvoices.totalAmountVat += Number(invoice.amountVat);
          inputInvoices.totalFinalAmount += Number(invoice.finalAmount);
          inputInvoices.total += 1;
        } else if (invoice.invoiceCategory == INVOICE_CATEGORY.OUTPUT_INVOICE) {
          outputInvoices.totalAmountBeforeVat += Number(
            invoice.amountBeforeVat,
          );
          outputInvoices.totalAmountVat += Number(invoice.amountVat);
          outputInvoices.totalFinalAmount += Number(invoice.finalAmount);
          outputInvoices.total += 1;
        }
      }

      res.send({
        result: 'success',
        invoices: {
          totalAmountBeforeVat:
            outputInvoices.totalAmountBeforeVat -
            inputInvoices.totalAmountBeforeVat,
          totalAmountVat:
            outputInvoices.totalAmountVat - inputInvoices.totalAmountVat,
          totalFinalAmount:
            outputInvoices.totalFinalAmount - inputInvoices.totalFinalAmount,
          total: outputInvoices.total + inputInvoices.total,
        },
        inputInvoices,
        outputInvoices,
      });
    } catch (error) {
      next(error);
    }
  },
};
