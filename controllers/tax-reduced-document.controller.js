const { Op } = require('sequelize');
const { isValidNumber } = require('../utils');
const { TaxReducedDocument } = require('../models');
const _ = require('lodash');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_NOT_FOUND,
  ERROR_TAX_DOCUMENT_TITLE_EXISTED,
} = require('../configs/error.vi');
const uploadFile = require('../utils/upload.helper');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let { q, limit, page } = req.query;

      q = q ?? '';

      /**
       * @type {import('sequelize').WhereOptions<import('../types').TaxReducedDocument>}
       */
      let conditions = [
        {
          [Op.or]: [
            { title: { [Op.like]: `%${q}%` } },
            { description: { [Op.like]: `%${q}%` } },
            { author: { [Op.like]: `%${q}%` } },
          ],
        },
      ];

      if (!isValidNumber(limit) || !isValidNumber(page)) {
        let rs = await TaxReducedDocument.findAll({ where: conditions });

        res.send({ result: 'success', taxReducedDocuments: rs });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        let rs = await TaxReducedDocument.findAndCountAll({
          limit,
          offset: limit * page,
          where: conditions,
        });

        res.send({
          result: 'success',
          page,
          total: rs.count,
          count: rs.rows.length,
          taxReducedDocuments: rs.rows,
        });
      }
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      let { code } = req.params;
      if (!code) {
        throw new Error(ERROR_MISSING_PARAMETERS);
      }

      const taxReducedDocument = await TaxReducedDocument.findOne({
        where: {
          code,
        },
      });

      if (!taxReducedDocument) {
        throw new Error(ERROR_NOT_FOUND);
      }

      res.send({
        result: 'success',
        taxReducedDocument,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    try {
      let { title, description, author, publishedDate, pdfUrl, note } =
        req.body;

      if (!title) throw new Error(ERROR_MISSING_PARAMETERS);

      if (!!(await TaxReducedDocument.findOne({ where: { title } }))) {
        throw new Error(ERROR_TAX_DOCUMENT_TITLE_EXISTED);
      }

      const uploadFilePath = await uploadFile(req, 'pdfUrl', 'tax-document');
      pdfUrl = uploadFilePath ?? pdfUrl;

      let taxReducedDocument = await TaxReducedDocument.create({
        title,
        description,
        author,
        publishedDate,
        pdfUrl,
        note,
      });

      res.send({ result: 'success', taxReducedDocument });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    try {
      let { code } = req.params;

      if (!code) throw new Error(ERROR_MISSING_PARAMETERS);

      let taxReducedDocument = await TaxReducedDocument.findOne({
        where: { code },
      });

      if (!taxReducedDocument) {
        throw new Error(ERROR_NOT_FOUND);
      }

      const deleteTaxReducedDocument = await taxReducedDocument.destroy();

      res.send({ result: 'success', deleteTaxReducedDocument });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    try {
      let { code } = req.params;

      if (!code) {
        throw new Error(ERROR_MISSING_PARAMETERS);
      }

      let taxReducedDocument = await TaxReducedDocument.findOne({
        where: { code },
      });

      if (!taxReducedDocument) throw new Error(ERROR_NOT_FOUND);

      let { title, description, note, pdfUrl, publishedDate } = req.body;

      const uploadFilePath = await uploadFile(
        req,
        'pdfUrl',
        'tax-document',
        taxReducedDocument.pdfUrl,
      );
      pdfUrl = uploadFilePath ?? pdfUrl;

      await taxReducedDocument.update({
        title,
        description,
        publishedDate,
        pdfUrl,
        note,
      });

      res.send({ result: 'success', taxReducedDocument });
    } catch (error) {
      next(error);
    }
  },
};
