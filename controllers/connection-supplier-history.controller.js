const {
  ConnectionSupplier,
  ConnectionSupplierHistory,
  ConnectionSupplierInvoice,
  Supplier,
  Sequelize,
  Invoice,
  OrganizationDepartment,
  Organization,
} = require('../models');
const {
  ERROR_NOT_FOUND,
  ERROR_PERMISSION_DENIED,
  ERROR_INVALID_DATE,
  ERROR_MISSING_PARAMETERS,
  ERROR_USER_NOT_IN_ORGANIZATION,
  ERROR_DANG_DONG_BO,
} = require('../configs/error.vi');
const { Op } = require('sequelize');
const _ = require('lodash');
const moment = require('moment');
const { isValidNumber, isValidDate } = require('../utils');
const { _synchronize } = require('../vietinvoice/vietinvoice.helper');
const {
  filterInvoicesNotHaveDuplicatedByInvoiceId,
} = require('../utils/supplier-invoice.helper');
const supplierInvoiceHelper = require('../utils/supplier-invoice.helper');
const invoiceHelper = require('../utils/invoice.helper');
const { SUPPLIER_KEY } = require('../configs/constants/supplier.constant');
const { vietinvoiceHelper } = require('../vietinvoice');
const notificationHelper = require('../utils/notification.helper');
const {
  NOTIFICATION_TYPE,
  TARGET_CODE,
} = require('../configs/constants/notification.constant');
const connectionSupplierHelper = require('../utils/connection-supplier.helper');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      const { organizationId, organizationDepartmentId } = req.account;

      let { limit, page, orderMode, from, to } = req.query;

      let orderField = 'syncDate';
      orderMode = orderMode ?? 'DESC';

      if (from && !isValidDate(from)) throw new Error(ERROR_INVALID_DATE);
      if (to && !isValidDate(to)) throw new Error(ERROR_INVALID_DATE);

      let conditions = [
        true ? { organizationId } : {},
        true ? { organizationDepartmentId } : {},
        from ? { createdAt: { [Op.gte]: moment(from).startOf('date') } } : {},
        to ? { createdAt: { [Op.lte]: moment(to).endOf('date') } } : {},
      ];

      if (!isValidNumber(limit) || !isValidNumber(page)) {
        const connectionSupplierHistories =
          await ConnectionSupplierHistory.findAll({
            where: conditions,
            order: [[orderField, orderMode]],
          });

        res.send({
          result: 'success',
          total: connectionSupplierHistories.length,
          connectionSupplierHistories,
        });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);
        const connectionSupplierHistories =
          await ConnectionSupplierHistory.findAndCountAll({
            where: conditions,
            limit,
            offset: limit * page,
            order: [[orderField, orderMode]],
          });

        res.send({
          result: 'success',
          page,
          total: connectionSupplierHistories.count,
          count: connectionSupplierHistories.rows.length,
          connectionSupplierHistories: connectionSupplierHistories.rows,
        });
      }
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      const { connectionSupplierHistoryId } = req.params;

      const { organizationId, organizationDepartmentId } = req.account;

      if (!organizationId && !organizationDepartmentId)
        throw new Error(ERROR_USER_NOT_IN_ORGANIZATION);

      const connectionSupplierHistory = await ConnectionSupplierHistory.findOne(
        {
          where: {
            [Op.and]: [
              { connectionSupplierHistoryId },
              true ? { organizationId } : {},
              true ? { organizationDepartmentId } : {},
            ],
          },
          // include: {
          //   model: ConnectionSupplier,
          //   attributes: [
          //     'organizationDepartmentId',
          //     'organizationId',
          //     'supplierId',
          //   ],
          // },
        },
      );

      if (!connectionSupplierHistory) throw new Error(ERROR_NOT_FOUND);

      let { q, limit, page, orderMode, from, to } = req.query;

      let orderField = 'createdAt';
      q = q ?? '';
      orderMode = orderMode ?? 'DESC';

      if (from && !isValidDate(from)) throw new Error(ERROR_INVALID_DATE);
      if (to && !isValidDate(to)) throw new Error(ERROR_INVALID_DATE);

      let conditions = [
        {
          connectionSupplierHistoryId: connectionSupplierHistoryId,
        },
        from ? { createdAt: { [Op.gte]: moment(from).startOf('date') } } : {},
        to ? { createdAt: { [Op.lte]: moment(to).endOf('date') } } : {},
        {
          [Op.or]: [
            {
              invoiceNumber: { [Op.like]: `%${q}%` },
            },
            {
              '$ConnectionSupplier.Supplier.companyName$': {
                [Op.like]: `%${q}%`,
              },
            },
            {
              '$ConnectionSupplier.usernameConnection$': {
                [Op.like]: `%${q}%`,
              },
            },
          ],
        },
      ];

      if (!isValidNumber(limit) || !isValidNumber(page)) {
        const connectionSupplierInvoices =
          await ConnectionSupplierInvoice.findAll({
            where: conditions,
            order: [[orderField, orderMode]],
            include: [
              {
                model: ConnectionSupplier,
                include: [
                  {
                    model: Supplier,
                    attributes: ['companyName'],
                  },
                ],
              },
            ],
          });

        res.send({
          result: 'success',
          total: connectionSupplierInvoices.length,
          connectionSupplierInvoices,
        });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        const connectionSupplierInvoices =
          await ConnectionSupplierInvoice.findAndCountAll({
            where: conditions,
            order: [[orderField, orderMode]],
            include: [
              {
                model: ConnectionSupplier,
                include: [
                  {
                    model: Supplier,
                    attributes: ['companyName'],
                  },
                ],
              },
            ],
            limit,
            offset: limit * page,
          });

        let vietInvoiceDict = (
          await Invoice.findAll({
            where: {
              vietInvoiceId: {
                [Op.in]: connectionSupplierInvoices.rows.map(
                  item => item.vietInvoiceId,
                ),
              },
              organizationId,
              organizationDepartmentId,
            },
          })
        ).reduce((prev, curr) => {
          prev[curr.vietInvoiceId] = curr.toJSON();

          return prev;
        }, {});

        res.send({
          result: 'success',
          page,
          total: connectionSupplierInvoices.count,
          count: connectionSupplierInvoices.rows.length,
          connectionSupplierInvoices: connectionSupplierInvoices.rows.map(
            item => {
              let vietInvoiceInvoice = vietInvoiceDict[item.vietInvoiceId];

              if (vietInvoiceInvoice) {
                item.setDataValue(
                  'originXmlFile',
                  vietInvoiceInvoice.originXmlFile,
                );

                item.setDataValue('pdfFile', vietInvoiceInvoice.pdfFile);
              }

              return item;
            },
          ),
        });
      }
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async reload(req, res, next) {
    const t = undefined;
    let { organizationDepartmentId, organizationId, taxCode } = req.account;
    try {
      let { connectionSupplierHistoryId } = req.body;

      let listInvoice;
      if (!connectionSupplierHistoryId)
        throw new Error(ERROR_MISSING_PARAMETERS);

      if (!organizationDepartmentId && !organizationId)
        throw new Error(ERROR_PERMISSION_DENIED);

      /* neu dang dong bo => throw error */
      let dangDongBo = false;

      if (organizationDepartmentId) {
        dangDongBo = (
          await OrganizationDepartment.findOne({
            where: { organizationDepartmentId },
            transaction: t,
          })
        )?.dangDongBo;
      } else {
        dangDongBo = (
          await Organization.findOne({
            where: { organizationId },
            transaction: t,
          })
        )?.dangDongBo;
      }

      if (dangDongBo) {
        throw new Error(ERROR_DANG_DONG_BO);
      }

      /* set dangDongBo = true */
      if (organizationId) {
        await Organization.update(
          { dangDongBo: true },
          { where: { organizationId }, transaction: t },
        );
      } else if (organizationDepartmentId) {
        await OrganizationDepartment.update(
          { dangDongBo: true },
          { where: { organizationDepartmentId }, transaction: t },
        );
      }

      // if (!from || !to) throw new Error(ERROR_MISSING_PARAMETERS);

      // if (!isValidDate(from) || !isValidDate(to))
      //   throw new Error(ERROR_INVALID_PARAMETER);

      const findConnectionSupplierHistory =
        await ConnectionSupplierHistory.findOne({
          where: {
            connectionSupplierHistoryId,
          },
        });

      if (!findConnectionSupplierHistory) throw new Error(ERROR_NOT_FOUND);

      const connectionSuppliers = await ConnectionSupplier.findAll({
        where: {
          organizationDepartmentId,
          organizationId,
        },
        include: {
          model: Supplier,
        },
      });

      if (!connectionSuppliers.length) throw new Error(ERROR_NOT_FOUND);

      res.send({
        result: 'success',
      });

      await connectionSupplierHelper.synchronize(
        findConnectionSupplierHistory.syncDate,
        findConnectionSupplierHistory.syncDate,
        { organizationDepartmentId, organizationId, taxCode },
        t,
      );

      await t?.commit();

      try {
        let [noti] =
          await notificationHelper.createNotificationAndSendSyncedEvent([
            {
              type: NOTIFICATION_TYPE.ACTIVITY,
              targetCode: TARGET_CODE.SUPPLIER_SYNC,
              title: `Hoàn tất đồng bộ danh sách hoá đơn từ NCC ngày ${moment(
                findConnectionSupplierHistory.syncDate,
              ).format('DD/MM/YYYY')}`,
              Accounts: [{ accountId: req.account.accountId }],
            },
          ]);
      } catch (error) {}

      /* set dangDongBo => false */
      if (organizationId) {
        await Organization.update(
          { dangDongBo: false },
          { where: { organizationId }, transaction: t },
        );
      } else if (organizationDepartmentId) {
        await OrganizationDepartment.update(
          { dangDongBo: false },
          { where: { organizationDepartmentId }, transaction: t },
        );
      }
    } catch (error) {
      /* set dangDongBo => false */
      if (organizationId) {
        await Organization.update(
          { dangDongBo: false },
          { where: { organizationId }, transaction: t },
        );
      } else if (organizationDepartmentId) {
        await OrganizationDepartment.update(
          { dangDongBo: false },
          { where: { organizationDepartmentId }, transaction: t },
        );
      }

      await t?.rollback();
      next(error);
    }
  },
};
