const { Op } = require('sequelize');
const {
  ERROR_NOT_FOUND,
  ERROR_USER_NOT_FOUND,
} = require('../configs/error.vi');
const {
  ResourceHistory,
  Account,
  Company,
  Config,
  Sequelize,
} = require('../models');
const _ = require('lodash');
const moment = require('moment');
const {
  SUPPLY_TYPE,
  RESOURCE_HISTORY_STATUS,
} = require('../configs/constants/resource-history.constant');
const { parseBoolean, isValidNumber } = require('../utils');
const { PACKAGE_TYPE } = require('../configs/constants/package.constant');
const { getPublicAccountAttributes } = require('../utils/account.helper');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      const { companyId } = req.account;
      const resourceHistories = await ResourceHistory.findAll({
        where: { companyId },
      });
      if (!_.isArray(resourceHistories)) throw new Error(ERROR_NOT_FOUND);
      res.send({
        result: 'success',
        resourceHistories,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async findByMetaAccountCode(req, res, next) {
    try {
      let { metaAccountCode } = req.params;

      let account = await Account.findOne({ where: { metaAccountCode } });

      if (!account) throw new Error(ERROR_USER_NOT_FOUND);

      let resourceHistories = await ResourceHistory.findAll({
        where: {
          companyId: account.companyId,
          supplyType: { [Op.ne]: SUPPLY_TYPE.ORDER },
        },
        order: [['createdAt', 'desc']],
      });

      res.send({
        result: 'success',
        packageHistory: resourceHistories,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async doiSoat(req, res, next) {
    try {
      let {
        from,
        to,
        metaAccountCodes,
        sapHetHan,
        trang_thai_hoat_dong,
        packageCode,
        remainingAmount,
        diffDate,
        page,
        limit,
        dealerCode,
      } = req.body;

      let configDuration = await Config.findOne({
        where: { key: 'DEFAULT_ALMOST_EXPIRED_DATE_DIFF' },
      });

      let configQuantity = await Config.findOne({
        where: { key: 'DEFAULT_ALMOST_EXPIRED_QUANTITY_REMAIN' },
      });

      let accounts =
        true || metaAccountCodes || sapHetHan
          ? await Account.findAll({
              attributes: getPublicAccountAttributes(),
              where: [
                metaAccountCodes
                  ? {
                      metaAccountCode: { [Op.in]: metaAccountCodes },
                    }
                  : {
                      metaAccountCode: { [Op.not]: null },
                    },
                parseBoolean(sapHetHan)
                  ? [
                      {
                        [Op.or]: [
                          Sequelize.literal(
                            `IFNULL(Company.quantityRemain, 0) + IFNULL(Company.promotionRemain, 0) <= ${
                              configQuantity?.value ?? 10
                            }`,
                          ),
                          Sequelize.literal(
                            `Company.dateExpiredPackage IS NOT NULL AND DATEDIFF(Company.dateExpiredPackage, '${moment().format(
                              'YYYY-MM-DD HH:mm:ss',
                            )}') > 0 AND DATEDIFF(Company.dateExpiredPackage, '${moment().format(
                              'YYYY-MM-DD HH:mm:ss',
                            )}') <= ${configDuration?.value ?? 30}`,
                          ),
                        ],
                      },
                    ]
                  : [],
                {
                  loaiKhachHang: { QUAN_LY_HOA_DON: { [Op.like]: '%%' } },
                },
              ],
              include: [{ model: Company }],
            })
          : undefined;

      // if (sapHetHan) {
      //   if (parseBoolean(sapHetHan)) {
      //     accounts = accounts.filter(i => i.sapHetHan);
      //   } else {
      //     accounts = accounts.filter(i => !i.sapHetHan);
      //   }
      // }

      let { rows, count } = await ResourceHistory.findAndCountAll({
        limit: isValidNumber(limit) && isValidNumber(page) ? limit : undefined,
        offset:
          isValidNumber(limit) && isValidNumber(page)
            ? limit * page
            : undefined,
        where: {
          [Op.and]: [
            // !parseBoolean(sapHetHan)
            //   ? { supplyType: { [Op.ne]: SUPPLY_TYPE.ORDER } }
            //   : {},
            { supplyType: { [Op.ne]: SUPPLY_TYPE.ORDER } },
            from
              ? {
                  supplyDate: {
                    [Op.gte]: moment(from)
                      .startOf('date')
                      .format('YYYY-MM-DD HH:mm:ss'),
                  },
                }
              : {},
            to
              ? {
                  supplyDate: {
                    [Op.lte]: moment(to)
                      .endOf('date')
                      .format('YYYY-MM-DD HH:mm:ss'),
                  },
                }
              : {},
            accounts
              ? { companyId: { [Op.in]: accounts.map(i => i.companyId) } }
              : {},
            trang_thai_hoat_dong
              ? {
                  status: parseBoolean(trang_thai_hoat_dong)
                    ? RESOURCE_HISTORY_STATUS.DANG_HOAT_DONG
                    : RESOURCE_HISTORY_STATUS.DA_HUY,
                }
              : {},
            packageCode ? { package: { packageCode } } : {},
            isValidNumber(remainingAmount)
              ? {
                  left: { [Op.lte]: remainingAmount },
                  packageType: PACKAGE_TYPE.QUANTITY,
                }
              : {},
            diffDate
              ? [
                  {
                    packageType: PACKAGE_TYPE.DURATION,
                  },
                  Sequelize.literal(
                    `DATE_ADD('supplyDate', INTERVAL 'total' DAY) <= '${moment()
                      .add(diffDate, 'days')
                      .format('YYYY-MM-DD HH:mm')}'`,
                  ),
                ]
              : {},
            dealerCode ? { supplyBy: { dealerCode } } : {},
          ],
          // active: true,
        },

        // logging: console.log,
        order: [['supplyDate', 'DESC']],
      });

      let resourceHistories = rows.map(i => i.toJSON());

      let accountDict = {};

      if (!accounts) {
        accounts = await Account.findAll({
          attributes: getPublicAccountAttributes(),
          where: {
            companyId: { [Op.in]: resourceHistories.map(i => i.companyId) },
          },
          include: [{ model: Company }],
        });
      }

      accounts.forEach(item => {
        accountDict[item.companyId] = item;
      });

      accounts = accounts.map(item => {
        if (
          item.Company.quantityRemain + item.Company.promotionRemain <=
            (configQuantity?.value ?? 10) ||
          (item.Company.dateExpiredPackage &&
            moment(item.Company.dateExpiredPackage).diff(undefined, 'days') >
              0 &&
            moment(item.Company.dateExpiredPackage).diff(undefined, 'days') <=
              (configDuration?.value ?? 30))
        ) {
          item.sapHetHan = true;
        }

        return item;
      });

      resourceHistories = resourceHistories.map(i => {
        i['Account'] = accountDict[i.companyId];
        i['sapHetHan'] = i.Account?.sapHetHan;

        return i;
      });

      res.send({
        result: 'success',
        resourceHistories,
        expiredQuantityRemain: configQuantity?.value ?? 10,
        expiredDateDiff: configDuration?.value ?? 30,
        total: count,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async huyGoiDichVu(req, res, next) {
    try {
      const { resourceHistoryId } = req.params;
      // const { active } = req.body;
      const resourceHistory = await ResourceHistory.findOne({
        where: { resourceHistoryId },
      });
      if (!resourceHistory) throw new Error(ERROR_NOT_FOUND);
      const company = await Company.findOne({
        where: { companyId: resourceHistory.companyId },
      });

      if (resourceHistory.status == RESOURCE_HISTORY_STATUS.DANG_HOAT_DONG) {
        await resourceHistory.update({
          active: false,
          status: RESOURCE_HISTORY_STATUS.DA_HUY,
          using: false,
        });

        if (resourceHistory.packageType == PACKAGE_TYPE.QUANTITY) {
          /* tru so luong */
          await company.update({
            quantityRemain: company.quantityRemain - resourceHistory.left,
            quantityPurchased:
              company.quantityPurchased - resourceHistory.total,
          });
        } else {
          /* tru thoi han su dung */
          await company.update({
            dateExpiredPackage: moment(company.dateExpiredPackage)
              .subtract(resourceHistory.left, 'days')
              .toDate(),
          });
        }
      }

      res.send({
        result: 'success',
        resourceHistory,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detailResourceHistory(req, res, next) {
    try {
      const { resourceHistoryId } = req.query;

      let resourceHistory = await ResourceHistory.findOne({
        where: {
          resourceHistoryId: resourceHistoryId,
        },
        include: [
          {
            model: Company,
            // where: { companyId: account.companyId },
          },
        ],
      });

      if (!resourceHistory) throw new Error(ERROR_NOT_FOUND);

      res.send({
        result: 'success',
        resourceHistory,
      });
    } catch (error) {
      next(error);
    }
  },
};
