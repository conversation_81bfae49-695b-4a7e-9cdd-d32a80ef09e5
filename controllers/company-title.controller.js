const { Op } = require('sequelize');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_NOT_FOUND,
  ERROR_COMPANY_NOT_EXISTED,
  ERROR_COMPANY_TITLE_EXISTED,
} = require('../configs/error.vi');
const { CompanyTitle, Company } = require('../models');
const { isValidNumber } = require('../utils');
const _ = require('lodash');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async create(req, res, next) {
    try {
      const { name } = req.body;
      const companyId = req?.account?.companyId;
      if (!name) throw new Error(ERROR_MISSING_PARAMETERS);

      const companyTitle = await CompanyTitle.create({
        name,

        companyId,
      });
      return res.send({
        result: 'success',
        companyTitle,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detail(req, res, next) {
    try {
      const { companyTitleId } = req.params;
      const companyTitle = await CompanyTitle.findOne({
        where: { companyTitleId },
      });
      if (!companyTitle) throw new Error(ERROR_NOT_FOUND);
      return res.send({
        result: 'success',
        companyTitle,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async find(req, res, next) {
    try {
      let { q, limit, page, orderField, orderMode } = req.query;
      q = q ?? '';
      orderField = orderField ?? 'updatedAt';
      orderMode = orderMode ?? 'DESC';

      let conditions = {
        [Op.and]: [
          { name: { [Op.like]: `%${q}%` } },
          { companyId: req?.account?.companyId },
        ],
      };

      if (!isValidNumber(limit) || !isValidNumber(page)) {
        const companyTitle = await CompanyTitle.findAll({
          where: conditions,
          order: [[orderField, orderMode]],
        });

        return res.send({
          result: 'success',
          companyTitle: companyTitle.map(r => r.toJSON()),
        });
      } else {
        limit = _.toNumber(limit);
        page = _.toNumber(page);

        const companyTitle = await CompanyTitle.findAndCountAll({
          where: conditions,
          limit,
          offset: limit * page,
        });
        res.send({
          result: 'success',
          companyTitle: companyTitle,
        });
      }
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async update(req, res, next) {
    try {
      const { companyTitleId } = req.params;
      const companyTitle = await CompanyTitle.findOne({
        where: { companyTitleId },
      });

      if (!companyTitle) throw new Error(ERROR_NOT_FOUND);

      const { name } = req.body;
      const companyId = req?.account?.companyId;

      if (name) {
        const cTitle = await CompanyTitle.findOne({
          where: {
            [Op.and]: [
              {
                name,
              },
              {
                companyId: companyId,
              },
              { companyTitleId: { [Op.ne]: companyTitleId } },
            ],
          },
        });
        if (cTitle) throw new Error(ERROR_COMPANY_TITLE_EXISTED);
      }

      await companyTitle.update({
        name,
      });

      return res.send({
        result: 'success',
        companyTitle,
      });
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async delete(req, res, next) {
    try {
      const { companyTitleIds } = req.body;
      if (!_.isArray(companyTitleIds))
        throw new Error(ERROR_MISSING_PARAMETERS);

      const companyTitleDelete = await CompanyTitle.destroy({
        where: { companyTitleId: { [Op.in]: companyTitleIds } },
      });
      res.send({
        result: 'success',
        companyTitleDelete,
      });
    } catch (error) {
      next(error);
    }
  },
};
