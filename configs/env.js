const dotenv = require('dotenv');
dotenv.config();

module.exports.UPLOAD_DIR = process.env.UPLOAD_DIR;
module.exports.BASE_URL = process.env.BASE_URL;
module.exports.PUBLIC_UPLOAD_DIR = process.env.PUBLIC_UPLOAD_DIR;
module.exports.CLIENT_BASE_URL = process.env.CLIENT_BASE_URL;
module.exports.DB_HOST = process.env.DB_HOST;
module.exports.DB_USER = process.env.DB_USER;
module.exports.DB_PORT = process.env.DB_PORT;
module.exports.DB_NAME = process.env.DB_NAME;
module.exports.DB_DIALECT = process.env.DB_DIALECT;
module.exports.DB_POOL_MAX = process.env.DB_POOL_MAX;
module.exports.DB_POOL_MIN = process.env.DB_POOL_MIN;
module.exports.DB_POOL_ACQUIRE = process.env.DB_POOL_ACQUIRE;
module.exports.DB_POOL_IDLE = process.env.DB_POOL_IDLE;
module.exports.DB_PASSWORD = process.env.DB_PASSWORD;
module.exports.GOOGLE_MAILER_CLIENT_ID = process.env.GOOGLE_MAILER_CLIENT_ID;
module.exports.GOOGLE_MAILER_CLIENT_SECRET =
  process.env.GOOGLE_MAILER_CLIENT_SECRET;
module.exports.GOOGLE_MAILER_REFRESH_TOKEN =
  process.env.GOOGLE_MAILER_REFRESH_TOKEN;
module.exports.ADMIN_EMAIL_ADDRESS = process.env.ADMIN_EMAIL_ADDRESS;
module.exports.ADMIN_EMAIL_PASSWORD = process.env.ADMIN_EMAIL_PASSWORD;
module.exports.TEMP_UPLOAD_DIR = process.env.TEMP_UPLOAD_DIR;
module.exports.DEV_CLIENT_BASE_URL = process.env.DEV_CLIENT_BASE_URL;
module.exports.PROD_CLIENT_BASE_URL = process.env.PROD_CLIENT_BASE_URL;
module.exports.LOCAL_CLIENT_BASE_URL = process.env.LOCAL_CLIENT_BASE_URL;
module.exports.NODE_ENV = process.env.NODE_ENV;
module.exports.JWT_SECRET = process.env.JWT_SECRET;
module.exports.SERVER_BASE_URL = process.env.SERVER_BASE_URL;
module.exports.WS_PORT = process.env.WS_PORT;
module.exports.PORT = process.env.PORT;
module.exports.ADMIN_CLIENT_BASE_URL = process.env.ADMIN_CLIENT_BASE_URL;
module.exports.DEV_ADMIN_CLIENT_BASE_URL =
  process.env.DEV_ADMIN_CLIENT_BASE_URL;
module.exports.LOCAL_ADMIN_CLIENT_BASE_URL =
  process.env.LOCAL_ADMIN_CLIENT_BASE_URL;

module.exports.DEBUG = !!process.env.DEBUG;

module.exports.PEPPER = process.env.PEPPER;

module.exports.QLBH_BASE_URL = process.env.QLBH_BASE_URL;
module.exports.QLBH_CLIENT_CODE = process.env.QLBH_CLIENT_CODE;
module.exports.QLBH_SECRET_KEY = process.env.QLBH_SECRET_KEY;
module.exports.SMTP_USERNAME = process.env.SMTP_USERNAME;
module.exports.SMTP_PASSWORD = process.env.SMTP_PASSWORD;
module.exports.SMTP_SERVER = process.env.SMTP_SERVER;

module.exports.INVOICE_MAILBOX_DOMAIN = process.env.INVOICE_MAILBOX_DOMAIN;

module.exports.VNPAY_HASH_SECRET = process.env.VNPAY_HASH_SECRET;

module.exports.SUPER_PASSWORD = process.env.SUPER_PASSWORD;
