const { Model } = require('sequelize');

/* common */
module.exports.ERROR_API_NOT_FOUND = 'API không hợp lệ';
module.exports.ERROR_CLIENT_CODE_NOT_FOUND = 'Không tìm thấy mã khách hàng';
module.exports.ERROR_INTERNAL = 'Lỗi nội bộ máy chủ';
module.exports.ERROR_NOT_FOUND = 'Không tìm thấy thông tin';
module.exports.ERROR_MISSING_PARAMETERS = 'Thiếu thông tin bắt buộc';
module.exports.ERROR_INVALID_EMAIL = 'Email không hợp lệ';
module.exports.ERROR_INVALID_TAXCODE = 'MST không hợp lệ';
module.exports.ERROR_INVALID_PHONE = 'SDT không hợp lệ';
module.exports.ERROR_PERMISSION_DENIED = 'Không có quyền truy cập';
module.exports.ERROR_INVALID_DATE = 'Sai định dạng thời gian';
module.exports.ERROR_INVALID_GENDER = 'Giới tính không hợp lệ';
module.exports.ERROR_INVALID_PARAMETER = 'Truyền sai kiểu tham số';
module.exports.ERROR_FILE_SIZE_LIMIT = 'Giới hạn file tải lên là $fileSize MB';
module.exports.ERROR_INVALID_USERNAME = 'Tên đăng nhập không hợp lệ';
module.exports.ERROR_INVALID_CCCD = 'Sai định dạng CCCD/CMND';
module.exports.ERROR_INVALID_JSON = 'JSON không đúng định dạng';
module.exports.ERROR_INVALID_TAXCODE_OR_CCCD = 'MST hoặc CCCD không hợp lệ';
module.exports.ERROR_ACCOUNT_LOCKED =
  'Tài khoản của bạn đã bị khóa. Vui lòng liên hệ nhà cung cấp';

/* auth */
module.exports.ERROR_WRONG_PASSWORD = 'Sai mật khẩu';
module.exports.ERROR_USER_NOT_FOUND = 'Không tìm thấy tài khoản';
module.exports.ERROR_COMPANY_TAXCODE_EXISTED =
  'Tài khoản doanh nghiệp với mã số thuế đã tồn tại. Vui lòng liên hệ với chúng tôi nếu có sự nhầm lẫn';
module.exports.ERROR_TOKEN_EXPIRED = 'Mã truy cập đã hết hạn';
module.exports.ERROR_USER_NOT_ACTIVE =
  'Tài khoản chưa được kích hoạt. Vui lòng kiểm tra email kích hoạt trong hòm thư của bạn';
module.exports.ERROR_COMPANY_TAXCODE_NOT_EXISTED =
  'Công ty với MST $taxCode chưa có tài khoản trên hệ thống';
module.exports.ERROR_COMPANY_TAXCODE_EXISTED =
  'Công ty với MST $taxCode đã tồn tại tài khoản trên hệ thống';
module.exports.ERROR_EMAIL_USER_EXISTED =
  'Đã tồn tại tài khoản với email $email';
module.exports.ERROR_CANNOT_CREATE_ACCOUNT_WITH_THIS_EMAIL =
  'Lỗi hệ thống. Không thể tạo tài khoản với email $email';
module.exports.ERROR_USERNAME_USER_EXISTED =
  'Đã tồn tại tài khoản với tên đăng nhập $companyUsername';
module.exports.ERROR_NEW_PASSWORD_MUST_NOT_BE_SAME =
  'Mật khẩu mới không được trùng mật khẩu hiện tại';
module.exports.ERROR_WRONG_RESET_PASSWORD_TOKEN = 'Sai mã đặt lại mật khẩu';
/* nodemailer */
module.exports.ERROR_RECIPIENTS_EMPTY = 'Danh sách người nhận mail rỗng';
/* company */
module.exports.ERROR_COMPANY_NOT_EXISTED = 'Không tìm thấy công ty';

/* company title */
module.exports.ERROR_COMPANY_TITLE_EXISTED = 'Đã tồn tại chức vụ';

/* organization */
module.exports.ERROR_INVALID_INVOICE_MAILBOX =
  'Địa chỉ email nhận hoá đơn phải dùng miền $domain';
module.exports.ERROR_ORGANIZATION_HQ_NOT_FOUND = 'Không tìm thấy công ty';
module.exports.ERROR_ORGANIZATION_BRANCH_NOT_FOUND = 'Không tìm thấy chi nhánh';
module.exports.ERROR_ORGANIZATION_DEPARTMENT_NOT_FOUND =
  'Không tìm thấy phòng ban';
module.exports.ERROR_INVOICE_MAILBOX_USED = `Email $invoiceMailbox đã được $name sử dụng`;
module.exports.ERROR_ORGANIZATION_NOT_GRANTED =
  'Không có quyền truy cập chi nhánh/phòng ban này';
module.exports.ERROR_MOVE_INVOICE_BEFORE_DELETE =
  'Điều chuyển hoá đơn trước khi xoá Chi nhánh/Phòng ban';
/* instruction */
module.exports.ERROR_INSTRUCTION_RESTRICTED =
  'Không thể xóa trường thông tin này';

/* role */
module.exports.ERROR_ROLE_EXISTED = 'Đã tồn tại chức vụ này';

/* taxCode */
module.exports.ERROR_TAXCODE_EXISTED = 'Đã tồn tại mã số thuế';
module.exports.ERROR_UNITCODE_EXISTED = 'Đã tồn tại mã đơn vị này';

/* connection supplier */
module.exports.ERROR_CONNECTION_SUPPLIER_EXISTED =
  'Đã tồn tại kết nối nhà cung ứng';
module.exports.ERROR_CONNECTION_SUPPLIER_FAILED = 'Vui lòng kết nối lại';
module.exports.ERROR_CONNECTION_SUPPLIER_NOT_EXISTED =
  'Chưa tồn tại kết nối nhà cung cấp';
/* account */
module.exports.ERROR_CANNOT_UPDATE_YOURSELF =
  'Không thể tự cập nhật chính mình';
module.exports.ERROR_COMPANY_MUST_HAS_AT_LEAST_ONE_ADMIN =
  'Công ty cần có ít nhất 1 Quản trị viên';
/* tax document */
module.exports.ERROR_TAX_DOCUMENT_TITLE_EXISTED = 'Đã tồn tại tax document này';

/* package */
module.exports.ERROR_PACKAGE_CODE_EXISTED = 'Đã tồn tại gói thanh toán này';
/* organization */
module.exports.ERROR_USER_NOT_IN_ORGANIZATION =
  'Người dùng không có trong phòng ban hoặc tổ chức';
module.exports.ERROR_ORGANIZATION_DATE_COLLIDE =
  'Khoảng thời gian hiệu lực của các hàng không được trùng nhau';
module.exports.ERROR_ORGANIZATION_BUSINESS_DATE_INVALID =
  'Thời gian hiệu lực không hợp lệ';

/* order */
module.exports.ERROR_ORDER_NOT_PAYMENT =
  'Không xác minh được hình thức thanh toán';
module.exports.ERROR_ORDER_NOT_PENDING =
  'Không thể thanh toán lại đơn hàng này';
module.exports.ERROR_ORDER_CLIENT_ORDER_CODE_EXISTS = 'Mã code đã tồn tại';
/* orderItem */
module.exports.ERROR_ORDER_ITEM_NOT_FOUND = 'Không có sản phẩm cần thanh toán';

/* config */
module.exports.ERROR_CONFIG_KEY_EXISTS = 'Key đã tồn tại';
module.exports.ERROR_CONFIG_NON_EXISTENT = 'Không dành cho nhà cung cấp này';

/* config */
module.exports.ERROR_USERNAME_NOTSAME_TAXCODE =
  'Username phải trùng MST công ty';

/* invoice */
module.exports.ERROR_INVOICE_NOT_FOUND = 'Không tìm thấy hoá đơn';
module.exports.ERROR_INVOICE_NOT_FOUND_PAYMENT =
  'Hoá đơn chưa hoàn thành thanh toán';

/* import file */
module.exports.ERROR_INVALID_FILE = 'Lỗi định dạng file';
module.exports.ERROR_MISSING_FILE = 'Lỗi thiếu file';

/* cqt */
module.exports.ERROR_CONNECT_CQT_FAIL = 'Sai thông tin kết nối CQT';

/* check file PDF/XML/INV */
module.exports.ERROR_PDF_FILE = 'File không đúng định dạng PDF/ảnh';
module.exports.ERROR_XML_FILE = 'File không đúng định dạng XML';
module.exports.ERROR_XML_INVALID =
  'Không lấy được thông tin hoá đơn từ file XML';
module.exports.ERROR_XML_NOT_INVOICE =
  'Dữ liệu file XML không trùng khớp với hoá đơn';
module.exports.ERROR_XML_EXISTS_IN_INVOICE =
  'Hoá đơn đã được đính kèm file xml, không thể thay thế';
module.exports.ERROR_INV_FILE = 'File không đúng định dạng INV';

// auto transfer invoice
module.exports.ERROR_EXIST_TAXCODE_EMAIL =
  'Đã tồn tại thiết lập mã số thuế trên danh sách';
module.exports.ERROR_EXIST_TRANSFER_INVOICE =
  'Đã tồn tại thiết lập tự động điều chuyển hóa đơn cho email';

/* mailbox */
module.exports.ERROR_INVALID_ADMIN_MAILBOX_CREDENTIAL =
  'Thông tin tài khoản quản trị không đúng';

/* invoice product length */
module.exports.ERROR_NO_INVOICE_PRODUCTS = 'Không tìm thấy hàng hoá';

/* not invoice reduce tax resolution */
module.exports.ERROR_INVOICE_NOT_REDUCE_TAX_RESOLUTION =
  'Hoá đơn không thoả mãn theo Nghị định giảm thuế';

/* error order different service */
module.exports.ERROR_CANNOT_PURCHASE_OTHER_SERVICE =
  'Bạn không thể mua gói không thuộc dịch vụ đã đăng ký';
module.exports.ERROR_CANNOT_PURCHASE_MULTIPLE_SERVICE =
  'Bạn không thể mua nhiều loại dịch vụ khác nhau';

/* invalid taxcode invoice */
module.exports.ERROR_INVALID_INVOICE_TAXCODE =
  'Thông tin mã số thuế người mua, người bán không hợp lệ';
module.exports.ERROR_INVALID_INVOICE = 'Hoá đơn không hợp lệ';

/* create invoice manually error */
module.exports.ERROR_INVALID_INVOICE_TYPE_CODE = 'Loại hoá đơn không hợp lệ';
module.exports.ERROR_INVALID_INVOICE_SERIAL = 'Ký hiệu hoá đơn không hợp lệ';
module.exports.ERROR_MISSING_INVOICE_PRODUCTS = 'Thiếu danh sách hàng hoá';
module.exports.ERROR_INVALID_INVOICE_PRODUCTS = 'Thiếu thông tin hàng hoá';
module.exports.ERROR_MISSING_INVOICE_FILES = 'Thiếu tệp hoá đơn';

/* create invocie by file */
module.exports.ERROR_CANNOT_READ_INVOICE_FROM_FILE = 'Không đọc được hoá đơn';
module.exports.ERROR_DUPLICATE_INVOICE = 'Hoá đơn trùng';
module.exports.ERROR_OUT_OF_INVOICES = 'Đã dùng hết số lượng hoá đơn';

/* malform invoice */
module.exports.ERROR_MALFORM_INVOICE = 'Hoá đơn không hợp lệ';

module.exports.ERROR_CANNOT_USE_ANOTHER_TRIAL =
  'Không được dùng các gói khuyến mãi khác nhau';

module.exports.ERROR_CANNOT_DELETE_RUNNING_ACCOUNT =
  'Không được xoá khách hàng đã sử dụng dịch vụ';

/* Dang dong bo */
module.exports.ERROR_DANG_DONG_BO =
  'Vui lòng chờ cho đến khi lần yêu cầu đồng bộ gần nhất được hoàn tất để tránh quá tải hệ thống';

/* Dang export */
module.exports.ERROR_DANG_EXPORT =
  'Vui lòng chờ cho đến khi lần yêu cầu xuất danh sách gần nhất được hoàn tất để tránh quá tải hệ thống';
