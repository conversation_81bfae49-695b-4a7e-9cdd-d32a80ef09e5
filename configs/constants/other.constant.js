/* Date time format */
module.exports.DATETIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';
module.exports.DATE_FORMAT = 'YYYY-MM-DD';
module.exports.HUMANIZE_DATETIME_FORMAT = 'HH:mm DD/MM/YYYY ';
module.exports.HUMANIZE_DATE_FORMAT = 'DD/MM/YYYY ';

/* Error code */
module.exports.REQ_ERROR_CODE = 400;

/* email type */
/**
 * @type {{[x in import('../../types').EmailType]:import('../../types').EmailType}}
 */
module.exports.EMAIL_TYPE = {
  ACTIVATE_ACCOUNT: 'ACTIVATE_ACCOUNT',
  NOTIFICATION: 'NOTIFICATION',
  OTHER: 'OTHER',
};
/**
 * @type {{[x in import('../../types').EmailStatus]:import('../../types').EmailStatus}}
 */
module.exports.EMAIL_STATUS = {
  DELIVERED: 'DELIVERED',
  FAILED: 'FAILED',
};
