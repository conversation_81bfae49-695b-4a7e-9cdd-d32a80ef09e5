/**
 * @type {{[x in import('../../types').INVOICE_TYPE]: import('../../types').INVOICE_TYPE}}
 */
module.exports.INVOICE_TYPE = {
  '01': '01',
  '02': '02',
  '03': '03',
  '04': '04',
  '05': '05',
  '06_01': '06_01',
  '06_02': '06_02',
};

/**
 * @type {{[x in import('../../types').INVOICE_TYPE_CODE]: import('../../types').INVOICE_TYPE_CODE}}
 */
module.exports.INVOICE_TYPE_CODE = {
  1: '1',
  2: '2',
  3: '3',
  4: '4',
  5: '5',
  6: '6',
};

module.exports.INVOICE_CATEGORY = {
  INPUT_INVOICE: 'INPUT_INVOICE',
  OUTPUT_INVOICE: 'OUTPUT_INVOICE',
};

/**
 * @type {{[x:string]: import('../../types').PAYMENT_METHOD}}
 */
module.exports.PAYMENT_METHOD = {
  TM: 'TM',
  CK: 'CK',
};

module.exports.OPTION_TIME_ANALYZE = {
  YESTERDAY: 'YESTERDAY', // hom qua
  LAST_7_DAYS: 'LAST_7_DAYS', // 7 ngày gần nhất
  THIS_WEEK: 'THIS_WEEK', // tuần này
  THIS_MONTH: 'THIS_MONTH', // tháng này
  LAST_MONTH: 'LAST_MONTH', // tháng trước
  LAST_30_DAYS: 'LAST_30_DAYS', // 30 ngày gần nhất
  THIS_YEAR: 'THIS_YEAR', // năm này
  CUSTOM: 'CUSTOM',
};
/**
 * @type {{[x:string]: import('../../types').PAYMENT_STATUS}}
 */
module.exports.PAYMENT_STATUS = {
  1: 'Chưa thanh toán',
  2: 'Thanh toán một phần',
  3: 'Thanh toán toàn phần',
  4: 'Đến hạn thanh toán',
  5: 'Quá hạn thanh toán',
};

module.exports.TAX_STATUS_TEXT = {
  NOT_CHECKED: 'Chưa kiểm tra',
  VALID: 'Chính xác',
  NOT_VALID: 'Không chính xác',
  CHECKING: 'Đang kiểm tra',
};

/**
 * @type {{[x in import('../../types').INVOICE_STATUS]: string}}
 */
module.exports.INVOICE_STATUS_TEXT = {
  1: 'Hoá đơn mới',
  2: 'Hoá đơn thay thế',
  3: 'Hoá đơn điều chỉnh',
  4: 'Hoá đơn đã bị thay thế',
  5: 'Hoá đơn đã bị điều chỉnh',
  6: 'Hoá đơn xoá bỏ',
  7: 'Hoá đơn chưa xác định',
  8: 'Hoá đơn nháp',
};

/**
 * @type {{[x in import('../../types').INVOICE_STATUS]: import('../../types').INVOICE_STATUS}}
 */
module.exports.INVOICE_STATUS = {
  1: '1',
  2: '2',
  3: '3',
  4: '4',
  5: '5',
  6: '6',
  7: '7',
  8: '8',
};

/**
 * @type {{[x:string]: import('../../types').INVOICE_HANDLE_STATUS}}
 */
module.exports.INVOICE_HANDLE_STATUS = {
  1: '1',
  2: '2',
  3: '3',
  4: '4',
  5: '5',
  6: '6',
  7: '7',
  8: '8',
  9: '9',
};

module.exports.DATE_TYPE = [
  {
    key: 1,
    attribute: '$invoiceDate$',
  },
  {
    key: 2,
    attribute: '$accountingDate$',
  },
  {
    key: 3,
    attribute: '$invoiceReceiveDate$',
  },
  {
    key: 4,
    attribute: '$reminderPaymentDate$',
  },
  {
    key: 5,
    attribute: '$expiredPaymentDate$',
  },
  {
    key: 6,
    attribute: '$expiredPaymentDate$',
  },
];

/**
 * @type {{[x in import('../../types').ProductType]: import('../../types').ProductType}}
 */
module.exports.PRODUCT_TYPE = {
  HHDV: 'HHDV',
  CKTM: 'CKTM',
  DG: 'DG',
  KM: 'KM',
};

/**
 * @type {{[x in import('../../types').VAT_TYPE]: import('../../types').VAT_TYPE}}
 */
module.exports.VAT_TYPE = {
  '0%': '0%',
  '10%': '10%',
  '8%': '8%',
  '5%': '5%',
  KCT: 'KCT',
  KHAC: 'KHAC',
  KKKNT: 'KKKNT',
};
/**
 * 1: 'Tải lên'
 * 2: 'Email'
 * 3: 'Tự động tải về'
 * 4: 'Nhập tay'
 * @type {{[x in import('../../types').TYPE_INVOICE_RECEIVE]: import('../../types').TYPE_INVOICE_RECEIVE}}
 */
module.exports.TYPE_INVOICE_RECEIVE = {
  1: '1',
  2: '2',
  3: '3',
  4: '4',
};

/**
 * @type {{[x in import('../../types').INVOICE_CHECK_STATUS]: import('../../types').INVOICE_CHECK_STATUS}}
 */
module.exports.INVOICE_CHECK_STATUS = {
  1: '1',
  2: '2',
  3: '3',
  4: '4',
  5: '5',
  6: '6',
};

/**
 * @type {{[x in import('../../types').INVOICE_CHECK_STATUS]: string}}
 */
module.exports.INVOICE_CHECK_STATUS_TEXT = {
  1: 'Đang xử lý',
  2: 'Đã xử lý',
  3: 'Hoá đơn trùng',
  4: 'Hoá đơn bỏ qua',
  5: 'Tải thất bại',
  6: 'Không được xử lý',
};

/**
 * Những tên thẻ có thể đặt làm mã tra cứu)
 */
module.exports.POSSIBLE_LOOKUP_CODE_XML_TAG = [
  'Mã số bí mật',
  'Mã tra cứu',
  'Mã tra cứu hóa đơn',
  'TransactionID',
  'Fkey',
  'Mã TC',
  'MaTraCuu',
  'Số bảo mật',
  'KeySearch',
  'MTCuu',
  'Mã bí mật',
];

/**
 * Những tên thẻ có thể đặt làm link tra cứu)
 */
module.exports.POSSIBLE_LOOKUP_WEBSITE_XML_TAG = [''];

/**
 * @type {{[x in import('../../types').TaxReduceValidateCheckResult]: import('../../types').TaxReduceValidateCheckResult}}
 */
module.exports.TAX_REDUCE_VALIDATE_RESULT = {
  CHECKING: 'CHECKING',
  INVALID: 'INVALID',
  VALID: 'VALID',
  NOT_CHECKED: 'NOT_CHECKED',
};

module.exports.PROPERTIES_INVOICE_EXPORT = {
  radioInputInvoice: [
    'tenCongTy',
    'mstCongTy',
    'tenChiNhanh',
    'mstChiNhanh',
    'tenPhongBan',
    'tenDonViMuaHang',
    'nguoiMuaHang',
    'diaChiBenMua',

    'emailNhanHoaDon',
    'mauSo',
    'kyHieu',
    'soHoaDon',
    'ngayNhanHoaDon',
    'mstNcc',
    'ngayHoaDon',
    'ngayKy',
    'ngayCapMa',
    'ngayHachToan',
    'nguoiHachToan',
    'maDonVi',
    'nhaCungCap',
    'diaChiNCC',
    'maSoThue',
    'tongTien',
    'loaiTien',
    'tyGia',
    'tongTienQuyDoi',
    'ketQuaKiemTra',
    'trangThaiGuiEmail',
    'kiemTraThueSuat',
    'trangThaiHoaDon',
    'ngayThayDoiTrangThayTrenCQT',
    'ngayPhatHienThayDoiTrangThai',
    'ngayNhacHanThanhToan',
    'hanThanhToan',
    'trangThaiThanhToan',
    'thongTinThanhToan',
    'tongTienDaThanhToan',
    'tienChuaThanhToan',
    'nhanDan',
    'soChungTu',
    'ghiChu',
    'tepHoaDon',
  ],
};
