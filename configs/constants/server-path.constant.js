/* api paths */
module.exports.PATH_PUBLIC_DIR = '/resources';

module.exports.PATH_UPLOAD_DIR = '/uploads';

module.exports.PATH_TEMP_DIR = '/temp';

module.exports.PATH_CQT = '/api/v1/cqt';
module.exports.PATH_TEST = '/api/v1/test';

module.exports.PATH_ACCOUNT_ACTIVATE = '/api/v1/account/activate';
module.exports.PATH_ACCOUNT_LOGIN = '/api/v1/account/login';
module.exports.PATH_ACCOUNT_LOGOUT = '/api/v1/account/logout';
module.exports.PATH_ACCOUNT_REGISTER = '/api/v1/account/register';
module.exports.PATH_ACCOUNT_DELETE = '/api/v1/account/delete';
module.exports.PATH_ACCOUNT_REQUEST_RESET_PASSWORD =
  '/api/v1/account/request-reset-password';
module.exports.PATH_ACCOUNT_RESET_PASSWORD = '/api/v1/account/reset-password';
module.exports.PATH_FIND_DEALER_BY_REFERER_CODE =
  '/api/v1/account/find-dealer-by-referer-code';
module.exports.PATH_MISC = '/api/v1/misc';
module.exports.PATH_TEST = '/api/v1/test';
module.exports.PATH_COMPANY_FIND_BY_TAXCODE =
  '/api/v1/company/find-by-tax-code';
module.exports.PATH_COMPANY_FIND_BY_TAXCODES =
  '/api/v1/company/find-by-tax-codes';
module.exports.PATH_COMMENT_CUSTOMER_FIND = '/api/v1/comment-customer/find';
module.exports.PATH_CONTACT_CUSTOMER_CREATE = '/api/v1/contact-customer/create';
module.exports.PATH_PACKAGE_FIND = '/api/v1/package/find';
module.exports.PATH_PACKAGE_DETAIL = '/api/v1/package/detail';

module.exports.PATH_CONFIG_FIND = '/api/v1/config/find';

module.exports.PATH_VNPAY_IPN = '/api/v1/order/vnpay-ipn';

module.exports.PATH_FAQ_FIND = '/api/v1/faq/find';
module.exports.PATH_FAQ_DETAIL = '/api/v1/faq/detail';

module.exports.PATH_INSTRUCTION_FIND = '/api/v1/instruction/find';

module.exports.PATH_QLBH = '/api/v1/qlbh';

module.exports.PATH_RISKY_COMPANY_FIND = '/api/v1/risky-company/find';

module.exports.PATH_VALIDATE_ACCESS_TOKEN =
  '/api/v1/account/validate-access-token';

module.exports.PATH_FIND_DEALER_BY_DEALER_CODE =
  '/api/v1/account/find-dealer-by-referer-code';

module.exports.PATH_EMAIL_HISTORY_RESEND = '/api/v1/email-history/resend';

module.exports.PATH_EMAIL_HISTORY_FIND = '/api/v1/email-history/find';

module.exports.PATH_EMAIL_HISTORY_DELETE = '/api/v1/email-history/delete';
