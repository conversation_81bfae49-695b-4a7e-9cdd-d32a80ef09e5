module.exports.PERMISSION_ROUTES = [
  '/api/v1/invoice/isDeleted',
  '/api/v1/invoice/accounting',
  '/api/v1/invoice/payment',
  '/api/v1/invoice/move',
  '/api/v1/mail/into-trash',
  '/api/v1/mail/out-trash',
  '/api/v1/organization/create',
  '/api/v1/organization/update',
  '/api/v1/organization/delete',
  '/api/v1/accept-partner-company-detail/create',
];

module.exports.ROLE_PERMISSION_ROUTES = {
  DELETE_AND_RESTORE_INPUT_INVOICE: ['/api/v1/invoice/isDeleted'],
  ACCOUNTED_MARK_INPUT_INVOICE: ['/api/v1/invoice/accounting'],
  PAID_MARK_INPUT_INVOICE: [
    // '/api/v1/invoice/payment/:invoiceId',
    '/api/v1/invoice/payment',
  ],
  TRANSFER_INPUT_INVOICE: ['/api/v1/invoice/move'],
  DELETE_AND_RESTORE_OUTPUT_INVOICE: ['/api/v1/invoice/isDeleted'],
  TRANSFER_OUTPUT_INVOICE: ['/api/v1/invoice/move'],

  DELETE_AND_RESTORE_MAILBOX: [
    '/api/v1/mail/into-trash',
    '/api/v1/mail/out-trash',
  ],

  CREATE_ORGANIZATION_STRUCTURE: ['/api/v1/organization/create'],
  UPDATE_ORGANIZATION_STRUCTURE: [
    // '/api/v1/organization/update/:organizationId',
    '/api/v1/organization/update',
  ],
  DELETE_ORGANIZATION_STRUCTURE: ['/api/v1/organization/delete'],

  ACCEPT_BUYER_SELLER_INFO: [
    // '/api/v1/accept-partner-company-detail/create/:invoiceId',
    '/api/v1/accept-partner-company-detail/create',
  ],
  // ACCEPT_DIGITAL_SIGNATURE_INFO: [],
  // ACCEPT_STATE_ACTIVE_AND_RISK_SELLER: [],
  // NOT_CHECK_EXIST_IN_TAX_AGENCY: [],
  // INVOICE_SUPPLIER: [],
  // TAX_ORGANIZATION: [],
};
