/**
 * @type {{[x in import('../../types').StatusValidMail]: import('../../types').StatusValidMail}}
 */

module.exports.STATUS_VALID_MAIL = {
  VALID_INVOICE: 'VALID_INVOICE',
  INVALID_INVOICE: 'INVALID_INVOICE',
  INVALID_EMAIL: 'INVALID_EMAIL',
  DUPLICATE_INVOICE: 'DUPLICATE_INVOICE',
  MALFORMED_INVOICE: 'MALFORMED_INVOICE',
  EXPIRE_DATE: 'EXPIRE_DATE',
  DELETE_INVOICE: 'DELETE_INVOICE',
  DRAFT: 'DRAFT',
  CHECKING: 'CHECKING',
  INVOICE_NOT_FOUND: 'INVOICE_NOT_FOUND',
  OUT_OF_DATE: 'OUT_OF_DATE',
};

module.exports.TYPE_MAIL = {
  IMPORTANT: 'IMPORTANT',
  TRASH: 'TRASH',
};

module.exports.READ_STATUS = {
  READ: 'READ',
  UNREAD: 'UNREAD',
};

module.exports.TYPE_SEND_MAIL = {
  CC: 'CC',
  BCC: 'BCC',
  TO: 'TO',
};

/* zimbra mail server */
module.exports.ZIMBRA_SOAP_BASE_URL =
  'https://mail.i-ca.vn:7071/service/admin/soap';
module.exports.ZIMBRA_SOAP_ADMIN_AUTH_REQUEST =
  '/service/admin/soap/AuthRequest';
module.exports.ZIMBRA_SOAP_CREATE_ACCOUNT = '/CreateAccountRequest';
module.exports.ZIMBRA_SOAP_DETAIL_ACCOUNT = '/GetAccountInfoRequest';
module.exports.ZIMBRA_SOAP_DELETE_ACCOUNT = '/DeleteAccount';
module.exports.ZIMBRA_SOAP_SET_PASSWORD = '/SetPasswordRequest';
module.exports.ZIMBRA_ADMIN_EMAIL_ADDRESS =
  process.env.ZIMBRA_ADMIN_EMAIL_ADDRESS;
module.exports.ZIMBRA_ADMIN_PASSWORD = process.env.ZIMBRA_ADMIN_PASSWORD;
module.exports.ZIMBRA_MAIL_HOST_SERVER = 'mail.i-ca.vn';
module.exports.ZIMBRA_DEFAULT_EMAIL_PASSWORD =
  process.env.ZIMBRA_DEFAULT_EMAIL_PASSWORD;
