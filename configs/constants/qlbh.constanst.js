module.exports.QLBH_CLIENT_CODE_REGISTER =
  '/api/v1/account/client-code-register';
module.exports.QLBH_FIND_DEALER_BY_DEALER_CODE =
  '/api/v1/account/find-dealer-by-dealer-code';

module.exports.QLBH_CLIENT_CODE_LOGIN = '/api/v1/account/client-code-login';

module.exports.QLBH_CLIENT_CODE_CREATE_ACCOUNT =
  '/api/v1/account/client-code-create-account';

module.exports.QLBH_ACCOUNT_FIND_BY_META_ACCOUNT_CODES =
  '/api/v1/account/find-by-meta-account-codes';

module.exports.QLBH_CLIENT_CODE_CHANGE_PASSWORD =
  '/api/v1/account/client-code-change-password';

module.exports.QLBH_CLIENT_CODE_UPDATE = '/api/v1/account/client-code-update';

module.exports.QLBH_CLIENT_CODE_GET_VOUCHER_BY_CODE =
  '/api/v1/voucher/client-code-get-voucher-by-code';

module.exports.QLBH_CLIENT_CODE_FIND_VOUCHER =
  '/api/v1/voucher/client-code-find';

module.exports.QLBH_CLIENT_CODE_USE_VOUCHER = '/api/v1/voucher/client-code-use';

module.exports.QLBH_CLIENT_CODE_REQUEST_RESET_PASSWORD =
  '/api/v1/account/request-reset-password';

module.exports.QLBH_CLIENT_CODE_RESET_PASSWORD =
  '/api/v1/account/reset-password';

module.exports.QLBH_CLIENT_ACTIVATE_ACCOUNT = '/api/v1/account/activate';

module.exports.QLBH_NOTIFY_COMPANIES = '/api/v1/no-auth/company-notify';

module.exports.QLBH_TT_HOP_DONG = '/api/v1/customer/trang-thai-hop-dong';
