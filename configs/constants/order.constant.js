const { NODE_ENV } = require('../env');

/**
 * @type {{[x in import('../../types').TypePayment]: import('../../types').TypePayment}}
 */
module.exports.TYPE_PAYMENT = {
  TRANSFER: 'TRANSFER',
  VNPAY: 'VNPAY',
};
/**
 * @type {{[x in import('../../types').Position]: import('../../types').Position}}
 */
module.exports.POSITION = {
  'Tổng giám đốc': 'Tổng giám đốc',
  'Giám đốc': 'Giám đốc',
  'Kế toán trưởng': 'Kế toán trưởng',
  'Kế toán': 'Kế toán',
  Khác: 'Khác',
};
/**
 * @type {{[x in import('../../types').StatusOrder]: import('../../types').StatusOrder}}
 */
module.exports.STATUS_ORDER = {
  PENDING: 'PENDING',
  COMPLETE: 'COMPLETE',
  CANCEL: 'CANCEL',
  FAILED: 'FAILED',
};

/* VNPAY */
module.exports.VNP_RETURN_URL = '/store/vnpay-return';
module.exports.TMN_CODE = NODE_ENV == 'dev' ? 'ICORP002' : 'ICORPJSC';
module.exports.VNP_URL =
  NODE_ENV == 'dev'
    ? 'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html'
    : 'https://pay.vnpay.vn/vpcpay.html';
module.exports.VNPAY_LOG_FILE_PATH = './vnpay.log';
