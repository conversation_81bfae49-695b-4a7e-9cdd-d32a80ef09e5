/**
 * @type {{[x in import('../../types').PermissionCode]: import('../../types').PermissionCode}}
 */

module.exports.PERMISSION_CODE_INPUT_INVOICE = {
  DELETE_AND_RESTORE: 'DELETE_AND_RESTORE',
  TRANSFER: 'TRANSFER',
};

module.exports.PERMISSION_CODE_OUTPUT_INVOICE = {
  DELETE_AND_RESTORE: 'DELETE_AND_RESTORE',
  TRANSFER: 'TRANSFER',
};

module.exports.PERMISSION_CODE_MAILBOX = {
  DELETE_AND_RESTORE: 'DELETE_AND_RESTORE',
};

module.exports.PERMISSION_CODE_ORGANIZATION = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
};
