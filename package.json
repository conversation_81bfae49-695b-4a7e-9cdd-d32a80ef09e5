{"name": "HDDV Backend", "version": "1.0.0", "description": "Backend Hoá đơn đầu vào", "main": "index.js", "type": "commonjs", "scripts": {"test": "test", "start": "node index.js", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier . --write", "prepare": "husky install", "nodemon": "nodemon index.js", "postinstall": "patch-package"}, "repository": {"type": "git", "url": "https://git.mdcgate.com:8443/icorp1/einvoice/hddv-backend.git"}, "keywords": ["icorp", "qlbh"], "author": "MDC MEDIA CO.,LTD", "license": "ISC", "devDependencies": {"@types/express": "^4.17.17", "@types/express-fileupload": "^1.4.1", "@typescript-eslint/eslint-plugin": "^5.60.1", "@typescript-eslint/parser": "^5.60.1", "eslint": "^8.44.0", "eslint-config-prettier": "^8.8.0", "husky": "^8.0.3", "lint-staged": "^13.2.3", "nodemon": "^2.0.22", "patch-package": "^8.0.0", "prettier": "2.8.8", "typescript": "^5.1.6", "xlsx": "^0.18.5"}, "dependencies": {"@babel/register": "^7.22.5", "@chilkat/ck-node16-linux64": "^9.50.95", "@chilkat/ck-node18-win64": "^9.50.96", "@peculiar/webcrypto": "^1.4.3", "axios": "^1.4.0", "body-parser": "^1.20.2", "compression": "^1.7.4", "cors": "^2.8.5", "decompress": "^4.2.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-fileupload": "^1.4.0", "form-data": "^4.0.0", "google-auth-library": "^8.9.0", "handlebars": "^4.7.8", "html-pdf-node": "^1.0.8", "imap": "^0.8.19", "jquery": "^3.7.0", "jsdom": "^22.1.0", "jsonwebtoken": "^9.0.0", "lodash": "^4.17.21", "mailparser": "^3.6.5", "mariadb": "^3.2.3", "method-override": "^3.0.0", "moment": "^2.29.4", "morgan": "^1.10.0", "mysql2": "^3.4.3", "node-cron": "^3.0.2", "node-gyp": "^10.0.1", "node-html-parser": "^6.1.8", "node-webcrypto-ossl": "^2.1.3", "nodemailer": "^6.9.3", "npm": "^10.0.0", "pdfmake": "^0.2.7", "qs": "^6.11.2", "sequelize": "^6.32.1", "ua-parser-js": "^1.0.35", "websocket": "^1.0.34", "xml2js": "^0.6.2", "xmldsigjs": "^2.5.1"}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint --cache --fix", "*.{js,css,md}": "prettier --write"}}