# Kết nối websocket HHDV

- Địa chỉ:
  - wss://hddv-dev.vietinvoice.vn/ws (DEV)
  - null (PROD)

## Thông điệp

Tất cả message gửi dạng chuỗi JSON

```typescript
type TargetCode = 'SUPPLIER_SYNC' | 'PARTNER_SYNC' | 'CQT_SYNC' | ...;
```

1. <PERSON><PERSON><PERSON> nh<PERSON>p

```json
{
  "code": "00",
  "accessToken": ""
}
```

- <PERSON><PERSON><PERSON> hồi

```typescript
{
  code: "01",
  result: "success"|"failed",
  reason: string
}
```

2. <PERSON><PERSON><PERSON> xuất

```json
{
  "code": "01"
}
```

- <PERSON><PERSON><PERSON> hồ<PERSON>

```json
{
  "code": "02",
  "result": "success"
}
```

3. Server gửi khi có thông báo mới

```typescript
{
  code: "600",
  result: "success",
  message: string,
  targetCode: TargetCode
}
```

4. Server gửi khi có kết quả lịch sử đồng bộ, kết quả ktra ...

```typescript
{
  code: "03",
  result: "success",
  message: string,
  targetCode: TargetCode
}
```

5. <PERSON> gửi khi tài khoản thay đổi tổ chức/chi nhánh ...

```typescript
{
  code: "04",
  result: "success",
  message: string
}
```
