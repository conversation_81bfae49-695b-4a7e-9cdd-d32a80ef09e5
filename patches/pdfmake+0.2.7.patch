diff --git a/node_modules/pdfmake/src/printer.js b/node_modules/pdfmake/src/printer.js
index 3b578ce..1db7cec 100644
--- a/node_modules/pdfmake/src/printer.js
+++ b/node_modules/pdfmake/src/printer.js
@@ -481,7 +481,8 @@ function renderLine(line, x, y, patterns, pdfKitDoc) {
 	//TODO: line.optimizeInlines();
 	for (var i = 0, l = line.inlines.length; i < l; i++) {
 		var inline = line.inlines[i];
-		var shiftToBaseline = lineHeight - ((inline.font.ascender / 1000) * inline.fontSize) - descent;
+		// var shiftToBaseline = lineHeight - ((inline.font.ascender / 1000) * inline.fontSize) - descent;
+		var shiftToBaseline = (lineHeight - ((inline.font.ascender - inline.font.descender) / 1000 * inline.fontSize)) / 2;
 
 		if (inline._pageNodeRef) {
 			preparePageNodeRefLine(inline._pageNodeRef, inline);
diff --git a/node_modules/pdfmake/src/textTools.js b/node_modules/pdfmake/src/textTools.js
index 39f7edb..5cb9dbb 100644
--- a/node_modules/pdfmake/src/textTools.js
+++ b/node_modules/pdfmake/src/textTools.js
@@ -89,7 +89,7 @@ TextTools.prototype.sizeOfString = function (text, styleContextStack) {
 
 	return {
 		width: widthOfString(text, font, fontSize, characterSpacing, fontFeatures),
-		height: font.lineHeight(fontSize) * lineHeight,
+		height: /* font.lineHeight */(fontSize) * lineHeight,
 		fontSize: fontSize,
 		lineHeight: lineHeight,
 		ascender: font.ascender / 1000 * fontSize,
@@ -326,7 +326,7 @@ function measure(fontProvider, textArray, styleContextStack) {
 		var font = fontProvider.provideFont(fontName, bold, italics);
 
 		item.width = widthOfString(item.text, font, fontSize, characterSpacing, fontFeatures);
-		item.height = font.lineHeight(fontSize) * lineHeight;
+		item.height = /* font.lineHeight */(fontSize) * lineHeight;
 
 		if (!item.leadingCut) {
 			item.leadingCut = 0;
