import { Account } from './account';

export interface AuditLog {
  auditLogId: number;
  accountId: number;
  action: string;
  description: string;
  reference: string; // tham chiếu
  organization: string;
  ipAddress: string;
  extraData: string;

  /* associations */

  Account: Account;
}

export interface AuthLog {
  authLogId: number;
  accountId: number;
  device: string;
  browser: string;
  ipAddress: string;
  status: AuthLogStatus;
}

export type AuthLogStatus = 'SUCCESS' | 'FAILED';
