import { Account, Gender, Service } from './account';

/**
 * <PERSON><PERSON> chức (CTY, CHI NHÁNH) trong HDDV (ko có ràng buộc)
 */
export interface Organization {
  organizationId: number;
  parentOrganizationId: number;
  companyId: number;
  organizationName: string;
  organizationType: OrganizationType;
  /**
   * ends with `@icorp.vn`
   */
  invoiceMailbox: string;
  taxCode: string;
  businessPermitDate: Date;
  businessPermitAddress: string;
  // information connect to CQT
  linkWebsite: string;
  username: string;
  usernameOld: string;
  password: string;
  isInput: boolean;
  isOutput: boolean;
  cqtToken: string;
  cqtTokenExpiration: Date;

  // notification
  remindNotify: boolean;
  remindBeforeDays: number;

  // name file
  // mã số thuế người
  nameFileBuyerTaxCode: boolean;
  // ký
  nameFileSerial: boolean;
  // invoice date
  nameFileInvoiceDate: boolean;
  // mẫu số
  nameFileSample: boolean;
  // số hóa đơn
  nameFileInvoiceNum: boolean;
  // tên nhà cung cấp
  nameFileSupplierName: boolean;

  // hóa đơn đầu ra
  downloadAllOutputInvoice: boolean;
  downloadOutputInvoiceNotTable: boolean; // // Tải về hóa đơn không mã gửi theo hình thức bảng tổng hợp
  selectDownloadTypeOutputInvoice: {
    hoaDonDienTuGiaTriGiaTang: {
      hoaDonDienTu: boolean;
      khoiTaoTuMayTinhTien: boolean;
    };
    hoaDonDienTuBanHang: {
      hoaDonDienTu: boolean;
      khoiTaoTuMayTinhTien: boolean;
    };
    hoaDonDienTuBanTaiSanCong: boolean;
    hoaDonDienTuBanHangDuTruQuocGia: boolean;
    temVeTheDienTu: {
      hoaDonDienTu: boolean;
      khoiTaoTuMayTinhTien: boolean;
    };
    phieuXuatKhoDienTu: {
      khiemVanChuyenNoiBo: boolean;
      hangGuiBanDaiLy: boolean;
    };
  }; // Chọn lạoi hóa đơn tải

  // hóa đơn đầu vào
  adjustNotDownloadWithSupplier: boolean; // Thiết lập không tải về hóa đơn theo nhà cung cấp
  autoAdjustInvoice: boolean; // Tự động điều chuyển hóa đơn cho chi nhánh/phòng ban
  downloadInputInvoiceNotTable: boolean;
  selectDownloadTypeInputInvoice: {
    hoaDonDienTuGiaTriGiaTang: {
      hoaDonDienTu: boolean;
      khoiTaoTuMayTinhTien: boolean;
    };
    hoaDonDienTuBanHang: {
      hoaDonDienTu: boolean;
      khoiTaoTuMayTinhTien: boolean;
    };
    hoaDonDienTuBanTaiSanCong: boolean;
    hoaDonDienTuBanHangDuTruQuocGia: boolean;
    temVeTheDienTu: {
      hoaDonDienTu: boolean;
      khoiTaoTuMayTinhTien: boolean;
    };
    phieuXuatKhoDienTu: {
      khiemVanChuyenNoiBo: boolean;
      hangGuiBanDaiLy: boolean;
    };
  };

  // Thiết lập điều chuyển hóa đơn từ nguồn email
  autoAdjustWithBuyerTaxCode: boolean;

  // Thiết lập cảnh báo hóa đơn chưa chính xác về tính toán số liệu
  warningInListInvoice: boolean;

  /* thoong tin nguoi dai dien */
  representativeName: string;
  representativeEmail: string;
  representativePhone: string;
  representativeJobTitle: string;

  /* return by server */
  sellInvoices: number;
  buyInvoices: number;
  acceptPartnerCompanyDetails: AcceptPartnerCompanyDetail[];
  employees: Account[];

  /**
   * Dang dong bo
   */
  dangDongBo: boolean;
  /**
   * Dang export
   */
  dangExport: boolean;

  /* associations */
  /**
   * if is `HQ`
   */
  OrganizationBranches?: Organization[];
  /**
   * if is `BRANCH`
   */
  OrganizationDepartments?: OrganizationDepartment[];

  /**
   * if is `BRANCH`
   */
  OrganizationHQ?: Organization;

  OrganizationAddresses: OrganizationAddress[];
}

export type OrganizationType = 'HQ' | 'BRANCH';

export interface OrganizationAddress {
  organizationAddressId: number;
  organizationId: number;
  organizationName: string;
  businessPermitDateStart: Date;
  businessPermitDateEnd: Date;
  businessPermitAddress: string;
  acceptBy: string;
}

export interface OrganizationDepartment {
  organizationDepartmentId: number;
  departmentName: string;
  organizationId: number;
  // information connect to CQT
  linkWebsite: string;
  username: string;
  usernameOld: string;
  password: string;
  isInput: boolean;
  isOutput: boolean;
  /**
   * ends with `@icorp.vn`
   */
  invoiceMailbox: string;
  cqtToken: string;
  cqtTokenExpiration: Date;

  // notification
  remindNotify: boolean;
  remindBeforeDays: number;

  // name file
  // mã số thuế người
  nameFileBuyerTaxCode: boolean;
  // ký
  nameFileSerial: boolean;
  // invoice date
  nameFileInvoiceDate: boolean;
  // mẫu số
  nameFileSample: boolean;
  // số hóa đơn
  nameFileInvoiceNum: boolean;
  // tên nhà cung cấp
  nameFileSupplierName: boolean;

  // hóa đơn đầu ra
  downloadAllOutputInvoice: boolean;
  downloadOutputInvoiceNotTable: boolean; // // Tải về hóa đơn không mã gửi theo hình thức bảng tổng hợp
  selectDownloadTypeOutputInvoice: {
    hoaDonDienTuGiaTriGiaTang: {
      hoaDonDienTu: boolean;
      khoiTaoTuMayTinhTien: boolean;
    };
    hoaDonDienTuBanHang: {
      hoaDonDienTu: boolean;
      khoiTaoTuMayTinhTien: boolean;
    };
    hoaDonDienTuBanTaiSanCong: boolean;
    hoaDonDienTuBanHangDuTruQuocGia: boolean;
    temVeTheDienTu: {
      hoaDonDienTu: boolean;
      khoiTaoTuMayTinhTien: boolean;
    };
    phieuXuatKhoDienTu: {
      khiemVanChuyenNoiBo: boolean;
      hangGuiBanDaiLy: boolean;
    };
  }; // Chọn lạoi hóa đơn tải

  // hóa đơn đầu vào
  adjustNotDownloadWithSupplier: boolean; // Thiết lập không tải về hóa đơn theo nhà cung cấp

  //adjustNotDownloadOutputInvoiceWithSupplier: boolean;
  autoAdjustInvoice: boolean; // Tự động điều chuyển hóa đơn cho chi nhánh/phòng ban
  downloadInputInvoiceNotTable: boolean;
  selectDownloadTypeInputInvoice: {
    hoaDonDienTuGiaTriGiaTang: {
      hoaDonDienTu: boolean;
      khoiTaoTuMayTinhTien: boolean;
    };
    hoaDonDienTuBanHang: {
      hoaDonDienTu: boolean;
      khoiTaoTuMayTinhTien: boolean;
    };
    hoaDonDienTuBanTaiSanCong: boolean;
    hoaDonDienTuBanHangDuTruQuocGia: boolean;
    temVeTheDienTu: {
      hoaDonDienTu: boolean;
      khoiTaoTuMayTinhTien: boolean;
    };
    phieuXuatKhoDienTu: {
      khiemVanChuyenNoiBo: boolean;
      hangGuiBanDaiLy: boolean;
    };
  };

  // Thiết lập điều chuyển hóa đơn từ nguồn email: Tự động điều chuyển theo mã số thuế người mua
  autoAdjustWithBuyerTaxCode: boolean;

  // Thiết lập cảnh báo hóa đơn chưa chính xác về tính toán số liệu
  warningInListInvoice: boolean;

  /* thoong tin nguoi dai dien */
  representativeName: string;
  representativeEmail: string;
  representativePhone: string;
  representativeJobTitle: string;
  /* return by server */
  sellInvoices: number;
  buyInvoices: number;
  acceptPartnerCompanyDetails: AcceptPartnerCompanyDetail[];
  employees: Account[];

  /**
   * Dang dong bo
   */
  dangDongBo: boolean;
  /**
   * Dang export
   */
  dangExport: boolean;

  /* association */
  OrganizationBranch: Organization;
}

/**
 * Cty khi đăng ký lần đầu sử dụng dịch vụ
 */
export interface Company {
  companyId: number;
  companyName: string;
  companyLogo: string;
  taxCode: string;
  businessPermitDate: Date;
  businessPermitAddress: string;
  companyPhone: string;
  companyAddress: string;
  companyEmail?: string;
  bankName: string;
  bankAccountNumber: string;
  contactPersonName?: string;
  contactPersonPosition?: string;
  contactPersonEmail?: string;
  contactPersonPhone?: string;
  contactPersonDoB?: string;
  contactPersonGender: Gender;
  fax: string;
  website: string;
  quantityPurchased: number;
  quantityRemain: number;
  dateExpiredPackage: Date;
  service: Service;
  /* KM */
  promotionRemain: number;
  promotionExpired: Date;

  /* da ky hd voi NCC */
  daKyHopDongNCC: boolean;
  linkKyHopDong: string;

  districtCode: string;
  provinceCode: string;
  taxAuthority: string;
}

export interface SupplierNotDownloadInvoice {
  supplierNotDownloadInvoiceId: number;
  supplierId: number;
  organizationId: number;
  organizationDepartmentId: number;
}

export interface AutoTransferInvoice {
  autoTransferInvoiceId: number;

  organizationId: number;
  organizationDepartmentId: number;

  autoTransferOrganizationId: number;
  autoTransferOrganizationDepartmentId: number;

  partnerCompanyIds: number;
}

export interface AccountOrganizationAccess {
  accountOrganizationAccessId: number;
  accountId: number;
  organizationId: number;
  organizationDepartmentId: number;
}

export interface CompanyTitle {
  companyTitleId: number;
  name: string;
  companyId: number;
  description: string;
}

export interface Role {
  roleId: number;
  name: string;
  companyId: number;
  permissionCodes: string[];

  /* associations */
  Accounts: Account[];
  /* return by server */
  totalEmployees: number;
}

export interface PartnerCompany {
  partnerCompanyId: number;
  taxCode: string;
  organizationId: number;
  organizationDepartmentId: number;
  unitCode: string;
  partnerCompanyName: string;
  bankAccountNumber: string;
  bankAccountName: string;
  bank: string;
  bankBranch: string;
  expirePayment: number;
  contactName: string;
  contactEmail: string;
  contactPhone: string;
  contactPosition: string;
  dateCheck: Date | string;
  status: PartnerCompanyStatus;
  address: string;
  buyInvoices: number;
  sellInvoices: number;
  nntStatus: string;

  /* return by server */
  isRisky: boolean;
  acceptPartnerCompanyDetails: AcceptPartnerCompanyDetail[];
}

export type PartnerCompanyStatus = 'ACTIVE' | 'UNKNOWN' | 'INACTIVE';

/**
 * Doanh nghiep rui ro ve thue va hoa don
 */
export interface RiskyCompany {
  companyId: number;
  taxCode: string;
  companyName: string;
  cqtCode: string;
  cqtName: string;
  note: string;
  type: RiskyCompanyType;
}

export type RiskyCompanyType = 'RUI_RO' | 'BUON_BAN';

export interface AcceptPartnerCompanyDetail {
  acceptPartnerCompanyDetailId: number;
  acceptName: string;
  acceptAddress: string;
  from: Date;
  to: Date;
  acceptBy: number;
  taxCode: string;
  organizationId: number;
  organizationDepartmentId: number;

  /* association */
  Account: Account;
}

export interface Business {
  name: string;
  businessCode: string;
}

export interface BusinessCompany {
  businessCompanyId: number;
  businessCode: string;
  taxCode: string;

  Business: Business;
}
