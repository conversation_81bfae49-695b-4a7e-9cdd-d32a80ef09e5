export interface Mail {
  mailId: number;
  /**
   * Unique worldwide
   */
  messageId: string;
  subject: string;
  senderName: string;
  senderEmail: string;
  receiveDate: Date;
  content: string;
  isImportant: boolean;
  isTrash: boolean;
  readStatus: boolean;
  attachments: MailAttacment[];

  organizationId: number;
  organizationDepartmentId: number;

  StatusMails: StatusMail[];
}

export interface MailAttacment {
  filename: string;
  contentType: string;
  size: number;
  url: string;

  invoiceId?: number;
}

export interface StatusMail {
  statusMailId: number;
  mailId: number;
  statusValidMail: StatusValidMail;
  count: number;
}

export type StatusValidMail =
  | 'VALID_INVOICE'
  | 'INVALID_EMAIL'
  | 'INVALID_INVOICE'
  | 'DUPLICATE_INVOICE'
  | 'MALFORMED_INVOICE'
  | 'DRAFT'
  | 'CHECKING'
  | 'OUT_OF_DATE'
  | 'DELETE_INVOICE'
  | 'INVOICE_NOT_FOUND';
