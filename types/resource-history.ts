import { Order } from './order';
import { Package, PackageType } from './package';

export interface ResourceHistory {
  resourceHistoryId: number;
  supplyDate: Date;
  expiredDate: Date;
  total: number;
  description: string;
  left: number;
  package: Package;
  packageType: PackageType;
  using: boolean;

  /**
   * @deprecated
   * để lưu mã hợp đồng
   */
  metaContractCode: string;
  /**
   * được kích hoạt hay chưa
   */
  active: boolean;

  /**
   * Phương thức nạp  tài nguyên: tự mua/ ncc mua/ đl mua
   */
  supplyType: SupplyType;
  /**
   * Ng<PERSON><PERSON><PERSON> nạp hộ (nếu có)
   */
  supplyBy: any;
  order: Order;

  companyId: number;

  listPrice: number; // Giá niêm yết. Nếu cấp dứoi mua thì là giá cấp trên niêm yết cho
  purchasedPrice: number; // Giá NCC/ĐL tự điền (nếu có)

  /* TT goi: DANG_HOAT_DONG|DA_HUY */
  status: ResourceHistoryStatus;
}

export type SupplyType = 'ORDER' | 'DEALER_SUPPLY' | 'SUPPLIER_SUPPLY';
export type ResourceHistoryStatus = 'DANG_HOAT_DONG' | 'DA_HUY';
