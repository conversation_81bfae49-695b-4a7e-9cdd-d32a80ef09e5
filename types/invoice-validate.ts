export interface InvoiceValidate {
  invoiceValidateId: number;
  invoiceId: number;
  checkResultBuyerName: boolean;
  resultBuyerName: string;
  fromDateAcceptBuyerName: Date;
  toDateAcceptBuyerName: Date;
  checkResultBuyerTaxCode: boolean;
  resultBuyerTaxCode: string;
  fromDateAcceptBuyerTaxCode: Date;
  toDateAcceptBuyerTaxCode: Date;
  checkResultBuyerAddress: boolean;
  resultBuyerAddress: string;
  resultSellerTaxCode: string;
  checkResultSellerTaxCode: boolean;
  fromDateAcceptBuyerAddress: Date;
  toDateAcceptBuyerAddress: Date;
  checkResultSellerName: boolean;
  resultSellerName: string;
  fromDateAcceptSellerName: Date;
  toDateAcceptSellerName: Date;
  checkResultSellerAddress: boolean;
  resultSellerAddress: string;
  fromDateAcceptSellerAddress: Date;
  toDateAcceptSellerAddress: Date;
  checkResultSignatureNCC: boolean;
  checkResultSignatureCQT: boolean;
  /**
   * Có mã cqt cấp
   */
  checkResultHasInvoiceCode: boolean;
  /**
   * có kq tra cứu từ cqt
   */
  checkResultHasCQTRecord: boolean;
  checkDate: Date | string;

  /* TT CTS Hop le */
  cqt_thong_tin_cts_hop_le: boolean;
  cqt_cts_con_hieu_luc: boolean;
  cqt_file_xml_chua_bi_sua: boolean;
  cqt_cts_hop_le: boolean;

  nb_thong_tin_cts_hop_le: boolean;
  nb_cts_con_hieu_luc: boolean;
  nb_file_xml_chua_bi_sua: boolean;
  nb_cts_hop_le: boolean;

  /* nb dang hd */
  nb_dang_hoat_dong: boolean;
  nb_khong_rui_ro_tai_tdlap: boolean;
  /* tb phat hanh */
  mauso_kyhieu_da_tb: boolean;
  so_hd_thuoc_khoang_phat_hanh: boolean;
  ngay_hoa_don_tu_ngay_su_dung: boolean;
}
