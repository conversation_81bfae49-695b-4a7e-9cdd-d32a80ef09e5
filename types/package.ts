import { Service } from './account';

export type PackageType = 'DURATION' | 'QUANTITY' | 'LICENSE';
export type PackageUnit = 'VND' | 'USD';

export interface Package {
  packageId: number;
  packageCode: string;
  packageCost: string;
  unit: PackageUnit;
  packageType: PackageType;
  /**
   * TH gói SL. đ/v: số hđ
   */
  quantity: number;
  /**
   * TH gói thời hạn. đ/v: ngày
   */
  duration: number;
  description: string;
  recommend: boolean;
  service: Service;
  dealerMetaAccountCode: string;
  /* code de phan biet loai KM */
  kmCode: string;
  /* for dealer */
  dealerPackage: boolean;
}
