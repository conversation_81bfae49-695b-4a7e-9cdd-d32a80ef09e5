import {
  INVOICE_CATEGORY,
  INVOICE_CHECK_STATUS,
  INVOICE_TYPE_CODE,
} from './invoice';

export interface CqtInvoiceHistory {
  cqtInvoiceHistoryId: number;
  syncDate: Date | string;
  type: INVOICE_CATEGORY;
  organizationId: number;
  organizationDepartmentId: number;
  succeededDownload: number;
  failedDownload: number;
  processed: number;
  processing: number;
  duplicated: number;
  skipped: number;

  /* associations */
  CqtInvoices: CqtInvoice[];
}

/**
 *
 */
export interface CqtInvoice {
  cqtInvoiceId: number;
  cqtInvoiceHistoryId: number;
  invoiceCQTId: string;

  /**
   * ma CQT
   */
  maCQT: string;
  /**
   * ngày hóa đơn
   */
  invoiceDate: Date | string;
  /**
   * mẫu hóa đơn
   */
  invoiceTypeCode: INVOICE_TYPE_CODE;
  /**
   * số hóa đơn
   */
  invoiceNumber: string;
  /**
   * kí hiệu hóa đơn
   */
  serial: string;
  /**
   * mô tả chi tiết
   */
  description: string;
  /**
   * Nguoi ban
   */
  sellerName: string;
  sellerTaxCode: string;

  /**
   * Nguoi mua
   */
  buyerName: string;
  buyerTaxCode: string;

  invoiceCheckStatus: INVOICE_CHECK_STATUS;
  invoiceCheckStatusText: string;

  duplicateInvoiceId: number;
}
