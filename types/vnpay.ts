export interface VNPAY_PaymentParams {
  /**
   * <PERSON><PERSON><PERSON> bản api mà merchant kết nối. Phiên bản hiện tại là : 2.1.0
   */
  vnp_Version: string;
  /**
   * Mã <PERSON> sử dụng, mã cho giao dịch thanh toán là: pay
   */
  vnp_Command: string;
  /**
   * Mã website của merchant trên hệ thống của VNPAY. Ví dụ: 2QXUI4J4
   */
  vnp_TmnCode: string;
  /**
   * Số tiền thanh toán. Số tiền không mang các ký tự phân tách thập phân, phần nghìn,
   * ký tự tiền tệ. <PERSON><PERSON> gửi số tiền thanh toán là 10,000 VND
   * (mười nghìn VNĐ) thì merchant cần nhân thêm 100 lần
   * (khử phần thập phân), sau <PERSON><PERSON> g<PERSON> sang VNPAY là: 1000000
   */
  vnp_Amount: number;
  /**
   * <PERSON><PERSON> phương thức thanh toán, mã lo<PERSON><PERSON> ngân hàng hoặc ví điện tử thanh toán.
   *N<PERSON><PERSON> không gử<PERSON> sang tham số này, chuyển hướng người dùng sang VNPAY chọn phương thức thanh toán.
   *Lưu ý:
   *Các mã loại hình thức thanh toán lựa chọn tại website-ứng dụng của merchant
   *`vnp_BankCode=VNPAYQR` Thanh toán quét mã QR
   *`vnp_BankCode=VNBANK` Thẻ ATM - Tài khoản ngân hàng nội địa
   *`vnp_BankCode=INTCARD` Thẻ thanh toán quốc tế
   */
  vnp_BankCode: string;
  /**
   * Là thời gian phát sinh giao dịch định dạng yyyyMMddHHmmss (Time zone GMT+7) Ví dụ: **************
   */
  vnp_CreateDate: string;
  /**
   * Đơn vị tiền tệ sử dụng thanh toán. Hiện tại chỉ hỗ trợ VND
   */
  vnp_CurrCode: string;
  /**
   * Địa chỉ IP của khách hàng thực hiện giao dịch. Ví dụ: *************
   */
  vnp_IpAddr: string;
  /**
   * Ngôn ngữ giao diện hiển thị. Hiện tại hỗ trợ Tiếng Việt (vn), Tiếng Anh (en)
   */
  vnp_Locale: string;
  /**
     * Thông tin mô tả nội dung thanh toán quy định dữ liệu gửi sang VNPAY (Tiếng Việt không dấu và không bao gồm các ký tự đặc biệt)
  Ví dụ: Nap tien cho thue bao **********. So tien 100,000 VND
     */
  vnp_OrderInfo: string;
  /**
   * Mã danh mục hàng hóa. Mỗi hàng hóa sẽ thuộc một nhóm danh mục do VNPAY quy định. Xem thêm bảng Danh mục hàng hóa
   */
  vnp_OrderType: string;
  /**
   * URL thông báo kết quả giao dịch khi Khách hàng kết thúc thanh toán. Ví dụ: https://domain.vn/VnPayReturn
   */
  vnp_ReturnUrl: string;
  /**
   * Thời gian hết hạn thanh toán GMT+7, định dạng: yyyyMMddHHmmss
   */
  vnp_ExpireDate: string;
  /**
   * Mã tham chiếu của giao dịch tại hệ thống của merchant. Mã này là duy nhất dùng để phân biệt các đơn hàng gửi sang VNPAY. Không được trùng lặp trong ngày. Ví dụ: 23554
   */
  vnp_TxnRef: string;
  /**
   * Mã kiểm tra (checksum) để đảm bảo dữ liệu của giao dịch không bị thay đổi trong quá trình chuyển từ merchant sang VNPAY. Việc tạo ra mã này phụ thuộc vào cấu hình của merchant và phiên bản api sử dụng. Phiên bản hiện tại hỗ trợ SHA256, HMACSHA512.
   */
  vnp_SecureHash: string;
}

export interface VNPAY_ReturnParams {
  /**
   * Mã website của merchant trên hệ thống của VNPAY. Ví dụ: 2QXUI4J4
   */
  vnp_TmnCode: string;
  /**
   * Số tiền thanh toán. VNPAY phản hồi số tiền nhân thêm 100 lần.
   */
  vnp_Amount: number;
  /**
   * Mã Ngân hàng thanh toán. Ví dụ: NCB
   */
  vnp_BankCode: string;
  /**
     * Mã giao dịch tại Ngân hàng. Ví dụ: NCB20170829152730
  
     */
  vnp_BankTranNo: string;
  /**
   * Loại tài khoản/thẻ khách hàng sử dụng:ATM,QRCODE
   */
  vnp_CardType: string;
  /**
   * Thời gian thanh toán. Định dạng: yyyyMMddHHmmss
   */
  vnp_PayDate: string;
  /**
   * Thông tin mô tả nội dung thanh toán (Tiếng Việt, không dấu). Ví dụ: **Nap tien cho thue bao **********. So tien 100,000 VND**
   *
   */
  vnp_OrderInfo: string;
  /**
   * Mã giao dịch ghi nhận tại hệ thống VNPAY. Ví dụ: **************
   */
  vnp_TransactionNo: string;
  /**
   * Mã phản hồi kết quả thanh toán. Quy định mã trả lời 00 ứng với kết quả Thành công cho tất cả các API. Tham khảo thêm tại bảng mã lỗi
   */
  vnp_ResponseCode: VNPAY_ResponseCode;
  /**
   * Mã phản hồi kết quả thanh toán. Tình trạng của giao dịch tại Cổng thanh toán VNPAY. Tham khảo thêm tại bảng mã lỗi
   */
  vnp_TransactionStatus: VNPAY_ResponseCode;
  /**
   * Giống mã gửi sang VNPAY khi gửi yêu cầu thanh toán. Ví dụ: 23554
   */
  vnp_TxnRef: string;
  /**
   * Loại mã băm sử dụng: SHA256, HmacSHA512
   */
  vnp_SecureHashType: string;
  /**
   * Mã kiểm tra (checksum) để đảm bảo dữ liệu của giao dịch không bị thay đổi trong quá trình chuyển từ VNPAY về Website TMĐT.
   * Cần kiểm tra đúng checksum khi bắt đầu xử lý yêu cầu (trước khi thực hiện các yêu cầu khác)
   */
  vnp_SecureHash: string;
}

/**
 * 00: Giao dịch thành công
 *
 * 07: Trừ tiền thành công. Giao dịch bị nghi ngờ (liên quan tới lừa đảo, giao dịch bất thường)
 *
 * 09: Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng chưa đăng ký dịch vụ InternetBanking tại ngân hàng
 *
 * 10: Giao dịch không thành công do: Khách hàng xác thực thông tin thẻ/tài khoản không đúng quá 3 lần
 *
 * 11: Giao dịch không thành công do: Đã hết hạn chờ thanh toán. Xin quý khách vui lòng thực hiện lại giao dịch.
 *
 * 12: Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng bị khóa.
 *
 * 13: Giao dịch không thành công do Quý khách nhập sai mật khẩu xác thực giao dịch (OTP). Xin quý khách vui lòng thực hiện lại giao dịch.
 *
 * 24: Giao dịch không thành công do: Khách hàng hủy giao dịch
 *
 * 51: Giao dịch không thành công do: Tài khoản của quý khách không đủ số dư để thực hiện giao dịch.
 *
 * 65: Giao dịch không thành công do: Tài khoản của Quý khách đã vượt quá hạn mức giao dịch trong ngày.
 *
 * 75: Ngân hàng thanh toán đang bảo trì.
 *
 * 79: Giao dịch không thành công do: KH nhập sai mật khẩu thanh toán quá số lần quy định. Xin quý khách vui lòng thực hiện lại giao dịch
 *
 * 99: Các lỗi khác (lỗi còn lại, không có trong danh sách mã lỗi đã liệt kê)
 */
export type VNPAY_ResponseCode =
  | '00'
  | '07'
  | '09'
  | '10'
  | '11'
  | '12'
  | '13'
  | '24'
  | '51'
  | '65'
  | '75'
  | '79'
  | '99';
