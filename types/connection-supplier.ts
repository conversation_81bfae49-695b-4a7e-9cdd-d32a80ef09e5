import { Organization, OrganizationDepartment } from './company';
import { INVOICE_TYPE, INVOICE_TYPE_CODE } from './invoice';

export interface Supplier {
  supplierId: number;
  companyName: string;
  supplierType: SupplierType;
  urls: string[];
  logo: string;
  visible: SupplierVisible;
  description: string;
  supplierKey: string;
}

export type SupplierVisible = 'HIDE' | 'SHOW';

export interface ConnectionSupplier {
  connectionSupplierId: number;
  organizationDepartmentId: number;
  organizationId: number;
  supplierId: number;
  usernameConnection: string;
  url: string;
  passwordConnection: string;
  xCSRFToken: string;

  /* relation */
  OrganizationDepartment: OrganizationDepartment;
  Organization: Organization;
  Supplier: Supplier;
}

export type SupplierType = 'SUPPLIER_E_INVOICE' | 'SUPPLIER_SERVICE';

export interface ConnectionSupplierHistory {
  connectionSupplierHistoryId: number;
  organizationId: number;
  organizationDepartmentId: number;
  // connectionSupplierId: number;
  succeededDownload: number;
  failedDownload: number;
  processed: number;
  processing: number;
  duplicated: number;
  /**
   * ko doc dc
   */
  malformed: number;
  syncDate: Date;
  hasMaCQT: number;
  total: number;
}

export interface ConnectionSupplierInvoice {
  connectionSupplierInvoiceId: number;
  connectionSupplierHistoryId: number;
  connectionSupplierId: number;

  /**
   * Loại hoá đơn
   */
  invoiceType: INVOICE_TYPE;
  /**
   * Ký hiệu hoá đơn
   */
  serial: string;
  /**
   * Ký hiệu loại hoá đơn
   */
  invoiceTypeCode: INVOICE_TYPE_CODE;
  /**
   * Số hoá đơn
   */
  invoiceNumber: string;
  /**
   * Ngày hoá đơn
   */
  invoiceDate: Date | string;

  /* ma CQT */
  maCQT: string;

  /**
   * MST nguoi mua
   */
  buyerTaxCode: string;
  buyerName: string;

  /**
   * MST nguoi ban
   */
  sellerTaxCode: string;

  supplierReferenceCode: string;

  issueStatus: string;

  vietInvoiceId: string;

  duplicateInvoiceId: number;
}
