import { Company } from './company';
import { OrderItem } from './order-item';

export type TypePayment = 'TRANSFER' | 'VNPAY';
export type Position =
  | 'Tổng giám đốc'
  | 'Giám đốc'
  | 'Kế toán trưởng'
  | 'Kế toán'
  | 'Khác';
export type StatusOrder = 'PENDING' | 'COMPLETE' | 'CANCEL' | 'FAILED';

export interface Order {
  orderId: number;
  accountId: number;
  companyId: number;
  taxCode: string;
  companyName: string;
  // country: string;
  // city: string;
  // province: string;
  // wards: string;
  address: string;
  // presidentName: string;
  // presidentPhone: string;
  // presidentEmail: string;
  // presidentPosition: PresidentPosition;
  orderName: string;
  orderPhone: string;
  orderEmail: string;
  orderPosition: string;
  invoiceCustomerName: string;
  invoiceCustomerPhone: string;
  invoiceCustomerEmail: string;
  invoiceCustomerAddress: string;
  voucherCode: string;
  voucher: any;
  totalCost: number; // tổng tiền gốc
  promotion: number; // khuyến mại
  totalPayment: number; // tổng phải thanh toán
  typePayment: TypePayment;
  statusOrder: StatusOrder;
  orderItems: OrderItem[];

  metaPaymentCode: string;

  orderCode: string;
  clientOrderCode: string;

  transactionNo: string;
  bankTranNo: string;

  /* association */
  OrderItems: OrderItem[];
  Company: Company;
}
