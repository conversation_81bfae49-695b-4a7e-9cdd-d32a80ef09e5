/* eslint-disable @typescript-eslint/no-empty-interface */
import express from 'express';
import { Account } from './account';
import { Invoice } from './invoice';
import {
  AccountOrganizationAccess,
  Company,
  CompanyTitle,
  Organization,
  OrganizationDepartment,
  PartnerCompany,
  Role,
} from './company';
import { Notification } from './notification';
import { AuthLog } from './audit-log';
import { Order } from './order';

/* no need to be too detail as it less common used */
export interface ParamDict {}

export interface ResponseBody {
  result: 'success' | 'failed';
  reason?: string;
  message?: string;
  page: number;
  total: number;
  count: number;
  deleteCount: number;

  accessTokenExpireDate: string;
  accessToken: string;

  account?: Account;
  accounts?: Account[];

  dealer: { fullname: string };

  companies: Company[];
  company: Company;

  accountService: { metaAccountServiceCode: string };

  organization: Organization;
  organizations: Organization[];

  organizationDepartment: OrganizationDepartment;
  organizationDepartments: OrganizationDepartment[];

  accountOrganizationAccesses: AccountOrganizationAccess[];

  invoice: Invoice;
  invoices: Invoice[];

  authLogs: AuthLog[];
  devices: AuthLog[];
}

export interface RequestBody
  extends Account,
    Invoice,
    Company,
    Organization,
    OrganizationDepartment,
    Role,
    CompanyTitle,
    Notification,
    PartnerCompany,
    Order {
  dealerCode?: string;
  rememberMe: boolean;
  activateToken: string;

  accountOrganizationAccesses: AccountOrganizationAccess[];

  accountIds: number[];
}

export interface RequestQuery {
  limit: number;
  q: string;
  page: string;
  orderField: string;
  orderMode: 'ASC' | 'DESC';

  /*  */
  activateToken: string;
  dealerCode: string;
  parentCompanyId: number;
}

//For server
export interface RequestHandler {
  (
    req: express.Request<ParamDict, ResponseBody, RequestBody, RequestQuery> & {
      account: Account;
      sessionToken: string;
      accessToken: string;
    },
    res?: express.Response<ResponseBody>,
    next?: express.NextFunction,
  ): void | Promise<void>;
}

export interface ErrorRequestHandler {
  (
    err: Error,
    req: express.Request<ParamDict, ResponseBody, RequestBody, RequestQuery> & {
      account?: Account;
    },
    res?: express.Response<ResponseBody>,
    next?: express.NextFunction,
  ): void | Promise<void>;
}
