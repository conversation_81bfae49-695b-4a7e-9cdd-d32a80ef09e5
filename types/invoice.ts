import { Organization, OrganizationDepartment } from './company';
import { ConnectionSupplier } from './connection-supplier';
import { InvoiceValidate } from './invoice-validate';

export interface Invoice {
  invoiceId: number;
  invoiceVersion: string;
  originInvoiceId: number;
  targetInvoiceId: number;
  organizationId: number;
  organizationDepartmentId: number;
  invoiceCQTId: string | null;
  vietInvoiceId: string;
  invoiceRelationId: number;
  invoiceDuplicateId: number;
  /**
   * Loại hoá đơn
   */
  invoiceType: INVOICE_TYPE;
  /**
   * Ký hiệu hoá đơn
   */
  serial: string;
  /**
   * Ký hiệu loại hoá đơn
   */
  invoiceTypeCode: INVOICE_TYPE_CODE;
  /**
   * Số hoá đơn
   */
  invoiceNumber: string;
  /**
   * Ngày hoá đơn
   */
  invoiceDate: Date | string;
  /**
   * <PERSON><PERSON><PERSON> nhận hoá đơn
   */
  invoiceReceiveDate: Date | string;
  /**
   * <PERSON><PERSON><PERSON> cấp mã
   */
  providedCodeDate: Date | string;
  /**
   * <PERSON><PERSON>y kí
   */
  signDate: Date | string;
  /**
   * danh mục hoá đơn
   */
  invoiceCategory: INVOICE_CATEGORY;
  /**
   * Nguoi ban
   */
  sellerCode: string;
  sellerName: string;
  sellerTaxCode: string;
  sellerBankAccount: string;
  sellerBankName: string;
  sellerAddress: string;
  sellerPhone: string;
  sellerMail: string;

  /**
   * Nguoi mua
   */
  buyerName: string;
  buyerTaxCode: string;
  buyerAddress: string;
  buyerPersonName: string;
  buyerBankAccount: string;
  buyerBankName: string;
  buyerCode: string;
  buyerPhone: string;
  buyerMail: string;

  /**
   * Tổng tiền sau thue
   */
  amountAfterVat: number;
  /**
   * TT truoc thue
   */
  amountBeforeVat: number;
  /**
   * Thanh tien
   */
  finalAmount: number;
  /**
   * Tổng tiền quy đổi
   */
  finalAmountExchange: number;
  /**
   * Thanh tien in word
   */
  finalAmountInWord: string;
  /**
   * % chiet khau
   */
  discount: number;
  discountWithoutVAT: number;
  discountOther: number;

  /**
   * TT truoc CK
   */
  amountBeforeDiscount: number;

  /**
   * TT thue 0%
   */
  amountVat0: number;
  vat0: number;
  amountAfterVat0: number;
  amountBeforeVat0: number;

  /**
   * TT thue 10%
   */
  amountVat10: number;
  vat10: number;
  amountAfterVat10: number;
  amountBeforeVat10: number;
  /**
   * TT thue 8%
   */
  amountVat8: number;
  vat8: number;
  amountAfterVat8: number;
  amountBeforeVat8: number;
  /**
   * Thanh Tien thue 5%
   */
  amountVat5: number;
  vat5: number;
  amountAfterVat5: number;
  amountBeforeVat5: number;
  /**
   *  TT thue khac
   */
  amountVatKHAC: number;
  vatKHAC: number;
  amountAfterVatKHAC: number;
  amountBeforeVatKHAC: number;

  /**
   * TT thue KCT
   */
  amountVatKCT: number;
  vatKCT: number;
  amountAfterVatKCT: number;
  amountBeforeVatKCT: number;
  /**
   * TT thue KKKNT
   */
  amountVatKKKNT: number;
  vatKKKNT: number;
  amountAfterVatKKKNT: number;
  amountBeforeVatKKKNT: number;
  /**
   * TT thue
   */
  amountVat: number;

  totalDiscountAmount: number;
  /**
   * Loại tiền
   */
  paymentCurrency: string;
  /**
   * Tỉ giá
   */
  paymentExchangeRate: number;
  paymentMethod: PAYMENT_METHOD;
  /**
   * Trạng thái hoá đơn
   */
  statusInvoice: INVOICE_STATUS;
  /**
   * Trạng thái hoá đơn
   */
  statusInvoiceText: string;
  /**
   * Trạng thái xử lý hoá đơn
   */
  statusHandleInvoice: INVOICE_HANDLE_STATUS;
  statusHandleInvoiceText: string;
  /**
   * ghi chú
   */
  noteInvoice: number;
  description: string;

  /* ma CQT */
  maCQT: string;

  /**
   * file XML (từ CQT)
   */
  xmlFile: string;
  /**
   * file XML (từ NCC)
   */
  originXmlFile: string;
  pdfFile: string;
  previewPdfFile: string;
  parsedFromXml: boolean;
  /**
   * Đã gọi lấy chi tiết từ cqt hay chưa
   */
  parsedDetailFromCqt: boolean;
  /**
   * Đã gọi lấy chi tiết từ NCC hay chưa
   */
  parsedDetailFromNCC: boolean;
  /**
   * Ko tồn tại HĐ gốc trên CQT (ko lấy được XML)
   */
  xmlCqtNotExist: boolean;
  /**
   * Ko tồn tại HĐ gốc trên CQT
   */
  detailCqtNotExist: boolean;

  /**
   * ko tồn tại xml trên NCC
   */
  xmlNCCNotExist: boolean;
  /**
   * Ko tồn tại pdf trên NCC
   */
  pdfNCCNotExist: boolean;

  issueStatus: string;
  issueDate: Date;
  /**
   * dùng cho supplier đã biết
   */
  supplierKey: string;
  /**
   * msttcgp: MST tổ chức cung cấp giải pháp
   */
  supplierTaxCode: string;
  /**
   * Mã tra cứu
   */
  supplierReferenceCode: string;
  /**
   * Link tra cuu
   */
  supplierLookupWebsite: string;

  // customize
  // hạch toán
  noDocument: string; //số chứng từ
  accountingDate: Date; //ngày hạch toán
  accountingPersonName: string; //người hạch toán

  statusPayment: PAYMENT_STATUS; //trạng thái thanh toán
  reminderPaymentDate: Date; //ngày nhắc thanh toán
  expiredPaymentDate: Date; // ngày phải thanh toán
  debtPayment: number; //tiền chưa thanh toán
  amountPayment: number; //tiền đã thanh toán
  statusTax: string; //kiểm tra thuế suất
  connectionSupplier: ConnectionSupplier;
  isDeleted: boolean;
  sellerCKS: {
    Issuer: string;
    SigningTime: string;
    Subject: string;
    NotBefore: string;
    NotAfter: string;
  };
  cqtCKS: {
    Issuer: string;
    SigningTime: string;
    Subject: string;
    NotBefore: string;
    NotAfter: string;
  };
  sellerSignDate: Date;
  cqtSignDate: Date;
  dateCQTChangeStatus: Date;
  dateDetectChangeStatus: Date;
  isSendMailSupplier: boolean;
  dateReturnPayment: Date;
  dateReturnAccounting: Date;
  hdgocData: {
    khmshdon: string;
    khhdon: INVOICE_TYPE_CODE;
    shdon: string;
    tdlap: string;
    lhdon: INVOICE_TYPE;
  };
  reduceTaxResolution: { name: string; key: string } | null;
  imageFile: string;
  isManualInput: boolean;
  typeInvoiceReceive: TYPE_INVOICE_RECEIVE;

  //return by server
  isRiskyInvoice: boolean;

  /* cqt mtt */
  cqtMtt?: boolean;
  //
  // association
  Organization: Organization;
  OrganizationDepartment: OrganizationDepartment;
  InvoiceProducts: InvoiceProduct[];
  InvoiceValidate: InvoiceValidate;
  TaxReduceValidates: TaxReduceValidate[];
}

export type PAYMENT_METHOD = 'TM' | 'CK';
export type PAYMENT_STATUS = 1 | 2 | 3 | 4 | 5;
export type INVOICE_STATUS =
  | 1
  | 2
  | 3
  | 4
  | 5
  | 6
  | /* HD ko xac dinh */ 7
  | /* HD NHÁP */ 8;
export type INVOICE_HANDLE_STATUS = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9; // trạng thái xử lý hoá đơn
export type INVOICE_CHECK_STATUS = 1 | 2 | 3 | 4 | 5 | 6;
export type VAT_TYPE = 'KCT' | '8%' | '10%' | '0%' | '5%' | 'KHAC' | 'KKKNT';
/**
 * 1: 'Tải lên'
 * 2: 'Email'
 * 3: 'Tự động tải về'
 * 4: 'Nhập tay'
 */
export type TYPE_INVOICE_RECEIVE = 1 | 2 | 3 | 4;

export type INVOICE_TYPE =
  /* GTGT */
  | '01'
  /* BAN_HANG */
  | '02'
  /* BAN_TAI_SAN_CONG */
  | '03'
  /* BAN_HANG_DU_TRU_QUOC_GIA */
  | '04'
  /* KHAC */
  | '05'
  /* XUAT_KHO_VAN_CHUYEN_NOI_BO */
  | '06_01'
  /* XUAT_KHO_BAN_HANG_DAI_LY */
  | '06_02';

export type INVOICE_TYPE_CODE =
  /* KH_1 */
  | '1'
  /* KH_2 */
  | '2'
  /* KH_3 */
  | '3'
  /* KH_4 */
  | '4'
  /* KH_5 */
  | '5'
  /* KH_6 */
  | '6';

export type INVOICE_CATEGORY = 'INPUT_INVOICE' | 'OUTPUT_INVOICE';

export interface InvoiceFromXml {
  HDon: {
    DLHDon: [
      {
        $: {
          Id: 'A4I3IX294WD';
        };
        TTChung: [
          {
            PBan: ['2.0.0'];
            THDon: ['Hóa đơn giá trị gia tăng'];
            KHMSHDon: ['1'];
            KHHDon: ['C22TYY'];
            SHDon: ['00000003'];
            NLap: ['2022-01-29'];
            DVTTe: ['VND'];
            TGia: ['1.00'];
            HTTToan: ['TM/CK'];
            MSTTCGP: ['0101243150'];
            TTKhac: [
              {
                TTin: [
                  {
                    TTruong: ['EnvironmmentFeeRate'];
                    KDLieu: ['numeric'];
                    DLieu: ['0.0'];
                  },
                ];
              },
            ];
            TTHDLQuan: [
              {
                TCHDon: ['2'];
                LHDCLQuan: ['1'];
                KHMSHDCLQuan: ['1'];
                KHHDCLQuan: ['C23TAA'];
                SHDCLQuan: ['00000154'];
                NLHDCLQuan: ['2023-08-24'];
              },
            ];
          },
        ];
        NDHDon: [
          {
            NBan: [
              {
                Ten: ['CÔNG TY CỔ PHẦN ĐẦU TƯ INDO'];
                MST: ['0106660221'];
                DChi: [
                  'Số 32/337, Phố Cầu Giấy, Phường Dịch Vọng, Quận Cầu Giấy, Hà Nội, Việt Nam',
                ];
                STKNHang: ['*************'];
                TNHang: ['Ngân hàng TMCP Quân Đội-Chi nhánh Trần Duy Hưng'];
                SDThoai: ['**********'];
                DCTDTu: ['<EMAIL>'];
                TTKhac: [
                  {
                    TTin: [
                      {
                        TTruong: ['SellerAddress'];
                        KDLieu: ['string'];
                        DLieu: [
                          'Số 32/337, Phố Cầu Giấy, Phường Dịch Vọng, Quận Cầu Giấy, Hà Nội, Việt Nam',
                        ];
                      },
                      {
                        TTruong: ['SellerBankAccount'];
                        KDLieu: ['string'];
                        DLieu: ['*************'];
                      },
                      {
                        TTruong: ['SellerBankName'];
                        KDLieu: ['string'];
                        DLieu: [
                          'Ngân hàng TMCP Quân Đội-Chi nhánh Trần Duy Hưng',
                        ];
                      },
                    ];
                  },
                ];
              },
            ];
            NMua: [
              {
                Ten: ['CÔNG TY CỔ PHẦN THƯƠNG MẠI ĐẦU TƯ GAJA'];
                MST: ['**********'];
                DChi: [
                  'số 22 đường số 5, khu dân cư HIm Lam, Xã Bình Hưng, Huyện Bình Chánh, Thành phố Hồ Chí Minh, Việt Nam',
                ];
                SDThoai: ['**********'];
                DCTDTu: ['<EMAIL>'];
              },
            ];
            DSHHDVu: [
              {
                HHDVu: [
                  {
                    TChat: ['1'];
                    STT: ['1'];
                    MHHDVu: ['OS_NEW_3Y'];
                    THHDVu: [
                      'Gói dịch vụ FastCA dành cho TC/DN Cấp mới 3 năm (OS_NEW_3Y)',
                    ];
                    DVTinh: ['Gói'];
                    SLuong: ['5.000000'];
                    DGia: ['367545.000000'];
                    TLCKhau: ['0.0000'];
                    STCKhau: ['0.000000'];
                    ThTien: ['1837725.000000'];
                    TSuat: ['10%'];
                    TTKhac: [
                      {
                        TTin: [
                          {
                            TTruong: ['Amount'];
                            KDLieu: ['numeric'];
                            DLieu: ['1837725.0'];
                          },
                          {
                            TTruong: ['AmountAfterTax'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['AmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['1837725.0'];
                          },
                          {
                            TTruong: ['DiscountAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['SortOrder'];
                            KDLieu: ['numeric'];
                            DLieu: ['1'];
                          },
                          {
                            TTruong: ['TaxReductionAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['TaxReductionAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['UnitPriceAfterTax'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['VATAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['183772.0'];
                          },
                          {
                            TTruong: ['VATAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['183772.0'];
                          },
                          {
                            TTruong: ['WageAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WageAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WageDiscountAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WagePriceAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WagePriceDiscountAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                        ];
                      },
                    ];
                  },
                  {
                    TChat: ['1'];
                    STT: ['2'];
                    MHHDVu: ['OS_TRANF_1Y_CB6'];
                    THHDVu: [
                      'Gói dịch vụ FastCA dành cho TC/DN chuyển đổi 1 năm cộng bù 6 tháng (OS_TRANF_1Y_CB6)',
                    ];
                    DVTinh: ['Gói'];
                    SLuong: ['2.000000'];
                    DGia: ['127700.000000'];
                    TLCKhau: ['0.0000'];
                    STCKhau: ['0.000000'];
                    ThTien: ['255400.000000'];
                    TSuat: ['10%'];
                    TTKhac: [
                      {
                        TTin: [
                          {
                            TTruong: ['Amount'];
                            KDLieu: ['numeric'];
                            DLieu: ['255400.0'];
                          },
                          {
                            TTruong: ['AmountAfterTax'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['AmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['255400.0'];
                          },
                          {
                            TTruong: ['DiscountAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['SortOrder'];
                            KDLieu: ['numeric'];
                            DLieu: ['2'];
                          },
                          {
                            TTruong: ['TaxReductionAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['TaxReductionAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['UnitPriceAfterTax'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['VATAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['25540.0'];
                          },
                          {
                            TTruong: ['VATAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['25540.0'];
                          },
                          {
                            TTruong: ['WageAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WageAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WageDiscountAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WagePriceAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WagePriceDiscountAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                        ];
                      },
                    ];
                  },
                  {
                    TChat: ['1'];
                    STT: ['3'];
                    MHHDVu: ['OS_TRANF_3Y_CB6'];
                    THHDVu: [
                      'Gói dịch vụ FastCA dành cho TC/DN chuyển đổi 3 năm cộng bù 6 tháng (OS_TRANF_3Y_CB6)',
                    ];
                    DVTinh: ['Gói'];
                    SLuong: ['4.000000'];
                    DGia: ['291000.000000'];
                    TLCKhau: ['0.0000'];
                    STCKhau: ['0.000000'];
                    ThTien: ['1164000.000000'];
                    TSuat: ['10%'];
                    TTKhac: [
                      {
                        TTin: [
                          {
                            TTruong: ['Amount'];
                            KDLieu: ['numeric'];
                            DLieu: ['1164000.0'];
                          },
                          {
                            TTruong: ['AmountAfterTax'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['AmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['1164000.0'];
                          },
                          {
                            TTruong: ['DiscountAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['SortOrder'];
                            KDLieu: ['numeric'];
                            DLieu: ['3'];
                          },
                          {
                            TTruong: ['TaxReductionAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['TaxReductionAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['UnitPriceAfterTax'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['VATAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['116400.0'];
                          },
                          {
                            TTruong: ['VATAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['116400.0'];
                          },
                          {
                            TTruong: ['WageAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WageAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WageDiscountAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WagePriceAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WagePriceDiscountAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                        ];
                      },
                    ];
                  },
                  {
                    TChat: ['1'];
                    STT: ['4'];
                    MHHDVu: ['TOKEN_TC_DKM_1Y'];
                    THHDVu: [
                      'Dịch vụ Chữ ký số Newtel CA - Gói TOKEN_TC_DKM_1Y',
                    ];
                    DVTinh: ['Gói'];
                    SLuong: ['1.000000'];
                    DGia: ['333864.000000'];
                    TLCKhau: ['0.0000'];
                    STCKhau: ['0.000000'];
                    ThTien: ['333864.000000'];
                    TSuat: ['10%'];
                    TTKhac: [
                      {
                        TTin: [
                          {
                            TTruong: ['Amount'];
                            KDLieu: ['numeric'];
                            DLieu: ['333864.0'];
                          },
                          {
                            TTruong: ['AmountAfterTax'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['AmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['333864.0'];
                          },
                          {
                            TTruong: ['DiscountAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['SortOrder'];
                            KDLieu: ['numeric'];
                            DLieu: ['4'];
                          },
                          {
                            TTruong: ['TaxReductionAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['TaxReductionAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['UnitPriceAfterTax'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['VATAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['33386.0'];
                          },
                          {
                            TTruong: ['VATAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['33386.0'];
                          },
                          {
                            TTruong: ['WageAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WageAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WageDiscountAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WagePriceAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WagePriceDiscountAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                        ];
                      },
                    ];
                  },
                  {
                    TChat: ['1'];
                    STT: ['5'];
                    MHHDVu: ['TOKEN_TC_DKM_3Y'];
                    THHDVu: [
                      'Dịch vụ Chữ ký số Newtel CA - Gói TOKEN_TC_DKM_3Y',
                    ];
                    DVTinh: ['Gói'];
                    SLuong: ['3.000000'];
                    DGia: ['423955.000000'];
                    TLCKhau: ['0.0000'];
                    STCKhau: ['0.000000'];
                    ThTien: ['1271865.000000'];
                    TSuat: ['10%'];
                    TTKhac: [
                      {
                        TTin: [
                          {
                            TTruong: ['Amount'];
                            KDLieu: ['numeric'];
                            DLieu: ['1271865.0'];
                          },
                          {
                            TTruong: ['AmountAfterTax'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['AmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['1271865.0'];
                          },
                          {
                            TTruong: ['DiscountAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['SortOrder'];
                            KDLieu: ['numeric'];
                            DLieu: ['5'];
                          },
                          {
                            TTruong: ['TaxReductionAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['TaxReductionAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['UnitPriceAfterTax'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['VATAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['127187.0'];
                          },
                          {
                            TTruong: ['VATAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['127187.0'];
                          },
                          {
                            TTruong: ['WageAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WageAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WageDiscountAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WagePriceAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WagePriceDiscountAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                        ];
                      },
                    ];
                  },
                  {
                    TChat: ['1'];
                    STT: ['6'];
                    MHHDVu: ['TOKEN_TC_CD_3Y'];
                    THHDVu: [
                      'Dịch vụ Chữ ký số Newtel CA - Gói TOKEN_TC_CD_3Y',
                    ];
                    DVTinh: ['Gói'];
                    SLuong: ['1.000000'];
                    DGia: ['423955.000000'];
                    TLCKhau: ['0.0000'];
                    STCKhau: ['0.000000'];
                    ThTien: ['423955.000000'];
                    TSuat: ['10%'];
                    TTKhac: [
                      {
                        TTin: [
                          {
                            TTruong: ['Amount'];
                            KDLieu: ['numeric'];
                            DLieu: ['423955.0'];
                          },
                          {
                            TTruong: ['AmountAfterTax'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['AmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['423955.0'];
                          },
                          {
                            TTruong: ['DiscountAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['SortOrder'];
                            KDLieu: ['numeric'];
                            DLieu: ['6'];
                          },
                          {
                            TTruong: ['TaxReductionAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['TaxReductionAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['UnitPriceAfterTax'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['VATAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['42396.0'];
                          },
                          {
                            TTruong: ['VATAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['42396.0'];
                          },
                          {
                            TTruong: ['WageAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WageAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WageDiscountAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WagePriceAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WagePriceDiscountAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                        ];
                      },
                    ];
                  },
                  {
                    TChat: ['1'];
                    STT: ['7'];
                    MHHDVu: ['Token-FCA'];
                    THHDVu: ['USB PKI Token - FCA'];
                    DVTinh: ['Cái'];
                    SLuong: ['1.000000'];
                    DGia: ['160000.000000'];
                    TLCKhau: ['0.0000'];
                    STCKhau: ['0.000000'];
                    ThTien: ['160000.000000'];
                    TSuat: ['10%'];
                    TTKhac: [
                      {
                        TTin: [
                          {
                            TTruong: ['Amount'];
                            KDLieu: ['numeric'];
                            DLieu: ['160000.0'];
                          },
                          {
                            TTruong: ['AmountAfterTax'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['AmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['160000.0'];
                          },
                          {
                            TTruong: ['DiscountAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['SortOrder'];
                            KDLieu: ['numeric'];
                            DLieu: ['7'];
                          },
                          {
                            TTruong: ['TaxReductionAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['TaxReductionAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['UnitPriceAfterTax'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['VATAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['16000.0'];
                          },
                          {
                            TTruong: ['VATAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['16000.0'];
                          },
                          {
                            TTruong: ['WageAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WageAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WageDiscountAmountOC'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WagePriceAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                          {
                            TTruong: ['WagePriceDiscountAmount'];
                            KDLieu: ['numeric'];
                            DLieu: ['0.0'];
                          },
                        ];
                      },
                    ];
                  },
                ];
              },
            ];
            TToan: [
              {
                THTTLTSuat: [
                  {
                    LTSuat: [
                      {
                        TSuat: ['10%'];
                        ThTien: ['5446809.000000'];
                        TThue: ['544681.000000'];
                      },
                    ];
                  },
                ];
                TgTCThue: ['5446809.000000'];
                TgTThue: ['544681.000000'];
                TTCKTMai: ['0.000000'];
                TgTTTBSo: ['5991490.000000'];
                TgTTTBChu: [
                  'Năm triệu chín trăm chín mươi mốt nghìn bốn trăm chín mươi đồng chẵn.',
                ];
                TTKhac: [
                  {
                    TTin: [
                      {
                        TTruong: ['TotalAmount'];
                        KDLieu: ['numeric'];
                        DLieu: ['5991490.0'];
                      },
                      {
                        TTruong: ['TotalAmountInWordsByENG'];
                        KDLieu: ['string'];
                        DLieu: [
                          'Five million, nine hundred ninety-one thousand, four hundred ninety.',
                        ];
                      },
                      {
                        TTruong: ['TotalDiscountAmount'];
                        KDLieu: ['numeric'];
                        DLieu: ['0.0'];
                      },
                      {
                        TTruong: ['TotalSaleAmount'];
                        KDLieu: ['numeric'];
                        DLieu: ['5446809.0'];
                      },
                      {
                        TTruong: ['TotalSaleAmountOC'];
                        KDLieu: ['numeric'];
                        DLieu: ['5446809.0'];
                      },
                      {
                        TTruong: ['TotalVATAmount'];
                        KDLieu: ['numeric'];
                        DLieu: ['544681.0'];
                      },
                    ];
                  },
                ];
              },
            ];
          },
        ];
        TTKhac: [
          {
            TTin: [
              {
                TTruong: ['InvoiceTemplateID'];
                KDLieu: ['string'];
                DLieu: ['78c74b38-d5e2-4557-a86b-76828f435257'];
              },
              {
                TTruong: ['IsTaxReduction'];
                KDLieu: ['numeric'];
                DLieu: ['False'];
              },
              {
                TTruong: ['IsTaxReduction43'];
                KDLieu: ['numeric'];
                DLieu: ['False'];
              },
              {
                TTruong: ['OrganizationUnitID'];
                KDLieu: ['string'];
                DLieu: ['ff7316d1-b939-11eb-b989-005056b219fb'];
              },
              {
                TTruong: ['RefID'];
                KDLieu: ['string'];
                DLieu: ['ad053e41-5648-42ab-ad83-94703eb7a30b'];
              },
              {
                TTruong: ['AmountDecimalDigits'];
                KDLieu: ['string'];
                DLieu: ['0'];
              },
              {
                TTruong: ['AmountOCDecimalDigits'];
                KDLieu: ['string'];
                DLieu: ['2'];
              },
              {
                TTruong: ['CoefficientDecimalDigits'];
                KDLieu: ['string'];
                DLieu: ['2'];
              },
              {
                TTruong: ['ExchangRateDecimalDigits'];
                KDLieu: ['string'];
                DLieu: ['2'];
              },
              {
                TTruong: ['MainCurrency'];
                KDLieu: ['string'];
                DLieu: ['VND'];
              },
              {
                TTruong: ['QuantityDecimalDigits'];
                KDLieu: ['string'];
                DLieu: ['2'];
              },
              {
                TTruong: ['UnitPriceDecimalDigits'];
                KDLieu: ['string'];
                DLieu: ['0'];
              },
              {
                TTruong: ['UnitPriceOCDecimalDigits'];
                KDLieu: ['string'];
                DLieu: ['0'];
              },
              {
                TTruong: ['AppID'];
                KDLieu: ['string'];
                DLieu: ['3A136051-81E2-4372-BFC7-5C6C12B73159'];
              },
              {
                TTruong: ['TransactionID'];
                KDLieu: ['string'];
                DLieu: ['A4I3IX294WD'];
              },
            ];
          },
        ];
      },
    ];
    MCCQT: [
      {
        _: '000801A9E9F8524BA3B69E2BFBE3D7D154';
        $: {
          Id: 'Id-fdd3717906c64a0eb0d42327f6ba8d48';
        };
      },
    ];
    DLQRCode: [
      '00020199930032EBB754C3DB104DB399316A78A663B00B01100106660221020110306C22TYY040130508202201290607599149063042101',
    ];
    DSCKS: [
      {
        NBan: [
          {
            Signature: [
              {
                $: {
                  Id: 'seller';
                  xmlns: 'http://www.w3.org/2000/09/xmldsig#';
                };
                SignedInfo: [
                  {
                    CanonicalizationMethod: [
                      {
                        $: {
                          Algorithm: 'http://www.w3.org/TR/2001/REC-xml-c14n-20010315';
                        };
                      },
                    ];
                    SignatureMethod: [
                      {
                        $: {
                          Algorithm: 'http://www.w3.org/2000/09/xmldsig#rsa-sha1';
                        };
                      },
                    ];
                    Reference: [
                      {
                        $: {
                          URI: '#A4I3IX294WD';
                        };
                        Transforms: [
                          {
                            Transform: [
                              {
                                $: {
                                  Algorithm: 'http://www.w3.org/2000/09/xmldsig#enveloped-signature';
                                };
                              },
                            ];
                          },
                        ];
                        DigestMethod: [
                          {
                            $: {
                              Algorithm: 'http://www.w3.org/2000/09/xmldsig#sha1';
                            };
                          },
                        ];
                        DigestValue: ['gKT+I251kB3qwfCH10DCsy8VX+M='];
                      },
                      {
                        $: {
                          URI: '#SigningTime';
                        };
                        DigestMethod: [
                          {
                            $: {
                              Algorithm: 'http://www.w3.org/2000/09/xmldsig#sha1';
                            };
                          },
                        ];
                        DigestValue: ['xxsAkMGDe/Vv5A7vWjGyFYO2uPg='];
                      },
                    ];
                  },
                ];
                SignatureValue: [
                  'XRNF55bSpiBvT2YRjQBTMVjAAbyGM5WjFxKI0K+ZUbIMDwfEEJCv+uVnV9quK/36ntI4+BjrySVvzcIsiLHbBZE45F7+JMqbBY5ifPOJwteTSAhKpwt2hUBw3qAVP9+ntfK/a/C9E0YpZVOLtd9Pampq5OapD7W97ZrwWXXD+3M=',
                ];
                KeyInfo: [
                  {
                    X509Data: [
                      {
                        X509SubjectName: [
                          'OID.0.9.2342.19200300.100.1.1=MST:0106660221, CN=CÔNG TY CỔ PHẦN ĐẦU TƯ INDO, L=Quận Cầu Giấy, S=HÀ NỘI, C=VN',
                        ];
                        X509Certificate: [
                          'MIIFuTCCA6GgAwIBAgIQVAEBAdBrEXWqGO4Lzjz8YzANBgkqhkiG9w0BAQUFADBpMQswCQYDVQQGEwJWTjETMBEGA1UEChMKVk5QVCBHcm91cDEeMBwGA1UECxMVVk5QVC1DQSBUcnVzdCBOZXR3b3JrMSUwIwYDVQQDExxWTlBUIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MB4XDTIxMTIwMzEwMDYwMFoXDTIzMDYwMzEwMDYwMFowgY4xCzAJBgNVBAYTAlZOMRIwEAYDVQQIDAlIw4AgTuG7mEkxHDAaBgNVBAcME1F14bqtbiBD4bqndSBHaeG6pXkxLTArBgNVBAMMJEPDlE5HIFRZIEPhu5QgUEjhuqZOIMSQ4bqmVSBUxq8gSU5ETzEeMBwGCgmSJomT8ixkAQEMDk1TVDowMTA2NjYwMjIxMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCqrR4cbAU/ASNzhNfouBNTy6ydJn+Q36njnoR8LkJ/AjqDT6+MDc/+fqQ2+fkHq4nX8+QOpBLuuoj+MXYdJOoqMaJEAHmIA+0AzpKadvztyYVvksvn+FgZ4u7ZJvGn6sNiqioKXQTNSmuD47B62TUgJu1M7QDc9lNGwQN67557jwIDAQABo4IBuTCCAbUwcAYIKwYBBQUHAQEEZDBiMDIGCCsGAQUFBzAChiZodHRwOi8vcHViLnZucHQtY2Eudm4vY2VydHMvdm5wdGNhLmNlcjAsBggrBgEFBQcwAYYgaHR0cDovL29jc3Audm5wdC1jYS52bi9yZXNwb25kZXIwHQYDVR0OBBYEFJ1qOvqTh1kW0k/vm4bGCKIwXkSUMAwGA1UdEwEB/wQCMAAwHwYDVR0jBBgwFoAUBmnA1dUCihWNRn3pfOJoClWsaq8waAYDVR0gBGEwXzBdBg4rBgEEAYHtAwEBAwEBATBLMCIGCCsGAQUFBwICMBYeFABPAEkARAAtAFMAVAAtADEALgAwMCUGCCsGAQUFBwIBFhlodHRwOi8vcHViLnZucHQtY2Eudm4vcnBhMDEGA1UdHwQqMCgwJqAkoCKGIGh0dHA6Ly9jcmwudm5wdC1jYS52bi92bnB0Y2EuY3JsMA4GA1UdDwEB/wQEAwIE8DAgBgNVHSUEGTAXBgorBgEEAYI3CgMMBgkqhkiG9y8BAQUwJAYDVR0RBB0wG4EZdGhvYW5uZ3V5ZW41NjU2QGdtYWlsLmNvbTANBgkqhkiG9w0BAQUFAAOCAgEACw64OyJ6UM9AM5zAaZFHPfbmNQuE/+ocJNrn5Z1aJpCJzGI01ZMqTt3hbXe3+QoGgohnK6Cyg77wMXcq7tW7aRrRB7q4sKSjmJkTQKdcnbL0rBl8N5P4zhJ0jaXPa8OTdHqwsWYIahkB0muzShc/51FdQs2UZJ7YPDwhPplbA2Lr9JigwHaQREzwGHyLck+oLi9FEiVlw7HWmvpEI9ZtusL/RMoY9jkqVvxnch19YPf8NpvxS7BhgWdhDcSJeRyiYpPHTdfUyQwbSmSZbytSFs3e3sX8ExsSsqDW1RQU5sVgLMfMt3RaED0rdqIm/WXamfKgb8wRTnRfnGZuNsXuTWi1IVQisWTppIOH/cLLfh++XUPyWZGH6Y8D5xcrpMdq7BTXufo3w6iYSg+nKoNKuUoMjyKYwG4z0Qy30W4SX4U0CPYAbjefT8vkFoMWIYlgMnyFJ5C90iPEabT2O2NxWQD4fpD2eEZptixy2/LS+7jMu8qI2BxTzIAX3/edHQX8g1MPnItKB1tLYlf03Rm3AF7OCAlswZcyrWvccGW3v3auUmVXeUdAuVsKipF8chBSnK9FvhzHFw4Nx2W4ohzNtRCzGwmDUfN8q018v13HcjzpbzB5xuAuqwkXK5CYRSDQbD7vublcALL6wHwnHZhZtKZRXMEQp+Quu7U2/buGNrg=',
                        ];
                      },
                    ];
                  },
                ];
                Object: [
                  {
                    $: {
                      Id: 'SigningTime';
                    };
                    SignatureProperties: [
                      {
                        SignatureProperty: [
                          {
                            $: {
                              Target: '#seller';
                            };
                            SigningTime: ['2022-01-29T23:41:14'];
                          },
                        ];
                      },
                    ];
                  },
                ];
              },
            ];
          },
        ];
        CQT: [
          {
            Signature: [
              {
                $: {
                  Id: 'Tct-5f89ce445700425aa2883c88ea406da2';
                  xmlns: 'http://www.w3.org/2000/09/xmldsig#';
                };
                SignedInfo: [
                  {
                    CanonicalizationMethod: [
                      {
                        $: {
                          Algorithm: 'http://www.w3.org/TR/2001/REC-xml-c14n-20010315';
                        };
                      },
                    ];
                    SignatureMethod: [
                      {
                        $: {
                          Algorithm: 'http://www.w3.org/2001/04/xmldsig-more#rsa-sha256';
                        };
                      },
                    ];
                    Reference: [
                      {
                        $: {
                          URI: '#A4I3IX294WD';
                        };
                        DigestMethod: [
                          {
                            $: {
                              Algorithm: 'http://www.w3.org/2001/04/xmlenc#sha256';
                            };
                          },
                        ];
                        DigestValue: [
                          'EUeHiiZ5U9ChX/me1I3sVkN6xkfilY0HCewxklEYAS0=',
                        ];
                      },
                      {
                        $: {
                          URI: '#SigningTime-690484161a57496e947787de9c3111bd';
                        };
                        DigestMethod: [
                          {
                            $: {
                              Algorithm: 'http://www.w3.org/2001/04/xmlenc#sha256';
                            };
                          },
                        ];
                        DigestValue: [
                          'Pu8ZFP8gTL35NmOXG1YQz9M8R6k91WBsX74kWUUwJpM=',
                        ];
                      },
                      {
                        $: {
                          URI: '#Id-fdd3717906c64a0eb0d42327f6ba8d48';
                        };
                        DigestMethod: [
                          {
                            $: {
                              Algorithm: 'http://www.w3.org/2001/04/xmlenc#sha256';
                            };
                          },
                        ];
                        DigestValue: [
                          'aQgWY7hC/SJcRU3a43Ni1JdL9FC8PElsOI8MjRHi3cw=',
                        ];
                      },
                    ];
                  },
                ];
                SignatureValue: [
                  'gya+pxO30nmWxpmyjuHzYowlIkNumt6eR5tJAVf2QFYiMJg3TQd+7ag18i+umvs255oGF/3ciSUgs+Pjg+8ibGCKTkz6NTzUPX2kX8u8qhtmiFEjjAErYLdAhFBDly02E1lVeR8kRCJNjKIwlpfk0wRQxFBQqLGLIAYRqf7Ochn6uIr8lx5rcdpwJHGN0GfTWrUb0xr5dA2hJdDzuRZxnvnXGfyl5jtjjlrPYAUzdFjPEtfcHu5eTn14rXYXkyuNFEn+QiiKt4LC9UMtdED2pyiAbWhZVB6amM0kl0uZwlulfk1hGCNjVIwPUVDe25kSv08y/plEUdN0ogrHtETiKg==',
                ];
                KeyInfo: [
                  {
                    X509Data: [
                      {
                        X509SubjectName: [
                          'CN=TỔNG CỤC THUẾ,O=BỘ TÀI CHÍNH,L=Hà Nội,C=VN',
                        ];
                        X509Certificate: [
                          '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',
                        ];
                      },
                    ];
                  },
                ];
                Object: [
                  {
                    $: {
                      Id: 'SigningTime-690484161a57496e947787de9c3111bd';
                    };
                    SignatureProperties: [
                      {
                        SignatureProperty: [
                          {
                            $: {
                              Target: 'signatureProperties';
                            };
                            SigningTime: ['2022-01-29T23:41:15'];
                          },
                        ];
                      },
                    ];
                  },
                ];
              },
            ];
          },
        ];
      },
    ];
  };
}

export interface VietInvoiceInvoice {
  invoiceVersion: '2.0.1';
  printEmptyRows: 10;
  classTypeEnumId: 'InvClsOriginal';
  invoiceForm: '1';
  sellerAddress: 'Tổ 6 (Nhà bà Lê Thị Thanh Tâm), Thị Trấn An Dương, Huyện An Dương, Thành phố Hải Phòng, Việt Nam';
  isMultipleTax: 'N';
  toAddress: 'Số NO18-LK18-30, Khu đất dịch vụ Dọc Bún 2, Phường La Khê, Quận Hà Đông, Thành phố Hà Nội, Việt Nam';
  printConvertedEmptyRows: 10;
  referenceNumber: 'D16A2C6C8ECA0A92';
  exchangeRate: 1;
  paymentId: '636047';
  toEmailAddress: '<EMAIL>';
  sentEmail: 'Y';
  invoiceNo: '00000039';
  sellerExtraFields: [];
  invoiceFormSerial: '1C23TQF';
  toPartyName: 'CÔNG TY TNHH SẢN XUẤT THƯƠNG MẠI DỊCH VỤ SAO VIỆT NAM';
  toPseudoId: '_NA_';
  invoiceNoInt: 39;
  baseCurrencyUomId: 'VND';
  invoiceTotal: 3250000;
  discountTypeEnumId: 'InvDiscNotApplicable';
  taxRateInfos: [
    {
      taxRateEnum: 'KKKNT';
      amountExcludeTax: 3250000;
      taxAmount: 0;
    },
  ];
  taxRateEnumId: 'VatTaxKKKNT';
  isPaid: 'N';
  statusId: 'InvoiceFinalized';
  invoiceSerial: 'C23TQF';
  paymentInstrumentEnumId: 'PiCashOrWireTransfer';
  saleTaxExemptEnumId: 'SaleTaxExempt1';
  taxAmount: 0;
  toPartyId: '_NA_';
  totalTaxAmount: 0;
  fromPartyName: 'CÔNG TY TNHH VIỆT QUANG FARM';
  sellerBankName: '';
  issuedDate: *************;
  amountInWords: 'Ba triệu hai trăm năm mươi nghìn đồng';
  fromPartyId: '110523';
  taxRateEnum: 'KKKNT';
  isVatInvoice: 'Y';
  paymentInstrumentEnum: 'TM/CK';
  fromPartyTaxId: '**********';
  applyVatTaxExempt: 'N';
  issueStatus: 'CQT đã cấp mã';
  toPartyTaxId: '**********';
  productItems: [
    {
      itemTypeEnum: 'Product';
      itemClassTypeCode: '1';
      invoiceItemSeqNum: 1;
      invoiceId: '641404';
      invoiceItemSeqId: '01';
      itemTypeEnumId: 'ItemProduct';
      quantity: 250;
      amount: 13000;
      description: 'Gạo BC';
      quantityUomDesc: 'Kg';
      isPromotion: 'N';
      amountTotal: 3250000;
      itemClassTypeEnumId: 'IiclsProduct';
      itemName: 'Gạo BC';
      unitPrice: '13.000';
      unitOfMeasure: 'Kg';
      invoiceItemAssocTypeEnumIds: [];
      itemTotal: '3.250.000';
      itemTotalExcludeVat: '3.250.000';
      itemTotalWithTax: '3.250.000';
      amountTotalWithDiscount: 3250000;
      tableColumnExtraFields: [];
    },
  ];
  decreeCircularEnumId: 'DecCir78';
  invoiceTemplatePartyId: '110603';
  externalId: '00A0AB3EC25FF54A408CB4BD72DC1D2B36';
  invoiceDate: *************;
  issueStatusId: 'InvTaxAuthCodeGranted';
  sellerAccountNumber: '';
  decreeCircularEnum: 'Nghị định số 123/2020/NĐ-CP';
  currencyUomId: 'VND';
  templateName: 'Hóa đơn giá trị gia tăng';
  buyerExtraFields: [];
  classTypeEnum: 'Hóa đơn gốc';
  invoiceId: '641404';
  invoiceTypeEnumId: 'InvoiceTax';
}

/**
 * Invoice class from CQT
 */
export interface CQTInvoice {
  nbmst: '**********-999';
  khmshdon: 1;
  khhdon: 'C23TMN';
  shdon: 32;
  cqt: '7915';
  cttkhac: [
    {
      ttruong: 'Mã tra cứu hóa đơn';
      kdlieu: 'String';
      dlieu: 'C7BCDF62B1';
    },
    {
      ttruong: 'Mã công ty';
      kdlieu: 'string';
      dlieu: '**********-999';
    },
    {
      ttruong: 'Loại thuế suất hóa đơn';
      kdlieu: 'string';
      dlieu: '10%';
    },
    {
      ttruong: 'Ngày ký';
      kdlieu: 'dateTime';
      dlieu: '2023-06-08';
    },
    {
      ttruong: 'Hình thức thanh toán';
      kdlieu: 'string';
      dlieu: 'TM/CK';
    },
    {
      ttruong: 'Số chứng từ nội bộ';
      kdlieu: 'string';
      dlieu: 'ad4aa257-bb8a-49f7-99e8-a663f2e15a00';
    },
    {
      ttruong: 'Ngày chứng từ nội bộ khác';
      kdlieu: 'date';
      dlieu: '2023-06-08';
    },
    {
      ttruong: 'Ngày hợp đồng khác';
      kdlieu: 'string';
      dlieu: '2023-06-08';
    },
    {
      ttruong: 'Tổng số lượng tất cả sản phẩm';
      kdlieu: 'numeric';
      dlieu: '1';
    },
    {
      ttruong: 'Tính chất hóa đơn (nội bộ)';
      kdlieu: 'numeric';
      dlieu: '3';
    },
    {
      ttruong: 'Mã lưu trữ';
      kdlieu: 'string';
      dlieu: 'HDAP2306/010065';
    },
    {
      ttruong: 'TransactionID';
      kdlieu: 'string';
      dlieu: 'VWT4IAG2E48';
    },
  ];
  dvtte: 'VND';
  hdon: '01';
  hsgcma: 'e6b07248-846f-419e-87bf-7534151da261';
  hsgoc: 'f826603e-b114-464d-b207-f52f592e257b';
  hthdon: 1;
  htttoan: 9;
  id: '855049ec-2acf-417d-886c-a0b197a8209b';
  idtbao: null;
  khdon: null;
  khhdgoc: 'C23TMN';
  khmshdgoc: '1';
  lhdgoc: 1;
  mhdon: '00215EC0B49D944F4A97C99AD1EA9AA677';
  mtdiep: null;
  mtdtchieu: 'V**********F0BDB574967D4A4CB6F657EA2E309D3E';
  nbdchi: 'TP HCM';
  nbhdktngay: null;
  nbhdktso: null;
  nbhdso: null;
  nblddnbo: null;
  nbptvchuyen: null;
  nbstkhoan: '10805847';
  nbten: 'CÔNG TY TEST';
  nbtnhang: 'Ngân hàng ACB';
  nbtnvchuyen: null;
  nbttkhac: [
    {
      ttruong: 'Là hộ kinh doanh';
      kdlieu: 'number';
      dlieu: '0';
    },
  ];
  ncma: '2023-06-08T08:26:03.566Z';
  ncnhat: '2023-06-08T08:26:03.578Z';
  ngcnhat: 'tvan_wintech';
  nky: '2023-06-08T08:26:00Z';
  nmdchi: 'TEST';
  nmmst: '**********-999';
  nmstkhoan: null;
  nmten: 'TEST';
  nmtnhang: null;
  nmtnmua: null;
  nmttkhac: [];
  ntao: '2023-06-08T08:26:03.536Z';
  ntnhan: '2023-06-08T08:26:03.510Z';
  pban: '2.0.1';
  ptgui: 1;
  shdgoc: 31;
  tchat: 2;
  tdlap: '2023-06-07T17:00:00Z';
  tgia: 1;
  tgtcthue: 0;
  tgtthue: 0;
  tgtttbchu: 'Không  đồng';
  tgtttbso: 0;
  thdon: 'HÓA ĐƠN GIÁ TRỊ GIA TĂNG';
  thlap: 202306;
  thttlphi: [];
  thttltsuat: [
    {
      tsuat: '10%';
      thtien: 0;
      tthue: 0;
      gttsuat: null;
    },
  ];
  tlhdon: 'HÓA ĐƠN GIÁ TRỊ GIA TĂNG';
  ttcktmai: 0;
  tthai: 2;
  ttkhac: [];
  tttbao: 1;
  ttttkhac: [];
  ttxly: 5;
  tvandnkntt: '**********';
  mhso: null;
  ladhddt: 1;
  mkhang: null;
  nbsdthoai: '0903786837';
  nbdctdtu: '<EMAIL>';
  nbfax: '028 -2222 8888';
  nbwebsite: 'yeuketoan.vn';
  nbcks: '{"Subject":"OID.0.9.2342.19200300.100.1.1=MST:**********-999, CN=WinTech - Kiểm thử HĐĐT có mã, O=WinTech - Kiểm thử HĐĐT có mã, S=Hồ Chí Minh, C=VN","SerialNumber":"54011647BABEBC89566D7DA287A000FF","Issuer":"C=VN, O=WINGROUP, CN=WINCA","NotAfter":"2026-11-27T08:23:28","NotBefore":"2023-06-01T08:23:28","SigningTime":"2023-06-08T15:26:00"}';
  nmsdthoai: null;
  nmdctdtu: null;
  nmcmnd: null;
  nmcks: null;
  bhphap: 0;
  hddunlap: null;
  gchdgoc: null;
  tbhgtngay: null;
  bhpldo: null;
  bhpcbo: null;
  bhpngay: null;
  tdlhdgoc: '2023-06-07T17:00:00Z';
  tgtphi: null;
  unhiem: null;
  mstdvnunlhdon: null;
  tdvnunlhdon: null;
  nbmdvqhnsach: null;
  nbsqdinh: null;
  nbncqdinh: null;
  nbcqcqdinh: null;
  nbhtban: null;
  nmmdvqhnsach: null;
  nmddvchden: null;
  nmtgvchdtu: null;
  nmtgvchdden: null;
  nbtnban: null;
  dcdvnunlhdon: null;
  dksbke: null;
  dknlbke: null;
  thtttoan: 'TM/CK';
  msttcgp: '**********';
  cqtcks: '{"Subject":"CN=TỔNG CỤC THUẾ,O=BỘ TÀI CHÍNH,L=Hà Nội,C=VN","SerialNumber":"75580955FC81D6A7","Issuer":"CN=CA phục vụ các cơ quan Nhà nước G2, O=Ban Cơ yếu Chính phủ, C=VN","NotAfter":"2026-11-16T08:09:00","NotBefore":"2021-11-17T08:09:00","SigningTime":"2023-06-08T15:26:03"}';
  gchu: '';
  kqcht: null;
  hdhhdvu: {
    idhdon: '8d489b27-a6ee-4f86-a532-0d0b3538388f';
    id: 'd56c3a00-00af-4393-8726-bd628604c99f';
    dgia: null;
    dvtinh: null;
    ltsuat: '10%';
    sluong: 0.0;
    stbchu: null;
    stckhau: null;
    stt: 43;
    tchat: null;
    ten: null;
    thtcthue: null;
    thtien: 545455.0;
    tlckhau: null;
    tsuat: 0.1;
    tthue: 54545;
    sxep: 1;
    ttkhac: [];
    dvtte: null;
    tgia: null;
  }[];
  qrcode: null;
  ttmstten: null;
  ladhddtten: null;
  hdxkhau: null;
  hdxkptquan: null;
  hdgktkhthue: null;
  hdonLquans: null;
  tthdclquan: true;
  pdndungs: null;
  hdtbssrses: null;
}

/**
 * Invoice class from CQT MTT
 */
export interface CQTInvoiceMTT {
  nbmst: '0108392659';
  khmshdon: 1;
  khhdon: 'C23MAA';
  shdon: 17596;
  cqt: '0101';
  cttkhac: [
    {
      ttruong: 'InvoiceTemplateID';
      kdlieu: 'string';
      dlieu: 'afd44082-eda2-4ce8-8656-e57c645146ff';
    },
    {
      ttruong: 'IsTaxReduction43';
      kdlieu: 'numeric';
      dlieu: 'True';
    },
    {
      ttruong: 'RefID';
      kdlieu: 'string';
      dlieu: 'a5d8fd24af6d1c5fe47197f6ba245940';
    },
    {
      ttruong: 'TaxReductionDescription';
      kdlieu: 'string';
      dlieu: '101/2023/QH15';
    },
    {
      ttruong: 'AmountDecimalDigits';
      kdlieu: 'string';
      dlieu: '0';
    },
    {
      ttruong: 'AmountOCDecimalDigits';
      kdlieu: 'string';
      dlieu: '2';
    },
    {
      ttruong: 'CoefficientDecimalDigits';
      kdlieu: 'string';
      dlieu: '2';
    },
    {
      ttruong: 'ExchangRateDecimalDigits';
      kdlieu: 'string';
      dlieu: '0';
    },
    {
      ttruong: 'MainCurrency';
      kdlieu: 'string';
      dlieu: 'VND';
    },
    {
      ttruong: 'QuantityDecimalDigits';
      kdlieu: 'string';
      dlieu: '2';
    },
    {
      ttruong: 'UnitPriceDecimalDigits';
      kdlieu: 'string';
      dlieu: '2';
    },
    {
      ttruong: 'UnitPriceOCDecimalDigits';
      kdlieu: 'string';
      dlieu: '2';
    },
    {
      ttruong: 'AppID';
      kdlieu: 'string';
      dlieu: '169958F0-DAA7-4E7F-9FF7-B1487C54401F';
    },
    {
      ttruong: 'TransactionID';
      kdlieu: 'string';
      dlieu: '1MTRTE7NQK2';
    },
  ];
  hdon: '01';
  hsgoc: '978efa87-f9be-40b9-9103-5dd9cffb7d67';
  hthdon: 5;
  id: '6f84a62a-6fb6-44e7-88b4-7f30812e921d';
  idtbao: null;
  idtbhgthdon: null;
  idtbhgtrinh: null;
  khhdgoc: null;
  khmshdgoc: null;
  lhdgoc: null;
  mhdon: 'M1-23-QPIK8-00000017596';
  mtdtchieu: 'V01012431500038AD7B993C40BB8E0E098395A9C6D0';
  nbdchi: 'Phòng A10, tầng 29, Toà Đông, 29-01 Lotte Center Hà Nội, Số 54 Liễu Giai, Phường Cống Vị, Quận Ba Đình, Thành phố Hà Nội, Việt Nam';
  nbten: 'CÔNG TY TNHH HAI DI LAO VIET NAM HOLDINGS';
  ncnhat: '2023-09-03T14:44:17.617Z';
  ngcnhat: null;
  nky: null;
  nmmst: '0106870211';
  nmten: 'CÔNG TY CỔ PHẦN ICORP';
  ntao: '2023-09-03T14:44:17.617Z';
  ntnhan: '2023-09-03T14:44:17.005Z';
  pban: '2.0.1';
  ptgui: 1;
  shdgoc: null;
  tchat: 1;
  tdlap: '2023-09-02T17:00:00Z';
  tgtcthue: 4676000.0;
  tgtthue: 374080.0;
  tgtttbchu: 'năm triệu không trăm năm mươi ngàn không trăm tám mươi đồng';
  tgtttbso: 5050080.0;
  thdon: 'Hóa đơn khởi tạo từ máy tính tiền';
  thlap: 202309;
  thttltsuat: [
    {
      tsuat: '8%';
      thtien: 4676000.0;
      tthue: 374080.0;
      gttsuat: null;
    },
  ];
  tlhdon: 'Hóa đơn khởi tạo từ máy tính tiền';
  ttcktmai: 0.0;
  tthai: 1;
  tttbao: 0;
  ttttkhac: [
    {
      ttruong: 'TotalAmount';
      kdlieu: 'numeric';
      dlieu: '5050080.0000';
    },
    {
      ttruong: 'TotalAmountWithoutVAT';
      kdlieu: 'numeric';
      dlieu: '4676000.0000';
    },
    {
      ttruong: 'TotalAmountWithoutVATOC';
      kdlieu: 'numeric';
      dlieu: '4676000.0000';
    },
    {
      ttruong: 'TotalDiscountAmount';
      kdlieu: 'numeric';
      dlieu: '0.0000';
    },
    {
      ttruong: 'TotalDiscountAmountOC';
      kdlieu: 'numeric';
      dlieu: '0.0000';
    },
    {
      ttruong: 'TotalSaleAmount';
      kdlieu: 'numeric';
      dlieu: '4676000';
    },
    {
      ttruong: 'TotalSaleAmountOC';
      kdlieu: 'numeric';
      dlieu: '4676000';
    },
    {
      ttruong: 'TotalVATAmount';
      kdlieu: 'numeric';
      dlieu: '374080.0000';
    },
  ];
  ttxly: 8;
  tvandnkntt: '0101243150';
  ladhddt: 1;
  nbsdthoai: '0919 309 046';
  nbcks: null;
  nmsdthoai: '0989860402';
  nmcccd: null;
  bhphap: 0;
  gchdgoc: null;
  tbhgtngay: null;
  bhpldo: null;
  bhpcbo: null;
  bhpngay: null;
  tdlhdgoc: null;
  tentvandnkntt: null;
  kqcht: null;
  nbttkhac: [
    {
      ttruong: 'SellerAddress';
      kdlieu: 'string';
      dlieu: 'Phòng A10, tầng 29, Toà Đông, 29-01 Lotte Center Hà Nội, Số 54 Liễu Giai, Phường Cống Vị, Quận Ba Đình, Thành phố Hà Nội, Việt Nam';
    },
    {
      ttruong: 'SellerEmail';
      kdlieu: 'string';
      dlieu: '<EMAIL>';
    },
    {
      ttruong: 'SellerPhoneNumber';
      kdlieu: 'string';
      dlieu: '0919 309 046';
    },
  ];
  nmttkhac: [];
  ttkhac: [];
  nmloai: '0100';
  tghdmman: 0;
  tghdmmldo: null;
  cnhan: 0;
  kqchtmloi: [];
  hdhhdvu: {
    idhdon: '6f84a62a-6fb6-44e7-88b4-7f30812e921d';
    id: '0c800103-b45d-4a1f-a11c-335b0a5906e6';
    dgia: 80000.0;
    dvtinh: 'Phần';
    ltsuat: '8%';
    mhhdvu: null;
    sluong: 1.0;
    stbchu: null;
    stckhau: 0.0;
    stt: 1;
    sxep: 1;
    tchat: 1;
    ten: 'Tôm Viên Phô Mai';
    thtcthue: null;
    thtien: 80000.0;
    tlckhau: 0.0;
    tsuat: 0.08;
    tthue: null;
    ttkhac: [
      {
        ttruong: 'Amount';
        kdlieu: 'numeric';
        dlieu: '80000';
      },
      {
        ttruong: 'AmountAfterExciseTax';
        kdlieu: 'numeric';
        dlieu: '0';
      },
      {
        ttruong: 'AmountAfterExciseTaxOC';
        kdlieu: 'numeric';
        dlieu: '0';
      },
      {
        ttruong: 'AmountOC';
        kdlieu: 'numeric';
        dlieu: '80000';
      },
      {
        ttruong: 'AmountWithoutVATOC';
        kdlieu: 'numeric';
        dlieu: '80000.0000';
      },
      {
        ttruong: 'DiscountAmount';
        kdlieu: 'numeric';
        dlieu: '0';
      },
      {
        ttruong: 'SortOrder';
        kdlieu: 'numeric';
        dlieu: '1';
      },
      {
        ttruong: 'UnitPrice';
        kdlieu: 'numeric';
        dlieu: '80000';
      },
      {
        ttruong: 'UnitPriceAfterExciseVAT';
        kdlieu: 'numeric';
        dlieu: '0';
      },
      {
        ttruong: 'VATAmount';
        kdlieu: 'numeric';
        dlieu: '6400';
      },
      {
        ttruong: 'VATAmountOC';
        kdlieu: 'numeric';
        dlieu: '6400.0000';
      },
      {
        ttruong: 'UnitPriceAfterExciseVAT';
        kdlieu: 'numeric';
        dlieu: '0';
      },
      {
        ttruong: 'AmountAfterExciseTaxOC';
        kdlieu: 'numeric';
        dlieu: '0';
      },
      {
        ttruong: 'AmountAfterExciseTax';
        kdlieu: 'numeric';
        dlieu: '0';
      },
    ];
  }[];
  qrcode: '00020101021202000400260052005300540058005900600062009967000001100108392659020110306C23MAA04051759605082023090306095050080.0';
  ttmstten: null;
  ladhddtten: null;
  hdxkhau: null;
  hdxkptquan: null;
  hdgktkhthue: null;
  hdonLquans: null;
  tthdclquan: false;
  pdndungs: null;
  mtthdtbssrs: null;
}

export interface InvoiceProduct {
  invoiceProductId: number;
  invoiceId: number;
  name: string;
  productType: ProductType;
  code: string;
  /**
   * STT
   */
  sortOrder: number;
  quantity: number;
  price: number;
  /**
   * Tong tien
   */
  amountTotal: number;
  finalAmount: number;
  vat: VAT_TYPE;
  vatRate: number;
  vatAmount: number;
  unit: string;
  /**
   * Tỉ lệ CK
   */
  discount: number;
  discountAmount: number;
}

export type ProductType =
  /**
   * 1
   */
  | 'HHDV'
  /**
   * 2
   */
  | 'KM'
  /**
   * 3
   */
  | 'CKTM'
  /**
   * 4
   */
  | 'DG';

export interface InvoiceSupplierOrg {
  invoiceSupplierOrgId: number;
  companyName: string;
  address: string;
  taxCode: string;
  website: string;
  lookupWebsite: string;
  note: string;
}

export interface TaxReduceValidate {
  taxReduceValidateId: number;
  invoiceId: number;
  checkResult: TaxReduceValidateCheckResult;
  productName: string;
  vat: VAT_TYPE;
  isReduceTax: boolean;
  productCode: string;
}

export type TaxReduceValidateCheckResult =
  | 'NOT_CHECKED'
  | 'CHECKING'
  | 'VALID'
  | 'INVALID';

export interface AcceptTaxReduce {
  acceptTaxReduceId: number;
  productName: string;
  isTaxReduce: boolean;
  acceptBy: number;
  organizationId: number;
  organizationDepartmentId: number;
}
