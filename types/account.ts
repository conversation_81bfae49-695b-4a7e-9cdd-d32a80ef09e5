import {
  AccountOrganizationAccess,
  Company,
  Organization,
  OrganizationDepartment,
  Role,
} from './company';
import { CompanyTitle } from './';

export interface Account {
  accountId: number;
  accountLevel: AccountLevel;
  companyId: number;
  companyTitleId: number;
  roleId: number;
  fullname: string;
  password: string;
  salt: string;
  activateToken: string;
  resetPasswordToken: string;
  expirationDateResetPasswordToken: Date;
  phone: string;
  avatar: string;
  /**
   * `false: Ngừng sử dụng dịch vụ HDĐT`
   */
  active: boolean;
  companyUsername: string;
  email: string;
  organizationId: number;
  organizationDepartmentId: number;

  gender: Gender;
  cccdFrontImg: string;
  cccdBackImg: string;
  cccdNumber: string;
  cccdDate: Date | string;
  cccdCreatePlace: string;
  dob: string | Date;
  address?: string;
  locked: boolean;

  /* QLBH */
  metaAccountCode: string;
  metaAccountServiceCode: string;
  metaAccessToken: string;
  metaAccessTokenExpireDate: string;

  /* loai khach hang */
  loaiKhachHang: { [x in LoaiKhachHang]: string };

  /* associations */
  Company: Company;
  CompanyTitle: CompanyTitle;
  AccountOrganizationAccesses: AccountOrganizationAccess[];
  Organization: Organization;
  OrganizationDepartment: OrganizationDepartment;
  Role: Role;

  /**
   * @virtual
   * return by server only
   */
  taxCode?: string;
}

export type AccountLevel =
  /* NV cong ty */
  | 'COMPANY_USER'
  /* admin cong ty */
  | 'COMPANY_ADMIN';

export type Gender = 'MALE' | 'FEMALE' | 'OTHER';

export interface AccessToken {
  accessTokenId: number;
  accountId: number;
  accessToken: string;
  expireAt: Date | string;
}

export type Service = 'I_SMART' | 'I_PRO';

export type LoaiKhachHang = 'HOA_DON_DIEN_TU' | 'QUAN_LY_HOA_DON' | 'PETRO';
