import { Account } from './account';

export interface Notification {
  notificationId: number;
  type: NotificationType;
  thumbnail: string;
  description: string;
  url: string;
  title: string;
  targetCode: string;
  /* relation */
  Accounts: Account[];
}

export type NotificationType = 'ACTIVITY' | 'SYSTEM' | 'PROMOTION';

export interface AccountNotification {
  accountNotificationId: number;
  notificationId: number;
  accountId: number;

  /* relation */
  Account: Account;
  Notification: Notification;
}
