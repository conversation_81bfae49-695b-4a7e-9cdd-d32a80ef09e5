[{"method": "GET", "path": "/api/v1/test/api-list", "description": "Test only", "visible": 0}, {"method": "GET", "path": "/api/v1/test/list-hddr", "description": "Test only", "visible": 0}, {"method": "POST", "path": "/api/v1/test/validate-xml", "description": "Test only", "visible": 0}, {"method": "POST", "path": "/api/v1/test/xml-to-json", "description": "Test only", "visible": 0}, {"method": "GET", "path": "/api/v1/test/mails", "description": "Test only", "visible": 0}, {"method": "POST", "path": "/api/v1/test/macqt-from-xml", "description": "Test only", "visible": 0}, {"method": "POST", "path": "/api/v1/test/tracuu-cqt", "description": "Test only", "visible": 0}, {"method": "POST", "path": "/api/v1/test/tracuu-mst", "description": "Test only", "visible": 0}, {"method": "POST", "path": "/api/v1/test/preview-xml-in-pdf", "description": "Test only", "visible": 0}, {"method": "POST", "path": "/api/v1/test/fast-test", "description": "Test only", "visible": 0}, {"method": "POST", "path": "/api/v1/test/cqt-authenticate", "description": "Test only", "visible": 0}, {"method": "POST", "path": "/api/v1/test/send-html-to-email", "description": "Test only", "visible": 0}, {"method": "POST", "path": "/api/v1/qlbh/account/register", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/qlbh/account/login", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/qlbh/account/logout", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/account/find-dealer-by-referer-code", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/qlbh/account/create", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/qlbh/account/update/:accountId", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/qlbh/account/update", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/account/detail/:accountId", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/account/profile", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/account/find", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/qlbh/account/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/account/many-details", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/qlbh/account/change-password", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/qlbh/account/change-organization", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/qlbh/account/request-reset-password", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/qlbh/account/reset-password", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/qlbh/account/delete-account", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/qlbh/account/create-account", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/account/register", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/account/login", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/account/logout", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/account/find-dealer-by-referer-code", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/account/create", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/account/update/:accountId", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/account/update", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/account/detail/:accountId", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/account/profile", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/account/find", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/account/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/account/many-details", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/account/change-password", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/account/change-organization", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/account/request-reset-password", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/account/reset-password", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/account/delete-account", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/account/create-account", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/qlbh/instruction/create", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/instruction/find", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/qlbh/instruction/update/:instructionId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/qlbh/instruction/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/instruction/detail/:instructionId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/instruction/create", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/instruction/find", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/instruction/update/:instructionId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/instruction/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/instruction/detail/:instructionId", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/qlbh/company/update/:taxCode", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/company/detail/", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/company/update", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/company/find-by-tax-code", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/company/find-by-tax-codes", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/company-title/create", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/company-title/find", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/company-title/update/:companyTitleId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/company-title/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/company-title/detail/:companyTitleId", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/organization/find", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/organization/create", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/organization/update/:organizationId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/organization/delete", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/organization/create-with-many-address", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/organization/update-with-many-address/:organizationId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/organization-department/create", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/organization-department/update/:organizationDepartmentId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/organization-department/delete", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/qlbh/instruction-category/create", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/instruction-category/find", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/qlbh/instruction-category/update/:instructionCategoryId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/qlbh/instruction-category/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/instruction-category/detail/:instructionCategoryId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/instruction-category/create", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/instruction-category/find", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/instruction-category/update/:instructionCategoryId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/instruction-category/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/instruction-category/detail/:instructionCategoryId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/qlbh/faq/create", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/faq/find", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/qlbh/faq/update/:faqId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/qlbh/faq/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/faq/detail/:faqId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/faq/create", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/faq/find", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/faq/update/:faqId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/faq/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/faq/detail/:faqId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/role/create", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/role/find", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/role/update/:roleId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/role/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/role/detail/:roleId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/partner-company/create", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/partner-company/find", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/partner-company/update/:partnerCompanyId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/partner-company/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/partner-company/detail/:partnerCompanyId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/partner-company/synchronize", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/partner-company/import", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/partner-company/businesses", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/tag/create", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/tag/find", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/tag/update/:tagId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/tag/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/tag/detail/:tagId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/invoice-tag/create", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/invoice-tag/delete", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/qlbh/notification/create", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/notification/find", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/qlbh/notification/update/:notificationId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/qlbh/notification/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/notification/detail/:notificationId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/notification/create", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/notification/find", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/notification/update/:notificationId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/notification/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/notification/detail/:notificationId", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/misc/find-company-by-tax-code", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/misc/find-companies-by-queries", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/misc/find-voucher-by-code", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/misc/push-file", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/misc/find-voucher", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/permission-code/", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/audit-log/find", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/supplier/create", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/supplier/find", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/supplier/detail/:supplierId", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/supplier/visible/:supplierId", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/supplier/update/:supplierId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/supplier/delete", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/connection-supplier/connect", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/connection-supplier/find", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/connection-supplier/update/:connectionSupplierId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/connection-supplier/delete", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/connection-supplier/synchronize", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/invoice/find", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/invoice/detail/:invoiceId", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/invoice/payment/:invoiceId", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/invoice/return-payment/:invoiceId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/invoice/validate", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/invoice/tag", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/invoice/isDeleted", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/invoice/accounting", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/invoice/return-accountings", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/invoice/move", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/invoice/note", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/invoice/upload/:invoiceId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/invoice/delete-pdf/:invoiceId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/invoice/email", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/invoice/accept/:invoiceId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/invoice/export", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/invoice/print-result/:invoiceId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/invoice/accept-tax-reduce", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/invoice/manual-create/file", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/invoice/update-manual-invoice-input/:invoiceId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/invoice/manual-create/input", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/auth-log/mine", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/auth-log/mine-devices", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/comment-customer/find", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/comment-customer/detail/:commentCustomerId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/comment-customer/create", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/comment-customer/update/:commentCustomerId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/comment-customer/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/contact-customer/find", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/contact-customer/detail/:contactId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/qlbh/contact-customer/create", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/qlbh/contact-customer/update/:contactId", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/contact-customer/summary", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/contact-customer/find", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/contact-customer/detail/:contactId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/contact-customer/create", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/contact-customer/update/:contactId", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/contact-customer/summary", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/tax-reduced-document/find", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/tax-reduced-document/create", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/tax-reduced-document/update/:code", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/tax-reduced-document/detail/:code", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/tax-reduced-document/delete/:code", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/risky-company/find", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/risky-company/detail/:companyId", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/vietinvoice/detail/:invoiceId", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/vietinvoice/:invoiceId", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/vietinvoice/", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/package/find", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/package/find-dealer-package", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/package/detail/:packageId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/qlbh/package/create", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/qlbh/package/update/:packageId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/qlbh/package/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/package/find", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/package/find-dealer-package", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/package/detail/:packageId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/package/create", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/package/update/:packageId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/package/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/order/find", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/order/history", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/order/detail/:orderId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/order/create", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/order/update/:orderId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/order/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/order/vnpay_return", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/order/repayment/:orderId", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/order/email_payment/:orderId", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/config/find", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/qlbh/config/detail/:configId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/qlbh/config/create", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/qlbh/config/update/:configId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/qlbh/config/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/config/find", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/config/detail/:configId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/config/create", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/config/update/:configId", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/config/delete", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/connection-supplier-history/find", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/connection-supplier-history/detail/:connectionSupplierHistoryId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/connection-supplier-history/reload", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/cqt-invoice-history/find", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/cqt-invoice-history/detail/:cqtInvoiceHistoryId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/cqt-invoice-history/reload", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/cqt/connection", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/cqt/disconnection", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/cqt/reconnection", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/cqt/synchronization", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/cqt/create-new-organization", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/dashboard/analyze", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/dashboard/analyze-day", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/dashboard/analyze-total-value", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/dashboard/report-total-invoice", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/resource-history/find", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/invoice-attach/delete", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/product/import", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/product/update", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/product/find", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/list-supplier-not-download-invoice/create", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/list-supplier-not-download-invoice/find", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/setting/", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/mail/find", "description": "", "visible": 1}, {"method": "DELETE", "path": "/api/v1/mail/delete", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/mail/into-trash", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/mail/out-trash", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/mail/set-important", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/mail/set-unimportant", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/mail/detail/:mailId", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/mail/mark-unread/", "description": "", "visible": 1}, {"method": "PUT", "path": "/api/v1/mail/mark-read/", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/mail/forward/:mailId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/mail/synchronize", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/mail/validate", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/mail/test", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/auto-transfer-invoice-department/create", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/auto-transfer-invoice-department/find", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/auto-transfer-invoice-department/detail/:autoTransferInvoiceId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/auto-transfer-invoice-from-email/create", "description": "", "visible": 1}, {"method": "GET", "path": "/api/v1/auto-transfer-invoice-from-email/find", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/accept-partner-company-detail/create/:invoiceId", "description": "", "visible": 1}, {"method": "POST", "path": "/api/v1/accept-partner-company-detail/create", "description": "", "visible": 1}]