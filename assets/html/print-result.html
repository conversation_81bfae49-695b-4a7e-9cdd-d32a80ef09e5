<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <!-- <link rel="stylesheet" href="globals.css" />
    <link rel="stylesheet" href="styleguide.css" /> -->
    <!-- <link rel="stylesheet" href="style.css" /> -->
    <style>
      @import url('https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css');
      * {
        -webkit-font-smoothing: antialiased;
        box-sizing: border-box;
      }
      html,
      body {
        margin: 0px;
        height: 100%;
      }
      /* a blue color as a generic focus style */
      button:focus-visible {
        outline: 2px solid #4a90e2 !important;
        outline: -webkit-focus-ring-color auto 5px !important;
      }
      a {
        text-decoration: none;
      }
      @import url('https://fonts.googleapis.com/css?family=Inter:400');
      @font-face {
        font-family: 'Font Awesome 6 Pro-Solid';
        src: url('https://anima-uploads.s3.amazonaws.com/projects/604bac611956acf622fcb3ae/fonts/font-awesome-6-pro-solid-900.otf')
          format('opentype');
      }

      :root {
        --secondaryred: rgba(244, 65, 65, 1);
        --secondaryyeallow: rgba(255, 163, 24, 1);
        --secondarygreen: rgba(40, 160, 67, 1);
        --primaryturquois: rgba(0, 187, 179, 1);
        --primaryblue: rgba(69, 112, 254, 1);
        --texttext-1: rgba(28, 34, 86, 1);
        --texttext-2: rgba(60, 67, 118, 1);
        --texttext-3: rgba(113, 119, 168, 1);
        --systemblue-grey: rgba(221, 229, 238, 1);
        --systemstroke: rgba(232, 233, 235, 1);
        --systemgrey: rgba(245, 245, 245, 1);
        --systembg-select: rgba(248, 248, 248, 1);
        --systemwhite: rgba(255, 255, 255, 1);
        --system-selectselected: rgba(225, 240, 255, 1);
        --system-selectstroke-select: rgba(198, 220, 242, 1);
        --landingpagetext-1: rgba(24, 33, 77, 1);
        --landingpagetext-2: rgba(92, 96, 119, 1);
        --header-h1-font-family: 'Inter-Bold', Helvetica;
        --header-h1-font-weight: 700;
        --header-h1-font-size: 40px;
        --header-h1-letter-spacing: 0px;
        --header-h1-line-height: 60px;
        --header-h1-font-style: normal;
        --header-h2-font-family: 'Inter-ExtraBold', Helvetica;
        --header-h2-font-weight: 800;
        --header-h2-font-size: 32px;
        --header-h2-letter-spacing: 0px;
        --header-h2-line-height: 38.870948791503906px;
        --header-h2-font-style: normal;
        --header-h3-font-family: 'Inter-Bold', Helvetica;
        --header-h3-font-weight: 700;
        --header-h3-font-size: 28px;
        --header-h3-letter-spacing: 0px;
        --header-h3-line-height: 34px;
        --header-h3-font-style: normal;
        --header-h4-font-family: 'Inter-SemiBold', Helvetica;
        --header-h4-font-weight: 600;
        --header-h4-font-size: 28px;
        --header-h4-letter-spacing: 0px;
        --header-h4-line-height: 34px;
        --header-h4-font-style: normal;
        --header-h5-font-family: 'Inter-SemiBold', Helvetica;
        --header-h5-font-weight: 600;
        --header-h5-font-size: 20px;
        --header-h5-letter-spacing: 0px;
        --header-h5-line-height: 120.00000476837158%;
        --header-h5-font-style: normal;
        --header-h6-font-family: 'Inter-Bold', Helvetica;
        --header-h6-font-weight: 700;
        --header-h6-font-size: 18px;
        --header-h6-letter-spacing: 0px;
        --header-h6-line-height: 22px;
        --header-h6-font-style: normal;
        --header-mini-header-font-family: 'Inter-SemiBold', Helvetica;
        --header-mini-header-font-weight: 600;
        --header-mini-header-font-size: 12px;
        --header-mini-header-letter-spacing: 0px;
        --header-mini-header-line-height: 16px;
        --header-mini-header-font-style: normal;
        --subtittle-label-bold-16-font-family: 'Inter-Bold', Helvetica;
        --subtittle-label-bold-16-font-weight: 700;
        --subtittle-label-bold-16-font-size: 16px;
        --subtittle-label-bold-16-letter-spacing: -0.14399999618530274px;
        --subtittle-label-bold-16-line-height: 16px;
        --subtittle-label-bold-16-font-style: normal;
        --subtittle-subtitle-semi-bold-16-font-family: 'Inter-SemiBold',
          Helvetica;
        --subtittle-subtitle-semi-bold-16-font-weight: 600;
        --subtittle-subtitle-semi-bold-16-font-size: 16px;
        --subtittle-subtitle-semi-bold-16-letter-spacing: 0px;
        --subtittle-subtitle-semi-bold-16-line-height: 20px;
        --subtittle-subtitle-semi-bold-16-font-style: normal;
        --body-text-bold-14-font-family: 'Inter-Bold', Helvetica;
        --body-text-bold-14-font-weight: 700;
        --body-text-bold-14-font-size: 14px;
        --body-text-bold-14-letter-spacing: 0px;
        --body-text-bold-14-line-height: 139.9999976158142%;
        --body-text-bold-14-font-style: normal;
        --body-text-medium-font-family: 'Inter-Medium', Helvetica;
        --body-text-medium-font-weight: 500;
        --body-text-medium-font-size: 14px;
        --body-text-medium-letter-spacing: 0px;
        --body-text-medium-line-height: 139.9999976158142%;
        --body-text-medium-font-style: normal;
        --body-text-regular-14-font-family: 'Inter-Regular', Helvetica;
        --body-text-regular-14-font-weight: 400;
        --body-text-regular-14-font-size: 14px;
        --body-text-regular-14-letter-spacing: 0px;
        --body-text-regular-14-line-height: 139.9999976158142%;
        --body-text-regular-14-font-style: normal;
        --body-description-12-font-family: 'Inter-Regular', Helvetica;
        --body-description-12-font-weight: 400;
        --body-description-12-font-size: 12px;
        --body-description-12-letter-spacing: 0px;
        --body-description-12-line-height: 16px;
        --body-description-12-font-style: normal;
        --icon-ICON-font-family: 'FontAwesome 6 Pro-Regular', Helvetica;
        --icon-ICON-font-weight: 400;
        --icon-ICON-font-size: 16px;
        --icon-ICON-letter-spacing: 0px;
        --icon-ICON-line-height: normal;
        --icon-ICON-font-style: normal;
        --icon-icon-solid-font-family: 'FontAwesome 6 Pro-Solid', Helvetica;
        --icon-icon-solid-font-weight: 400;
        --icon-icon-solid-font-size: 14px;
        --icon-icon-solid-letter-spacing: 0px;
        --icon-icon-solid-line-height: normal;
        --icon-icon-solid-font-style: normal;
        --icon-icon-stroke-font-family: 'FontAwesome 6 Pro-Regular', Helvetica;
        --icon-icon-stroke-font-weight: 400;
        --icon-icon-stroke-font-size: 14px;
        --icon-icon-stroke-letter-spacing: 0px;
        --icon-icon-stroke-line-height: normal;
        --icon-icon-stroke-font-style: normal;
        --shadow: 0px 8px 24px 0px rgba(37, 42, 91, 0.05);
        --shadow-2: 0px 5px 10px 0px rgba(44, 63, 88, 0.1);
      }
      .frame {
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        gap: 24px;
        padding: 60px 0px;
        position: relative;
      }

      .frame .div {
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        position: relative;
        flex: 0 0 auto;
      }

      .frame .text-wrapper {
        position: relative;
        width: fit-content;
        margin-top: -1px;
        font-family: var(--header-h2-font-family);
        font-weight: var(--header-h2-font-weight);
        color: var(--texttext-1);
        font-size: var(--header-h2-font-size);
        letter-spacing: var(--header-h2-letter-spacing);
        line-height: var(--header-h2-line-height);
        white-space: nowrap;
        font-style: var(--header-h2-font-style);
      }

      .frame .p {
        position: relative;
        width: fit-content;
        font-family: var(--header-h5-font-family);
        font-weight: var(--header-h5-font-weight);
        color: var(--texttext-3);
        font-size: var(--header-h5-font-size);
        letter-spacing: var(--header-h5-letter-spacing);
        line-height: var(--header-h5-line-height);
        white-space: nowrap;
        font-style: var(--header-h5-font-style);
      }

      .frame .div-2 {
        width: 960px;
        align-items: flex-start;
        gap: 24px;
        padding: 24px 80px;
        background-color: var(--systemwhite);
        display: flex;
        position: relative;
        flex: 0 0 auto;
      }

      .frame .div-3 {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        position: relative;
        flex: 1;
        flex-grow: 1;
      }

      .frame .div-4 {
        gap: 8px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        position: relative;
        align-self: stretch;
        width: 100%;
        flex: 0 0 auto;
      }

      .frame .div-5 {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 8px 0px;
        position: relative;
        align-self: stretch;
        width: 100%;
        flex: 0 0 auto;
      }

      .frame .text-wrapper-2 {
        position: relative;
        width: fit-content;
        font-family: 'Font Awesome 6 Pro-Solid', Helvetica;
        font-weight: 400;
        color: var(--primaryblue);
        font-size: 20px;
        letter-spacing: 0;
        line-height: normal;
        white-space: nowrap;
      }

      .frame .text-wrapper-3 {
        position: relative;
        width: fit-content;
        margin-top: -1px;
        font-family: var(--header-h5-font-family);
        font-weight: var(--header-h5-font-weight);
        color: var(--primaryblue);
        font-size: var(--header-h5-font-size);
        letter-spacing: var(--header-h5-letter-spacing);
        line-height: var(--header-h5-line-height);
        white-space: nowrap;
        font-style: var(--header-h5-font-style);
      }

      .frame .div-6 {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 11px;
        position: relative;
        align-self: stretch;
        width: 100%;
        flex: 0 0 auto;
      }

      .frame .div-7 {
        display: flex;
        align-items: flex-start;
        gap: 4px;
        position: relative;
        align-self: stretch;
        width: 100%;
        flex: 0 0 auto;
      }

      .frame .text-wrapper-4 {
        position: relative;
        width: 100px;
        margin-top: -1px;
        font-family: var(--body-text-medium-font-family);
        font-weight: var(--body-text-medium-font-weight);
        color: var(--texttext-2);
        font-size: var(--body-text-medium-font-size);
        letter-spacing: var(--body-text-medium-letter-spacing);
        line-height: var(--body-text-medium-line-height);
        font-style: var(--body-text-medium-font-style);
      }

      .frame .text-wrapper-5 {
        position: relative;
        flex: 1;
        margin-top: -1px;
        font-family: var(--body-text-medium-font-family);
        font-weight: var(--body-text-medium-font-weight);
        color: var(--texttext-1);
        font-size: var(--body-text-medium-font-size);
        letter-spacing: var(--body-text-medium-letter-spacing);
        line-height: var(--body-text-medium-line-height);
        font-style: var(--body-text-medium-font-style);
      }

      .frame .text-wrapper-6 {
        font-family: var(--body-text-medium-font-family);
        font-weight: var(--body-text-medium-font-weight);
        position: relative;
        width: fit-content;
        margin-top: -1px;
        color: var(--texttext-1);
        font-size: var(--body-text-medium-font-size);
        letter-spacing: var(--body-text-medium-letter-spacing);
        line-height: var(--body-text-medium-line-height);
        white-space: nowrap;
        font-style: var(--body-text-medium-font-style);
      }

      .frame .text-wrapper-7 {
        position: relative;
        width: fit-content;
        margin-top: -1px;
        font-family: var(--body-text-medium-font-family);
        font-weight: var(--body-text-medium-font-weight);
        color: var(--texttext-3);
        font-size: var(--body-text-medium-font-size);
        letter-spacing: var(--body-text-medium-letter-spacing);
        line-height: var(--body-text-medium-line-height);
        white-space: nowrap;
        font-style: var(--body-text-medium-font-style);
      }

      .frame .sperator {
        position: relative;
        align-self: stretch;
        width: 100%;
        height: 1px;
        background-color: var(--systemstroke);
      }

      .frame .text-wrapper-8 {
        position: relative;
        width: 200px;
        margin-top: -1px;
        font-family: var(--body-text-medium-font-family);
        font-weight: var(--body-text-medium-font-weight);
        color: var(--texttext-2);
        font-size: var(--body-text-medium-font-size);
        letter-spacing: var(--body-text-medium-letter-spacing);
        line-height: var(--body-text-medium-line-height);
        font-style: var(--body-text-medium-font-style);
      }

      .frame .text-wrapper-9 {
        flex: 1;
        position: relative;
        margin-top: -1px;
        font-family: var(--body-text-bold-14-font-family);
        font-weight: var(--body-text-bold-14-font-weight);
        color: var(--texttext-1);
        font-size: var(--body-text-bold-14-font-size);
        letter-spacing: var(--body-text-bold-14-letter-spacing);
        line-height: var(--body-text-bold-14-line-height);
        font-style: var(--body-text-bold-14-font-style);
      }

      .frame .text-wrapper-10 {
        font-family: var(--body-text-bold-14-font-family);
        font-weight: var(--body-text-bold-14-font-weight);
        position: relative;
        width: fit-content;
        margin-top: -1px;
        color: var(--texttext-1);
        font-size: var(--body-text-bold-14-font-size);
        letter-spacing: var(--body-text-bold-14-letter-spacing);
        line-height: var(--body-text-bold-14-line-height);
        white-space: nowrap;
        font-style: var(--body-text-bold-14-font-style);
      }

      .frame .div-8 {
        display: inline-flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
        position: relative;
        flex: 0 0 auto;
      }

      .frame .h-a-n-h-p-l-wrapper {
        align-items: center;
        justify-content: center;
        gap: 10px;
        padding: 16px;
        align-self: stretch;
        width: 100%;
        background-color: rgba(40, 160, 67, 0.05);
        border-radius: 8px;
        display: flex;
        position: relative;
        flex: 0 0 auto;
      }

      .frame .h-a-n-h-p-l {
        position: relative;
        width: fit-content;
        margin-top: -1px;
        font-family: var(--subtittle-label-bold-16-font-family);
        font-weight: var(--subtittle-label-bold-16-font-weight);
        color: var(--secondarygreen);
        font-size: var(--subtittle-label-bold-16-font-size);
        letter-spacing: var(--subtittle-label-bold-16-letter-spacing);
        line-height: var(--subtittle-label-bold-16-line-height);
        white-space: nowrap;
        font-style: var(--subtittle-label-bold-16-font-style);
      }

      .frame .text-wrapper-11 {
        position: relative;
        width: fit-content;
        font-family: var(--body-description-12-font-family);
        font-weight: var(--body-description-12-font-weight);
        color: var(--texttext-3);
        font-size: var(--body-description-12-font-size);
        letter-spacing: var(--body-description-12-letter-spacing);
        line-height: var(--body-description-12-line-height);
        white-space: nowrap;
        font-style: var(--body-description-12-font-style);
      }

      .frame .div-9 {
        display: flex;
        flex-direction: column;
        width: 283px;
        align-items: flex-start;
        gap: 11px;
        padding: 24px;
        position: relative;
        flex: 0 0 auto;
        border-radius: 8px;
        border: 1px solid;
        border-color: var(--systemstroke);
      }

      .frame .text-wrapper-12 {
        position: relative;
        width: fit-content;
        margin-top: -1px;
        font-family: var(--body-text-medium-font-family);
        font-weight: var(--body-text-medium-font-weight);
        color: var(--texttext-2);
        font-size: var(--body-text-medium-font-size);
        letter-spacing: var(--body-text-medium-letter-spacing);
        line-height: var(--body-text-medium-line-height);
        white-space: nowrap;
        font-style: var(--body-text-medium-font-style);
      }

      .frame .text-wrapper-13 {
        position: relative;
        flex: 1;
        margin-top: -1px;
        font-family: var(--body-text-bold-14-font-family);
        font-weight: var(--body-text-bold-14-font-weight);
        color: var(--texttext-1);
        font-size: var(--body-text-bold-14-font-size);
        text-align: right;
        letter-spacing: var(--body-text-bold-14-letter-spacing);
        line-height: var(--body-text-bold-14-line-height);
        font-style: var(--body-text-bold-14-font-style);
      }

      .frame .text-wrapper-14 {
        position: relative;
        width: fit-content;
        margin-top: -1px;
        font-family: var(--body-text-medium-font-family);
        font-weight: var(--body-text-medium-font-weight);
        color: var(--primaryturquois);
        font-size: var(--body-text-medium-font-size);
        letter-spacing: var(--body-text-medium-letter-spacing);
        line-height: var(--body-text-medium-line-height);
        white-space: nowrap;
        font-style: var(--body-text-medium-font-style);
      }

      .frame .frame-wrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        position: relative;
        align-self: stretch;
        width: 100%;
        flex: 0 0 auto;
      }

      .frame .div-10 {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        padding: 16px;
        align-self: stretch;
        width: 100%;
        background-color: #28a0421a;
        border-radius: 8px;
        position: relative;
        flex: 0 0 auto;
      }

      .frame .text-wrapper-15 {
        position: relative;
        width: fit-content;
        margin-top: -1px;
        font-family: var(--icon-ICON-font-family);
        font-weight: var(--icon-ICON-font-weight);
        color: #28a042;
        font-size: var(--icon-ICON-font-size);
        letter-spacing: var(--icon-ICON-letter-spacing);
        line-height: var(--icon-ICON-line-height);
        white-space: nowrap;
        font-style: var(--icon-ICON-font-style);
      }

      .frame .text-wrapper-16 {
        position: relative;
        width: fit-content;
        margin-top: -1px;
        font-family: var(--subtittle-label-bold-16-font-family);
        font-weight: var(--subtittle-label-bold-16-font-weight);
        color: #28a042;
        font-size: var(--subtittle-label-bold-16-font-size);
        letter-spacing: var(--subtittle-label-bold-16-letter-spacing);
        line-height: var(--subtittle-label-bold-16-line-height);
        white-space: nowrap;
        font-style: var(--subtittle-label-bold-16-font-style);
      }

      .frame .div-wrapper {
        display: flex;
        flex-direction: column;
        width: 960px;
        align-items: center;
        gap: 10px;
        padding: 0px 80px;
        position: relative;
        flex: 0 0 auto;
      }

      .frame .div-11 {
        align-items: flex-start;
        gap: 16px;
        padding: 24px;
        align-self: stretch;
        width: 100%;
        background-color: var(--systemwhite);
        border-radius: 20px;
        border: 2px solid;
        border-color: var(--system-selectstroke-select);
        display: flex;
        position: relative;
        flex: 0 0 auto;
      }

      .frame .text-wrapper-17 {
        position: relative;
        align-self: stretch;
        margin-top: -1px;
        font-family: var(--subtittle-label-bold-16-font-family);
        font-weight: var(--subtittle-label-bold-16-font-weight);
        color: var(--primaryblue);
        font-size: var(--subtittle-label-bold-16-font-size);
        letter-spacing: var(--subtittle-label-bold-16-letter-spacing);
        line-height: var(--subtittle-label-bold-16-line-height);
        font-style: var(--subtittle-label-bold-16-font-style);
      }

      .frame .div-12 {
        gap: 30px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        position: relative;
        align-self: stretch;
        width: 100%;
        flex: 0 0 auto;
      }

      .frame .div-13 {
        display: flex;
        align-items: center;
        gap: 8px;
        position: relative;
        align-self: stretch;
        width: 100%;
        flex: 0 0 auto;
      }

      .frame .text-wrapper-18 {
        width: fit-content;
        color: var(--secondaryred);
        white-space: nowrap;
        position: relative;
        font-family: var(--icon-ICON-font-family);
        font-weight: var(--icon-ICON-font-weight);
        font-size: var(--icon-ICON-font-size);
        letter-spacing: var(--icon-ICON-letter-spacing);
        line-height: var(--icon-ICON-line-height);
        font-style: var(--icon-ICON-font-style);
      }

      .frame .text-wrapper-19 {
        position: relative;
        width: fit-content;
        margin-top: -1px;
        font-family: var(--body-text-bold-14-font-family);
        font-weight: var(--body-text-bold-14-font-weight);
        color: var(--texttext-2);
        font-size: var(--body-text-bold-14-font-size);
        letter-spacing: var(--body-text-bold-14-letter-spacing);
        line-height: var(--body-text-bold-14-line-height);
        white-space: nowrap;
        font-style: var(--body-text-bold-14-font-style);
      }

      .frame .div-14 {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        gap: 4px;
        padding: 0px 0px 0px 16px;
        position: relative;
        align-self: stretch;
        width: 100%;
        flex: 0 0 auto;
      }

      .frame .text-wrapper-20 {
        position: relative;
        align-self: stretch;
        margin-top: -1px;
        font-family: var(--body-text-medium-font-family);
        font-weight: var(--body-text-medium-font-weight);
        color: var(--texttext-3);
        font-size: var(--body-text-medium-font-size);
        letter-spacing: var(--body-text-medium-letter-spacing);
        line-height: var(--body-text-medium-line-height);
        font-style: var(--body-text-medium-font-style);
      }

      .frame .text-wrapper-21 {
        position: relative;
        align-self: stretch;
        font-family: var(--body-text-medium-font-family);
        font-weight: var(--body-text-medium-font-weight);
        color: var(--secondaryred);
        font-size: var(--body-text-medium-font-size);
        letter-spacing: var(--body-text-medium-letter-spacing);
        line-height: var(--body-text-medium-line-height);
        font-style: var(--body-text-medium-font-style);
      }

      .frame .text-wrapper-22 {
        position: relative;
        align-self: stretch;
        margin-top: -1px;
        font-family: var(--body-text-medium-font-family);
        font-weight: var(--body-text-medium-font-weight);
        color: var(--secondaryred);
        font-size: var(--body-text-medium-font-size);
        letter-spacing: var(--body-text-medium-letter-spacing);
        line-height: var(--body-text-medium-line-height);
        font-style: var(--body-text-medium-font-style);
      }

      .frame .ng-a-ch-ng-i-b-n-i {
        position: relative;
        align-self: stretch;
        font-family: 'Inter-Medium', Helvetica;
        font-weight: 400;
        color: var(--texttext-3);
        font-size: 14px;
        letter-spacing: 0;
        line-height: 19.6px;
      }

      .frame .span {
        font-weight: var(--body-text-medium-font-weight);
        font-family: var(--body-text-medium-font-family);
        font-style: var(--body-text-medium-font-style);
        letter-spacing: var(--body-text-medium-letter-spacing);
        line-height: var(--body-text-medium-line-height);
        font-size: var(--body-text-medium-font-size);
      }

      .frame .text-wrapper-23 {
        font-family: 'Inter-Italic', Helvetica;
        font-style: italic;
        font-size: 12px;
        line-height: 16.8px;
      }

      .frame .div-15 {
        align-items: flex-start;
        gap: 8px;
        align-self: stretch;
        width: 100%;
        display: flex;
        position: relative;
        flex: 0 0 auto;
      }

      /* width: 16px; */
      .frame .text-wrapper-24 {
        margin-top: -1px;
        color: var(--secondarygreen);
        position: relative;
        font-family: var(--icon-ICON-font-family);
        font-weight: var(--icon-ICON-font-weight);
        font-size: var(--icon-ICON-font-size);
        letter-spacing: var(--icon-ICON-letter-spacing);
        line-height: var(--icon-ICON-line-height);
        font-style: var(--icon-ICON-font-style);
      }

      .frame .text-wrapper-25 {
        position: relative;
        flex: 1;
        margin-top: -1px;
        font-family: var(--body-text-bold-14-font-family);
        font-weight: var(--body-text-bold-14-font-weight);
        color: var(--texttext-2);
        font-size: var(--body-text-bold-14-font-size);
        letter-spacing: var(--body-text-bold-14-letter-spacing);
        line-height: var(--body-text-bold-14-line-height);
        font-style: var(--body-text-bold-14-font-style);
      }

      .frame .text-wrapper-26 {
        position: relative;
        align-self: stretch;
        font-family: var(--body-text-medium-font-family);
        font-weight: var(--body-text-medium-font-weight);
        color: var(--texttext-3);
        font-size: var(--body-text-medium-font-size);
        letter-spacing: var(--body-text-medium-letter-spacing);
        line-height: var(--body-text-medium-line-height);
        font-style: var(--body-text-medium-font-style);
      }

      .frame .text-wrapper-27 {
        position: relative;
        align-self: stretch;
        margin-top: -1px;
        font-family: var(--body-text-bold-14-font-family);
        font-weight: var(--body-text-bold-14-font-weight);
        color: var(--primaryblue);
        font-size: var(--body-text-bold-14-font-size);
        letter-spacing: var(--body-text-bold-14-letter-spacing);
        line-height: var(--body-text-bold-14-line-height);
        font-style: var(--body-text-bold-14-font-style);
      }

      .frame .text-wrapper-28 {
        position: relative;
        width: fit-content;
        font-family: var(--body-text-medium-font-family);
        font-weight: var(--body-text-medium-font-weight);
        color: var(--texttext-3);
        font-size: var(--body-text-medium-font-size);
        letter-spacing: var(--body-text-medium-letter-spacing);
        line-height: var(--body-text-medium-line-height);
        white-space: nowrap;
        font-style: var(--body-text-medium-font-style);
      }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/handlebars@latest/dist/handlebars.js"></script>
  </head>
  <body>
    <div class="frame">
      <div
        class="div"
        style="display: flex; justify-items: center; align-items: center"
      >
        <p
          class="text-wrapper"
          style="font-family: system-ui; font-weight: 800; margin: 0"
        >
          KẾT QUẢ KIỂM TRA THÔNG TIN HÓA ĐƠN
        </p>
        <div>
          <div class="p" style="margin: 0">
            Tại thời điểm ngày {{checkDate}}
            <!-- <div class="p" id="dateSearch">{{checkDate}}</div> -->
          </div>
        </div>
      </div>
      <div class="div-2" style="padding-bottom: 0; padding-top: 0">
        <div class="div-3">
          <div class="div-4">
            <div class="div-5">
              <div class="text-wrapper-2">
                <svg
                  width="18"
                  height="21"
                  viewBox="0 0 18 21"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M4.375 4.875C4.375 2.49219 6.32812 0.5 8.75 0.5C11.1328 0.5 13.125 2.49219 13.125 4.875V6.75H15.625C16.6406 6.75 17.5 7.60938 17.5 8.625V16.75C17.5 18.8203 15.8203 20.5 13.75 20.5H3.75C1.64062 20.5 0 18.8203 0 16.75V8.625C0 7.60938 0.820312 6.75 1.875 6.75H4.375V4.875ZM6.25 6.75H11.25V4.875C11.25 3.50781 10.1172 2.375 8.75 2.375C7.34375 2.375 6.25 3.50781 6.25 4.875V6.75ZM5.3125 10.5C5.82031 10.5 6.25 10.1094 6.25 9.5625C6.25 9.05469 5.82031 8.625 5.3125 8.625C4.76562 8.625 4.375 9.05469 4.375 9.5625C4.375 10.1094 4.76562 10.5 5.3125 10.5ZM12.1875 8.625C11.6406 8.625 11.25 9.05469 11.25 9.5625C11.25 10.1094 11.6406 10.5 12.1875 10.5C12.6953 10.5 13.125 10.1094 13.125 9.5625C13.125 9.05469 12.6953 8.625 12.1875 8.625Z"
                    fill="#4570FE"
                  />
                </svg>
              </div>
              <div class="text-wrapper-3">Đơn vị bán hàng</div>
            </div>
            <div class="div-6">
              <div class="div-7">
                <div class="text-wrapper-4">Đơn vị:</div>
                <div class="text-wrapper-5" id="sellerName">{{sellerName}}</div>
              </div>
              <div class="div-7">
                <div class="text-wrapper-4">Mã số thuế:</div>
                <div class="text-wrapper-6" id="sellerTaxCode">
                  {{sellerTaxCode}}
                </div>
              </div>
              <div class="div-7">
                <div class="text-wrapper-4">Địa chỉ:</div>
                <div
                  class="text-wrapper-7"
                  id="buyerAddress"
                  style="white-space: normal; width: 385px"
                >
                  {{sellerAddress}}
                </div>
              </div>
            </div>
          </div>
          <div class="sperator"></div>
          <div class="div-4">
            <div class="div-5">
              <div class="text-wrapper-2">
                <svg
                  width="23"
                  height="21"
                  viewBox="0 0 23 21"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M3.75 0.5C4.17969 0.5 4.57031 0.851562 4.64844 1.28125L4.72656 1.75H21.1328C21.9531 1.75 22.5781 2.57031 22.3438 3.35156L20.2344 10.8516C20.0781 11.3984 19.6094 11.75 19.0234 11.75H6.64062L6.99219 13.625H19.0625C19.5703 13.625 20 14.0547 20 14.5625C20 15.1094 19.5703 15.5 19.0625 15.5H6.21094C5.78125 15.5 5.39062 15.1875 5.3125 14.7578L2.96875 2.375H0.9375C0.390625 2.375 0 1.98438 0 1.4375C0 0.929688 0.390625 0.5 0.9375 0.5H3.75ZM5 18.625C5 17.6094 5.82031 16.75 6.875 16.75C7.89062 16.75 8.75 17.6094 8.75 18.625C8.75 19.6797 7.89062 20.5 6.875 20.5C5.82031 20.5 5 19.6797 5 18.625ZM20 18.625C20 19.6797 19.1406 20.5 18.125 20.5C17.0703 20.5 16.25 19.6797 16.25 18.625C16.25 17.6094 17.0703 16.75 18.125 16.75C19.1406 16.75 20 17.6094 20 18.625Z"
                    fill="#4570FE"
                  />
                </svg>
              </div>
              <div class="text-wrapper-3">Đơn vị mua hàng</div>
            </div>
            <div class="div-6">
              <div class="div-7">
                <div class="text-wrapper-4">Đơn vị:</div>
                <div class="text-wrapper-5" id="buyerName">{{buyerName}}</div>
              </div>
              <div class="div-7">
                <div class="text-wrapper-4">Mã số thuế:</div>
                <div class="text-wrapper-6" id="buyerTaxCode">
                  {{buyerTaxCode}}
                </div>
              </div>
              <div class="div-7">
                <div class="text-wrapper-4">Địa chỉ:</div>
                <!-- <textarea class="text-wrapper-7" id="buyerAddress"  >  {{buyerAddress}}</textarea> -->
                <div
                  class="text-wrapper-7"
                  id="buyerAddress"
                  style="white-space: normal; width: 385px"
                >
                  {{buyerAddress}}
                </div>
              </div>
            </div>
          </div>
          <div class="sperator"></div>
          <div class="div-4">
            <div class="div-5">
              <div class="text-wrapper-2">
                <svg
                  width="23"
                  height="15"
                  viewBox="0 0 23 15"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20 0C21.3672 0 22.5 1.13281 22.5 2.5V12.5C22.5 13.9062 21.3672 15 20 15H2.5C1.09375 15 0 13.9062 0 12.5V2.5C0 1.13281 1.09375 0 2.5 0H20ZM5 12.5C5 11.1328 3.86719 10 2.5 10V12.5H5ZM2.5 5C3.86719 5 5 3.90625 5 2.5H2.5V5ZM20 12.5V10C18.5938 10 17.5 11.1328 17.5 12.5H20ZM20 2.5H17.5C17.5 3.90625 18.5938 5 20 5V2.5ZM11.25 11.25C13.3203 11.25 15 9.57031 15 7.5C15 5.42969 13.3203 3.75 11.25 3.75C9.14062 3.75 7.5 5.42969 7.5 7.5C7.5 9.57031 9.14062 11.25 11.25 11.25Z"
                    fill="#4570FE"
                  />
                </svg>
              </div>
              <div class="text-wrapper-3">Thông tin thanh toán</div>
            </div>
            <div class="div-6">
              <div class="div-7">
                <div class="text-wrapper-8">Tổng tiền hàng:</div>
                <div class="text-wrapper-9" id="totalAmount">
                  {{amountBeforeVat}}
                </div>
              </div>
              {{#if amountVat0}}
              <div class="div-7">
                <div class="text-wrapper-8">Thuế GTGT (thuế 0%) :</div>
                <div class="text-wrapper-10" id="taxVAT_10">{{amountVat0}}</div>
              </div>
              {{/if}} {{#if amountVat5}}
              <div class="div-7">
                <div class="text-wrapper-8">Thuế GTGT (thuế 5%) :</div>
                <div class="text-wrapper-10" id="taxVAT_10">{{amountVat5}}</div>
              </div>
              {{/if}} {{#if amountVat8}}
              <div class="div-7">
                <div class="text-wrapper-8">Thuế GTGT (thuế 8%) :</div>
                <div class="text-wrapper-10" id="taxVAT_10">{{amountVat8}}</div>
              </div>
              {{/if}} {{#if amountVat10}}
              <div class="div-7">
                <div class="text-wrapper-8">Thuế GTGT (thuế 10%) :</div>
                <div class="text-wrapper-10" id="taxVAT_10">
                  {{amountVat10}}
                </div>
              </div>
              {{/if}}
              <div class="div-7">
                <div class="text-wrapper-8">Tổng tiền thanh toán:</div>
                <div class="text-wrapper-10" id="totalPayment">
                  {{finalAmount}}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="div-8">
          <div class="div-4" style="background-color: #28a042; margin: 0">
            <div class="h-a-n-h-p-l-wrapper">
              {{#ifCond checkResultBuyerInfo checkResultSellerInfo
              checkEnoughSellerInfo checkResultInCQT sellerCKS cqtCKS}}
              <div class="h-a-n-h-p-l" style="color: #28a043">
                Hóa đơn hợp lệ
              </div>
              {{else}}
              <div class="h-a-n-h-p-l" style="color: #f44141">
                Hóa đơn không hợp lệ
              </div>
              {{/ifCond}}
            </div>
            <div
              style="display: flex; flex-direction: row; align-items: center"
            >
              <div class="text-wrapper-11">
                Tại thời điểm tra cứu ngày {{checkDate}}
              </div>
            </div>
          </div>
          <div class="div-9">
            <div class="div-7">
              <div class="text-wrapper-12">Loại:</div>
              <div class="text-wrapper-13" id="typeInvoice">
                {{invoiceTypeText}}
              </div>
            </div>
            <div class="div-7">
              <div class="text-wrapper-12">Mẫu số:</div>
              <div class="text-wrapper-13" id="sample">1</div>
            </div>
            <div class="div-7">
              <div class="text-wrapper-12">Ký hiệu:</div>
              <div class="text-wrapper-13" id="serial">{{serial}}</div>
            </div>
            <div class="div-7">
              <div class="text-wrapper-12">Số hóa đơn:</div>
              <div class="text-wrapper-13" id="numInvoice">
                {{invoiceNumber}}
              </div>
            </div>
            <div class="div-7">
              <div class="text-wrapper-12">Ngày lập:</div>
              <div class="text-wrapper-13" id="foundDate">{{invoiceDate}}</div>
            </div>
            <div class="div-7">
              <div class="text-wrapper-12">Ngày ký:</div>
              <div class="text-wrapper-13">{{sellerCKSSignDate}}</div>
            </div>
            <div class="div-7">
              <div class="text-wrapper-12">Ngày cấp mã:</div>
              <div class="text-wrapper-13">{{signDateCQTCKS}}</div>
            </div>
            {{#if isSellerCKSSignDate}} {{#if checkSignWithInvoiceDate}}
            <div class="div-7">
              <div
                class="text-wrapper-14"
                style="color: #5180fb; font-weight: 500"
              >
                Ngày ký trùng ngày lập
              </div>
            </div>
            {{/if}} {{/if}} {{#if isSignDateCQTCKS }} {{#if startWith}} {{else}}
            {{#if checkProvideWithSignDate}}
            <div class="div-7">
              <div class="text-wrapper-14" style="color: #5180fb">
                Ngày cấp mã trùng ngày ký
              </div>
            </div>
            {{/if}} {{/if}} {{/if}}
          </div>
          <div class="frame-wrapper">
            <div class="div-10">
              <div class="text-wrapper-15">
                {{#if isManualInput}}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  height="1em"
                  viewBox="0 0 512 512"
                >
                  <path
                    d="M441 58.9L453.1 71c9.4 9.4 9.4 24.6 0 33.9L424 134.1 377.9 88 407 58.9c9.4-9.4 24.6-9.4 33.9 0zM209.8 256.2L344 121.9 390.1 168 255.8 302.2c-2.9 2.9-6.5 5-10.4 6.1l-58.5 16.7 16.7-58.5c1.1-3.9 3.2-7.5 6.1-10.4zM373.1 25L175.8 222.2c-8.7 8.7-15 19.4-18.3 31.1l-28.6 100c-2.4 8.4-.1 17.4 6.1 23.6s15.2 8.5 23.6 6.1l100-28.6c11.8-3.4 22.5-9.7 31.1-18.3L487 138.9c28.1-28.1 28.1-73.7 0-101.8L474.9 25C446.8-3.1 401.2-3.1 373.1 25zM88 64C39.4 64 0 103.4 0 152V424c0 48.6 39.4 88 88 88H360c48.6 0 88-39.4 88-88V312c0-13.3-10.7-24-24-24s-24 10.7-24 24V424c0 22.1-17.9 40-40 40H88c-22.1 0-40-17.9-40-40V152c0-22.1 17.9-40 40-40H200c13.3 0 24-10.7 24-24s-10.7-24-24-24H88z"
                    fill="#8255ff "
                  />
                </svg>
                {{else}}
                <svg
                  width="12"
                  height="16"
                  viewBox="0 0 12 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M11.4062 2.9375C11.7812 3.3125 12 3.8125 12 4.34375V14C12 15.125 11.0938 16 10 16H2C0.875 16 0 15.125 0 14V2C0 0.90625 0.875 0 2 0H7.65625C8.1875 0 8.6875 0.21875 9.0625 0.59375L11.4062 2.9375ZM10 14.5C10.25 14.5 10.5 14.2812 10.4688 14V5H8C7.4375 5 7 4.5625 7 4V1.53125H2C1.71875 1.53125 1.5 1.75 1.5 2.03125V14.0312C1.5 14.2812 1.71875 14.5 2 14.5H10Z"
                    fill="#28A043"
                  />
                </svg>
                {{/if}}
              </div>

              {{#if isManualInput}}
              <div class="text-wrapper-16" style="color: #8255ff">
                Hóa đơn nhập tay
              </div>
              {{else}}
              <div class="text-wrapper-16" style="color: {{colorInvoiceText}}">
                {{statusInvoiceText}}
              </div>
              {{/if}}
            </div>
          </div>
        </div>
      </div>
      <div class="div-wrapper">
        <div class="div-11">
          <div class="div-3">
            <div class="text-wrapper-17">Kết quả kiểm tra hóa đơn</div>
            <div class="div-12">
              <div class="div-4">
                <div class="div-13">
                  <div class="text-wrapper-18">
                    {{#if checkResultBuyerInfo}}
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M7.59375 10.625C7.25 10.9688 6.71875 10.9688 6.375 10.625L4.375 8.625C4.03125 8.28125 4.03125 7.75 4.375 7.40625C4.71875 7.0625 5.25 7.0625 5.59375 7.40625L7 8.78125L10.375 5.40625C10.7188 5.0625 11.25 5.0625 11.5938 5.40625C11.9375 5.75 11.9375 6.28125 11.5938 6.625L7.59375 10.625ZM16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0C12.4062 0 16 3.59375 16 8ZM8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5Z"
                        fill="#28A043"
                      />
                    </svg>
                    {{else}}
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M8 0C12.4062 0 16 3.59375 16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0ZM8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5ZM8 9.5C7.5625 9.5 7.25 9.1875 7.25 8.75V4.75C7.25 4.34375 7.5625 4 8 4C8.40625 4 8.75 4.34375 8.75 4.75V8.75C8.75 9.1875 8.40625 9.5 8 9.5ZM8 10.5625C8.53125 10.5625 8.96875 11 8.96875 11.5312C8.96875 12.0625 8.53125 12.5 8 12.5C7.4375 12.5 7 12.0625 7 11.5312C7 11 7.4375 10.5625 8 10.5625Z"
                        fill="#F44141"
                      />
                    </svg>

                    {{/if}}
                  </div>
                  {{#if checkResultBuyerInfo}}
                  <div class="text-wrapper-19">Đúng thông tin người mua</div>
                  {{else}}
                  <div class="text-wrapper-19">Sai thông tin người mua</div>
                  {{/if}}
                </div>
                <div class="div-14">
                  <!-- name -->
                  {{#if checkResultBuyerName}}
                  <div class="text-wrapper-22" style="color: #7177a8">
                    Đúng tên người mua
                  </div>
                  {{else}}
                  <!-- is null buyer name -->
                  {{#ifNull buyerName}}
                  <div class="text-wrapper-22" style="color: #e18700">
                    Không có tên người mua trên hóa đơn
                  </div>
                  {{/ifNull}}

                  <!-- is null check result buyer name -->
                  {{#ifNull checkResultBuyerName}}
                  <div class="text-wrapper-22" style="color: #e18700">
                    Không có tên người mua trên cơ quan thuế
                  </div>
                  {{/ifNull}}

                  <!-- is false buyer name  -->
                  {{#ifFalse checkResultBuyerName}}
                  <div class="text-wrapper-22" style="color: #f44141">
                    Sai tên người mua
                  </div>
                  {{/ifFalse}} {{/if}}

                  <!-- address -->
                  {{#if checkResultBuyerAddress}}
                  <div class="text-wrapper-22" style="color: #7177a8">
                    Đúng địa chỉ người mua
                  </div>
                  {{else}}
                  <!-- is null address -->
                  {{#ifNull buyerAddress}}
                  <div class="text-wrapper-22" style="color: #e18700">
                    Không có địa chỉ người mua trên hóa đơn
                  </div>
                  {{/ifNull}}

                  <!-- is null buyer address -->
                  {{#ifNull checkResultBuyerAddress}}
                  <div class="text-wrapper-22" style="color: #e18700">
                    Không có địa chỉ người mua trên cơ quan thuế
                  </div>
                  {{/ifNull}}

                  <!-- is false buyer address -->
                  {{#ifFalse checkResultBuyerAddress}}
                  <div class="text-wrapper-22" style="color: #f44141">
                    Sai địa chỉ người mua
                  </div>
                  {{/ifFalse}} {{/if}}

                  <!-- buyer taxcode -->
                  {{#if checkResultBuyerTaxCode}}
                  <div class="text-wrapper-22" style="color: #7177a8">
                    Đúng mã số thuế người mua
                  </div>
                  {{else}}
                  <!-- is null address -->
                  {{#ifNull buyerTaxCode}}
                  <div class="text-wrapper-22" style="color: #e18700">
                    Không có mã số thuế người mua trên hóa đơn
                  </div>
                  {{/ifNull}}
                  <!-- is null buyer address -->
                  {{#ifNull checkResultBuyerTaxCode}}
                  <div class="text-wrapper-22" style="color: #e18700">
                    Không có mã số thuế người mua trên cơ quan thuế
                  </div>
                  {{/ifNull}}

                  <!-- is false buyer address -->
                  {{#ifFalse checkResultBuyerTaxCode}}
                  <div class="text-wrapper-22" style="color: #f44141">
                    Sai mã số thuế người mua
                  </div>
                  {{/ifFalse}} {{/if}}

                  <!-- <span class="text-wrapper-23" style="color: #7177a8"
                    >Đối chiếu với dữ liệu CQT ngày {{checkDate}}
                  </span> -->
                </div>
              </div>

              <div class="div-4">
                <div class="div-13">
                  <div class="text-wrapper-18">
                    {{#if checkResultSellerInfo}}
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M7.59375 10.625C7.25 10.9688 6.71875 10.9688 6.375 10.625L4.375 8.625C4.03125 8.28125 4.03125 7.75 4.375 7.40625C4.71875 7.0625 5.25 7.0625 5.59375 7.40625L7 8.78125L10.375 5.40625C10.7188 5.0625 11.25 5.0625 11.5938 5.40625C11.9375 5.75 11.9375 6.28125 11.5938 6.625L7.59375 10.625ZM16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0C12.4062 0 16 3.59375 16 8ZM8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5Z"
                        fill="#28A043"
                      />
                    </svg>
                    {{else}}
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M8 0C12.4062 0 16 3.59375 16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0ZM8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5ZM8 9.5C7.5625 9.5 7.25 9.1875 7.25 8.75V4.75C7.25 4.34375 7.5625 4 8 4C8.40625 4 8.75 4.34375 8.75 4.75V8.75C8.75 9.1875 8.40625 9.5 8 9.5ZM8 10.5625C8.53125 10.5625 8.96875 11 8.96875 11.5312C8.96875 12.0625 8.53125 12.5 8 12.5C7.4375 12.5 7 12.0625 7 11.5312C7 11 7.4375 10.5625 8 10.5625Z"
                        fill="#F44141"
                      />
                    </svg>
                    {{/if}}
                  </div>

                  {{#if checkResultSellerInfo}}
                  <div class="text-wrapper-19">Đúng thông tin người bán</div>
                  {{else}}
                  <div class="text-wrapper-19">Sai thông tin người bán</div>
                  {{/if}}
                </div>

                <div class="div-14">
                  {{#if checkResultSellerName}}
                  <div class="text-wrapper-22" style="color: #7177a8">
                    Đúng tên người bán
                  </div>
                  {{else}} {{#ifNull sellerName}}
                  <div class="text-wrapper-22" style="color: #e18700">
                    Không có tên người bán trên hóa đơn
                  </div>
                  {{/ifNull}} {{#ifNull checkResultSellerName}}
                  <div class="text-wrapper-22" style="color: #e18700">
                    Không có tên người bán trên cơ quan thuế
                  </div>
                  {{/ifNull}} {{#ifFalse checkResultSellerName}}
                  <div class="text-wrapper-22" style="color: #f44141">
                    Sai tên người bán
                  </div>
                  {{/ifFalse}} {{/if}} {{#if checkResultSellerAddress}}
                  <div class="text-wrapper-22" style="color: #7177a8">
                    Đúng địa chỉ người bán
                  </div>
                  {{else}} {{#ifNull sellerAddress}}
                  <div class="text-wrapper-22" style="color: #e18700">
                    Không có địa chỉ người bán trên hóa đơn
                  </div>
                  {{/ifNull}} {{#ifNull checkResultSellerAddress}}
                  <div class="text-wrapper-22" style="color: #e18700">
                    Không có địa chỉ người bán trên cơ quan thuế
                  </div>
                  {{/ifNull}} {{#ifFalse checkResultSellerAddress}}
                  <div class="text-wrapper-22" style="color: #f44141">
                    Sai địa chỉ người bán
                  </div>
                  {{/ifFalse}} {{/if}}

                  <!-- seller taxcode -->
                  {{#if checkResultSellerTaxCode}}
                  <div class="text-wrapper-22" style="color: #7177a8">
                    Đúng mã số thuế người bán
                  </div>
                  {{else}}
                  <!-- is null taxCode
                  {{#ifNull resultSellerTaxCode}}
                  <div class="text-wrapper-22" style="color: #e18700">
                    Không có mã số thuế người bán trên hóa đơn
                  </div>
                  {{/ifNull}} -->
                  <!-- is null sellerTaxcode -->
                  {{#ifNull checkResultSellerTaxCode}}
                  <div class="text-wrapper-22" style="color: #e18700">
                    Không có mã số thuế người bán trên cơ quan thuế
                  </div>
                  {{/ifNull}}

                  <!-- is false buyer address -->
                  {{#ifFalse checkResultSellerTaxCode}}
                  <div class="text-wrapper-22" style="color: #f44141">
                    Sai mã số thuế người bán
                  </div>
                  {{/ifFalse}} {{/if}}

                  <span class="text-wrapper-23" style="color: #7177a8"
                    >Đối chiếu với dữ liệu CQT ngày {{checkDate}}</span
                  >
                </div>
              </div>
              {{#if isManualInput}} {{else}}
              <div class="div-4">
                <div class="div-13">
                  {{#if checkEnoughSellerInfo}}
                  <div class="text-wrapper-18">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M7.59375 10.625C7.25 10.9688 6.71875 10.9688 6.375 10.625L4.375 8.625C4.03125 8.28125 4.03125 7.75 4.375 7.40625C4.71875 7.0625 5.25 7.0625 5.59375 7.40625L7 8.78125L10.375 5.40625C10.7188 5.0625 11.25 5.0625 11.5938 5.40625C11.9375 5.75 11.9375 6.28125 11.5938 6.625L7.59375 10.625ZM16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0C12.4062 0 16 3.59375 16 8ZM8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5Z"
                        fill="#28A043"
                      />
                    </svg>
                  </div>

                  <div class="text-wrapper-19">Đủ thông tin người bán</div>
                  {{else}}
                  <div class="text-wrapper-18">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M8 0C12.4062 0 16 3.59375 16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0ZM8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5ZM8 9.5C7.5625 9.5 7.25 9.1875 7.25 8.75V4.75C7.25 4.34375 7.5625 4 8 4C8.40625 4 8.75 4.34375 8.75 4.75V8.75C8.75 9.1875 8.40625 9.5 8 9.5ZM8 10.5625C8.53125 10.5625 8.96875 11 8.96875 11.5312C8.96875 12.0625 8.53125 12.5 8 12.5C7.4375 12.5 7 12.0625 7 11.5312C7 11 7.4375 10.5625 8 10.5625Z"
                        fill="#F44141"
                      />
                    </svg>
                  </div>
                  <div class="text-wrapper-19">
                    Không đủ thông tin người bán
                  </div>
                  {{/if}}
                </div>
                <div class="div-14">
                  {{#if checkResultSignatureNCC }}
                  <div class="text-wrapper-22" style="color: #7177a8">
                    Chữ ký điện tử NCC hợp lệ
                  </div>

                  {{else}} {{#ifCondEqual checkResultSignatureNCC null}}
                  <div class="text-wrapper-22" style="color: #f44141">
                    Không có chữ ký nhà cung cấp
                  </div>
                  {{else}}
                  <div class="text-wrapper-22" style="color: #f44141">
                    Chữ ký điển tử NCC không hợp lệ
                  </div>
                  {{/ifCondEqual}} {{/if}} {{#if startWith}} {{else}} {{#if
                  checkResultSignatureCQT }}
                  <div class="text-wrapper-22" style="color: #7177a8">
                    Đã được ký bởi cơ quan thuế
                  </div>
                  {{else}}
                  <div class="text-wrapper-21" style="color: #f44141">
                    Chữ ký bởi cơ quan thuế sai
                  </div>
                  {{/if}} {{/if}}
                </div>
              </div>
              {{/if}}
            </div>
            {{#if hideCheckCaculator}}
            <div class="text-wrapper-17">Kiểm tra tính toán về số liệu</div>
            <div class="div-12">
              <div class="div-4">
                <div class="div-15">
                  {{#if checkCalculation}}
                  <div class="text-wrapper-26">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M7.59375 10.625C7.25 10.9688 6.71875 10.9688 6.375 10.625L4.375 8.625C4.03125 8.28125 4.03125 7.75 4.375 7.40625C4.71875 7.0625 5.25 7.0625 5.59375 7.40625L7 8.78125L10.375 5.40625C10.7188 5.0625 11.25 5.0625 11.5938 5.40625C11.9375 5.75 11.9375 6.28125 11.5938 6.625L7.59375 10.625ZM16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0C12.4062 0 16 3.59375 16 8ZM8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5Z"
                        fill="#28A043"
                      />
                    </svg>
                  </div>
                  <div class="text-wrapper-25" style="color: #7177a8">
                    Hóa đơn chính xác về tính toán số liệu
                  </div>
                  {{else}}
                  <div class="text-wrapper-24">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M8 0C12.4062 0 16 3.59375 16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0ZM8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5ZM8 9.5C7.5625 9.5 7.25 9.1875 7.25 8.75V4.75C7.25 4.34375 7.5625 4 8 4C8.40625 4 8.75 4.34375 8.75 4.75V8.75C8.75 9.1875 8.40625 9.5 8 9.5ZM8 10.5625C8.53125 10.5625 8.96875 11 8.96875 11.5312C8.96875 12.0625 8.53125 12.5 8 12.5C7.4375 12.5 7 12.0625 7 11.5312C7 11 7.4375 10.5625 8 10.5625Z"
                        fill="#F44141"
                      />
                    </svg>
                  </div>
                  <div class="text-wrapper-26" style="color: #f44141">
                    Hóa đơn chưa chính xác về tính toán số liệu
                  </div>
                  {{/if}}
                </div>
              </div>
            </div>
            {{/if}}
          </div>
          <div class="div-3">
            <div class="text-wrapper-17">
              Kiểm tra với hệ thống tổng cục thuế
            </div>

            {{#if isManualInput}}
            <div class="div-12">
              <div class="div-4">
                <!-- người bán hoạt động/ không hoạt động -->
                <div class="div-15">
                  <!-- nb_dang_hoat_dong -->
                  {{#if nb_dang_hoat_dong}}
                  <div class="text-wrapper-24">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M7.59375 10.625C7.25 10.9688 6.71875 10.9688 6.375 10.625L4.375 8.625C4.03125 8.28125 4.03125 7.75 4.375 7.40625C4.71875 7.0625 5.25 7.0625 5.59375 7.40625L7 8.78125L10.375 5.40625C10.7188 5.0625 11.25 5.0625 11.5938 5.40625C11.9375 5.75 11.9375 6.28125 11.5938 6.625L7.59375 10.625ZM16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0C12.4062 0 16 3.59375 16 8ZM8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5Z"
                        fill="#28A043"
                      />
                    </svg>
                  </div>
                  <div class="text-wrapper-25">Người bán đang hoạt động</div>
                  {{else}}
                  <!-- if null -->
                  {{#ifNull nb_dang_hoat_dong}}
                  <div class="text-wrapper-24">
                    <div class="text-wrapper-24">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M8 0C12.4062 0 16 3.59375 16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0ZM8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5ZM8 9.5C7.5625 9.5 7.25 9.1875 7.25 8.75V4.75C7.25 4.34375 7.5625 4 8 4C8.40625 4 8.75 4.34375 8.75 4.75V8.75C8.75 9.1875 8.40625 9.5 8 9.5ZM8 10.5625C8.53125 10.5625 8.96875 11 8.96875 11.5312C8.96875 12.0625 8.53125 12.5 8 12.5C7.4375 12.5 7 12.0625 7 11.5312C7 11 7.4375 10.5625 8 10.5625Z"
                          fill="#FFA318"
                        />
                      </svg>
                    </div>
                  </div>
                  <div class="text-wrapper-25" style="color: #ffa318">
                    Không kiểm tra được trạng thái của người bán
                  </div>
                  {{/ifNull}}
                  <!-- if false -->
                  {{#ifFalse nb_dang_hoat_dong}}
                  <div class="text-wrapper-24">
                    <div class="text-wrapper-24">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M8 0C12.4062 0 16 3.59375 16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0ZM8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5ZM8 9.5C7.5625 9.5 7.25 9.1875 7.25 8.75V4.75C7.25 4.34375 7.5625 4 8 4C8.40625 4 8.75 4.34375 8.75 4.75V8.75C8.75 9.1875 8.40625 9.5 8 9.5ZM8 10.5625C8.53125 10.5625 8.96875 11 8.96875 11.5312C8.96875 12.0625 8.53125 12.5 8 12.5C7.4375 12.5 7 12.0625 7 11.5312C7 11 7.4375 10.5625 8 10.5625Z"
                          fill="#F44141"
                        />
                      </svg>
                    </div>
                  </div>
                  <div class="text-wrapper-25" style="color: #f44141">
                    Người bán không hoạt động
                  </div>
                  {{/ifFalse}} {{/if}}
                </div>

                <!-- người bán rủi ro/ không rủi ro -->
                <div class="div-15">
                  <!-- nb_dang_hoat_dong -->
                  {{#if nb_khong_rui_ro_tai_tdlap}}
                  <div class="text-wrapper-24">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M7.59375 10.625C7.25 10.9688 6.71875 10.9688 6.375 10.625L4.375 8.625C4.03125 8.28125 4.03125 7.75 4.375 7.40625C4.71875 7.0625 5.25 7.0625 5.59375 7.40625L7 8.78125L10.375 5.40625C10.7188 5.0625 11.25 5.0625 11.5938 5.40625C11.9375 5.75 11.9375 6.28125 11.5938 6.625L7.59375 10.625ZM16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0C12.4062 0 16 3.59375 16 8ZM8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5Z"
                        fill="#28A043"
                      />
                    </svg>
                  </div>
                  <div class="text-wrapper-25">
                    Người bán thuộc danh sách cảnh báo của Tổng Cục thuế tại
                    thời điểm phát hành hóa đơn
                  </div>
                  {{else}}
                  <!-- if null -->
                  {{#ifNull nb_khong_rui_ro_tai_tdlap}}
                  <div class="text-wrapper-24">
                    <div class="text-wrapper-24">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M8 0C12.4062 0 16 3.59375 16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0ZM8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5ZM8 9.5C7.5625 9.5 7.25 9.1875 7.25 8.75V4.75C7.25 4.34375 7.5625 4 8 4C8.40625 4 8.75 4.34375 8.75 4.75V8.75C8.75 9.1875 8.40625 9.5 8 9.5ZM8 10.5625C8.53125 10.5625 8.96875 11 8.96875 11.5312C8.96875 12.0625 8.53125 12.5 8 12.5C7.4375 12.5 7 12.0625 7 11.5312C7 11 7.4375 10.5625 8 10.5625Z"
                          fill="#FFA318"
                        />
                      </svg>
                    </div>
                  </div>
                  <div class="text-wrapper-25">
                    Không kiểm tra được rủi ro của người bán tại thời điểm phát
                    hành hóa đơn
                  </div>
                  {{/ifNull}}
                  <!-- if false -->
                  {{#ifFalse nb_khong_rui_ro_tai_tdlap}}
                  <div class="text-wrapper-24">
                    <div class="text-wrapper-24">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M8 0C12.4062 0 16 3.59375 16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0ZM8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5ZM8 9.5C7.5625 9.5 7.25 9.1875 7.25 8.75V4.75C7.25 4.34375 7.5625 4 8 4C8.40625 4 8.75 4.34375 8.75 4.75V8.75C8.75 9.1875 8.40625 9.5 8 9.5ZM8 10.5625C8.53125 10.5625 8.96875 11 8.96875 11.5312C8.96875 12.0625 8.53125 12.5 8 12.5C7.4375 12.5 7 12.0625 7 11.5312C7 11 7.4375 10.5625 8 10.5625Z"
                          fill="#F44141"
                        />
                      </svg>
                    </div>
                  </div>
                  <div class="text-wrapper-25">
                    Người bán không rủi ro tại thời điểm phát hành hóa đơn
                  </div>
                  {{/ifFalse}} {{/if}}
                </div>

                <!-- hóa đơn thông báo phát hành -->
                <div class="div-15">
                  <!-- nb_dang_hoat_dong -->
                  {{#if icon_tb_phat_hanh}}
                  <div class="text-wrapper-24">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M7.59375 10.625C7.25 10.9688 6.71875 10.9688 6.375 10.625L4.375 8.625C4.03125 8.28125 4.03125 7.75 4.375 7.40625C4.71875 7.0625 5.25 7.0625 5.59375 7.40625L7 8.78125L10.375 5.40625C10.7188 5.0625 11.25 5.0625 11.5938 5.40625C11.9375 5.75 11.9375 6.28125 11.5938 6.625L7.59375 10.625ZM16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0C12.4062 0 16 3.59375 16 8ZM8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5Z"
                        fill="#28A043"
                      />
                    </svg>
                  </div>
                  {{else}} {{#ifFalse icon_tb_phat_hanh}}
                  <div class="text-wrapper-24">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M8 0C12.4062 0 16 3.59375 16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0ZM8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5ZM8 9.5C7.5625 9.5 7.25 9.1875 7.25 8.75V4.75C7.25 4.34375 7.5625 4 8 4C8.40625 4 8.75 4.34375 8.75 4.75V8.75C8.75 9.1875 8.40625 9.5 8 9.5ZM8 10.5625C8.53125 10.5625 8.96875 11 8.96875 11.5312C8.96875 12.0625 8.53125 12.5 8 12.5C7.4375 12.5 7 12.0625 7 11.5312C7 11 7.4375 10.5625 8 10.5625Z"
                        fill="#F44141"
                      />
                    </svg>
                  </div>
                  {{/ifFalse}} {{#ifNull icon_tb_phat_hanh}}
                  <div class="text-wrapper-24">
                    <svg
                      width="17"
                      height="16"
                      viewBox="0 0 17 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M11.25 7.25C11.6562 7.25 12 7.59375 12 8C12 8.4375 11.6562 8.75 11.25 8.75H5.75C5.3125 8.75 5 8.4375 5 8C5 7.59375 5.3125 7.25 5.75 7.25H11.25ZM16.5 8C16.5 12.4375 12.9062 16 8.5 16C4.0625 16 0.5 12.4375 0.5 8C0.5 3.59375 4.0625 0 8.5 0C12.9062 0 16.5 3.59375 16.5 8ZM8.5 1.5C4.90625 1.5 2 4.4375 2 8C2 11.5938 4.90625 14.5 8.5 14.5C12.0625 14.5 15 11.5938 15 8C15 4.4375 12.0625 1.5 8.5 1.5Z"
                        fill="#FFA318"
                      />
                    </svg>
                  </div>

                  {{/ifNull}} {{/if}}
                  <div class="text-wrapper-25">{{text_tb_phat_hanh}}</div>
                </div>
                <div class="div-14">
                  {{#if mauso_kyhieu_da_tb}}
                  <div class="text-wrapper-22" style="color: #28a043">
                    Mẫu số ký hiệu đã được thông báo phát hành
                  </div>
                  {{else}} {{#ifFalse mauso_kyhieu_da_tb}}
                  <div class="text-wrapper-22" style="color: #f44141">
                    Mẫu số ký hiệu chưa được thông báo phát hành
                  </div>
                  {{/ifFalse}} {{#ifNull mauso_kyhieu_da_tb}}
                  <div class="text-wrapper-22" style="color: #ffa318">
                    Không kiểm tra được mẫu số ký hiệu
                  </div>
                  {{/ifNull}} {{/if}} {{#if so_hd_thuoc_khoang_phat_hanh}}
                  <div class="text-wrapper-22" style="color: #28a043">
                    Số hóa đơn thuộc khoảng hóa đơn thông báo phát hành
                  </div>
                  {{else}} {{#ifFalse so_hd_thuoc_khoang_phat_hanh}}
                  <div class="text-wrapper-22" style="color: #f44141">
                    Số hóa đơn không thuộc khoảng hóa đơn thông báo phát hành
                  </div>
                  {{/ifFalse}} {{#ifNull so_hd_thuoc_khoang_phat_hanh}}
                  <div class="text-wrapper-22" style="color: #ffa318">
                    Không kiểm tra được số hóa đơn
                  </div>
                  {{/ifNull}} {{/if}} {{#if ngay_hoa_don_tu_ngay_su_dung}}
                  <div class="text-wrapper-22" style="color: #28a043">
                    Ngày hóa đơn lớn hơn ngày bắt đầu sử dụng
                  </div>
                  {{else}} {{#ifFalse ngay_hoa_don_tu_ngay_su_dung}}
                  <div class="text-wrapper-22" style="color: #f44141">
                    Ngày hóa đơn nhỏ hơn ngày bắt đầu sử dụng
                  </div>
                  {{/ifFalse}} {{#ifNull ngay_hoa_don_tu_ngay_su_dung}}
                  <div class="text-wrapper-22" style="color: #ffa318">
                    Không kiểm tra được ngày hóa đơn
                  </div>
                  {{/ifNull}} {{/if}}
                </div>
              </div>
            </div>
            {{else}}
            <div class="div-12">
              <div class="div-4">
                <div class="div-15">
                  {{#if checkResultInCQT}}
                  <div class="text-wrapper-24">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M7.59375 10.625C7.25 10.9688 6.71875 10.9688 6.375 10.625L4.375 8.625C4.03125 8.28125 4.03125 7.75 4.375 7.40625C4.71875 7.0625 5.25 7.0625 5.59375 7.40625L7 8.78125L10.375 5.40625C10.7188 5.0625 11.25 5.0625 11.5938 5.40625C11.9375 5.75 11.9375 6.28125 11.5938 6.625L7.59375 10.625ZM16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0C12.4062 0 16 3.59375 16 8ZM8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5Z"
                        fill="#28A043"
                      />
                    </svg>
                  </div>
                  <div class="text-wrapper-25">
                    Kiểm tra trên hệ thống của Tổng cục thuế
                  </div>
                  {{else}}
                  <div class="text-wrapper-24">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M8 0C12.4062 0 16 3.59375 16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0ZM8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5ZM8 9.5C7.5625 9.5 7.25 9.1875 7.25 8.75V4.75C7.25 4.34375 7.5625 4 8 4C8.40625 4 8.75 4.34375 8.75 4.75V8.75C8.75 9.1875 8.40625 9.5 8 9.5ZM8 10.5625C8.53125 10.5625 8.96875 11 8.96875 11.5312C8.96875 12.0625 8.53125 12.5 8 12.5C7.4375 12.5 7 12.0625 7 11.5312C7 11 7.4375 10.5625 8 10.5625Z"
                        fill="#F44141"
                      />
                    </svg>
                  </div>
                  <div class="text-wrapper-25">
                    Kiểm tra trên hệ thống của Tổng cục thuế
                  </div>
                  {{/if}}
                </div>
                <div class="div-14">
                  {{#if startWith}} {{#if checkResultHasInvoiceCode}}
                  <!-- co K co ma -->
                  <div class="text-wrapper-26" style="color: #7177a8">
                    Trạng thái xử lý hóa đơn: Tổng cục thuế đã nhận không mã
                  </div>
                  {{else}}
                  <!-- co K khong ma -->
                  {{/if}} {{else}}
                  <div class="text-wrapper-26" style="color: #7177a8">
                    Trạng thái xử lý hóa đơn: Đã cấp mã hóa đơn
                  </div>
                  {{/if}}
                </div>
              </div>
            </div>
            <div class="div-4">
              <div class="text-wrapper-17">
                Kiểm tra thông tin chữ ký số trên hóa đơn
              </div>
            </div>
            <div class="div-12">
              <div class="div-4">
                {{#if sellerCKS}}
                <div class="div-15">
                  <div class="text-wrapper-24">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M7.59375 10.625C7.25 10.9688 6.71875 10.9688 6.375 10.625L4.375 8.625C4.03125 8.28125 4.03125 7.75 4.375 7.40625C4.71875 7.0625 5.25 7.0625 5.59375 7.40625L7 8.78125L10.375 5.40625C10.7188 5.0625 11.25 5.0625 11.5938 5.40625C11.9375 5.75 11.9375 6.28125 11.5938 6.625L7.59375 10.625ZM16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0C12.4062 0 16 3.59375 16 8ZM8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5Z"
                        fill="#28A043"
                      />
                    </svg>
                  </div>
                  <div class="text-wrapper-25">Chữ ký số người bán</div>
                </div>
                <div class="div-14">
                  <div class="text-wrapper-26">Đơn vị ký: {{IssuerSeller}}</div>

                  <div class="text-wrapper-26">
                    Nhà phát hành chứng thư: {{sellerCKSSubject}}
                  </div>

                  {{#if sellerCKSSerialNumber}}
                  <div class="text-wrapper-26">
                    Serial number: {{sellerCKSSerialNumber}}
                  </div>
                  {{/if}}

                  <div style="display: flex; flex-direction: row">
                    <div class="text-wrapper-26" style="margin-right: 10px">
                      Hiệu lực:
                    </div>
                    <div style="display: flex; flex-direction: column">
                      <div class="text-wrapper-26">
                        Từ: {{sellerCKSNotBefore}}
                      </div>
                      <div class="text-wrapper-26">
                        Đến: {{sellerCKSNotAfter}}
                      </div>
                    </div>
                  </div>
                </div>
                {{else}}
                <div class="div-15">
                  <div class="text-wrapper-24">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M8 0C12.4062 0 16 3.59375 16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0ZM8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5ZM8 9.5C7.5625 9.5 7.25 9.1875 7.25 8.75V4.75C7.25 4.34375 7.5625 4 8 4C8.40625 4 8.75 4.34375 8.75 4.75V8.75C8.75 9.1875 8.40625 9.5 8 9.5ZM8 10.5625C8.53125 10.5625 8.96875 11 8.96875 11.5312C8.96875 12.0625 8.53125 12.5 8 12.5C7.4375 12.5 7 12.0625 7 11.5312C7 11 7.4375 10.5625 8 10.5625Z"
                        fill="#F44141"
                      />
                    </svg>
                  </div>
                  <div class="text-wrapper-25">Chữ ký số người bán</div>
                </div>
                <div class="div-14">
                  <div class="text-wrapper-26">Không có thông tin</div>
                </div>
                {{/if}}
              </div>

              <div class="div-4">
                {{#if cqtCKS}}
                <div class="div-15">
                  <div class="text-wrapper-24">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M7.59375 10.625C7.25 10.9688 6.71875 10.9688 6.375 10.625L4.375 8.625C4.03125 8.28125 4.03125 7.75 4.375 7.40625C4.71875 7.0625 5.25 7.0625 5.59375 7.40625L7 8.78125L10.375 5.40625C10.7188 5.0625 11.25 5.0625 11.5938 5.40625C11.9375 5.75 11.9375 6.28125 11.5938 6.625L7.59375 10.625ZM16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0C12.4062 0 16 3.59375 16 8ZM8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5Z"
                        fill="#28A043"
                      />
                    </svg>
                  </div>
                  <div class="text-wrapper-25">Chữ ký số Cơ quan thuế</div>
                </div>
                <div class="div-14">
                  <div class="text-wrapper-26">
                    Đơn vị ký: {{subjectCQTCKS}}
                  </div>
                  <div class="text-wrapper-26">
                    Nhà phát hành chứng thư: {{issuerCQTCKS}}
                  </div>
                </div>
                {{else}}
                <div class="div-15">
                  <div class="text-wrapper-24">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M8 0C12.4062 0 16 3.59375 16 8C16 12.4375 12.4062 16 8 16C3.5625 16 0 12.4375 0 8C0 3.59375 3.5625 0 8 0ZM8 14.5C11.5625 14.5 14.5 11.5938 14.5 8C14.5 4.4375 11.5625 1.5 8 1.5C4.40625 1.5 1.5 4.4375 1.5 8C1.5 11.5938 4.40625 14.5 8 14.5ZM8 9.5C7.5625 9.5 7.25 9.1875 7.25 8.75V4.75C7.25 4.34375 7.5625 4 8 4C8.40625 4 8.75 4.34375 8.75 4.75V8.75C8.75 9.1875 8.40625 9.5 8 9.5ZM8 10.5625C8.53125 10.5625 8.96875 11 8.96875 11.5312C8.96875 12.0625 8.53125 12.5 8 12.5C7.4375 12.5 7 12.0625 7 11.5312C7 11 7.4375 10.5625 8 10.5625Z"
                        fill="#F44141"
                      />
                    </svg>
                  </div>
                  <div class="text-wrapper-25">Chữ ký số Cơ quan thuế</div>
                </div>
                <div class="div-14">
                  <div class="text-wrapper-26">Không có thông tin</div>
                </div>
                {{/if}}
              </div>
            </div>
            {{/if}}
          </div>
        </div>
      </div>
      <div class="text-wrapper-28">
        Thông tin và kết quả kiểm tra theo file XML của hóa đơn
      </div>
    </div>
  </body>
</html>
