const express = require('express');
const invoiceController = require('../controllers/invoice.controller');
const invoiceTagController = require('../controllers/invoice-tag.controller');
const fileUploadMiddleware = require('../middlewares/file-upload.middleware');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.get('/find', invoiceController.find);
  router.get('/detail/:invoiceId', invoiceController.detail);
  router.put('/payment/:invoiceId', invoiceController.payment);
  router.put('/return-payment/:invoiceId', invoiceController.returnPayment);
  router.post('/validate', invoiceController.validate);
  router.put('/tag', invoiceTagController.create);
  router.put('/isDeleted', invoiceController.isDeleted);
  router.put('/accounting', invoiceController.accounting);
  router.put('/return-accountings', invoiceController.returnAccounting);
  router.put('/move', invoiceController.move);
  router.put('/note', invoiceController.note);
  router.put(
    '/upload/:invoiceId',
    fileUploadMiddleware(),
    invoiceController.upload,
  );
  router.delete('/delete-pdf/:invoiceId', invoiceController.deletePDF);
  router.post('/email', invoiceController.email);
  router.put('/accept/:invoiceId', invoiceController.accept);
  router.post('/export', invoiceController.export);
  router.get('/print-result/:invoiceId', invoiceController.printResult);
  router.post('/accept-tax-reduce', invoiceController.acceptTaxReduce);
  router.post(
    '/manual-create/file',
    fileUploadMiddleware(),
    invoiceController.manualCreateFile,
  );
  router.put(
    '/update-manual-invoice-input/:invoiceId',
    // fileUploadMiddleware(),
    invoiceController.updateManualInvoiceInput,
  );
  router.post(
    '/manual-create/input',
    fileUploadMiddleware(),
    invoiceController.manualCreateInput,
  );

  app.use('/api/v1/invoice', router);
};
