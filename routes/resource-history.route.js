const express = require('express');
const ResourceHistoryController = require('../controllers/resource-history.controller');

/**
 *
 * @param {express.Application} app
 */

module.exports = app => {
  const router = express.Router();
  router.get('/find', ResourceHistoryController.find);
  router.get(
    '/find-by-meta-account-code/:metaAccountCode',
    ResourceHistoryController.findByMetaAccountCode,
  );
  router.post('/doi-soat', ResourceHistoryController.doiSoat);
  router.put(
    '/huy-goi-dich-vu/:resourceHistoryId',
    ResourceHistoryController.huyGoiDichVu,
  );
  router.get(
    '/detail/:resourceHistoryId',
    ResourceHistoryController.detailResourceHistory,
  );

  app.use('/api/v1/qlbh/resource-history', router);
  app.use('/api/v1/resource-history', router);
};
