const express = require('express');

const tagController = require('../controllers/tag.controller');

module.exports = app => {
  const router = express.Router();
  router.post('/create', tagController.create);
  router.get('/find', tagController.find);
  router.put('/update/:tagId', tagController.update);
  router.delete('/delete', tagController.delete);
  router.get('/detail/:tagId', tagController.detail);

  app.use('/api/v1/tag', router);
};
