const express = require('express');
const miscController = require('../controllers/misc.controller');
const fileUploadMiddleware = require('../middlewares/file-upload.middleware');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.get('/find-company-by-tax-code', miscController.findCompanyByTaxCode);
  router.get('/find-companies-by-queries', miscController.findCompanyByQueries);
  router.get('/find-voucher-by-code', miscController.findVoucherByCode);
  router.post('/push-file', fileUploadMiddleware(), miscController.pushFile);
  router.get('/find-voucher', miscController.findVoucher);
  router.get(
    '/find-by-meta-account-code/:metaAccountCode',
    miscController.findByMetaAccountCode,
  );

  app.use('/api/v1/misc', router);
};
