const express = require('express');

const CommentCustomerController = require('../controllers/comment-customer.controller');
const fileUploadMiddleware = require('../middlewares/file-upload.middleware');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.get('/find', CommentCustomerController.find);
  router.get('/detail/:commentCustomerId', CommentCustomerController.detail);
  router.post(
    '/create',
    fileUploadMiddleware(),
    CommentCustomerController.create,
  );
  router.put(
    '/update/:commentCustomerId',
    fileUploadMiddleware(),
    CommentCustomerController.update,
  );
  router.delete('/delete', CommentCustomerController.delete);

  app.use('/api/v1/comment-customer', router);
};
