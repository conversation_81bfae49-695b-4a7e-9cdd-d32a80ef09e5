const express = require('express');

const ContactCustomerHelper = require('../controllers/contact-customer.controller');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.get('/find', ContactCustomerHelper.find);
  router.get('/detail/:contactId', ContactCustomerHelper.detail);
  router.post('/create', ContactCustomerHelper.create);
  router.put('/update/:contactId', ContactCustomerHelper.update);
  router.get('/summary', ContactCustomerHelper.summary);
  // router.delete('/delete', ContactCustomerHelper.delete);

  app.use('/api/v1/qlbh/contact-customer', router);

  app.use('/api/v1/contact-customer', router);
};
