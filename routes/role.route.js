const express = require('express');
const roleController = require('../controllers/role.controller');

module.exports = app => {
  const router = express.Router();

  router.post('/create', roleController.create);
  router.get('/find', roleController.find);
  router.put('/update/:roleId', roleController.update);
  router.delete('/delete', roleController.delete);
  router.get('/detail/:roleId', roleController.detail);

  app.use('/api/v1/role', router);
};
