const express = require('express');
const companyController = require('../controllers/company.controller');
const fileUploadMiddleware = require('../middlewares/file-upload.middleware');
const { replaceInString } = require('../utils');
const { ERROR_FILE_SIZE_LIMIT } = require('../configs/error.vi');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.get('/detail/', companyController.detail);
  router.put('/update', fileUploadMiddleware(), companyController.update);
  router.get('/find-by-tax-code', companyController.findCompanyByTaxCode);
  router.use('/find-by-tax-codes', companyController.findCompanyByTaxCodes);
  app.put(
    '/api/v1/qlbh/company/update/:taxCode',
    fileUploadMiddleware(),
    companyController.updateCompanyByTaxCode,
  );
  router.put('/top-up', companyController.topUpToCompany);
  router.post(
    '/khach-hang-ky-hop-dong-ncc',
    companyController.khachHangKyHopDongNCC,
  );

  app.use('/api/v1/qlbh/company', router);
  // router.put('/setting', companyController.setting);
  app.use('/api/v1/company', router);
};
