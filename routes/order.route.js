const express = require('express');

const orderController = require('../controllers/order.controller');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.get('/find', orderController.find);
  router.get('/history', orderController.history);
  router.get('/detail/:orderId', orderController.detail);
  router.post('/create', orderController.create);
  router.put('/update/:orderId', orderController.update);
  router.delete('/delete', orderController.delete);
  router.use('/vnpay-ipn', orderController.vnPayIpn);
  router.get('/vnpay_return', orderController.vnPayReturn);
  router.post('/repayment/:orderId', orderController.repayment);
  router.get('/email_payment/:orderId', orderController.emailPayment);
  router.get(
    '/history/:metaAccountCode',
    orderController.historyMetaAccountCode,
  );

  app.use('/api/v1/qlbh/order', router);
  app.use('/api/v1/order', router);
};
