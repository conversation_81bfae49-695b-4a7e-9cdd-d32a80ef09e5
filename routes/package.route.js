const express = require('express');

const packageController = require('../controllers/package.controller');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.get('/find', packageController.find);
  // router.get(
  //   '/find-dealer-package',
  //   (req, res, next) => {
  //     req.query = req.query ?? {};
  //     req.query.dealerPackage = true;

  //     next();
  //   },
  //   packageController.find,
  // );
  router.get('/detail/:packageId', packageController.detail);
  router.post('/create', packageController.create);
  router.put('/update/:packageId', packageController.update);
  router.delete('/delete', packageController.delete);

  // router.get('/dealer-package/sell', packageController.findSellDealerPackage);
  // router.get('/dealer-package/buy', packageController.findBuyDealerPackage);

  router.get('/history/:metaAccountCode', packageController.history);

  app.use('/api/v1/qlbh/package', router);
  app.use('/api/v1/package', router);
};
