const express = require('express');
const PartnerCompanyController = require('../controllers/partner-company.controller');
const fileUploadMiddleware = require('../middlewares/file-upload.middleware');

module.exports = app => {
  const router = express.Router();
  router.post('/create', PartnerCompanyController.create);
  router.get('/find', PartnerCompanyController.find);
  router.put('/update/:partnerCompanyId', PartnerCompanyController.update);
  router.delete('/delete', PartnerCompanyController.delete);
  router.get('/detail/:partnerCompanyId', PartnerCompanyController.detail);
  router.post('/synchronize', PartnerCompanyController.synchronize);
  router.post(
    '/import',
    fileUploadMiddleware(),
    PartnerCompanyController.importFile,
  );
  router.get('/businesses', PartnerCompanyController.getBusinesses);

  app.use('/api/v1/partner-company', router);
};
