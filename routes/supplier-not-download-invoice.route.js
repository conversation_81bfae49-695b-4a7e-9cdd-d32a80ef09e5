const express = require('express');
const SupplierNotDownloadInvoice = require('../controllers/supplier-not-download-invoice.controller');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.post('/create', SupplierNotDownloadInvoice.create);
  router.get('/find', SupplierNotDownloadInvoice.find);

  app.use('/api/v1/list-supplier-not-download-invoice', router);
};
