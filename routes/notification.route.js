const express = require('express');
const notificationController = require('../controllers/notification.controller');
const fileUploadMiddleware = require('../middlewares/file-upload.middleware');
const { replaceInString } = require('../utils');
const { ERROR_FILE_SIZE_LIMIT } = require('../configs/error.vi');
const checkHashMiddleware = require('../middlewares/check-hash.middleware');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.post(
    '/create',
    fileUploadMiddleware(),
    checkHashMiddleware,
    notificationController.create,
  );
  router.get('/find', notificationController.find);
  router.put(
    '/update/:notificationId',
    fileUploadMiddleware(),
    checkHashMiddleware,
    notificationController.update,
  );
  router.delete('/delete', notificationController.delete);
  router.get('/detail/:notificationId', notificationController.detail);

  app.use('/api/v1/qlbh/notification', router);
  app.use('/api/v1/notification', router);
};
