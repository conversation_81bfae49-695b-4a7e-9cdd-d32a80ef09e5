const express = require('express');
const InstructionController = require('../controllers/instruction.controller');
const fileUploadMiddleware = require('../middlewares/file-upload.middleware');
const checkHashMiddleware = require('../middlewares/check-hash.middleware');
// const accountLevelMiddleware = require('../middlewares/account-level.middleware');
// const { ACCOUNT_LEVEL } = require('../configs/constants/account.constant');

module.exports = app => {
  const router = express.Router();
  router.post(
    '/create',
    fileUploadMiddleware(),
    checkHashMiddleware,
    InstructionController.create,
  );
  router.get('/find', InstructionController.find);
  router.put(
    '/update/:instructionId',
    fileUploadMiddleware(),
    checkHashMiddleware,
    InstructionController.update,
  );
  router.delete('/delete', InstructionController.delete);
  router.get('/detail/:instructionId', InstructionController.detail);

  app.use('/api/v1/qlbh/instruction', router);

  app.use('/api/v1/instruction', router);
};
