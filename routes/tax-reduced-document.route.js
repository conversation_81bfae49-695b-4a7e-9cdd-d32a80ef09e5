const express = require('express');
const taxReducedDocumentController = require('../controllers/tax-reduced-document.controller');
const fileUploadMiddleware = require('../middlewares/file-upload.middleware');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.get('/find', taxReducedDocumentController.find);
  router.post(
    '/create',
    fileUploadMiddleware({ limits: { fileSize: 1024 * 1024 * 100 } }),
    taxReducedDocumentController.create,
  );
  router.put(
    '/update/:code',
    fileUploadMiddleware({ limits: { fileSize: 1024 * 1024 * 100 } }),
    taxReducedDocumentController.update,
  );
  router.get('/detail/:code', taxReducedDocumentController.detail);
  router.delete('/delete/:code', taxReducedDocumentController.delete);
  app.use('/api/v1/tax-reduced-document', router);
};
