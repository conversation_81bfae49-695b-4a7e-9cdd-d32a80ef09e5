const express = require('express');
const organizationController = require('../controllers/organization.controller');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.get('/find', organizationController.find);
  router.post('/create', organizationController.create);
  router.put('/update/:organizationId', organizationController.update);
  router.delete('/delete', organizationController.delete);
  router.post(
    '/create-with-many-address',
    organizationController.createWithManyAddress,
  );
  router.put(
    '/update-with-many-address/:organizationId',
    organizationController.update,
  );

  app.use('/api/v1/organization', router);
};
