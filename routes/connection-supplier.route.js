const express = require('express');
const connectionSupplierController = require('../controllers/connection-supplier.controller');
const fileUploadMiddleware = require('../middlewares/file-upload.middleware');
const { replaceInString } = require('../utils');

module.exports = app => {
  const router = express.Router();
  router.post('/connect', connectionSupplierController.connect);
  router.get('/find', connectionSupplierController.find);
  router.put(
    '/update/:connectionSupplierId',
    connectionSupplierController.update,
  );
  router.delete('/delete', connectionSupplierController.delete);

  router.post('/synchronize', connectionSupplierController.synchronize);

  app.use('/api/v1/connection-supplier', router);
};
