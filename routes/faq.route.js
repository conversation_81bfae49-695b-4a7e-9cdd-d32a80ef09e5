const express = require('express');
const faqController = require('../controllers/faq.controller');

module.exports = app => {
  const router = express.Router();
  router.post('/create', faqController.create);
  router.get('/find', faqController.find);
  router.put('/update/:faqId', faqController.update);
  router.delete('/delete', faqController.delete);
  router.get('/detail/:faqId', faqController.detail);

  app.use('/api/v1/qlbh/faq', router);

  app.use('/api/v1/faq', router);
};
