const express = require('express');
const mailController = require('../controllers/mail.controller');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();
  router.get('/find', mailController.find);
  router.delete('/delete', mailController.deleteMail);
  router.put('/into-trash', mailController.intoTrash);
  router.put('/out-trash', mailController.outTrash);
  router.put('/set-important', mailController.setImportantMail);
  router.put('/set-unimportant', mailController.setUnimportantMail);
  router.get('/detail/:mailId', mailController.detail);
  router.put('/mark-unread/', mailController.setUnread);
  router.put('/mark-read/', mailController.setRead);
  router.post('/forward/:mailId', mailController.forward);
  router.post('/synchronize', mailController.synchronize);
  router.post('/validate', mailController.validate);
  router.get('/test', mailController.testSendEmail);

  app.use('/api/v1/mail', router);
};
