const express = require('express');
const AutoAdjustInvoiceController = require('../controllers/auto-transfer-invoice.controller');
/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.post('/create', AutoAdjustInvoiceController.create);
  router.get('/find', AutoAdjustInvoiceController.find);
  router.get(
    '/detail/:autoTransferInvoiceId',
    AutoAdjustInvoiceController.detail,
  );

  app.use('/api/v1/auto-transfer-invoice-department', router);
};
