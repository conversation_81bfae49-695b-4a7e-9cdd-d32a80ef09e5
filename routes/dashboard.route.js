const express = require('express');
const dashboardController = require('../controllers/dashboard.controller');

module.exports = app => {
  const router = express.Router();
  router.get('/analyze', dashboardController.analyzeSystem);
  router.get('/analyze-day', dashboardController.analyzeInvoiceDate);
  router.get(
    '/analyze-total-value',
    dashboardController.analyzeInvoiceBuyerName,
  );
  router.get('/report-total-invoice', dashboardController.reportTotalInvoice);

  app.use('/api/v1/dashboard', router);
};
