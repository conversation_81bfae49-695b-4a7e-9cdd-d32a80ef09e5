const express = require('express');
const ProductController = require('../controllers/product.controller');
const fileUploadMiddleware = require('../middlewares/file-upload.middleware');

/**
 *
 * @param {express.Application} app
 */

module.exports = app => {
  const router = express.Router();
  router.post('/import', fileUploadMiddleware(), ProductController.import);
  router.post('/update', fileUploadMiddleware(), ProductController.reduceTax);
  router.get('/find', ProductController.find);

  app.use('/api/v1/product', router);
};
