const express = require('express');

const ConnectionSupplierHistory = require('../controllers/connection-supplier-history.controller');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.get('/find', ConnectionSupplierHistory.find);
  router.get(
    '/detail/:connectionSupplierHistoryId',
    ConnectionSupplierHistory.detail,
  );
  router.post('/reload', ConnectionSupplierHistory.reload);

  app.use('/api/v1/connection-supplier-history', router);
};
