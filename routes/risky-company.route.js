const express = require('express');
const riskyCompanyController = require('../controllers/risky-company.controller');
const checkHashMiddleware = require('../middlewares/check-hash.middleware');
const fileUploadMiddleware = require('../middlewares/file-upload.middleware');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.get('/find', riskyCompanyController.find);
  router.get('/detail/:companyId', riskyCompanyController.detail);
  router.post('/create', checkHashMiddleware, riskyCompanyController.create);
  router.put(
    '/update/:companyId',
    checkHashMiddleware,
    riskyCompanyController.update,
  );
  router.delete('/delete', riskyCompanyController.delete);
  router.post(
    '/upload-file',
    fileUploadMiddleware(),
    riskyCompanyController.uploadFile,
  );

  app.use('/api/v1/qlbh/risky-company', router);
  app.use('/api/v1/risky-company', router);
};
