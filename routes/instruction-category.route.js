const express = require('express');
const InstructionCategoryController = require('../controllers/instruction-category.controller');

module.exports = app => {
  const router = express.Router();
  router.post('/create', InstructionCategoryController.create);
  router.get('/find', InstructionCategoryController.find);
  router.put(
    '/update/:instructionCategoryId',
    InstructionCategoryController.update,
  );
  router.delete('/delete', InstructionCategoryController.delete);
  router.get(
    '/detail/:instructionCategoryId',
    InstructionCategoryController.detail,
  );

  app.use('/api/v1/qlbh/instruction-category', router);
  app.use('/api/v1/instruction-category', router);
};
