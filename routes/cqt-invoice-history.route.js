const express = require('express');
const cqtInvoiceHistoryController = require('../controllers/cqt-invoice-history.controller');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.get('/find', cqtInvoiceHistoryController.find);
  router.get(
    '/detail/:cqtInvoiceHistoryId',
    cqtInvoiceHistoryController.detail,
  );
  router.post('/reload', cqtInvoiceHistoryController.reload);

  app.use('/api/v1/cqt-invoice-history', router);
};
