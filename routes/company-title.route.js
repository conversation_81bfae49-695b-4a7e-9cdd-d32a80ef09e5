const express = require('express');
const companyTitleController = require('../controllers/company-title.controller');

module.exports = app => {
  const router = express.Router();
  router.post('/create', companyTitleController.create);
  router.get('/find', companyTitleController.find);
  router.put('/update/:companyTitleId', companyTitleController.update);
  router.delete('/delete', companyTitleController.delete);
  router.get('/detail/:companyTitleId', companyTitleController.detail);

  app.use('/api/v1/company-title', router);
};
