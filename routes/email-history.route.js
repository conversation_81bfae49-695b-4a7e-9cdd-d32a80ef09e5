const express = require('express');
const emailHistoryController = require('../controllers/email-history.controller');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.use('/find', emailHistoryController.find);
  router.post('/resend', emailHistoryController.resend);
  router.delete('/delete', emailHistoryController.delete);

  app.use('/api/v1/email-history', router);
};
