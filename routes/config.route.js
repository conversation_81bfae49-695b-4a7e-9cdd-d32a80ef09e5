const express = require('express');

const Config = require('../controllers/config.controller');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.get('/find', Config.find);
  router.get('/detail/:configId', Config.detail);
  router.post('/create', Config.create);
  router.put('/update/:configId', Config.update);
  router.delete('/delete', Config.delete);

  app.use('/api/v1/qlbh/config', router);

  app.use('/api/v1/config', router);
};
