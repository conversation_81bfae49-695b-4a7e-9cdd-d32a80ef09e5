const express = require('express');
const cqt = require('../controllers/cqt.controller');

module.exports = app => {
  const router = express.Router();
  router.post('/connection', cqt.connection);
  router.post('/disconnection', cqt.disconnection);
  router.post('/reconnection', cqt.reconnection);
  router.post('/synchronization', cqt.synchronization);
  router.post('/create-new-organization', cqt.createNewOrganization);

  app.use('/api/v1/cqt', router);
};
