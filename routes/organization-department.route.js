const express = require('express');
const organizationDepartmentController = require('../controllers/organization-department.controller');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.post('/create', organizationDepartmentController.create);
  router.put(
    '/update/:organizationDepartmentId',
    organizationDepartmentController.update,
  );
  router.delete('/delete', organizationDepartmentController.delete);

  app.use('/api/v1/organization-department', router);
};
