const express = require('express');
const SupplierController = require('../controllers/supplier.controller');
const fileUploadMiddleware = require('../middlewares/file-upload.middleware');
const { replaceInString } = require('../utils');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();

  router.post('/create', fileUploadMiddleware(), SupplierController.create);
  router.get('/find', SupplierController.find);
  router.get('/detail/:supplierId', SupplierController.detail);
  router.put('/visible/:supplierId', SupplierController.setVisible);
  router.put(
    '/update/:supplierId',
    fileUploadMiddleware(),
    SupplierController.update,
  );
  router.delete('/delete', SupplierController.delete);

  app.use('/api/v1/supplier', router);
};
