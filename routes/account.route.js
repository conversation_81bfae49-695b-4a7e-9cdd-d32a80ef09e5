const express = require('express');
const accountController = require('../controllers/account.controller');
const fileUploadMiddleware = require('../middlewares/file-upload.middleware');

/**
 *
 * @param {express.Application} app
 */
module.exports = app => {
  const router = express.Router();
  const qlbhRouter = express.Router();

  router.post('/register', accountController.register);
  router.post('/login', accountController.login);
  router.post('/logout', accountController.logout);
  router.get(
    '/find-dealer-by-referer-code',
    accountController.findDealerByDealerCode,
  );
  router.post('/create', accountController.create);
  router.put(
    '/update/:accountId',
    fileUploadMiddleware(),
    accountController.update,
  );
  router.put('/update', fileUploadMiddleware(), accountController.update);
  router.get('/detail/:accountId', accountController.detail);
  router.get('/profile', accountController.detail);
  router.get('/find', accountController.find);
  router.delete('/delete', accountController.delete);
  router.get('/many-details', accountController.manyDetails);
  router.put('/change-password', accountController.changePassword);
  router.put('/change-organization', accountController.changeOrganization);
  router.post(
    '/request-reset-password',
    accountController.requestResetPassword,
  );
  router.post('/reset-password', accountController.resetPassword);
  router.use('/activate', accountController.activate);
  router.get('/validate-access-token', accountController.validateAccessToken);

  /* QLBH */
  qlbhRouter.delete('/delete-account', accountController.qlbhDelete);
  qlbhRouter.post('/create-account', accountController.qlbhCreate);
  qlbhRouter.get(
    '/detail-account/:metaAccountCode',
    accountController.qlbhDetail,
  );
  qlbhRouter.put(
    '/update-account/:metaAccountCode',
    fileUploadMiddleware(),
    accountController.qlbhUpdate,
  );
  qlbhRouter.post('/many-details', accountController.qlbhManyDetails);
  qlbhRouter.put(
    '/change-password',
    accountController.qlbhChangePasswordBySupplier,
  );
  qlbhRouter.put('/lock', accountController.qlbhLockCustomer);
  qlbhRouter.put('/unlock', accountController.qlbhUnlockCustomer);

  app.use('/api/v1/qlbh/account', qlbhRouter);
  app.use('/api/v1/qlbh/account', router);

  app.use('/api/v1/account', router);
};
