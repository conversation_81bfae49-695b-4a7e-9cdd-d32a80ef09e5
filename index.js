/* eslint-disable @typescript-eslint/no-empty-function */
const path = require('path');
const {
  PATH_UPLOAD_DIR,
  PATH_PUBLIC_DIR,
} = require('./configs/constants/server-path.constant');
const {
  PORT,
  DEBUG,
  NODE_ENV,
  UPLOAD_DIR,
  PUBLIC_UPLOAD_DIR,
  TEMP_UPLOAD_DIR,
  WS_PORT,
} = require('./configs/env');
const { ERROR_NOT_FOUND, ERROR_API_NOT_FOUND } = require('./configs/error.vi');
const authorizationMiddleware = require('./middlewares/authorization.middleware');
const errorHandlerMiddleware = require('./middlewares/error-handler.middleware');
const parseJsonMiddleware = require('./middlewares/parse-json.middleware');
const refererMiddleware = require('./middlewares/referer.middleware');
const permissionMiddleware = require('./middlewares/permission.middleware');
const { now } = require('./utils');
const {
  CronClearExpiredAccessToken,
  CronSynchronizeSupplier,
  CronUpdateStatusPayment,
  CronSynchronizeCQT,
  CronFetchFromCqt,
  CronFetchFromSupplier,
  CronFetchMailbox,
  CronBackupDatabase,
  CronSyncPartnerCompany,
  CronSyncAlmostExpired,
  CronDurationResourceConsuming,
} = require('./utils/cron.helper');
const deleteEmptyBodyMiddleware = require('./middlewares/delete-empty-body.middleware');
const auditLogMiddleware = require('./middlewares/audit-log.middleware');
const {
  PROCESS_EMIT_SOCKET_EVENT,
} = require('./configs/constants/websocket.constant');
const {
  sendMessageToAccountSocket,
  createSocketServer,
} = require('./utils/websocket.helper');

async function start() {
  const express = require('express');
  const cors = require('cors');
  const fs = require('fs');
  const http = require('http');
  var morgan = require('morgan');
  const moment = require('moment');
  var compression = require('compression');

  /**
   * @type {import('cluster').Cluster}
   */
  const cluster = require('cluster');
  let totalCPUs = require('os').cpus().length;

  if (NODE_ENV == 'dev' && !!DEBUG) {
    totalCPUs = 1;
  } else {
    // totalCPUs = Math.min(2, Math.ceil(totalCPUs / 2));
  }

  if (cluster.isPrimary) {
    console.log(now() + ` - [MAIN] ICORP-HDDV Application Server V1.00`);
    console.log(now() + ` - [MAIN] Number of CPUs is ${totalCPUs}`);
    console.log(now() + ` - [MAIN] Master process is ${process.pid}`);
    // Fork workers.
    /**
     * @type {cluster.Worker[]}
     */
    const workers = [];
    for (let i = 0; i < totalCPUs; i++) {
      const fork = cluster.fork();
      workers.push(fork);
      /* fire message to all processes */
      fork.on('message', event =>
        workers.forEach(worker =>
          worker.send(event, undefined, undefined, () => {}),
        ),
      );
    }

    /* set workers to MASTER global */
    global.workers = workers;

    cluster.on('exit', (worker, code, signal) => {
      console.log(
        now() +
          ` - [WORKER] ${worker.process.pid} died. Forking another process in 3 seconds...`,
      );
      setTimeout(() => {
        cluster.fork();
      }, 3000);
    });

    // Initialized crons
    const db = require('./models');

    // await db.Sequelize.sync({ alter: true, logging: true }).then(() => {
    //   console.log(
    //     `[MAIN] Worker ${process.pid} `,
    //     'Sequelize sync alter:true successfully!',
    //   );
    // });
    await db.Sequelize.sync();
    console.log(
      `[MAIN] Worker ${process.pid} `,
      'Sequelize sync successfully!',
    );

    /* reset trang thai dang dong bo */
    await db.Organization.update(
      { dangDongBo: false, dangExport: false },
      { where: db.Sequelize.literal('TRUE') },
    );
    await db.OrganizationDepartment.update(
      { dangDongBo: false, dangExport: false },
      { where: db.Sequelize.literal('TRUE') },
    );

    /* Cron here */
    if (!DEBUG) {
      CronClearExpiredAccessToken.setMaxListeners(1).start();
      CronSynchronizeSupplier.setMaxListeners(1).start();
      CronUpdateStatusPayment.setMaxListeners(1).start();
      CronSynchronizeCQT.setMaxListeners(1).start();
      // CronFetchFromCqt.setMaxListeners(1).start();
      // CronFetchFromSupplier.setMaxListeners(1).start();
      CronFetchMailbox.setMaxListeners(1).start();
      CronBackupDatabase.setMaxListeners(1).start();
      // CronSyncPartnerCompany.setMaxListeners(1).start();
      CronSyncAlmostExpired.setMaxListeners(1).start();
      CronDurationResourceConsuming.setMaxListeners(1).start();
    }
  } else {
    console.log(now() + ` - [WORKER] Worker ${process.pid} started`);

    /* ----------INIT DIR------- */
    // Upload dir
    if (!fs.existsSync(UPLOAD_DIR)) fs.mkdirSync(UPLOAD_DIR);
    // Public dir
    if (!fs.existsSync(PUBLIC_UPLOAD_DIR)) fs.mkdirSync(PUBLIC_UPLOAD_DIR);
    // Temp dir
    if (!fs.existsSync(TEMP_UPLOAD_DIR)) fs.mkdirSync(TEMP_UPLOAD_DIR);
    // company logo
    if (!fs.existsSync(path.join(PUBLIC_UPLOAD_DIR, 'company-logo')))
      fs.mkdirSync(path.join(PUBLIC_UPLOAD_DIR, 'company-logo'));
    // notification thumbnail
    if (!fs.existsSync(path.join(PUBLIC_UPLOAD_DIR, 'notification-thumbnail')))
      fs.mkdirSync(path.join(PUBLIC_UPLOAD_DIR, 'notification-thumbnail'));
    // connection supplier logo
    if (!fs.existsSync(path.join(PUBLIC_UPLOAD_DIR, 'supplier-logo')))
      fs.mkdirSync(path.join(PUBLIC_UPLOAD_DIR, 'supplier-logo'));
    // avatar customer comment
    if (!fs.existsSync(path.join(PUBLIC_UPLOAD_DIR, 'avatar-customer')))
      fs.mkdirSync(path.join(PUBLIC_UPLOAD_DIR, 'avatar-customer'));
    // common file
    if (!fs.existsSync(path.join(PUBLIC_UPLOAD_DIR, 'common-file')))
      fs.mkdirSync(path.join(PUBLIC_UPLOAD_DIR, 'common-file'));
    // xml
    if (!fs.existsSync(path.join(UPLOAD_DIR, 'invoice-xml')))
      fs.mkdirSync(path.join(UPLOAD_DIR, 'invoice-xml'));
    // img
    if (!fs.existsSync(path.join(UPLOAD_DIR, 'invoice-img')))
      fs.mkdirSync(path.join(UPLOAD_DIR, 'invoice-img'));
    // origin xml
    if (!fs.existsSync(path.join(UPLOAD_DIR, 'invoice-origin-xml')))
      fs.mkdirSync(path.join(UPLOAD_DIR, 'invoice-origin-xml'));
    // pdf
    if (!fs.existsSync(path.join(UPLOAD_DIR, 'invoice-pdf')))
      fs.mkdirSync(path.join(UPLOAD_DIR, 'invoice-pdf'));
    // company partner
    if (!fs.existsSync(path.join(PUBLIC_UPLOAD_DIR, 'partner-company')))
      fs.mkdirSync(path.join(PUBLIC_UPLOAD_DIR, 'partner-company'));
    // invoice attach
    if (!fs.existsSync(path.join(PUBLIC_UPLOAD_DIR, 'invoice-attach')))
      fs.mkdirSync(path.join(PUBLIC_UPLOAD_DIR, 'invoice-attach'));
    // invoice preview
    if (!fs.existsSync(path.join(PUBLIC_UPLOAD_DIR, 'invoice-preview')))
      fs.mkdirSync(path.join(PUBLIC_UPLOAD_DIR, 'invoice-preview'));
    // mail attachments
    if (!fs.existsSync(path.join(UPLOAD_DIR, 'mail-attachment')))
      fs.mkdirSync(path.join(UPLOAD_DIR, 'mail-attachment'));
    // instruction
    if (!fs.existsSync(path.join(PUBLIC_UPLOAD_DIR, 'instruction')))
      fs.mkdirSync(path.join(PUBLIC_UPLOAD_DIR, 'instruction'));
    if (!fs.existsSync(path.join(PUBLIC_UPLOAD_DIR, 'instruction/thumbnail')))
      fs.mkdirSync(path.join(PUBLIC_UPLOAD_DIR, 'instruction/thumbnail'));
    if (!fs.existsSync(path.join(PUBLIC_UPLOAD_DIR, 'instruction/url')))
      fs.mkdirSync(path.join(PUBLIC_UPLOAD_DIR, 'instruction/url'));
    /* avatar */
    if (!fs.existsSync(path.join(PUBLIC_UPLOAD_DIR, 'avatars')))
      fs.mkdirSync(path.join(PUBLIC_UPLOAD_DIR, 'avatars'));

    if (!fs.existsSync(path.join(PUBLIC_UPLOAD_DIR, 'cccdBackImg')))
      fs.mkdirSync(path.join(PUBLIC_UPLOAD_DIR, 'cccdBackImg'));

    if (!fs.existsSync(path.join(PUBLIC_UPLOAD_DIR, 'cccdFrontImg')))
      fs.mkdirSync(path.join(PUBLIC_UPLOAD_DIR, 'cccdFrontImg'));

    /* ---------------------- */

    const db = require('./models');

    await db.Sequelize.sync();
    console.log(
      `[WORKER] Worker ${process.pid}`,
      'Sequelize sync successfully!',
    );

    /* config express */
    const app = express();

    // compress all responses
    app.use(compression());

    morgan.token('date', (req, res) => {
      return moment().format('YYYY-MM-DD HH:mm:ss');
    });
    app.use(
      morgan(
        '[:date[clf]] [HTTP] :remote-addr ":method :url HTTP/:http-version" :status :res[content-length] :response-time ms ":referrer" ":user-agent"',
      ),
    );

    app.set('trust proxy', true);

    app.use(
      cors({
        origin: '*',
      }),
    );

    app.use(
      `${PATH_PUBLIC_DIR}/temp`,
      express.static(TEMP_UPLOAD_DIR),
      /**
       * @type {import('./types').RequestHandler}
       */
      (_, res) => {
        res.status(404).send({ result: 'failed', reason: ERROR_NOT_FOUND });
      },
    );

    app.use(
      PATH_PUBLIC_DIR,
      express.static(PUBLIC_UPLOAD_DIR),
      /**
       * @type {import('./types').RequestHandler}
       */
      (_, res) => {
        res.status(404).send({ result: 'failed', reason: ERROR_NOT_FOUND });
      },
    );

    app.use(
      PATH_UPLOAD_DIR,
      refererMiddleware,
      express.static(UPLOAD_DIR),
      /**
       * @type {import('./types').RequestHandler}
       */
      (_, res) => {
        res.status(404).send({ result: 'failed', reason: ERROR_NOT_FOUND });
      },
    );
    /* multipart/form-data */
    // app.use(fileUploadMiddleware());
    // parse requests of content-type - application/json
    app.use(express.json({ limit: '128mb' }));
    // parse requests of content-type - application/x-www-form-urlencoded
    app.use(
      express.urlencoded({
        extended: true,
        limit: '128mb',
        parameterLimit: 100000,
      }),
    );

    /* auth middleware */
    app.use(refererMiddleware);
    app.use(authorizationMiddleware);
    app.use(permissionMiddleware);
    app.use(parseJsonMiddleware);
    app.use(deleteEmptyBodyMiddleware);
    app.use(auditLogMiddleware);

    /* routes */
    // if (NODE_ENV == 'dev') {
    require('./routes/test.route')(app);
    // }
    require('./routes/account.route')(app);
    require('./routes/instruction.route')(app);
    require('./routes/company.route')(app);
    require('./routes/company-title.route')(app);
    require('./routes/organization.route')(app);
    require('./routes/organization-department.route')(app);
    require('./routes/instruction-category.route')(app);
    require('./routes/faq.route')(app);
    require('./routes/role.route')(app);
    require('./routes/partner-company.route')(app);
    require('./routes/tag.route')(app);
    require('./routes/invoice-tag.route')(app);
    require('./routes/notification.route')(app);
    require('./routes/misc.route')(app);
    require('./routes/permission-code.route')(app);
    require('./routes/audit-log.route')(app);
    require('./routes/supplier.route')(app);
    require('./routes/connection-supplier.route')(app);
    require('./routes/invoice.route')(app);
    require('./routes/auth-log.route')(app);
    require('./routes/comment-customer.route')(app);
    require('./routes/contact-customer.route')(app);
    require('./routes/tax-reduced-document.route')(app);
    require('./routes/risky-company.route')(app);
    require('./vietinvoice/route.test.vietinvoice')(app);
    require('./routes/package.route')(app);
    require('./routes/order.route')(app);
    require('./routes/config.route')(app);
    require('./routes/connection-supplier-history.route')(app);
    require('./routes/cqt-invoice-history.route')(app);
    require('./routes/cqt.route')(app);
    require('./routes/dashboard.route')(app);
    require('./routes/resource-history.route')(app);
    require('./routes/invoice-attach.route')(app);
    require('./routes/product.route')(app);
    require('./routes/supplier-not-download-invoice.route')(app);
    require('./routes/setting.route')(app);
    require('./routes/mail.route')(app);
    require('./routes/auto-transfer-invoice.route')(app);
    require('./routes/auto-transfer-invoice-from-email.route')(app);
    require('./routes/accept-partner-company-detail.route')(app);
    require('./routes/customer-package.route')(app);
    require('./routes/screen-code.route')(app);
    require('./routes/email-history.route')(app);

    /* error handler */
    app.use(errorHandlerMiddleware);

    /* 404 */
    app.use(
      /**
       * @type {import('./types').RequestHandler}
       */
      (_req, res, next) => {
        res.status(404).send({ result: 'failed', reason: ERROR_API_NOT_FOUND });
      },
    );

    /* event from other processes */
    process.on('message', ({ action, payload }) => {
      switch (action) {
        case PROCESS_EMIT_SOCKET_EVENT:
          if (!!payload) {
            try {
              sendMessageToAccountSocket(payload);
            } catch (error) {
              console.warn(error);
            }
          }
          break;
      }
    });

    process.on('warning', e => console.warn(e.stack));

    const ws_server = http.createServer(() => {});
    createSocketServer(ws_server);

    ws_server.listen(WS_PORT, '0.0.0.0', () => {
      console.log(
        now() + ` - [WS] ICORP-HDDV server is running on port ${WS_PORT}.`,
      );
    });

    app.listen(PORT, () => {
      console.log(
        now() + ` - [HTTP] ICORP-HDDV server is running on port ${PORT}.`,
      );
    });
  }
}

start();
