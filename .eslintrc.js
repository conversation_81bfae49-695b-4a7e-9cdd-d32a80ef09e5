module.exports = {
  env: {
    commonjs: true,
    es2021: true,
    node: true,
  },
  extends: ['eslint:recommended', 'plugin:@typescript-eslint/recommended'],
  overrides: [],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'latest',
  },
  plugins: ['@typescript-eslint'],
  rules: {
    'no-empty': 'off',
    '@typescript-eslint/no-shadow': ['error'],
    'no-shadow': 'off',
    'no-extra-boolean-cast': 'off',
    '@typescript-eslint/no-var-requires': 'off',
    'no-useless-escape': 'off',
    'no-constant-condition': 'off',
    'no-case-declarations': 'off',
    'no-unsafe-optional-chaining': 'off',
  },
};
