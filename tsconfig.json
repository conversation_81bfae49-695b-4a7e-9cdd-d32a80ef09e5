{"compilerOptions": {"target": "ES2015", "lib": ["esnext"], "allowJs": true, "baseUrl": ".", "outDir": "./dist", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "CommonJS", "moduleResolution": "node", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "experimentalDecorators": true}, "include": ["types/index.ts"]}