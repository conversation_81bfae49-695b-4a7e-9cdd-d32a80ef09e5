const { default: axios } = require('axios');
const { EXPORT_TYPE } = require('./vietinvoice.constant');
const {
  ERROR_NOT_FOUND,
  ERROR_MISSING_PARAMETERS,
  ERROR_INTERNAL,
  ERROR_INVALID_PARAMETER,
  ERROR_WRONG_PASSWORD,
  ERROR_CONFIG_NON_EXISTENT,
  ERROR_USER_NOT_IN_ORGANIZATION,
} = require('../configs/error.vi');
const fs = require('fs');
const {
  ConnectionSupplier,
  OrganizationDepartment,
  Supplier,
  Organization,
} = require('../models');
const { Op } = require('sequelize');
const {
  ERROR_NOT_FOUND_CONNECTION_SUPPLIER,
  ERROR_VIETINVOICE_RESPONSE,
  ERROR_VIETINVOICE_AUTHORIZATION,
} = require('./error.vi');
const { login } = require('./vietinvoice.helper');
const https = require('https');
const crypto = require('crypto');

module.exports = {
  /**
   * @type {import('../types').RequestHandler}
   */
  async getInvoiceList(req, res, next) {
    try {
      const { xCSRFToken } = req.body;
      if (!xCSRFToken) throw new Error(ERROR_MISSING_PARAMETERS);

      let data = await axios.get(
        'http://hoadon.vietinvoice.vn/erp/rest/s1/iam-invoice/invoices/list',
        {
          headers: {
            Cookie: xCSRFToken,
          },
          httpsAgent: new https.Agent({
            // for self signed you could also add
            // rejectUnauthorized: false,

            // allow legacy server
            secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
          }),
        },
      );
      if (data.errorCode) throw new Error(ERROR_NOT_FOUND);
      return res.send(data.data);
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async exportInvoice(req, res, next) {
    try {
      const { invoiceId } = req.params;
      const { type, xCSRFToken } = req.body;

      if (!xCSRFToken) throw new Error(ERROR_MISSING_PARAMETERS);

      let data;

      if (type === EXPORT_TYPE.PDF) {
        data = await axios.get(
          `http://hoadon.vietinvoice.vn/erp/rest/s1/iam-invoice/invoices/${invoiceId}/pdf`,
          {
            headers: {
              Cookie: xCSRFToken,
            },
            responseType: 'arraybuffer',
            httpsAgent: new https.Agent({
              // for self signed you could also add
              // rejectUnauthorized: false,

              // allow legacy server
              secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
            }),
          },
        );
        if (data.errorCode) throw new Error(ERROR_NOT_FOUND);
        res.setHeader('Content-Type', 'application/pdf;charset=UTF-8');
      } else if (type === EXPORT_TYPE.XML) {
        data = await axios.get(
          `http://hoadon.vietinvoice.vn/erp/rest/s1/iam-invoice/invoices/${invoiceId}/xml`,
          {
            headers: {
              Cookie: xCSRFToken,
            },
            responseType: 'arraybuffer',
            httpsAgent: new https.Agent({
              // for self signed you could also add
              // rejectUnauthorized: false,

              // allow legacy server
              secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
            }),
          },
        );
        if (data.errorCode) throw new Error(ERROR_NOT_FOUND);
      }
      res.send(data?.data);
    } catch (error) {
      next(error);
    }
  },
  /**
   * @type {import('../types').RequestHandler}
   */
  async detailInvoice(req, res, next) {
    try {
      const { invoiceId } = req.params;

      const { xCSRFToken } = req.body;
      if (!xCSRFToken) throw new Error(ERROR_MISSING_PARAMETERS);

      const data = await axios.get(
        `http://hoadon.vietinvoice.vn/erp/rest/s1/iam-invoice/invoices/${invoiceId}/detail`,
        {
          headers: {
            Cookie: xCSRFToken,
          },
          httpsAgent: new https.Agent({
            // for self signed you could also add
            // rejectUnauthorized: false,

            // allow legacy server
            secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
          }),
        },
      );
      if (data.errorCode) throw new Error(ERROR_VIETINVOICE_AUTHORIZATION);

      res.send(data.data);
    } catch (error) {
      next(error);
    }
  },
};

module.exports.vietinvoiceHelper = require('./vietinvoice.helper');
