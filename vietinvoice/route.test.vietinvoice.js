const express = require('express');
const {
  getInvoiceList,
  exportInvoice,
  detailInvoice,
  connect,
} = require('./index');

module.exports = app => {
  const router = express.Router();
  // router.post('/login', connect);
  router.get('/detail/:invoiceId', detailInvoice);
  router.get('/:invoiceId', exportInvoice);
  router.get('/', getInvoiceList);

  app.use('/api/v1/vietinvoice', router);
};
