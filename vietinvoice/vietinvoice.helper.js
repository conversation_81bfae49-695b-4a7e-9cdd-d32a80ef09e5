const { default: axios } = require('axios');
const {
  VIETINVOICE_AUTH_URL,
  VIETINVOICE_INVOICE_URL,
} = require('./vietinvoice.constant');
const { ERROR_INTERNAL } = require('../configs/error.vi');
const moment = require('moment');
const { isValidNumber, parseNumber } = require('../utils');
const _ = require('lodash');
const {
  INVOICE_STATUS,
  INVOICE_STATUS_TEXT,
} = require('../configs/constants/invoice.constant');
const https = require('https');
const crypto = require('crypto');

const X_CSRF_Token = async () => {
  const token = await axios.get(`${VIETINVOICE_AUTH_URL}/token`, {
    httpsAgent: new https.Agent({
      // for self signed you could also add
      // rejectUnauthorized: false,

      // allow legacy server
      secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
    }),
  });

  if (token) return token?.data?.moquiSessionToken;
  return;
};

const login = async (usernameConnection, passwordConnection) => {
  const res = await axios.post(
    `${VIETINVOICE_AUTH_URL}/login`,
    {
      username: usernameConnection,
      password: passwordConnection,
    },
    {
      httpsAgent: new https.Agent({
        // for self signed you could also add
        // rejectUnauthorized: false,

        // allow legacy server
        secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
      }),
    },
  );

  if (res?.data?.isSuccess === 'Y') {
    const cookie = res.headers['set-cookie'][0].split(';')[0];
    //console.log(cookie);
    return cookie;
  } else if (res?.data?.errors[0].includes('No account found for username')) {
    throw new Error(`Không tim thấy tài khoản: ${usernameConnection}`);
  } else if (res?.data?.errors[0].includes(`Password incorrect for username`)) {
    throw new Error(`Sai mật khẩu cho tài khoản: ${usernameConnection}`);
  }
  throw new Error(res?.data?.errors?.[0] ?? ERROR_INTERNAL);
};

const _synchronize = async (username, password, from, to) => {
  let xCSRFToken = await login(username, password);
  if (from) from = moment(from).toDate().getTime();
  if (to) to = moment(to).toDate().getTime();
  const response = await axios.get(`${VIETINVOICE_INVOICE_URL}/invoices/list`, {
    headers: {
      Cookie: xCSRFToken,
    },
    params: {
      fromDate: from,
      thruDate: to,
    },
    httpsAgent: new https.Agent({
      // for self signed you could also add
      // rejectUnauthorized: false,

      // allow legacy server
      secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
    }),
  });

  if (response.status === 200)
    return {
      data: response.data.listData.filter(
        item => item.issueStatusId == 'InvTaxAuthCodeGranted',
      ),
      xCSRFToken,
    };

  throw new Error(ERROR_INTERNAL);
};

/**
 *
 * @param {string} username
 * @param {string} password
 * @param {string} invoiceId
 * @param {string=} xCSRFToken
 */
const detail = async (username, password, invoiceId, xCSRFToken) => {
  if (!xCSRFToken) {
    xCSRFToken = await login(username, password);
  }

  const response = await axios.get(
    `${VIETINVOICE_INVOICE_URL}/invoices/${invoiceId}/detail`,
    {
      headers: {
        Cookie: xCSRFToken,
      },
      httpsAgent: new https.Agent({
        // for self signed you could also add
        // rejectUnauthorized: false,

        // allow legacy server
        secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
      }),
    },
  );

  if (response.status === 200) return { data: response.data, xCSRFToken };

  return { xCSRFToken };
};

/**
 *
 * @param {import('../types').VietInvoiceInvoice} data
 * @returns {import('../types').Invoice}
 */
const toInvoice = data => {
  const invoiceHelper = require('../utils/invoice.helper');

  let vietInvoiceStatustoInvoiceStatus = enumId => {
    switch (enumId) {
      case 'InvClsOriginal':
        return INVOICE_STATUS[1];
      case 'InvClsReplacement':
        return INVOICE_STATUS[2];
      case 'InvClsAdjustment':
        return INVOICE_STATUS[3];
      case 'InvClsReplaced':
        return INVOICE_STATUS[4];
      case 'InvClsAdjusted':
        return INVOICE_STATUS[5];
      case 'InvClsCancelled':
        return INVOICE_STATUS[6];
      default:
        return INVOICE_STATUS[1];
    }
  };

  return {
    invoiceVersion: data.invoiceVersion,
    maCQT: data.externalId,
    sellerTaxCode: data.fromPartyTaxId,
    sellerBankName: data.sellerBankName,
    sellerBankAccount: data.sellerAccountNumber,
    sellerName: data.fromPartyName,

    buyerTaxCode: data.toPartyTaxId,
    buyerName: data.toPartyName,

    finalAmount: data.invoiceTotal,
    amountVat: data.totalTaxAmount,
    amountBeforeVat:
      isValidNumber(data.invoiceTotal) && isValidNumber(data.totalTaxAmount)
        ? _.toNumber(data.invoiceTotal) + _.toNumber(data.totalTaxAmount)
        : undefined,
    finalAmountInWord: data.amountInWords,
    paymentMethod: data.paymentInstrumentEnum,
    invoiceNumber: data.invoiceNo,
    paymentCurrency: data.baseCurrencyUomId,
    paymentExchangeRate: data.exchangeRate,
    vietInvoiceId: data.invoiceId,
    invoiceDate: moment(data.invoiceDate).toDate(),
    buyerAddress: data.toAddress,

    invoiceTypeCode: data.invoiceForm,
    invoiceType: invoiceHelper.getInvoiceTypeFromTypeCode(data.invoiceForm),
    serial: data.invoiceSerial,
    issueStatus: data.issueStatus,
    issueDate: moment(data.issuedDate).toDate(),
    supplierReferenceCode: data.referenceNumber,
    supplierKey: 'vietinvoice',
    supplierTaxCode: '0106870211',
    statusInvoice: vietInvoiceStatustoInvoiceStatus(data.classTypeEnumId),
    statusInvoiceText:
      INVOICE_STATUS_TEXT[
        vietInvoiceStatustoInvoiceStatus(data.classTypeEnumId)
      ],
    InvoiceProducts: data.productItems?.map(product => {
      /**
       * @type {import('../types').InvoiceProduct}
       */
      let item = {
        productType: invoiceHelper.getProductTypeFromCode(
          product.itemClassTypeCode,
        ),
        quantity: product.quantity,

        unit: product.unitOfMeasure,
        name: product.itemName,
        finalAmount: product.amountTotal,

        price: product.amount,
      };

      if (
        isValidNumber(product.amountTotalWithDiscount) &&
        isValidNumber(product.amountTotal)
      ) {
        item.discountAmount =
          product.amountTotal - product.amountTotalWithDiscount;
        item.discount = item.discountAmount / product.amountTotal;
      }

      if (isValidNumber(item.quantity) && isValidNumber(item.price)) {
        item.amountTotal = item.quantity * item.price;
      }

      if (product.itemTotalExcludeVat && product.itemTotalWithTax) {
        item.vatAmount =
          parseNumber(product.itemTotalWithTax) -
          parseNumber(product.itemTotalExcludeVat);
      }

      return item;
    }),
  };
};

/**
 *
 * @param {string} username
 * @param {string} password
 * @param {string} invoiceId
 * @param {string=} xCSRFToken
 * @returns
 */
const getXml = async (username, password, invoiceId, xCSRFToken) => {
  if (!xCSRFToken) {
    xCSRFToken = await login(username, password);
  }

  const response = await axios.get(
    `${VIETINVOICE_INVOICE_URL}/invoices/${invoiceId}/xml`,
    {
      headers: {
        Cookie: xCSRFToken,
      },
      httpsAgent: new https.Agent({
        // for self signed you could also add
        // rejectUnauthorized: false,

        // allow legacy server
        secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
      }),
    },
  );

  if (response.status === 200) return { data: response.data, xCSRFToken };

  return { xCSRFToken };
};

/**
 *
 * @param {string} username
 * @param {string} password
 * @param {string} invoiceId
 * @param {string=} xCSRFToken
 *
 * @returns
 */
const getPdf = async (username, password, invoiceId, xCSRFToken) => {
  if (!xCSRFToken) {
    xCSRFToken = await login(username, password);
  }

  const response = await axios.get(
    `${VIETINVOICE_INVOICE_URL}/invoices/${invoiceId}/pdf`,
    {
      headers: {
        Cookie: xCSRFToken,
      },
      responseType: 'arraybuffer',
      httpsAgent: new https.Agent({
        // for self signed you could also add
        // rejectUnauthorized: false,

        // allow legacy server
        secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
      }),
    },
  );

  if (response.status === 200) return { data: response.data, xCSRFToken };

  return { xCSRFToken };
};

module.exports = {
  X_CSRF_Token,
  login,
  _synchronize,
  detail,
  toInvoice,
  getXml,
  getPdf,
};
