const { default: axios } = require('axios');
const { ERROR_NOT_FOUND } = require('../configs/error.vi');
const _ = require('lodash');
const { isValidTaxCode } = require('.');
const { Business, BusinessCompany } = require('../models');

module.exports = {
  // eslint-disable-next-line no-inner-declarations
  /**
   *
   * @param {string} q
   * @returns {Promise<import('../types').NNT>}
   */
  async getCompany(q) {
    let url = 'https://taxcode-lookup.icorp.vn/api/v1/tax_code';
    let token =
      'eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ';

    let rs = await axios
      .get(url, {
        params: { q },
        headers: { Authorization: `Bearer ${token}` },
        timeout: 7000,
      })
      .catch(() => ({ data: {} }));

    let data = rs.data;

    if (data.result !== 'success' || (!data.data && !data.detail)) {
      let error = new Error(ERROR_NOT_FOUND);
      error.status = 404;

      throw error;
    }

    if (data.detail) {
      data.data = data?.detail?.payload?.[1];
    }

    let company = {
      ..._.pick(data.data, [
        'companyName',
        'taxCode',
        'address',
        'country',
        'province',
        'district',
        'managedBy',
        'phone',
        'email',
      ]),
      ...data.data,
      contactPerson: data.data.legalRepresentative,
      contactPersonPosition: data.data.position,
      contactPersonGender: (function () {
        switch (_.toLower(data.data.gender)) {
          case 'nam':
            return 'MALE';
          case 'nữ':
            return 'FEMALE';
          default:
            return 'OTHER';
        }
      })(),
      email: data.data.email,
      address: data.data.address,
      phone: data.data.phone,
      businessPermitCode: data.data.legalDocumentValue,
      businessPermitAddress: data.data.permanentAddress,
      businessPermitDate: data.data.dateOfOperation,
      query: isValidTaxCode(q) ? '' : q,
      status: data.data.status
        ? data.data.status
        : 'Đang hoạt động (đã được cấp GCN ĐKT)',
    };

    company.companyName = _.toUpper(company.companyName);
    if (company.contactPerson)
      company.contactPerson = _.toUpper(company.contactPerson);

    if (_.isArray(company.businesses)) {
      let _businesses = await Business.bulkCreate(company.businesses, {
        ignoreDuplicates: true,
      });

      await BusinessCompany.bulkCreate(
        _businesses.map(item => ({
          businessCode: item.businessCode,
          taxCode: company.taxCode,
        })),
        { ignoreDuplicates: true },
      );
    }
    return company;
  },
};
