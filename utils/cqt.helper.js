const { default: axios } = require('axios');
const {
  PATH_HOADONDIENTU_GDT_GOV_VN,
  PATH_CAPTCHA,
  BASE64_CAPTCHA_MODEL,
  PATH_AUTHENTICATE,
  PATH_HDDV,
  PATH_HDDR,
  PATH_CQT_LOOKUP,
  PATH_CQT_REGD_STATUS,
  PATH_CQT_EXPORT_INVOICE,
  PATH_CQT_DETAIL_INVOICE,
  PATH_HDDV_MTT,
  PATH_HDDR_MTT,
  PATH_CQT_DETAIL_INVOICE_MTT,
  PATH_CQT_EXPORT_INVOICE_MTT,
} = require('../configs/constants/cqt.constant');
const {
  Invoice,
  CqtInvoiceHistory,
  Sequelize,
  Organization,
  OrganizationDepartment,
  InvoiceValidate,
  AcceptPartnerCompanyDetail,
  PartnerCompany,
  Company,
  ResourceHistory,
  CqtInvoice,
  SupplierNotDownloadInvoice,
} = require('../models');
const { readFileSync, writeFileSync, existsSync } = require('fs');
const {
  INVOICE_TYPE_CODE,
  INVOICE_CATEGORY,
  INVOICE_TYPE,
  PAYMENT_STATUS,
  INVOICE_CHECK_STATUS,
  INVOICE_STATUS,
  INVOICE_STATUS_TEXT,
  VAT_TYPE,
  POSSIBLE_LOOKUP_CODE_XML_TAG,
  TYPE_INVOICE_RECEIVE,
  PRODUCT_TYPE,
  INVOICE_CHECK_STATUS_TEXT,
} = require('../configs/constants/invoice.constant');
const _ = require('lodash');
const moment = require('moment');
const { ERROR_MISSING_PARAMETERS } = require('../configs/error.vi');
const { Op, Transaction, Model } = require('sequelize');
const { json } = require('body-parser');
const nntHelper = require('./nnt.helper');
const { UPLOAD_DIR, PUBLIC_UPLOAD_DIR } = require('../configs/env');
const utils = require('.');
const {
  PARTNER_COMPANY_STATUS,
  PARTNER_COMPANY_TYPE,
} = require('../configs/constants/company.constant');
const companyHelper = require('./company.helper');
const { PACKAGE_TYPE } = require('../configs/constants/package.constant');
const https = require('https');
const crypto = require('crypto');

const axiosClient = axios.create({
  baseURL: PATH_HOADONDIENTU_GDT_GOV_VN,
  httpsAgent: new https.Agent({
    // for self signed you could also add
    // rejectUnauthorized: false,

    // allow legacy server
    secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
  }),
  timeout: 30000,
});

module.exports = {};
/**
 *
 * @param {string} username
 * @param {string} password
 */
async function cqt_authenticate(username, password) {
  /* TODO */
  let { key: ckey, content } = await captcha();

  let cvalue = readCaptchaSvg(content);

  let response = await axiosClient
    .post(PATH_AUTHENTICATE, {
      username,
      password,
      ckey,
      cvalue,
    })
    .catch(error => {
      // writeFileSync('fail_read.svg', content, 'utf-8');

      return { data: {} };
    });
  // console.log('cqttttt', response);
  let { token } = response.data;

  return { token };
}

module.exports.cqt_authenticate = cqt_authenticate;

async function captcha() {
  let response = await axiosClient.get(PATH_CAPTCHA);

  let { key, content } = response.data;

  return { key, content };
}

module.exports.cqt_captcha = captcha;

/**
 * read captcha
 * @param {string } content SVG content in string
 */
function readCaptchaSvg(content) {
  const jsdom = require('jsdom');
  const dom = new jsdom.JSDOM('');

  const $ = require('jquery')(dom.window);
  let svg = content;

  // $(svg);

  // this is fixed
  const model = BASE64_CAPTCHA_MODEL;

  var parsed_model = JSON.parse(atob(model));

  // console.log({ parsed_model });
  // var parser = new DOMParser();

  // var svg = $('svg');
  $(svg)
    .find('path')
    .each((__, p) => {
      if ($(p).attr('stroke') != undefined) $(p).remove();
    });
  let vals = [];
  $(svg)
    .find('path')
    .each((__, p) => {
      let idx = parseInt($(p).attr('d').split('.')[0].replace('M', ''));
      vals.push(idx);
    });
  var sorted = [...vals].sort(function (a, b) {
    return a - b;
  });

  var solution = [];

  $(svg)
    .find('path')
    .each((idx, p) => {
      var pattern = $(p)
        .attr('d')
        .replace(/[\d\.\s]/g, '');
      solution[sorted.indexOf(vals[idx])] =
        parsed_model[
          Object.keys(parsed_model).find(i => i.includes(`d="${pattern}"`))
        ] !== '0'
          ? parsed_model[
              Object.keys(parsed_model).find(i => i.includes(`d="${pattern}"`))
            ]
          : solution[sorted.indexOf(vals[idx])];
    });

  solution = solution.filter(i => i !== '0');

  //   console.log(solution); //solution array contains the decoded captcha

  return solution.join('');
}

module.exports.readCaptchaSvg = readCaptchaSvg;

/**
 *
 * @param {import('axios').AxiosRequestConfig} config
 * @param {number} organizationId
 * @param {number} organizationDepartmentId
 * @param {string} taxCode
 * @param {any} from
 * @param {any} to
 * @returns
 */
async function cqt_getHDDV(
  config,
  organizationId,
  organizationDepartmentId,
  taxCode,
  from,
  to,
) {
  try {
    let response = await axiosClient.get(PATH_HDDV, config);
    let { datas, total, state, time } = response.data;
    await asyncInvoiceCQT(
      datas,
      organizationId,
      organizationDepartmentId,
      taxCode,
      INVOICE_CATEGORY.INPUT_INVOICE,
      from,
      to,
    );
    return { datas, total, state, time };
  } catch (error) {
    console.log(error);
  }
}

module.exports.cqt_getHDDV = cqt_getHDDV;

/**
 *
 * @param {import('axios').AxiosRequestConfig} config
 * @param {number} organizationId
 * @param {number} organizationDepartmentId
 * @param {string} taxCode
 * @param {any} from
 * @param {any} to
 * @returns
 */
async function cqt_getHDDV_MTT(
  config,
  organizationId,
  organizationDepartmentId,
  taxCode,
  from,
  to,
) {
  try {
    let response = await axiosClient.get(PATH_HDDV_MTT, config);
    let { datas, total, state, time } = response.data;
    await asyncInvoiceCQT(
      datas,
      organizationId,
      organizationDepartmentId,
      taxCode,
      INVOICE_CATEGORY.INPUT_INVOICE,
      from,
      to,
      false,
    );
    return { datas, total, state, time };
  } catch (error) {
    console.log(error);
  }
}

module.exports.cqt_getHDDV_MTT = cqt_getHDDV_MTT;

/**
 *
 * @param {import('axios').AxiosRequestConfig} config
 * @param {number} organizationId
 * @param {number} organizationDepartmentId
 * @param {string} taxCode
 * @param {any} from
 * @param {any} to
 * @returns
 */
async function cqt_getHDDR(
  config,
  organizationId,
  organizationDepartmentId,
  taxCode,
  from,
  to,
) {
  let response = await axiosClient.get(PATH_HDDR, config);
  let { datas, total, state, time } = response.data;
  await asyncInvoiceCQT(
    datas,
    organizationId,
    organizationDepartmentId,
    taxCode,
    INVOICE_CATEGORY.OUTPUT_INVOICE,
    from,
    to,
  );

  return { datas, total, state, time };
}

module.exports.cqt_getHDDR = cqt_getHDDR;

/**
 *
 * @param {import('axios').AxiosRequestConfig} config
 * @param {number} organizationId
 * @param {number} organizationDepartmentId
 * @param {string} taxCode
 * @param {any} from
 * @param {any} to
 * @returns
 */
async function cqt_getHDDR_MTT(
  config,
  organizationId,
  organizationDepartmentId,
  taxCode,
  from,
  to,
) {
  let response = await axiosClient.get(PATH_HDDR_MTT, config);
  let { datas, total, state, time } = response.data;
  await asyncInvoiceCQT(
    datas,
    organizationId,
    organizationDepartmentId,
    taxCode,
    INVOICE_CATEGORY.OUTPUT_INVOICE,
    from,
    to,
    false,
  );

  return { datas, total, state, time };
}

module.exports.cqt_getHDDR_MTT = cqt_getHDDR_MTT;

/**
 *
 * @param {(import('../types').CQTInvoice|import('../types').CQTInvoiceMTT)[]} datas
 * @param {number} organizationId
 * @param {number} organizationDepartmentId
 * @param {string} taxCode
 * @param {import('../types').INVOICE_CATEGORY} invoiceCategory
 * @param {any} from
 * @param {any} to
 * @param {boolean=} notMtt lay hoa don tu MTT
 */
async function asyncInvoiceCQT(
  datas,
  organizationId,
  organizationDepartmentId,
  taxCode,
  invoiceCategory,
  from,
  to,
  notMtt = true,
) {
  // let t = await Sequelize.transaction();
  let t = undefined;

  try {
    const invoiceHelper = require('./invoice.helper');

    const organization = await Organization.findOne({
      where: { organizationId },
      transaction: t,
    });
    const organizationDepartment = await OrganizationDepartment.findOne({
      where: { organizationDepartmentId },
      include: [{ association: 'OrganizationBranch' }],
      transaction: t,
    });

    /* START: check cau hinh */
    // lọc theo isDownloadInputInvoiceNotTable
    const isDownloadInputInvoiceNotTable =
      !!organization?.downloadInputInvoiceNotTable ||
      !!organizationDepartment?.downloadInputInvoiceNotTable;
    if (
      !isDownloadInputInvoiceNotTable &&
      datas?.length > 0 &&
      invoiceCategory == 'INPUT_INVOICE'
    ) {
      datas = [
        ...datas?.filter(
          item => !(!item.nbcks && item.khhdon?.startsWith('K')),
        ),
      ];
    }

    // lọc theo isDownloadOutputInvoiceNotTable
    const isDownloadOutputInvoiceNotTable =
      !!organization?.downloadOutputInvoiceNotTable ||
      !!organizationDepartment?.downloadOutputInvoiceNotTable;
    if (
      !isDownloadOutputInvoiceNotTable &&
      datas?.length > 0 &&
      invoiceCategory == 'OUTPUT_INVOICE'
    ) {
      datas = [
        ...datas?.filter(
          item => !(!item.nbcks && item.khhdon?.startsWith('K')),
        ),
      ];
    }

    // lọc theo cấu hình selectDownloadTypeOutputInvoice selectDownloadTypeOutputInvoice
    let selectDownloadTypeInvoice =
      invoiceCategory === 'OUTPUT_INVOICE'
        ? organization?.selectDownloadTypeOutputInvoice ||
          organizationDepartment?.selectDownloadTypeOutputInvoice
        : invoiceCategory === 'INPUT_INVOICE'
        ? organization?.selectDownloadTypeInputInvoice ||
          organizationDepartment?.selectDownloadTypeInputInvoice
        : null;

    if (selectDownloadTypeInvoice) {
      // lọc theo hoaDonDienTuGiaTriGiaTang
      if (!selectDownloadTypeInvoice?.hoaDonDienTuGiaTriGiaTang?.hoaDonDienTu) {
        datas = [
          ...datas?.filter(
            item => !(item?.hdon == '01' && item?.khhdon?.serial[3] != 'M'),
          ),
        ];
      }
      if (
        !selectDownloadTypeInvoice?.hoaDonDienTuGiaTriGiaTang
          ?.khoiTaoTuMayTinhTien
      ) {
        datas = [
          ...datas?.filter(
            item => !(item?.hdon == '01' && item?.khhdon?.serial[3] == 'M'),
          ),
        ];
      }
      // lọc theo hoaDonDienTuBanHang
      if (!selectDownloadTypeInvoice?.hoaDonDienTuBanHang?.hoaDonDienTu) {
        datas = [
          ...datas?.filter(
            item => !(item?.hdon == '02' && item?.khhdon?.serial[3] == 'T'),
          ),
        ];
      }
      if (
        !selectDownloadTypeInvoice?.hoaDonDienTuBanHang?.khoiTaoTuMayTinhTien
      ) {
        datas = [
          ...datas?.filter(
            item => !(item?.hdon == '02' && item?.khhdon?.serial[3] == 'M'),
          ),
        ];
      }
      // lọc theo hoaDonDienTuBanTaiSanCong
      if (!selectDownloadTypeInvoice?.hoaDonDienTuBanTaiSanCong) {
        datas = [...datas?.filter(item => !(item?.hdon == '03'))];
      }
      // lọc theo hoaDonDienTuBanHangDuTruQuocGia
      if (!selectDownloadTypeInvoice?.hoaDonDienTuBanHangDuTruQuocGia) {
        datas = [...datas?.filter(item => !(item?.hdon == '04'))];
      }
      // loc theo temVeTheDienTu
      if (!selectDownloadTypeInvoice?.temVeTheDienTu?.hoaDonDienTu) {
        datas = [
          ...datas?.filter(
            item => !(item?.hdon == '05' && item?.khhdon?.serial[3] == 'T'),
          ),
        ];
      }
      if (!selectDownloadTypeInvoice?.temVeTheDienTu?.khoiTaoTuMayTinhTien) {
        datas = [
          ...datas?.filter(
            item => !(item?.hdon == '05' && item?.khhdon?.serial[3] == 'M'),
          ),
        ];
      }
      // loc theo phieuXuatKhoDienTu
      if (!selectDownloadTypeInvoice?.phieuXuatKhoDienTu?.hangGuiBanDaiLy) {
        datas = [...datas?.filter(item => !(item?.hdon == '06_02'))];
      }
      if (!selectDownloadTypeInvoice?.phieuXuatKhoDienTu?.khiemVanChuyenNoiBo) {
        datas = [...datas?.filter(item => !(item?.hdon == '06_01'))];
      }
    }

    // lọc theo adjustNotDownloadWithSupplier
    const isAdjustNotDownloadWithSupplier =
      !!organization?.adjustNotDownloadWithSupplier ||
      !!organizationDepartment?.adjustNotDownloadWithSupplier;
    if (isAdjustNotDownloadWithSupplier) {
      const supplierNotDownloadInvoices =
        await SupplierNotDownloadInvoice.findAll({
          where: { organizationId, organizationDepartmentId },
          include: { model: PartnerCompany },
        });
      const supplierTaxCodes = supplierNotDownloadInvoices?.map(
        item => item?.PartnerCompany?.taxCode,
      );
      if (supplierTaxCodes && supplierTaxCodes?.length > 0) {
        datas = [
          ...datas?.filter(item => !supplierTaxCodes.includes(item?.nbmst)),
        ];
      }
    }

    /* END: check cau hinh */
    const arrGroupDateInvoices = _.groupBy(datas, function (item) {
      return moment(item.tdlap).format('YYYY-MM-DD');
    });
    const dateInvoices = Object.keys(arrGroupDateInvoices);
    //update về số lượng về 0 và xoá trong bảng cqtIvoice nếu ngày hoá đơn k thấy khi đồng bộ
    // let cqtInvoiceHistories = await CqtInvoiceHistory.findAll({
    //   where: {
    //     [Op.and]: [
    //       { syncDate: { [Op.notIn]: dateInvoices } },
    //       {
    //         syncDate: {
    //           [Op.between]: [
    //             moment(dateInvoices[dateInvoices.length - 1] ?? from)
    //               .startOf('date')
    //               .format('YYYY-MM-DD'),
    //             moment(dateInvoices[0] ?? to)
    //               .startOf('date')
    //               .format('YYYY-MM-DD'),
    //           ],
    //         },
    //       },
    //     ],
    //     type: invoiceCategory,
    //     organizationId,
    //     organizationDepartmentId,
    //   },
    // });

    // if (cqtInvoiceHistories) {
    //   await Invoice.destroy({
    //     where: {
    //       cqtInvoiceHistoryId: {
    //         [Op.in]: cqtInvoiceHistories.map(item => item.cqtInvoiceHistoryId),
    //       },
    //     },
    //   });
    //   await CqtInvoiceHistory.update(
    //     {
    //       succeededDownload: 0,
    //       failedDownload: 0,
    //       processed: 0,
    //       processing: 0,
    //       duplicated: 0,
    //       skipped: 0,
    //     },
    //     {
    //       where: {
    //         cqtInvoiceHistoryId: {
    //           [Op.in]: cqtInvoiceHistories.map(item => item.cqtInvoiceHistoryId),
    //         },
    //         type: invoiceCategory,
    //       },
    //     },
    //   );
    // }
    for (let index = 0; index < dateInvoices.length; index++) {
      const dateInvoice = dateInvoices[index];
      const arrInvoiceDownload = arrGroupDateInvoices[dateInvoice];
      // const noOfInvoiceDownload = arrInvoiceDownload.length;
      let [cqtInvoiceHistory, createdNew] =
        await CqtInvoiceHistory.findOrCreate({
          where: {
            syncDate: dateInvoice,
            type: invoiceCategory,
            organizationId,
            organizationDepartmentId,
          },
          defaults: {
            syncDate: dateInvoice,
            type: invoiceCategory,
            organizationId,
            organizationDepartmentId,
            processing: arrInvoiceDownload.length,
            succeededDownload: arrInvoiceDownload.length,
          },
          transaction: t,
        });

      if (!createdNew) {
        let newDownloadCount =
          arrInvoiceDownload.length -
          (await CqtInvoice.count({
            where: {
              cqtInvoiceHistoryId: cqtInvoiceHistory.cqtInvoiceHistoryId,
              [Op.or]: arrInvoiceDownload.map(item => ({
                invoiceTypeCode: item.khmshdon,
                sellerTaxCode: item.nbmst,
                invoiceNumber: item.shdon,
                serial: item.khhdon,
              })),
            },
          }));

        if (newDownloadCount) {
          await cqtInvoiceHistory.update(
            {
              processing: cqtInvoiceHistory.processing + newDownloadCount,
              succeededDownload:
                cqtInvoiceHistory.succeededDownload + newDownloadCount,
            },
            { transaction: t },
          );
        }
      }

      let arr = await CqtInvoice.bulkCreate(
        arrInvoiceDownload.map(item => {
          let cqtInvoice = cqt_toInvoice(item);

          cqtInvoice.invoiceCheckStatus = INVOICE_CHECK_STATUS[1];
          cqtInvoice.invoiceCheckStatusText = INVOICE_CHECK_STATUS_TEXT[1];

          return {
            ...cqtInvoice,
            cqtInvoiceHistoryId: cqtInvoiceHistory.cqtInvoiceHistoryId,
          };
        }),
        { transaction: t, ignoreDuplicates: true },
      );

      // if (cqtInvoiceHistory) {
      //   await cqtInvoiceHistory.update({
      //     succeededDownload: noOfInvoiceDownload,
      //     failedDownload: 0,
      //     processed: 0,
      //     processing: 0,
      //     duplicated: 0,
      //     skipped: 0,
      //   });
      // } else {
      //   cqtInvoiceHistory = await CqtInvoiceHistory.create({
      //     syncDate: dateInvoice,
      //     type: invoiceCategory,
      //     organizationId,
      //     organizationDepartmentId,
      //     succeededDownload: noOfInvoiceDownload,
      //     failedDownload: 0,
      //     processed: 0,
      //     processing: 0,
      //     duplicated: 0,
      //     skipped: 0,
      //   });
      // }
      let cqtInvoiceHistoryId = cqtInvoiceHistory.cqtInvoiceHistoryId;
      // let noOfInvoiceDownload = 0;
      // let processed = 0;
      // let failedDownload = 0;
      // let processing = 0;
      // let duplicated = 0;
      // let skipped = 0;

      let companyId;

      if (organizationId) {
        companyId = organization.companyId;
      } else {
        companyId = organizationDepartment.OrganizationBranch.companyId;
      }

      let arrInvoice = [];

      for (let i = 0; i < arrInvoiceDownload.length; i++) {
        try {
          /**
           * @type {import('../types').CQTInvoice|import('../types').CQTInvoiceMTT}
           */
          const element = arrInvoiceDownload[i];
          //check taxcode
          if (taxCode != element.nmmst && taxCode != element.nbmst) continue;
          // noOfInvoiceDownload += 1;
          // thêm vào bảng invoice

          let amountBeforeVat = Number(
            element.tgtcthue ?? element.tgtttbso ?? 0,
          );
          let amountVat = Number(element.tgtthue ?? null);
          let discountWithoutVAT =
            Number(
              element.ttttkhac.find(
                item => item.ttruong == 'TotalDiscountAmount',
              )?.dlieu ?? 0,
            ) ?? 0;
          let discountOther =
            Number(
              element.ttttkhac.find(
                item => item.ttruong == 'TotalDiscountAmountOC',
              )?.dlieu ?? 0,
            ) ?? 0;
          let totalDiscountAmount = discountWithoutVAT + discountOther;
          let finalAmount = Number(element.tgtttbso ?? 0);
          let amountAfterVat10;
          let amountVat10;
          let amountBeforeVat10;

          let amountAfterVat8;
          let amountVat8;
          let amountBeforeVat8;

          let amountAfterVat5;
          let amountVat5;
          let amountBeforeVat5;

          let amountAfterVat0;
          let amountVat0;
          let amountBeforeVat0;

          let amountAfterVatKCT;
          let amountVatKCT;
          let amountBeforeVatKCT;

          let amountAfterVatKHAC;
          let amountVatKHAC;
          let amountBeforeVatKHAC;

          let amountAfterVatKKKNT;
          let amountVatKKKNT;
          let amountBeforeVatKKKNT;

          element.thttltsuat?.forEach(item => {
            if (item.tsuat === VAT_TYPE['5%']) {
              amountVat5 = Number(item.tthue);
              amountBeforeVat5 = Number(item.thtien);

              if (
                utils.isValidNumber(amountVat5) &&
                utils.isValidNumber(amountBeforeVat5)
              ) {
                amountAfterVat5 = amountBeforeVat5 + amountVat5;
              }
            }
            if (item.tsuat === VAT_TYPE['8%']) {
              amountVat8 = Number(item.tthue);
              amountBeforeVat8 = Number(item.thtien);

              if (
                utils.isValidNumber(amountVat8) &&
                utils.isValidNumber(amountBeforeVat8)
              ) {
                amountAfterVat8 = amountBeforeVat8 + amountVat8;
              }
            }
            if (item.tsuat === VAT_TYPE['10%']) {
              amountVat10 = Number(item.tthue);
              amountBeforeVat10 = Number(item.thtien);

              if (
                utils.isValidNumber(amountVat10) &&
                utils.isValidNumber(amountBeforeVat10)
              ) {
                amountAfterVat10 = amountBeforeVat10 + amountVat10;
              }
            }
            if (item.tsuat === VAT_TYPE['0%']) {
              amountVat0 = Number(item.tthue);
              amountBeforeVat0 = Number(item.thtien);

              if (
                utils.isValidNumber(amountVat0) &&
                utils.isValidNumber(amountBeforeVat0)
              ) {
                amountAfterVat0 = amountBeforeVat0 + amountVat0;
              }
            }

            if (item.tsuat === VAT_TYPE['KCT']) {
              amountVatKCT = Number(item.tthue);
              amountBeforeVatKCT = Number(item.thtien);

              if (
                utils.isValidNumber(amountVatKCT) &&
                utils.isValidNumber(amountBeforeVatKCT)
              ) {
                amountVatKCT = amountBeforeVatKCT + amountVatKCT;
              }
            }
            if (item.tsuat?.startsWith(VAT_TYPE['KHAC'])) {
              amountVatKHAC = Number(item.tthue);
              amountBeforeVatKHAC = Number(item.thtien);

              if (
                utils.isValidNumber(amountVatKHAC) &&
                utils.isValidNumber(amountBeforeVatKHAC)
              ) {
                amountVatKHAC = amountBeforeVatKHAC + amountVatKHAC;
              }
            }
            if (item.tsuat === VAT_TYPE['KKKNT']) {
              amountVatKKKNT = Number(item.tthue);
              amountBeforeVatKKKNT = Number(item.thtien);

              if (
                utils.isValidNumber(amountVatKKKNT) &&
                utils.isValidNumber(amountBeforeVatKKKNT)
              ) {
                amountAfterVatKKKNT = amountBeforeVatKKKNT + amountVatKKKNT;
              }
            }
          });
          let reminderPaymentDate = moment(element.tdlap)
            .add(1, 'M')
            .subtract(6, 'days');
          let expiredPaymentDate = moment(element.tdlap)
            .add(1, 'M')
            .subtract(2, 'days');
          let statusPayment = 1;
          if (expiredPaymentDate > new Date()) statusPayment = 5;
          if (
            expiredPaymentDate < new Date() &&
            reminderPaymentDate < new Date()
          )
            statusPayment = 4;

          /**
           * @type {import('../types').Invoice}
           */
          let data = {
            invoiceVersion: element.pban,
            invoiceCQTId: element.id,
            invoiceType: element.hdon,
            serial: element.khhdon,
            invoiceTypeCode:
              Object.values(INVOICE_TYPE_CODE).filter(
                item => item == element.khmshdon,
              ).length > 0
                ? Object.values(INVOICE_TYPE_CODE).filter(
                    item => item == element.khmshdon,
                  )[0]
                : null,
            cqtInvoiceHistoryId,
            invoiceNumber: element.shdon,
            maCQT: element.mhdon,
            invoiceDate: element.tdlap,
            sellerName: element.nbten,
            sellerTaxCode: element.nbmst,
            sellerBankAccount: element.nbstkhoan,
            sellerBankName: element.nbtnhang,
            sellerAddress: element.nbdchi,
            sellerPhone: element.nbsdthoai,
            sellerMail: element.nbdctdtu,
            buyerName: element.nmten,
            buyerTaxCode: element.nmmst,
            buyerAddress: element.nmdchi,
            buyerBankAccount: element.nbstkhoan,
            buyerBankName: element.nbtnhang,
            buyerPhone: element.nmsdthoai,
            buyerMail: element.nmdctdtu,
            amountAfterVat: amountBeforeVat + amountVat,
            amountBeforeVat,
            finalAmount,
            finalAmountInWord: element.tgtttbchu,
            // discount: null,
            // amountBeforeDiscount: element.tgtttbso,
            amountAfterVat10,
            amountVat10,
            amountBeforeVat10,

            amountAfterVat8,
            amountVat8,
            amountBeforeVat8,

            amountAfterVat5,
            amountVat5,
            amountBeforeVat5,

            amountAfterVat0,
            amountVat0,
            amountBeforeVat0,

            amountAfterVatKCT,
            amountVatKCT,
            amountBeforeVatKCT,

            amountAfterVatKHAC,
            amountVatKHAC,
            amountBeforeVatKHAC,

            amountAfterVatKKKNT,
            amountVatKKKNT,
            amountBeforeVatKKKNT,
            // amountVat0: null,
            // amountVatOther: null,
            // amountVatKKKNT: null,
            amountVat, //tgtthue
            totalDiscountAmount, //ttcktmai
            discountWithoutVAT, //ttcktmai
            discountOther, //ttcktmai
            paymentCurrency: element.dvtte, //dvtte
            paymentExchangeRate: element.tgia ?? 1, //tgia
            paymentMethod: element.thtttoan, //thtttoan
            providedCodeDate: element.ncma, //ncma
            signDate: element.nky, //nky
            invoiceReceiveDate: element.ntnhan, //ntnhan
            sellerCode: null, //
            finalAmountExchange: finalAmount * Number(element.tgia ?? 1),
            statusInvoice: Number(element.tthai), //tthai
            statusInvoiceText: INVOICE_STATUS_TEXT[Number(element.tthai)],
            statusHandleInvoice: Number(element.ttxly), //ttxly
            buyerPersonName: element.nmtnmua, //nmtnmua
            noteInvoice: element.gchu, //gchu
            invoiceCategory,
            organizationId,
            organizationDepartmentId,
            statusPayment,
            reminderPaymentDate,
            expiredPaymentDate,
            debtPayment: finalAmount,
            amountPayment: 0,
            typeInvoiceReceive: TYPE_INVOICE_RECEIVE[3],
            cqtCKS: element.cqtcks,
            sellerCKS: element.nbcks,
            cqtSignDate: utils.isJSONStringified(element.cqtcks)
              ? JSON.parse(element.cqtcks)?.SigningTime
              : null,
            sellerSignDate: utils.isJSONStringified(element.nbcks)
              ? JSON.parse(element.nbcks)?.SigningTime
              : null,
            supplierReferenceCode: element.cttkhac
              .filter(item =>
                POSSIBLE_LOOKUP_CODE_XML_TAG.includes(item.ttruong),
              )
              .find(item => item.dlieu)?.dlieu,
            supplierTaxCode: element.msttcgp,
            // invoiceCheckStatus: 1,
            description: 'Đang xử lý',
            hdgocData: {
              khhdon: element.khhdgoc,
              khmshdon: element.khmshdgoc,
              lhdon: element.lhdgoc,
              shdon: element.shdgoc,
              tdlap: element.tdlhdgoc,
            },
            cqtMtt: !notMtt,
          };

          arrInvoice.push(data);
        } catch (error) {
          console.log(error);
        }
      }

      /* ktra tai nguyen */
      let canSave = false;
      /**
       * SL cos theer trừ
       */
      let maxEntries = await companyHelper.getMaxInvoiceEntries(companyId, t);

      let remainResource = await companyHelper.getRemainResource(companyId, t);

      if (maxEntries.length) {
        /*  */
        /* 31/10: không tính thời hạn riêng từng lần cấp. Cộng dồn SL, thời hạn */
        let resourceHistories = await ResourceHistory.findAll({
          where: {
            companyId,
            left: { [Op.gt]: 0 },
            packageType: PACKAGE_TYPE.QUANTITY,
          },
          transaction: t,
        });

        canSave = true;

        /* SAVE INVOICE */
        if (canSave) {
          /* async fetch */

          // eslint-disable-next-line @typescript-eslint/no-shadow
          await (async function (_arrInvoice, _t) {
            let invoices = await invoiceHelper.saveToInvoice(
              _arrInvoice.map(
                /**
                 *
                 * @param {import('../types').CQTInvoice} i
                 * @returns
                 */
                i => {
                  return {
                    ...i,
                    typeInvoiceReceive: TYPE_INVOICE_RECEIVE[3],
                  };
                },
              ),
              {
                organizationDepartmentId,
                organizationId,
                taxCode,
                maxEntries: _.sum(maxEntries),
              },
              _t,
            );
            /* reduce left in resource history */
            if (invoices.length) {
              let total = invoices.length;
              /* first use promotion */
              if (remainResource.promotionRemain) {
                let used = Math.min(total, remainResource.promotionRemain);
                total -= used;
                await Company.update(
                  { promotionRemain: remainResource.promotionRemain - used },
                  { where: { companyId }, transaction: _t },
                );
              }
              /* then use resources */
              let useQuantityRemain = total;
              for (let i = 0; i < resourceHistories.length && total > 0; i++) {
                let used = Math.min(total, resourceHistories[i].left);
                total -= used;
                await resourceHistories[i].update(
                  { left: resourceHistories[i].left - used },
                  { transaction: _t },
                );
              }
              await Company.update(
                {
                  quantityRemain:
                    remainResource.quantityRemain - useQuantityRemain,
                },
                { where: { companyId }, transaction: _t },
              );
            }
            // /* update history */
            // if (invoices.length) {
            //   let noOfInvoiceDownload = invoices.length;
            //   let failedDownload = 0;
            //   let processed = invoices.filter(
            //     invoice => invoice.invoiceCheckStatus == INVOICE_CHECK_STATUS[2],
            //   ).length;
            //   let processing = 0;
            //   let duplicated = invoices.filter(
            //     invoice => invoice.invoiceCheckStatus == INVOICE_CHECK_STATUS[3],
            //   ).length;
            //   let skipped = invoices.filter(
            //     invoice => invoice.invoiceCheckStatus == INVOICE_CHECK_STATUS[6],
            //   ).length;
            //   if (resetCountHistory) {
            //     await cqtInvoiceHistory.update(
            //       {
            //         succeededDownload: noOfInvoiceDownload,
            //         failedDownload,
            //         processed,
            //         processing,
            //         duplicated,
            //         skipped,
            //       },
            //       { transaction: _t },
            //     );
            //   } else {
            //     await cqtInvoiceHistory.update(
            //       {
            //         succeededDownload:
            //           (cqtInvoiceHistory.succeededDownload ?? 0) +
            //           noOfInvoiceDownload,
            //         failedDownload:
            //           cqtInvoiceHistory.failedDownload + failedDownload,
            //         processed: cqtInvoiceHistory.processed + processed,
            //         processing: cqtInvoiceHistory.processing + processing,
            //         duplicated: cqtInvoiceHistory.duplicated + duplicated,
            //         skipped: cqtInvoiceHistory.skipped + skipped,
            //       },
            //       { transaction: _t },
            //     );
            //   }
            // }
            // await _t?.commit();
          })(arrInvoice, t).catch(console.log);
        }
      }
    }

    // if (arrInvoices.length > 0)
    //   await Invoice.bulkCreate(arrInvoices, {
    //     updateOnDuplicate: invoiceHelper.getUpdateOnDuplicateInvoiceAttrs(),
    //   });
    // if (arrCQTInvoices.length > 0) await CqtInvoice.bulkCreate(arrCQTInvoices);

    await t?.commit?.();

    console.log('sync CQT done');
  } catch (error) {
    await t?.rollback?.();
  }
}

/**
 *
 * @param {import('../types').Invoice} invoice
 *
 */
async function cqt_lookup(invoice) {
  let { content, key: ckey } = await captcha();

  let cvalue = readCaptchaSvg(content);

  let params = {
    cvalue,
    ckey,
    khmshdon: invoice.invoiceTypeCode,
    hdon: invoice.invoiceType,
    nbmst: invoice.sellerTaxCode,
    khhdon: invoice.serial,
    shdon: invoice.invoiceNumber,
    tgtttbso: invoice.finalAmount,
    tdlap: moment(invoice.invoiceDate).toDate().toISOString(),
  };

  let response = await axiosClient
    .get(PATH_CQT_LOOKUP, { params })
    .catch(err => {
      // console.log({ cvalue, content });

      // writeFileSync('fail_read.svg', content, 'utf-8');

      return { data: undefined };
    });

  return response.data;
}

module.exports.cqt_lookup = cqt_lookup;

/**
 *
 * @param {string} mst
 * @returns
 */
async function cqt_regdStatus(mst) {
  let { content, key: ckey } = await captcha();

  let cvalue = readCaptchaSvg(content);

  let params = {
    cvalue,
    ckey,
    mst,
  };

  let response = await axiosClient
    .get(PATH_CQT_REGD_STATUS, { params })
    .catch(err => {
      // console.log({ cvalue, content });

      return { data: undefined };
    });

  return response.data;
}

module.exports.cqt_regdStatus = cqt_regdStatus;

/**
 *
 * @param {import('../types').Invoice} invoice
 * @param {Transaction} t
 * @returns
 */
async function cqt_validate_invoice(invoice, t) {
  const invoiceHelper = require('./invoice.helper');

  const invoiceId = invoice.invoiceId;
  try {
    let _invoice = await Invoice.findOne({
      where: { invoiceId: invoice.invoiceId },
      transaction: t,
    });
    let [invoiceValidate] = await InvoiceValidate.findOrCreate({
      where: { invoiceId },
      defaults: { invoiceId },
      transaction: t,
    });

    let acceptPartnerCompanyDetails = await AcceptPartnerCompanyDetail.findAll({
      where: {
        organizationId: invoice.organizationId,
        organizationDepartmentId: invoice.organizationDepartmentId,
        taxCode: { [Op.in]: [invoice.sellerTaxCode, invoice.buyerTaxCode] },
        from: { [Op.lt]: new Date() },
        to: { [Op.gt]: new Date() },
      },
      transaction: t,
      order: [
        ['to', 'DESC'],
        ['from', 'ASC'],
      ],
    });
    /**
     * @type {import('../types').InvoiceValidate}
     */
    let update = {
      checkResultBuyerAddress: null,
      checkResultBuyerName: null,
      checkResultBuyerTaxCode: null,
      checkResultHasCQTRecord: null,
      checkResultHasInvoiceCode: null,
      checkResultSellerTaxCode: null,
      checkResultSellerAddress: null,
      checkResultSellerName: null,
      checkResultSignatureCQT: null,
      checkResultSignatureNCC: null,
      cqt_cts_con_hieu_luc: null,
      cqt_cts_hop_le: null,
      cqt_file_xml_chua_bi_sua: null,
      cqt_thong_tin_cts_hop_le: null,
      nb_cts_con_hieu_luc: null,
      nb_cts_hop_le: null,
      nb_file_xml_chua_bi_sua: null,
      nb_thong_tin_cts_hop_le: null,
      mauso_kyhieu_da_tb: null,
      nb_dang_hoat_dong: null,
      nb_khong_rui_ro_tai_tdlap: null,
      ngay_hoa_don_tu_ngay_su_dung: null,
      so_hd_thuoc_khoang_phat_hanh: null,
    };

    /* KTRA NNT */
    try {
      let buyerTaxCode = invoice.buyerTaxCode;
      let sellerTaxCode = invoice.sellerTaxCode;

      let _acceptPartnerCompanyDetails = acceptPartnerCompanyDetails.map(item =>
        item.toJSON(),
      );

      const buyerCQT = await nntHelper
        .getCompany(buyerTaxCode)
        .catch(() => ({}));

      const sellerCQT = await nntHelper
        .getCompany(sellerTaxCode)
        .catch(() => ({}));

      if (buyerCQT) {
        update.resultBuyerName = buyerCQT.companyName;

        update.nb_dang_hoat_dong =
          buyerCQT && utils.matchSearch(buyerCQT.status, 'Đang hoạt động');

        if (invoice.buyerName && update.resultBuyerName) {
          update.checkResultBuyerName = invoiceHelper.compareInvoiceValue(
            invoice.buyerName,
            update.resultBuyerName,
          );

          if (!update.checkResultBuyerName) {
            let found;
            if (
              (found = _acceptPartnerCompanyDetails.find(
                item =>
                  item.taxCode == invoice.buyerTaxCode &&
                  invoiceHelper.compareInvoiceValue(
                    item.acceptName,
                    invoice.buyerName,
                  ),
              ))
            ) {
              update.resultBuyerName = found.acceptName;

              update.checkResultBuyerName = true;
            }
          }
        }

        update.resultBuyerTaxCode = buyerCQT.taxCode;
        update.checkResultBuyerTaxCode =
          utils.isValidTaxCode(invoice.buyerTaxCode) &&
          !!(
            utils.escapeStr(invoice.buyerTaxCode) ===
            utils.escapeStr(buyerCQT.taxCode)
          );
        update.resultBuyerAddress = buyerCQT.address;

        if (invoice.buyerAddress && update.resultBuyerAddress) {
          update.checkResultBuyerAddress = !!invoiceHelper.compareInvoiceValue(
            invoice.buyerAddress,
            update.resultBuyerAddress,
          );

          if (!update.checkResultBuyerAddress) {
            let found;
            if (
              (found = _acceptPartnerCompanyDetails.find(
                item =>
                  item.taxCode == invoice.buyerTaxCode &&
                  invoiceHelper.compareInvoiceValue(
                    item.acceptAddress,
                    invoice.buyerAddress,
                  ),
              ))
            ) {
              update.resultBuyerAddress = found.acceptAddress;

              update.checkResultBuyerAddress = true;
            }
          }
        }
      }

      if (sellerCQT) {
        update.resultSellerName = sellerCQT.companyName;

        if (invoice.sellerName && update.resultSellerName) {
          update.checkResultSellerName = !!invoiceHelper.compareInvoiceValue(
            invoice.sellerName,
            sellerCQT.companyName,
          );

          if (!update.checkResultSellerName) {
            let found;
            if (
              (found = _acceptPartnerCompanyDetails.find(
                item =>
                  item.taxCode == invoice.sellerTaxCode &&
                  invoiceHelper.compareInvoiceValue(
                    item.acceptName,
                    invoice.sellerName,
                  ),
              ))
            ) {
              update.resultSellerName = found.acceptName;

              update.checkResultSellerName = true;
            }
          }
        }

        update.resultSellerTaxCode = sellerCQT.taxCode;
        update.checkResultSellerTaxCode =
          utils.isValidTaxCode(invoice.sellerTaxCode) &&
          !!(
            utils.escapeStr(invoice.sellerTaxCode) ===
            utils.escapeStr(sellerCQT.taxCode)
          );

        update.resultSellerAddress = sellerCQT.address;

        if (invoice.sellerAddress && update.resultSellerAddress) {
          update.checkResultSellerAddress = !!invoiceHelper.compareInvoiceValue(
            invoice.sellerAddress,
            update.resultSellerAddress,
          );

          if (!update.checkResultSellerAddress) {
            let found;
            if (
              (found = _acceptPartnerCompanyDetails.find(
                item =>
                  item.taxCode == invoice.sellerTaxCode &&
                  invoiceHelper.compareInvoiceValue(
                    item.acceptAddress,
                    invoice.sellerAddress,
                  ),
              ))
            ) {
              update.resultSellerAddress = found.acceptAddress;

              update.checkResultSellerAddress = true;
            }
          }
        }
      }
    } catch (error) {}

    /* KTRA VOI TCT */
    try {
      let data = await cqt_lookup(invoice);
      update.checkResultHasCQTRecord = !!invoice.invoiceCQTId || !!data;

      if (invoice.serial?.startsWith('C')) {
        update.checkResultHasInvoiceCode = !!invoice.maCQT;
      } else {
        update.checkResultHasInvoiceCode = true;
      }
      // await invoiceValidate.update({
      //   checkResultHasCQTRecord: !!data,
      //   checkResultHasInvoiceCode: !!invoice.maCQT,
      // });

      if (data?.tthai) {
        // update.statusInvoice = data.tthai;
        await _invoice.update(
          {
            statusInvoice: data.tthai,
            statusInvoiceTex: INVOICE_STATUS_TEXT[data.tthai],
          },
          { transaction: t },
        );
        // await Invoice.update(
        //   { statusInvoice: data.tthai },
        //   { where: { invoiceId } },
        // );
      }

      if (data?.ttxly) {
        await _invoice.update(
          { statusHandleInvoice: data.ttxly },
          { transaction: t },
        );
        // await Invoice.update(
        //   { statusHandleInvoice: data.ttxly },
        //   { where: { invoiceId } },
        // );
      }
    } catch (error) {
      console.log(error);
    }

    /* KTRA CKS */
    try {
      if (invoice.xmlFile) {
        let results = await invoiceHelper.validateXML(
          readFileSync(invoice.xmlFile.replace('uploads', UPLOAD_DIR), 'utf-8'),
        );
        update.checkResultSignatureNCC = !!results[0]?.verified;
        update.checkResultSignatureCQT = !!results[1]?.verified;

        update.cqt_file_xml_chua_bi_sua = !!update.checkResultSignatureCQT;
        update.nb_file_xml_chua_bi_sua = !!update.checkResultSignatureNCC;
      } else if (invoice.originXmlFile) {
        let results = await invoiceHelper.validateXML(
          readFileSync(
            invoice.originXmlFile.replace('uploads', UPLOAD_DIR),
            'utf-8',
          ),
        );
        update.checkResultSignatureNCC = !!results[0]?.verified;
        update.checkResultSignatureCQT = !!results[1]?.verified;

        update.cqt_file_xml_chua_bi_sua = !!update.checkResultSignatureCQT;
        update.nb_file_xml_chua_bi_sua = !!update.checkResultSignatureNCC;
      }

      if (invoice.cqtCKS) {
        update.cqt_cts_hop_le = true;
        update.cqt_cts_con_hieu_luc = moment(invoice.cqtCKS.NotAfter).isAfter(
          invoice.cqtCKS.SigningTime,
        );
        update.cqt_thong_tin_cts_hop_le = true;
      } else {
        if (invoice.serial?.startsWith('K')) {
          update.checkResultSignatureCQT = true;
        }
      }

      if (invoice.sellerCKS) {
        update.nb_cts_hop_le = invoice.sellerCKS.Subject?.includes(
          `MST:${invoice.sellerTaxCode}`,
        );
        update.nb_cts_con_hieu_luc = moment(invoice.sellerCKS.NotAfter).isAfter(
          invoice.sellerCKS.SigningTime,
        );
        update.nb_thong_tin_cts_hop_le = update.nb_cts_hop_le;
      }
    } catch (error) {
      /*  */
      console.log(error);
    }

    update.checkDate = new Date();
    await invoiceValidate.update(update, { transaction: t });

    return update;
  } catch (error) {
    return false;
  }
  // return response.data;
}

module.exports.cqt_validate_invoice = cqt_validate_invoice;
/**
 *
 * @param {Model<import('../types').Invoice> & import('../types').Invoice} invoice
 * @returns
 */
async function check_invoice(invoice, t) {
  try {
    let buyerTaxCode = invoice.buyerTaxCode;
    let sellerTaxCode = invoice.sellerTaxCode;
    let invoiceType = invoice.invoiceType;
    let serial = invoice.serial;
    let invoiceNumber = invoice.invoiceNumber;
    let maCQT = invoice.maCQT;

    let invoiceCheck = await Invoice.findOne({
      where: {
        // buyerTaxCode,
        sellerTaxCode,
        invoiceType,
        serial,
        invoiceNumber,
        organizationId: invoice.organizationId,
        organizationDepartmentId: invoice.organizationDepartmentId,
      },
      order: [['invoiceId', 'ASC']],
      transaction: t,
    });

    let cqtInvoice = await CqtInvoice.findOne({
      where: {
        cqtInvoiceHistoryId: invoice.cqtInvoiceHistoryId,
        serial: invoice.serial,
        invoiceTypeCode: invoice.invoiceTypeCode,
        invoiceNumber: invoice.invoiceNumber,
      },
      transaction: t,
    });

    // invoiceCheck && console.log(invoiceCheck.invoiceId);
    if (invoiceCheck && invoice.invoiceId === invoiceCheck.invoiceId) {
      await cqtInvoice?.update(
        {
          // description: 'Tải thành công',
          invoiceCheckStatus: INVOICE_CHECK_STATUS[2],
          invoiceCheckStatusText: INVOICE_CHECK_STATUS_TEXT[2],
        },
        { transaction: t },
      );

      await invoice.update(
        { description: 'Tải thành công' },
        { transaction: t },
      );
    }
    let invoiceCheckStatus;
    // bỏ qua
    if (serial.charAt(0) == 'C' && !maCQT) {
      invoiceCheckStatus = INVOICE_CHECK_STATUS[4];
      await invoice.update(
        {
          description: 'Không tồn tại hồ sơ gốc của hoá đơn',
        },
        { transaction: t },
      );

      await cqtInvoice?.update(
        {
          invoiceCheckStatus,
          invoiceCheckStatusText: INVOICE_CHECK_STATUS_TEXT[invoiceCheckStatus],
        },
        { transaction: t },
      );
    }
    // check trùng
    else if (invoiceCheck && invoice.invoiceId !== invoiceCheck.invoiceId) {
      invoiceCheckStatus = INVOICE_CHECK_STATUS[3];
      await invoice.update(
        {
          description: 'Hoá đơn đã tồn tại trong hệ thống',
        },
        { transaction: t },
      );

      await cqtInvoice?.update(
        {
          invoiceCheckStatus,
          invoiceCheckStatusText: INVOICE_CHECK_STATUS_TEXT[invoiceCheckStatus],
          duplicateInvoiceId: invoiceCheck.invoiceId,
        },
        { transaction: t },
      );
    } else {
      invoiceCheckStatus = INVOICE_CHECK_STATUS[2];
      await invoice.update(
        {
          description: 'Tải thành công',
        },
        { transaction: t },
      );

      await cqtInvoice?.update(
        {
          invoiceCheckStatus,
          invoiceCheckStatusText: INVOICE_CHECK_STATUS_TEXT[invoiceCheckStatus],
        },
        { transaction: t },
      );
    }

    return invoiceCheckStatus;
  } catch (error) {
    console.log(error, { invoice });
    return;
  }
  // return response.data;
}

module.exports.check_invoice = check_invoice;

/**
 *
 * @param {import('../types').Invoice} invoice
 * @param {import('axios').AxiosRequestHeaders} headers
 */
async function cqt_export_xml(invoice, headers) {
  let params = {
    nbmst: invoice.sellerTaxCode,
    khhdon: invoice.serial,
    shdon: invoice.invoiceNumber,
    khmshdon: invoice.invoiceTypeCode,
  };

  let response = await axiosClient
    .get(
      invoice.cqtMtt ? PATH_CQT_EXPORT_INVOICE_MTT : PATH_CQT_EXPORT_INVOICE,
      {
        params,
        headers,
        responseType: 'arraybuffer',
      },
    )
    .catch(err => {
      return { data: undefined };
    });

  return response.data;
}

module.exports.cqt_export_xml = cqt_export_xml;

/**
 *
 * @param {import('../types').Invoice} invoice
 * @param {import('axios').AxiosRequestHeaders} headers
 */
async function cqt_detail_invoice(invoice, headers) {
  let params = {
    nbmst: invoice.sellerTaxCode,
    khhdon: invoice.serial,
    shdon: invoice.invoiceNumber,
    khmshdon: invoice.invoiceTypeCode,
  };

  let response = await axiosClient
    .get(
      invoice.cqtMtt ? PATH_CQT_DETAIL_INVOICE_MTT : PATH_CQT_DETAIL_INVOICE,
      {
        params,
        headers,
      },
    )
    .catch(err => {
      return { data: undefined };
    });

  return response.data;
}

module.exports.cqt_detail_invoice = cqt_detail_invoice;

/**
 *
 * @param {import('../types').CQTInvoice} data
 */
function cqt_toInvoice(data) {
  const invoiceHelper = require('./invoice.helper');

  let amountBeforeVat = Number(data.tgtcthue ?? data.tgtttbso ?? 0);
  let amountVat = Number(data.tgtthue ?? 0);
  let discountWithoutVAT =
    Number(
      data.ttttkhac.find(item => item.ttruong == 'TotalDiscountAmount')
        ?.dlieu ?? 0,
    ) ?? 0;
  let discountOther =
    Number(
      data.ttttkhac.find(item => item.ttruong == 'TotalDiscountAmountOC')
        ?.dlieu ?? 0,
    ) ?? 0;
  let totalDiscountAmount = discountWithoutVAT + discountOther;
  let finalAmount = Number(data.tgtttbso ?? 0);
  let amountAfterVat10;
  let amountVat10;
  let amountBeforeVat10;

  let amountAfterVat8;
  let amountVat8;
  let amountBeforeVat8;

  let amountAfterVat5;
  let amountVat5;
  let amountBeforeVat5;

  let amountAfterVat0;
  let amountVat0;
  let amountBeforeVat0;

  let amountAfterVatKCT;
  let amountVatKCT;
  let amountBeforeVatKCT;

  let amountAfterVatKHAC;
  let amountVatKHAC;
  let amountBeforeVatKHAC;

  let amountAfterVatKKKNT;
  let amountVatKKKNT;
  let amountBeforeVatKKKNT;

  data.thttltsuat?.forEach(item => {
    if (item.tsuat === VAT_TYPE['5%']) {
      amountVat5 = Number(item.tthue);
      amountBeforeVat5 = Number(item.thtien);

      if (
        utils.isValidNumber(amountVat5) &&
        utils.isValidNumber(amountBeforeVat5)
      ) {
        amountAfterVat5 = amountBeforeVat5 + amountVat5;
      }
    }
    if (item.tsuat === VAT_TYPE['8%']) {
      amountVat8 = Number(item.tthue);
      amountAfterVat8 = Number(item.thtien);

      if (
        utils.isValidNumber(amountVat8) &&
        utils.isValidNumber(amountBeforeVat8)
      ) {
        amountAfterVat8 = amountBeforeVat8 + amountVat8;
      }
    }
    if (item.tsuat === VAT_TYPE['10%']) {
      amountVat10 = Number(item.tthue);
      amountBeforeVat10 = Number(item.thtien);

      if (
        utils.isValidNumber(amountVat10) &&
        utils.isValidNumber(amountBeforeVat10)
      ) {
        amountAfterVat10 = amountBeforeVat10 + amountVat10;
      }
    }
    if (item.tsuat === VAT_TYPE['0%']) {
      amountVat0 = Number(item.tthue);
      amountBeforeVat0 = Number(item.thtien);

      if (
        utils.isValidNumber(amountVat0) &&
        utils.isValidNumber(amountBeforeVat0)
      ) {
        amountAfterVat0 = amountBeforeVat0 + amountVat0;
      }
    }

    if (item.tsuat === VAT_TYPE['KCT']) {
      amountVatKCT = Number(item.tthue);
      amountBeforeVatKCT = Number(item.thtien);

      if (
        utils.isValidNumber(amountVatKCT) &&
        utils.isValidNumber(amountBeforeVatKCT)
      ) {
        amountAfterVatKCT = amountBeforeVatKCT + amountVatKCT;
      }
    }
    if (item.tsuat?.startsWith(VAT_TYPE['KHAC'])) {
      amountVatKHAC = Number(item.tthue);
      amountBeforeVatKHAC = Number(item.thtien);

      if (
        utils.isValidNumber(amountVatKHAC) &&
        utils.isValidNumber(amountBeforeVatKHAC)
      ) {
        amountAfterVatKHAC = amountBeforeVatKHAC + amountVatKHAC;
      }
    }
    if (item.tsuat === VAT_TYPE['KKKNT']) {
      amountVatKKKNT = Number(item.tthue);
      amountBeforeVatKKKNT = Number(item.thtien);

      if (
        utils.isValidNumber(amountVatKKKNT) &&
        utils.isValidNumber(amountBeforeVatKKKNT)
      ) {
        amountAfterVatKKKNT = amountBeforeVatKKKNT + amountVatKKKNT;
      }
    }
  });
  /**
   * @type {import('../types').Invoice}
   */
  let invoice = {
    serial: data.khhdon,
    invoiceVersion: data.pban,
    invoiceCQTId: data.id,
    invoiceType: data.hdon,
    invoiceTypeCode: data.khmshdon,
    invoiceNumber: data.shdon,
    maCQT: data.mhdon,
    invoiceDate: data.tdlap,
    sellerName: data.nbten,
    sellerTaxCode: data.nbmst,
    sellerBankAccount: data.nbstkhoan,
    sellerBankName: data.nbtnhang,
    sellerAddress: data.nbdchi,
    sellerPhone: data.nbsdthoai,
    sellerMail: data.nbdctdtu,
    buyerName: data.nmten,
    buyerTaxCode: data.nmmst,
    buyerAddress: data.nmdchi,
    buyerBankAccount: data.nbstkhoan,
    buyerBankName: data.nbtnhang,
    buyerPhone: data.nmsdthoai,
    buyerMail: data.nmdctdtu,
    amountAfterVat: amountBeforeVat + amountVat,
    amountBeforeVat,
    finalAmount,
    finalAmountInWord: data.tgtttbchu,
    // discount: null,
    amountBeforeDiscount: amountBeforeVat + amountVat,
    amountAfterVat10,
    amountVat10,
    amountBeforeVat10,

    amountAfterVat8,
    amountVat8,
    amountBeforeVat8,

    amountAfterVat5,
    amountVat5,
    amountBeforeVat5,

    amountAfterVat0,
    amountVat0,
    amountBeforeVat0,

    amountAfterVatKCT,
    amountVatKCT,
    amountBeforeVatKCT,

    amountAfterVatKHAC,
    amountVatKHAC,
    amountBeforeVatKHAC,

    amountAfterVatKKKNT,
    amountVatKKKNT,
    amountBeforeVatKKKNT,

    amountVat, //tgtthue
    totalDiscountAmount, //ttcktmai
    discountWithoutVAT, //ttcktmai
    discountOther, //ttcktmai
    paymentCurrency: data.dvtte, //dvtte
    paymentExchangeRate: data.tgia ?? 1, //tgia
    paymentMethod: data.thtttoan, //thtttoan
    providedCodeDate: data.ncma, //ncma
    signDate: data.nky, //nky
    invoiceReceiveDate: data.ntnhan, //ntnhan
    sellerCode: null, //
    finalAmountExchange: finalAmount * Number(data.tgia ?? 1),
    statusInvoice: Number(data.tthai), //tthai
    statusInvoiceText: INVOICE_STATUS_TEXT[Number(data.tthai)],
    statusHandleInvoice: Number(data.ttxly), //ttxly
    buyerPersonName: data.nmtnmua, //nmtnmua
    noteInvoice: data.gchu, //gchu
    cqtCKS: data.cqtcks,
    sellerCKS: data.nbcks,
    cqtSignDate: utils.isJSONStringified(data.cqtcks)
      ? JSON.parse(data.cqtcks)?.SigningTime
      : null,
    sellerSignDate: utils.isJSONStringified(data.nbcks)
      ? JSON.parse(data.nbcks)?.SigningTime
      : null,
    supplierReferenceCode: data.cttkhac
      .filter(item => POSSIBLE_LOOKUP_CODE_XML_TAG.includes(item.ttruong))
      .find(item => item.dlieu)?.dlieu,
    supplierTaxCode: data.msttcgp,
    // invoiceCheckStatus: 1,

    hdgocData: {
      khhdon: data.khhdgoc,
      khmshdon: data.khmshdgoc,
      lhdon: data.lhdgoc,
      shdon: data.shdgoc,
      tdlap: data.tdlhdgoc,
    },

    InvoiceProducts: data.hdhhdvu?.map(hhdv => {
      /**
       * @type {import('../types').InvoiceProduct}
       */
      let item = {
        // finalAmount: hhdv.thtien,
        vatAmount: hhdv.tthue,
        discount: hhdv.tlckhau,
        price: hhdv.dgia,
        quantity: hhdv.sluong,
        unit: hhdv.dvtinh,
        vat: hhdv.ltsuat,
        name: hhdv.ten,
        productType: hhdv.tchat,
        vatRate: invoiceHelper.getVatFromVatType(hhdv.ltsuat),
        sortOrder: hhdv.stt,
      };

      if (
        utils.isValidNumber(item.quantity) &&
        utils.isValidNumber(item.price)
      ) {
        item.amountTotal = item.quantity * item.price;
      } else {
        item.amountTotal = hhdv.thtien;
      }

      if (
        !utils.isValidNumber(item.vatAmount) &&
        utils.isValidNumber(item.amountTotal)
      ) {
        if (
          typeof invoiceHelper.getVatFromVatType(item.vat) == 'number' &&
          item.productType == PRODUCT_TYPE.HHDV
        ) {
          item.vatAmount =
            (invoiceHelper.getVatFromVatType(item.vat) / 100) *
            item.amountTotal;
        }
      }

      if (
        utils.isValidNumber(item.discount) &&
        utils.isValidNumber(item.amountTotal)
      ) {
        item.discountAmount = item.amountTotal * item.discount;
      }

      if (
        utils.isValidNumber(item.discount) &&
        utils.isValidNumber(item.amountTotal)
      ) {
        item.finalAmount =
          item.amountTotal + (item.vatAmount ?? 0) - item.discountAmount;
      }

      return item;
    }),
  };

  return invoice;
}

module.exports.cqt_toInvoice = cqt_toInvoice;
