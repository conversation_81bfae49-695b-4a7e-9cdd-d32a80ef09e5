const { Transaction } = require('sequelize');
const { Invoice } = require('../models');
const invoiceHelper = require('./invoice.helper');
const { INVOICE_CATEGORY } = require('../configs/constants/invoice.constant');

module.exports = {
  /**
   *
   * require `buyerTaxCode`, `invoiceNumber`
   *
   * @param {{buyerTaxCode:string, invoiceNumber:string, serial: string, invoiceTypeCode: string}[]} newInvoices
   * @param {{buyerTaxCode:string, invoiceNumber:string, serial: string, invoiceTypeCode: string}[]} oldInvoices
   *
   */
  filterInvoicesNotHaveDuplicated(newInvoices, oldInvoices) {
    let dict = oldInvoices.reduce((prev, curr) => {
      prev[
        `${curr.buyerTaxCode}:${curr.invoiceTypeCode}:${curr.serial}:${curr.invoiceNumber}`
      ] = true;

      return prev;
    }, {});

    return newInvoices.filter(
      item =>
        !dict[
          `${item.buyerTaxCode}:${item.invoiceTypeCode}:${item.serial}:${item.invoiceNumber}`
        ],
    );
  },
  /**
   *
   *
   * @param {{invoiceId: number}[]} newInvoices
   * @param {{invoiceId: number}[]} oldInvoices
   *
   */
  filterInvoicesNotHaveDuplicatedByInvoiceId(newInvoices, oldInvoices) {
    let dict = oldInvoices.reduce((prev, curr) => {
      prev[`${curr.invoiceId}`] = true;

      return prev;
    }, {});

    return newInvoices.filter(item => !dict[`${item.invoiceId}`]);
  },
};
