const { CLIENT_LOGIN_PATH } = require('../configs/constants/client.constant');
const {
  CLIENT_BASE_URL,
  BASE_URL,
  SERVER_BASE_URL,
  QLBH_BASE_URL,
} = require('../configs/env');
const handlebars = require('handlebars');
const fs = require('fs');
const {
  PATH_ACCOUNT_ACTIVATE,
} = require('../configs/constants/server-path.constant');
const { TYPE_PAYMENT } = require('../configs/constants/order.constant');
const moment = require('moment');
const { or } = require('sequelize');

module.exports = {
  /**
   *
   * @param {string} filePath absolute path in server
   * @param {Object} param1
   * @param {import('../types').Account=} param1.account
   * @param {{fullname:string,email:string,taxCode:string}} param1.recipient
   */
  async parseFromHTMLTemplate(
    filePath,
    { account, recipient, order, orderItems, ...others },
  ) {
    const db = require('../models');
    let data = fs.readFileSync(filePath, 'utf8');
    // console.log(data);
    let dict = {};
    let template = handlebars.compile(data);

    if (recipient) {
      dict = { ...recipientInformationToTemplates(recipient), ...dict };
    }

    if (account) {
      dict = {
        ...accountInformationToTemplates(account),
        ...dict,
      };
    }

    if (order) {
      dict['BANK_NAME'] = (
        await db.Config.findOne({
          where: {
            key: 'BANK_NAME',
          },
        })
      ).toJSON().value;
      dict['BANK_ACCOUNT_NUMBER'] = (
        await db.Config.findOne({
          where: {
            key: 'BANK_ACCOUNT_NUMBER',
          },
        })
      ).toJSON().value;
      dict['BANK_ACCOUNT_NAME'] = (
        await db.Config.findOne({
          where: {
            key: 'BANK_ACCOUNT_NAME',
          },
        })
      ).toJSON().value;
      dict['BANK_BRANCH'] = (
        await db.Config.findOne({
          where: {
            key: 'BANK_BRANCH',
          },
        })
      ).toJSON().value;
      dict['totalCost'] = Intl.NumberFormat('de-DE').format(order.totalCost);

      dict = {
        ...orderTemplates(order),
        ...dict,
      };
    }

    if (orderItems) {
      dict = {
        ...dict,
        orderItems,
      };
    }

    let urlDict = {};
    urlDict['CLIENT_BASE_URL'] = CLIENT_BASE_URL;
    urlDict['BASE_URL'] = BASE_URL;

    urlDict['PATH_ACCOUNT_ACTIVATE'] = PATH_ACCOUNT_ACTIVATE;
    urlDict['SERVER_BASE_URL'] = SERVER_BASE_URL;

    urlDict['CLIENT_LOGIN_PATH'] = `${CLIENT_LOGIN_PATH}`;
    urlDict['QLBH_BASE_URL'] = QLBH_BASE_URL;

    dict = { ...urlDict, ...dict };

    if (others) {
      dict = { ...dict, ...others };
    }

    handlebars.registerHelper('ifTrue', function (value) {
      return value !== undefined;
    });

    handlebars.registerHelper('inc', function (value, options) {
      return parseInt(value) + 1;
    });

    let htmlToSend = template(dict);

    return htmlToSend;
  },
};

/**
 * Thông tin recipient
 * @param {{name:string,email:string,taxCode:string}} recipient
 */
function recipientInformationToTemplates(recipient) {
  let dict = {
    recipient_name: recipient?.name,
    recipient_email: recipient?.email,
    recipient_taxCode: recipient?.taxCode,
  };

  return dict;
}

/**
 * Chèn thông tin tk
 * @param {import('../types').Account} account
 */
function accountInformationToTemplates(account) {
  let dict = { ...account };

  dict['username'] = account.email;
  dict['companyUsername'] = account.companyUsername;

  return dict;
}

/**
 * Chèn thông tin Order
 * @param {import('../types').Order} order
 */
function orderTemplates(order) {
  let dict = { ...order };

  dict['orderCode'] = order.clientOrderCode ?? order.orderCode;
  dict['orderTypePayment'] =
    order.typePayment == TYPE_PAYMENT.VNPAY ? 'VNPAY' : 'Chuyển khoản';
  dict['orderCreatedAt'] = moment(order.createdAt).format('L');
  dict['orderCompanyName'] = order.companyName;
  dict['orderTaxCode'] = order.taxCode;

  return dict;
}
