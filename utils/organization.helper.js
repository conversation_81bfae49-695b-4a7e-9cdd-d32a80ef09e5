const { Op } = require('sequelize');
const { Invoice, AcceptPartnerCompanyDetail, Account } = require('../models');
const { getPublicAccountAttributes } = require('./account.helper');

module.exports = {
  /**
   *
   * @param {import('../types').Organization[]} organizations
   */
  async mergeCountedInvoicesOrg(organizations) {
    let invoices = await Invoice.findAll({
      attributes: ['organizationId', 'sellerTaxCode', 'buyerTaxCode'],
      where: {
        organizationId: {
          [Op.in]: organizations.map(org => org.organizationId),
        },
      },
    });

    for (let organization of organizations) {
      organization.sellInvoices = invoices.filter(
        item =>
          item.organizationId == organization.organizationId &&
          item.sellerTaxCode == organization.taxCode,
      ).length;
      organization.buyInvoices = invoices.filter(
        item =>
          item.organizationId == organization.organizationId &&
          item.buyerTaxCode == organization.taxCode,
      ).length;
    }

    return organizations;
  },
  /**
   *
   * @param {import('../types').OrganizationDepartment[]} organizationDepartments
   * @param {string} taxCode
   */
  async mergeCountedInvoicesDep(organizationDepartments, taxCode) {
    let invoices = await Invoice.findAll({
      where: {
        organizationDepartmentId: {
          [Op.in]: organizationDepartments.map(
            org => org.organizationDepartmentId,
          ),
        },
      },
    });

    for (let deparment of organizationDepartments) {
      deparment.sellInvoices = invoices.filter(
        item =>
          item.organizationDepartmentId == deparment.organizationDepartmentId &&
          item.sellerTaxCode == taxCode,
      ).length;
      deparment.buyInvoices = invoices.filter(
        item =>
          item.organizationDepartmentId == deparment.organizationDepartmentId &&
          item.buyerTaxCode == taxCode,
      ).length;
    }

    return organizationDepartments;
  },
  /**
   *
   * @param {import('../types').Organization[]} organizations
   * @param {Object} param0
   * @param {number} param0.organizationId
   * @param {number} param0.organizationDepartmentId
   *
   *
   */
  async mergeAcceptDetailsOrg(
    organizations,
    { organizationId, organizationDepartmentId },
  ) {
    let acceptPartnerCompanyDetails = await AcceptPartnerCompanyDetail.findAll({
      where: {
        from: { [Op.lt]: new Date() },
        to: { [Op.gt]: new Date() },
        taxCode: { [Op.in]: organizations.map(item => item.taxCode) },
        organizationDepartmentId,
        organizationId,
      },
      include: [{ model: Account, attributes: getPublicAccountAttributes() }],
      order: [
        ['from', 'ASC'],
        ['to', 'DESC'],
      ],
    });

    for (let organization of organizations) {
      organization.acceptPartnerCompanyDetails =
        acceptPartnerCompanyDetails.filter(
          item => item.taxCode == organization.taxCode,
        );
    }

    return organizations;
  },
  /**
   *
   * @param {import('../types').OrganizationDepartment[]} departments
   * @param {string} taxCode
   * @param {Object} param0
   * @param {number} param0.organizationId
   * @param {number} param0.organizationDepartmentId
   *
   *
   */
  async mergeAcceptDetailsDep(
    departments,
    taxCode,
    { organizationId, organizationDepartmentId },
  ) {
    let acceptPartnerCompanyDetails = await AcceptPartnerCompanyDetail.findAll({
      where: {
        from: { [Op.lt]: new Date() },
        to: { [Op.gt]: new Date() },
        taxCode: { [Op.in]: [taxCode] },
        organizationDepartmentId,
        organizationId,
      },
      include: [{ model: Account, attributes: getPublicAccountAttributes() }],
      order: [
        ['from', 'ASC'],
        ['to', 'DESC'],
      ],
    });

    for (let department of departments) {
      department.acceptPartnerCompanyDetails =
        acceptPartnerCompanyDetails.filter(item => item.taxCode == taxCode);
    }

    return departments;
  },
};
