/* eslint-disable no-redeclare */
const xml2js = require('xml2js');
const {
  INVOICE_TYPE,
  INVOICE_TYPE_CODE,
  VAT_TYPE,
  PRODUCT_TYPE,
  INVOICE_STATUS_TEXT,
  INVOICE_STATUS,
  POSSIBLE_LOOKUP_CODE_XML_TAG,
  INVOICE_CATEGORY,
  TAX_REDUCE_VALIDATE_RESULT,
  TAX_STATUS_TEXT,
  TYPE_INVOICE_RECEIVE,
  INVOICE_CHECK_STATUS,
} = require('../configs/constants/invoice.constant');
const { SUPPLIER_KEY } = require('../configs/constants/supplier.constant');
const { vietinvoiceHelper } = require('../vietinvoice');
const {
  ConnectionSupplier,
  Invoice,
  Sequelize,
  InvoiceProduct,
  InvoiceSupplierOrg,
  OrganizationDepartment,
  Organization,
  Config,
  Product,
  TaxReduceValidate,
  AcceptTaxReduce,
} = require('../models');
const { Transaction, Op, Model } = require('sequelize');
const {
  UPLOAD_DIR,
  PUBLIC_UPLOAD_DIR,
  TEMP_UPLOAD_DIR,
} = require('../configs/env');
const fs = require('fs');
const { createPreviewInvoicePdf } = require('./preview-invoice-pdf.helper');
const { X509Certificate } = require('crypto');
const jwt = require('jsonwebtoken');
const {
  cqt_authenticate,
  cqt_export_xml,
  cqt_detail_invoice,
  cqt_toInvoice,
  cqt_validate_invoice,
  check_invoice,
} = require('./cqt.helper');
const {
  ERROR_CONNECT_CQT_FAIL,
  ERROR_NOT_FOUND,
} = require('../configs/error.vi');
const moment = require('moment');
const utils = require('.');
const decompress = require('decompress');
const _ = require('lodash');
const {
  PARTNER_COMPANY_STATUS,
} = require('../configs/constants/company.constant');
const db = require('../models');
const nntHelper = require('./nnt.helper');

var XmlDSigJs = require('xmldsigjs');
var { Crypto } = require('@peculiar/webcrypto');

XmlDSigJs.Application.setEngine('OpenSSL', new Crypto());

module.exports = {
  // /**
  //  * validate signature
  //  * @typedef Result
  //  * @property {boolean} verified
  //  * @property {{verified: boolean, reason?: string}[]} numRefDigests
  //  *
  //  * @param {string} xmlContent
  //  * @returns {Result[]}
  //  */
  // validateXML(xmlContent) {
  //   xmlContent = xmlContent.replace(/>\s*/g, '>');
  //   xmlContent = xmlContent.replace(/\s*</g, '<');
  //   xmlContent = xmlContent.replace(
  //     // Replace out the new line character.
  //     new RegExp('\\n', 'g'),
  //     '',
  //   );

  //   var os = require('os');
  //   if (os.platform() == 'win32') {
  //     if (os.arch() == 'ia32') {
  //       var chilkat = require('@chilkat/ck-node11-win-ia32');
  //     } else {
  //       var chilkat = require('@chilkat/ck-node18-win64');
  //     }
  //   } else if (os.platform() == 'linux') {
  //     if (os.arch() == 'arm') {
  //       var chilkat = require('@chilkat/ck-node11-arm');
  //     } else if (os.arch() == 'x86') {
  //       var chilkat = require('@chilkat/ck-node11-linux32');
  //     } else {
  //       var chilkat = require('@chilkat/ck-node16-linux64');
  //     }
  //   } else if (os.platform() == 'darwin') {
  //     var chilkat = require('@chilkat/ck-node11-macosx');
  //   }

  //   var dsig = new chilkat.XmlDSig();

  //   var success = dsig.LoadSignature(xmlContent);
  //   if (success !== true) {
  //     console.warn(dsig.LastErrorText);
  //     return [];
  //   }

  //   var numSignatures = dsig.NumSignatures;
  //   var i = 0;
  //   var result = [];

  //   while (i < numSignatures) {
  //     let resultItem = { verified: false, numRefDigests: [] };
  //     dsig.Selector = i;

  //     var bVerifyRefDigests = false;
  //     var bSignatureVerified = dsig.VerifySignature(bVerifyRefDigests);
  //     if (bSignatureVerified == true) {
  //       //   console.log('Signature ' + (i + 1) + 'verified');
  //       resultItem.verified = true;
  //     } else {
  //       //   console.log('Signature ' + (i + 1) + 'invalid');
  //     }

  //     // Check each of the reference digests separately..
  //     var numRefDigests = dsig.NumReferences;
  //     var j = 0;

  //     resultItem.numRefDigests = [...Array(numRefDigests)];
  //     while (j < numRefDigests) {
  //       var bDigestVerified = dsig.VerifyReferenceDigest(j);
  //       //   console.log(
  //       //     'reference digest ' + (j + 1) + ' verified = ' + bDigestVerified,
  //       //   );
  //       resultItem.numRefDigests[j] = {
  //         verified: !!bDigestVerified,
  //         reason: !bDigestVerified ? dsig.RefFailReason : undefined,
  //       };
  //       if (bDigestVerified == false) {
  //         // console.log('reference digest fail reason: ' + dsig.RefFailReason);
  //       }

  //       j = j + 1;
  //     }

  //     i = i + 1;

  //     result.push(resultItem);
  //   }

  //   return result;
  // },
  /**
   *
   * @param {string} xmlContent
   */
  async validateXML(xmlContent) {
    // xmlContent = xmlContent.replace(/>\s*/g, '>');
    // xmlContent = xmlContent.replace(/\s*</g, '<');
    // xmlContent = xmlContent.replace(
    //   // Replace out the new line character.
    //   new RegExp('\\n', 'g'),
    //   '',
    // );

    let signedDocument = XmlDSigJs.Parse(xmlContent);
    var signedXml = new XmlDSigJs.SignedXml(signedDocument);
    let json = await this.xmlToJSON(xmlContent);

    /**
     * @type {Result[]}
     */
    let results = [];
    /* KTRA NBAN */

    let dscks = signedDocument
      .getElementsByTagName('HDon')[0]
      .getElementsByTagName('DSCKS')[0];

    let cksNBAN = dscks
      ?.getElementsByTagName('NBan')[0]
      ?.getElementsByTagName('Signature')?.[0];

    if (cksNBAN) {
      signedXml.LoadXml(cksNBAN);
      let rt = await signedXml.Verify().catch(() => false);
      results.push({ verified: rt });
    } else {
      results.push({});
    }

    let cksCQT = dscks
      ?.getElementsByTagName('CQT')[0]
      ?.getElementsByTagName('Signature')?.[0];

    if (cksCQT) {
      signedXml.LoadXml(cksCQT);
      let rt = await signedXml.Verify().catch(() => false);
      results.push({ verified: rt });
    } else {
      results.push({});
    }

    return results;
  },
  /**
   *
   * @param {string} xmlContent
   *
   * @returns {Promise<import('../types').InvoiceFromXml>}
   * @throws exception if not valid xml
   */
  xmlToJSON(xmlContent) {
    return new Promise((resolve, reject) => {
      const parser = new xml2js.Parser({
        explicitArray: true,
        // attrkey: '@',
        // charkey: '#',
      });
      let jsonData = {};

      parser.parseString(xmlContent, function (err, result) {
        if (err) {
          return reject(err);
        }
        resolve(result);
      });

      resolve(jsonData);
    });
  },
  /**
   *
   * @param {string} xmlContent
   *
   * @returns {Promise<string>}
   */
  async getMaCqtFromXml(xmlContent) {
    let json = await this.xmlToJSON(xmlContent);

    return json['HDon'].MCCQT[0]._;
  },
  /**
   *
   * @param {import('../types').Invoice} invoice1
   * @param {import('../types').Invoice} invoice2
   */
  isSameInvoice(invoice1, invoice2) {
    return (
      invoice1.sellerTaxCode == invoice2.sellerTaxCode &&
      invoice1.invoiceNumber == invoice2.invoiceNumber &&
      invoice1.invoiceTypeCode == invoice2.invoiceTypeCode &&
      invoice1.serial == invoice2.serial
    );
  },
  /**
   *
   * @param {string} xmlContent
   */
  async invoiceFromXml(xmlContent) {
    let json = await this.xmlToJSON(xmlContent);

    /**
     * @type {import('../types').Invoice}
     */
    let invoice = {
      invoiceVersion: json.HDon.DLHDon?.[0]?.TTChung?.[0]?.PBan?.[0],
      buyerTaxCode: json['HDon']['DLHDon']?.[0].NDHDon?.[0].NMua?.[0].MST?.[0],
      buyerName: json.HDon.DLHDon?.[0].NDHDon?.[0].NMua?.[0].Ten?.[0],
      buyerAddress: json.HDon.DLHDon?.[0].NDHDon?.[0].NMua?.[0].DChi?.[0],
      statusInvoice:
        json.HDon.DLHDon?.[0].TTChung?.[0].TTHDLQuan?.[0]?.TCHDon?.[0] ??
        undefined,
      invoiceNumber: json['HDon']['DLHDon']?.[0]['TTChung']?.[0]['SHDon']?.[0],
      finalAmount:
        json['HDon']['DLHDon']?.[0].NDHDon?.[0].TToan?.[0].TgTTTBSo?.[0],
      finalAmountInWord:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0].TgTTTBChu?.[0],
      paymentMethod: json.HDon.DLHDon?.[0].TTChung?.[0].HTTToan?.[0],
      paymentCurrency: json.HDon.DLHDon?.[0].TTChung?.[0].DVTTe?.[0],

      invoiceTypeCode: json.HDon.DLHDon?.[0].TTChung?.[0].KHMSHDon?.[0],
      serial: json.HDon.DLHDon?.[0].TTChung?.[0].KHHDon?.[0],
      invoiceType: this.getInvoiceTypeFromTypeCode(
        json.HDon.DLHDon?.[0].TTChung?.[0].KHMSHDon?.[0],
      ),

      sellerTaxCode: json.HDon.DLHDon?.[0].NDHDon?.[0].NBan?.[0].MST?.[0],
      sellerName: json.HDon.DLHDon?.[0].NDHDon?.[0].NBan?.[0].Ten?.[0],
      sellerAddress: json.HDon.DLHDon?.[0].NDHDon?.[0].NBan?.[0].DChi?.[0],
      sellerBankName: json.HDon.DLHDon?.[0].NDHDon?.[0].NBan?.[0].TNHang?.[0],
      sellerBankAccount:
        json.HDon.DLHDon?.[0].NDHDon?.[0].NBan?.[0]?.STKNHang?.[0],

      invoiceDate: json.HDon.DLHDon?.[0].TTChung?.[0].NLap?.[0],

      amountVat: json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0].TgTThue?.[0],
      amountBeforeVat:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0].TgTCThue?.[0],
      totalDiscountAmount:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0].TTCKTMai?.[0],
      discountWithoutVAT:
        json.HDon.DLHDon?.[0].NDHDon?.[0]?.TToan?.[0]?.TTKhac?.[0]?.TTin?.find(
          item => item.TTruong[0] == 'TotalDiscountAmount',
        )?.DLieu?.[0],
      discountOther:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0]?.TTKhac?.[0]?.TTin?.find(
          item => item.TTruong[0] == 'TotalDiscountAmountOC',
        )?.DLieu[0],

      amountVat10:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0]?.THTTLTSuat?.[0]?.LTSuat?.find(
          item => item.TSuat[0] == VAT_TYPE['10%'],
        )?.TThue?.[0],
      amountBeforeVat10:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0]?.THTTLTSuat?.[0]?.LTSuat?.find(
          item => item.TSuat[0] == VAT_TYPE['10%'],
        )?.ThTien?.[0],

      amountVat0:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0]?.THTTLTSuat?.[0]?.LTSuat?.find(
          item => item.TSuat[0] == VAT_TYPE['0%'],
        )?.TThue?.[0],
      amountBeforeVat0:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0].THTTLTSuat?.[0]?.LTSuat?.find(
          item => item.TSuat[0] == VAT_TYPE['0%'],
        )?.ThTien?.[0],

      amountVat8:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0].THTTLTSuat?.[0]?.LTSuat?.find(
          item => item.TSuat[0] == VAT_TYPE['8%'],
        )?.TThue?.[0],
      amountBeforeVat8:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0].THTTLTSuat?.[0]?.LTSuat?.find(
          item => item.TSuat[0] == VAT_TYPE['8%'],
        )?.ThTien?.[0],

      amountVat5:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0].THTTLTSuat?.[0]?.LTSuat?.find(
          item => item.TSuat[0] == VAT_TYPE['5%'],
        )?.TThue?.[0],
      amountBeforeVat5:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0].THTTLTSuat?.[0].LTSuat?.find(
          item => item.TSuat[0] == VAT_TYPE['5%'],
        )?.ThTien?.[0],

      amountVatKHAC:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0].THTTLTSuat?.[0].LTSuat?.find(
          item => item.TSuat[0]?.startsWith(VAT_TYPE['KHAC']),
        )?.TThue?.[0],
      amountBeforeVatKHAC:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0].THTTLTSuat?.[0].LTSuat?.find(
          item => item.TSuat[0]?.startsWith(VAT_TYPE['KHAC']),
        )?.ThTien?.[0],

      amountVatKKKNT:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0].THTTLTSuat?.[0].LTSuat?.find(
          item => item.TSuat[0] == VAT_TYPE['KKKNT'],
        )?.TThue?.[0],
      amountBeforeVatKKKNT:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0].THTTLTSuat?.[0].LTSuat?.find(
          item => item.TSuat[0] == VAT_TYPE['KKKNT'],
        )?.ThTien?.[0],

      amountVatKCT:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0].THTTLTSuat?.[0].LTSuat?.find(
          item => item.TSuat[0] == VAT_TYPE['KCT'],
        )?.TThue?.[0],
      amountBeforeVatKCT:
        json.HDon.DLHDon?.[0].NDHDon?.[0].TToan?.[0].THTTLTSuat?.[0].LTSuat?.find(
          item => item.TSuat[0] == VAT_TYPE['KCT'],
        )?.ThTien?.[0],

      cqtCKS:
        json.HDon.DSCKS?.[0]?.CQT?.[0].Signature?.[0].KeyInfo[0].X509Data[0]
          .X509Certificate[0],
      sellerCKS:
        json.HDon.DSCKS?.[0]?.NBan?.[0].Signature?.[0].KeyInfo[0].X509Data[0]
          .X509Certificate[0],
      cqtSignDate:
        json.HDon.DSCKS?.[0]?.CQT?.[0].Signature?.[0].Object[0]
          .SignatureProperties[0].SignatureProperty[0].SigningTime[0],
      sellerSignDate:
        json.HDon.DSCKS?.[0]?.NBan?.[0].Signature?.[0].Object[0]
          .SignatureProperties[0].SignatureProperty[0].SigningTime[0],

      providedCodeDate:
        json.HDon.DSCKS?.[0]?.CQT?.[0].Signature?.[0].Object[0]
          .SignatureProperties[0].SignatureProperty[0].SigningTime[0],

      maCQT: json.HDon.MCCQT?.[0]?._,
      supplierTaxCode: json.HDon.DLHDon?.[0].TTChung?.[0].MSTTCGP?.[0],
      supplierReferenceCode:
        /*  */
        json.HDon.DLHDon?.[0].TTChung?.[0].TTKhac?.[0]?.TTin?.filter(item =>
          POSSIBLE_LOOKUP_CODE_XML_TAG.includes(item.TTruong[0]),
        ).find(item => item?.DLieu?.[0])?.DLieu?.[0],
      /*  */
      /* ?? json.HDon.DLHDon?.[0].TTKhac?.[0].TTin?.find(
          item => item.TTruong == 'TransactionID',
        )?.DLieu?.[0] ??
        json.HDon.DLHDon?.[0].$?.Id */ /* Products */
      InvoiceProducts:
        json.HDon.DLHDon?.[0].NDHDon?.[0].DSHHDVu?.[0]?.HHDVu?.map(hhdv => {
          /**
           * @type {import('../types').InvoiceProduct}
           */
          let product = {
            name: hhdv.THHDVu?.[0],
            code: hhdv.MHHDVu?.[0],
            price: utils.isValidNumber(hhdv.DGia?.[0])
              ? Number(hhdv.DGia?.[0])
              : undefined,
            quantity: utils.isValidNumber(hhdv.SLuong?.[0])
              ? Number(hhdv.SLuong[0])
              : undefined,
            amountTotal: utils.isValidNumber(hhdv.ThTien?.[0])
              ? Number(hhdv.ThTien[0])
              : undefined,
            productType: this.getProductTypeFromCode(hhdv.TChat?.[0]),
            vat: hhdv.TSuat?.[0],
            unit: hhdv.DVTinh?.[0],
            discount: utils.isValidNumber(hhdv.TLCKhau?.[0])
              ? Number(hhdv.TLCKhau[0])
              : null,
            sortOrder: hhdv.STT?.[0],
            discountAmount: utils.isValidNumber(hhdv.STCKhau?.[0])
              ? Number(hhdv.STCKhau[0])
              : null,
            vatRate: this.getVatFromVatType(hhdv.TSuat?.[0]),
          };

          if (
            !utils.isValidNumber(product.amountTotal) &&
            utils.isValidNumber(product.price) &&
            utils.isValidNumber(product.quantity)
          ) {
            product.amountTotal = product.price * product.quantity;
          }

          let findVATAmount;
          if (
            (findVATAmount = hhdv.TTKhac?.[0].TTin?.find(
              item => item.TTruong?.[0] === 'VATAmount',
            ))
          ) {
            product.vatAmount = Number(findVATAmount.DLieu?.[0]);
          } else if (
            typeof this.getVatFromVatType(product.vat) == 'number' &&
            utils.isValidNumber(product.amountTotal) &&
            product.productType == PRODUCT_TYPE.HHDV
          ) {
            product.vatAmount =
              (this.getVatFromVatType(product.vat) / 100) * product.amountTotal;
          }

          if (utils.isValidNumber(product.amountTotal)) {
            product.finalAmount =
              product.amountTotal +
              (product.vatAmount ?? 0) -
              (product.discountAmount ?? 0);
          }

          return product;
        }),

      /* hdgoc */
      hdgocData: {
        khhdon:
          json.HDon.DLHDon?.[0].TTChung?.[0].TTHDLQuan?.[0]?.KHHDCLQuan?.[0],
        khmshdon:
          json.HDon.DLHDon?.[0].TTChung?.[0].TTHDLQuan?.[0]?.KHMSHDCLQuan?.[0],
        lhdon:
          json.HDon.DLHDon?.[0].TTChung?.[0].TTHDLQuan?.[0]?.LHDCLQuan?.[0],
        shdon:
          json.HDon.DLHDon?.[0].TTChung?.[0].TTHDLQuan?.[0]?.SHDCLQuan?.[0],
        tdlap:
          json.HDon.DLHDon?.[0].TTChung?.[0].TTHDLQuan?.[0]?.NLHDCLQuan?.[0],
      },
    };

    if (utils.isValidNumber(invoice.hdgocData?.shdon)) {
      invoice.hdgocData.shdon = Number(invoice.hdgocData.shdon);
    }

    if (utils.isValidNumber(invoice.amountBeforeVat)) {
      invoice.amountAfterVat =
        parseFloat(invoice.amountBeforeVat) +
        parseFloat(invoice.amountVat ?? 0);
    }
    invoice.amountBeforeDiscount = invoice.amountAfterVat;

    invoice.statusInvoiceText = INVOICE_STATUS_TEXT[invoice.statusInvoice];

    if (invoice.sellerSignDate && !utils.isValidDate(invoice.sellerSignDate)) {
      invoice.sellerSignDate =
        json.HDon.DSCKS?.[0]?.NBan?.[0].Signature[0].Object[0].SignatureProperties[0].SignatureProperty[0].SigningTime[0]?._;
    }

    if (
      utils.isValidNumber(invoice.amountBeforeVat0) &&
      utils.isValidNumber(invoice.amountVat0)
    ) {
      invoice.amountAfterVat0 =
        Number(invoice.amountBeforeVat0) + Number(invoice.amountVat0);
    }

    if (
      utils.isValidNumber(invoice.amountBeforeVat5) &&
      utils.isValidNumber(invoice.amountVat5)
    ) {
      invoice.amountAfterVat5 =
        Number(invoice.amountBeforeVat5) + Number(invoice.amountVat5);
    }

    if (
      utils.isValidNumber(invoice.amountBeforeVat8) &&
      utils.isValidNumber(invoice.amountVat8)
    ) {
      invoice.amountAfterVat8 =
        Number(invoice.amountBeforeVat8) + Number(invoice.amountVat8);
    }

    if (
      utils.isValidNumber(invoice.amountBeforeVat10) &&
      utils.isValidNumber(invoice.amountVat10)
    ) {
      invoice.amountAfterVat10 =
        Number(invoice.amountBeforeVat10) + Number(invoice.amountVat10);
    }

    if (
      utils.isValidNumber(invoice.amountBeforeVatKCT) &&
      utils.isValidNumber(invoice.amountVatKCT)
    ) {
      invoice.amountAfterVatKCT =
        Number(invoice.amountBeforeVatKCT) + Number(invoice.amountVatKCT);
    }

    if (
      utils.isValidNumber(invoice.amountBeforeVatKKKNT) &&
      utils.isValidNumber(invoice.amountVatKKKNT)
    ) {
      invoice.amountAfterVatKKKNT =
        Number(invoice.amountBeforeVatKKKNT) + Number(invoice.amountVatKKKNT);
    }

    return invoice;
  },
  /**
   *
   * @param {import('../types').INVOICE_TYPE_CODE} invoiceTypeCode
   * @returns {import('../types').INVOICE_TYPE}
   */
  getInvoiceTypeFromTypeCode(invoiceTypeCode) {
    switch (invoiceTypeCode) {
      case INVOICE_TYPE_CODE[1]:
        return INVOICE_TYPE['01'];
      case INVOICE_TYPE_CODE[2]:
        return INVOICE_TYPE['02'];
      case INVOICE_TYPE_CODE[3]:
        return INVOICE_TYPE['03'];
      case INVOICE_TYPE_CODE[4]:
        return INVOICE_TYPE['04'];
      case INVOICE_TYPE_CODE[5]:
        return INVOICE_TYPE['05'];
      case INVOICE_TYPE_CODE[6]:
        return INVOICE_TYPE['06_01'];
    }
  },
  /**
   * Lấy chi tiết hđ bên NCC và cập nhật
   * @param {import('../types').Invoice} invoice
   * @param {Transaction=} t
   */
  async mergeWithSupplierInvoice(invoice, t) {
    try {
      if (invoice.connectionSupplier) {
        let {
          usernameConnection,
          passwordConnection,
          Supplier: { supplierKey },
          connectionSupplierId,
        } = invoice.connectionSupplier;

        let _invoice = await Invoice.findOne({
          where: { invoiceId: invoice.invoiceId },
          transaction: t,
        });

        if (supplierKey == SUPPLIER_KEY.VIETINVOICE) {
          if (invoice.vietInvoiceId) {
            let xCSRFToken;
            if (
              _invoice.xmlNCCNotExist != true &&
              (!_invoice.originXmlFile ||
                !fs.existsSync(
                  _invoice.originXmlFile.replace('uploads', UPLOAD_DIR),
                ))
            ) {
              try {
                let { data, xCSRFToken: _xCSRFToken } =
                  await vietinvoiceHelper.getXml(
                    usernameConnection,
                    passwordConnection,
                    invoice.vietInvoiceId,
                    xCSRFToken,
                  );

                xCSRFToken = _xCSRFToken;

                await ConnectionSupplier.update(
                  { xCSRFToken },
                  { where: { connectionSupplierId }, transaction: t },
                );

                if (!data) {
                  await _invoice.update(
                    { xmlNCCNotExist: true },
                    { transaction: t },
                  );

                  throw new Error(ERROR_NOT_FOUND);
                }

                if (!_invoice.originXmlFile) {
                  let filepath = `${UPLOAD_DIR}/invoice-origin-xml/${invoice.invoiceId}`;
                  let filename = 'orgin-invoice.xml';

                  if (fs.existsSync(filepath)) {
                    fs.rmSync(filepath, { recursive: true });
                  }

                  fs.mkdirSync(filepath);

                  fs.writeFileSync(`${filepath}/${filename}`, data, 'utf-8');

                  await _invoice.update(
                    {
                      originXmlFile: `uploads/invoice-origin-xml/${invoice.invoiceId}/${filename}`,
                    },
                    { transaction: t },
                  );
                }
              } catch (error) {
                // console.warn(error);
              }
            }

            if (
              _invoice.pdfNCCNotExist != true &&
              (!_invoice.pdfFile ||
                !fs.existsSync(_invoice.pdfFile.replace('uploads', UPLOAD_DIR)))
            ) {
              try {
                let { data, _xCSRFToken } = await vietinvoiceHelper.getPdf(
                  usernameConnection,
                  passwordConnection,
                  invoice.vietInvoiceId,
                  xCSRFToken,
                );

                xCSRFToken = _xCSRFToken;

                await ConnectionSupplier.update(
                  { xCSRFToken },
                  { where: { connectionSupplierId }, transaction: t },
                );

                if (!data) {
                  await _invoice.update(
                    { pdfNCCNotExist: true },
                    { transaction: t },
                  );

                  throw new Error(ERROR_NOT_FOUND);
                }

                let filepath = `${UPLOAD_DIR}/invoice-pdf/${invoice.invoiceId}`;
                let filename = `invoice.pdf`;

                if (!fs.existsSync(filepath)) {
                  fs.mkdirSync(filepath);
                }

                fs.writeFileSync(`${filepath}/${filename}`, data);

                await _invoice.update(
                  {
                    pdfFile: `uploads/invoice-pdf/${invoice.invoiceId}/${filename}`,
                  },
                  { transaction: t },
                );
              } catch (error) {
                // console.warn(error);
              }
            }

            try {
              if (_invoice.parsedDetailFromNCC != true) {
                let { data, xCSRFToken: _xCSRFToken } =
                  await vietinvoiceHelper.detail(
                    usernameConnection,
                    passwordConnection,
                    invoice.vietInvoiceId,
                    xCSRFToken,
                  );

                xCSRFToken = _xCSRFToken;

                await ConnectionSupplier.update(
                  { xCSRFToken },
                  { where: { connectionSupplierId }, transaction: t },
                );

                if (data) {
                  let vietinvoiceInvoice = vietinvoiceHelper.toInvoice(data);

                  vietinvoiceInvoice.parsedDetailFromNCC = true;

                  await _invoice.update(vietinvoiceInvoice, {
                    transaction: t,
                  });

                  if (vietinvoiceInvoice.InvoiceProducts) {
                    await InvoiceProduct.destroy({
                      where: { invoiceId: _invoice.invoiceId },
                      transaction: t,
                    });

                    await InvoiceProduct.bulkCreate(
                      vietinvoiceInvoice.InvoiceProducts.map(item => {
                        item.invoiceId = _invoice.invoiceId;

                        return item;
                      }),
                      { transaction: t },
                    );
                  }
                }
              }
            } catch (error) {}

            Object.assign(invoice, _invoice.toJSON());
          }
        }
      }
    } catch (error) {
      console.warn(error);
    }

    return invoice;
  },
  /**
   * Lấy loại HHDV từ mã trong XML
   * @param {'1'|'2'|'3'|'4'} code
   * @returns {import('../types').ProductType}
   */
  getProductTypeFromCode(code) {
    switch (code) {
      case '1':
        return PRODUCT_TYPE.HHDV;
      case '2':
        return PRODUCT_TYPE.KM;
      case '3':
        return PRODUCT_TYPE.CKTM;
      case '4':
        return PRODUCT_TYPE.DG;
      default:
        return PRODUCT_TYPE.HHDV;
    }
  },
  /**
   * Lấy só VAT từ loại VAT trong XML
   * @param {import('../types').VAT_TYPE} vatType
   */
  getVatFromVatType(vatType) {
    switch (vatType) {
      case VAT_TYPE['0%']:
        return 0;
      case VAT_TYPE['5%']:
        return 5;
      case VAT_TYPE['8%']:
        return 8;
      case VAT_TYPE['10%']:
        return 10;
      case VAT_TYPE.KCT:
        return 0;
      case VAT_TYPE.KKKNT:
        return 0;
      default:
        return null;
    }
  },
  getUpdateOnDuplicateInvoiceAttrs() {
    return Object.keys(Invoice.getAttributes()).filter(
      key =>
        ![
          'invoiceId',
          'sellerTaxCode',
          'invoiceNumber',
          'invoiceTypeCode',
          'serial',
          'organizationId',
          'organizationDepartmentId',
          // 'invoiceCQTId',
          // 'vietInvoiceId',
          'typeInvoiceReceive',
        ].includes(key),
    );
  },
  /**
   * add Preview url
   * @param {import('../types').Invoice} invoice
   * @param {Transaction=} t
   */
  async addPreviewPdf(invoice, t) {
    try {
      let _invoice = await Invoice.findOne({
        where: { invoiceId: invoice.invoiceId },
        include: [{ model: InvoiceProduct }],
        transaction: t,
      });

      if (
        !_invoice.previewPdfFile ||
        !fs.existsSync(
          _invoice.previewPdfFile.replace('resources', PUBLIC_UPLOAD_DIR),
        )
      ) {
        let filePath = `${PUBLIC_UPLOAD_DIR}/invoice-preview/${_invoice.invoiceId}`;
        let fileName = 'preview.pdf';

        // if (fs.existsSync(filePath)) {
        //   fs.rmSync(filePath, { recursive: true });
        // }

        if (!fs.existsSync(filePath)) {
          fs.mkdirSync(filePath);
        }

        let { buf, contentType } = await createPreviewInvoicePdf({
          invoice: _invoice.toJSON(),
        });

        fs.writeFileSync(`${filePath}/${fileName}`, buf);

        await _invoice.update(
          {
            previewPdfFile: `resources/invoice-preview/${_invoice.invoiceId}/${fileName}`,
          },
          { transaction: t },
        );
      }

      Object.assign(invoice, _invoice.toJSON());
    } catch (error) {
      console.warn(error);
    }

    return invoice;
  },
  /**
   * add Preview url
   * @param {import('../types').Invoice} invoice
   * @param {import('../types').Invoice} xml
   */
  compareXMLandInvoice(xml, invoice) {
    if (
      xml.buyerTaxCode === invoice.buyerTaxCode &&
      // xml.buyerName === invoice.buyerName &&
      // xml.buyerAddress === invoice.buyerAddress &&
      Number(xml.invoiceNumber) === Number(invoice.invoiceNumber) &&
      // xml.finalAmount == invoice.finalAmount &&
      // xml.finalAmountInWord === invoice.finalAmountInWord &&
      // xml.paymentCurrency === invoice.paymentCurrency &&
      xml.invoiceTypeCode === invoice.invoiceTypeCode &&
      xml.serial === invoice.serial &&
      xml.invoiceType === invoice.invoiceType &&
      xml.sellerTaxCode === invoice.sellerTaxCode
      // xml.sellerName === invoice.sellerName
      // xml.sellerAddress === invoice.sellerAddress &&
      // xml.sellerBankName === invoice.sellerBankName &&
      // xml.sellerBankAccount === invoice.sellerBankAccount &&
      // xml.invoiceDate === invoice.invoiceDate &&
      // xml.amountVat === invoice.amountVat &&
      // xml.amountBeforeVat === invoice.amountBeforeVat &&
      // xml.totalDiscountAmount === invoice.totalDiscountAmount
    ) {
      return true;
    } else {
      return false;
    }
  },
  /**
   *
   * @param {import('../types').Invoice} invoice
   */
  readX509Cert(invoice) {
    let cqtCTS = invoice.cqtCKS,
      sellerCTS = invoice.sellerCKS;

    try {
      if (invoice.cqtCKS) {
        let x = new X509Certificate(
          `-----BEGIN CERTIFICATE-----\n${invoice.cqtCKS}\n-----END CERTIFICATE-----`,
        );

        cqtCTS = {
          Issuer: x.issuer,
          SigningTime: invoice.cqtSignDate,
          Subject: x.subject,
          NotBefore: x.validFrom,
          NotAfter: x.validTo,
        };
      }
    } catch (error) {
      console.log(error);
    }

    try {
      if (invoice.sellerCKS) {
        let x = new X509Certificate(
          `-----BEGIN CERTIFICATE-----\n${invoice.sellerCKS}\n-----END CERTIFICATE-----`,
        );

        sellerCTS = {
          Issuer: x.issuer,
          SigningTime: invoice.cqtSignDate,
          Subject: x.subject,
          NotBefore: x.validFrom,
          NotAfter: x.validTo,
        };
      }
    } catch (error) {
      console.log(error);
    }

    if (utils.isJSONStringified(cqtCTS)) {
      cqtCTS = JSON.parse(cqtCTS);
    }

    if (utils.isJSONStringified(sellerCTS)) {
      sellerCTS = JSON.parse(sellerCTS);
    }

    return { cqtCTS, sellerCTS };
  },
  /**
   *
   * @param {import('../types').Invoice} invoice
   * @param {Transaction} t
   */
  async mergeWithXML(invoice, t) {
    try {
      let _invoice = await Invoice.findOne({
        where: { invoiceId: invoice.invoiceId },
        transaction: t,
      });

      if (
        _invoice.xmlFile &&
        fs.readFileSync(_invoice.xmlFile.replace('uploads', UPLOAD_DIR)) &&
        !_invoice.parsedFromXml
      ) {
        let fromXml = await this.invoiceFromXml(
          fs.readFileSync(_invoice.xmlFile.replace('uploads', UPLOAD_DIR)),
        );
        await _invoice.update(
          {
            ...fromXml,
            supplierReferenceCode:
              !_invoice.supplierReferenceCode ||
              _invoice.supplierReferenceCode === _invoice.maCQT
                ? fromXml.supplierReferenceCode
                : _invoice.supplierReferenceCode,
            cqtCKS: undefined,
            sellerCKS: undefined,
            parsedFromXml: true,
          },
          { transaction: t },
        );

        if (fromXml.InvoiceProducts) {
          await InvoiceProduct.destroy({
            where: { invoiceId: _invoice.invoiceId },
            transaction: t,
          });

          await InvoiceProduct.bulkCreate(
            fromXml.InvoiceProducts.map(item => {
              item.invoiceId = _invoice.invoiceId;

              return item;
            }),
            { transaction: t },
          );
        }

        /* read cert */
        if (fromXml.cqtCKS || fromXml.sellerCKS) {
          let { cqtCTS, sellerCTS } = this.readX509Cert(fromXml);

          await _invoice.update(
            { cqtCKS: cqtCTS, sellerCKS: sellerCTS },
            { transaction: t },
          );
        }
      } else if (
        _invoice.originXmlFile &&
        fs.readFileSync(
          _invoice.originXmlFile.replace('uploads', UPLOAD_DIR),
        ) &&
        !_invoice.parsedFromXml
      ) {
        let fromXml = await this.invoiceFromXml(
          fs.readFileSync(
            _invoice.originXmlFile.replace('uploads', UPLOAD_DIR),
          ),
        );
        await _invoice.update(
          {
            ...fromXml,
            supplierReferenceCode:
              _invoice.supplierReferenceCode ?? fromXml.supplierReferenceCode,
            cqtCKS: undefined,
            sellerCKS: undefined,
            parsedFromXml: true,
          },
          { transaction: t },
        );

        if (fromXml.InvoiceProducts) {
          await InvoiceProduct.destroy({
            where: { invoiceId: _invoice.invoiceId },
            transaction: t,
          });

          await InvoiceProduct.bulkCreate(
            fromXml.InvoiceProducts.map(item => {
              item.invoiceId = _invoice.invoiceId;

              return item;
            }),
            { transaction: t },
          );
        }

        /* read cert */
        if (fromXml.cqtCKS || fromXml.sellerCKS) {
          let { cqtCTS, sellerCTS } = this.readX509Cert(fromXml);

          await _invoice.update(
            { cqtCKS: cqtCTS, sellerCKS: sellerCTS },
            { transaction: t },
          );
        }
      }

      Object.assign(invoice, _invoice.toJSON());
    } catch (error) {
      console.log(error);
    }

    return invoice;
  },
  /**
   *
   * @param {import('../types').Invoice} invoice
   */
  async getSupplierReference(invoice) {
    if (invoice.supplierTaxCode) {
      let supplierOrg = await InvoiceSupplierOrg.findOne({
        where: { taxCode: invoice.supplierTaxCode },
      });

      if (supplierOrg) {
        invoice.supplierLookupWebsite = supplierOrg.lookupWebsite;
      }
    }

    let { supplierLookupWebsite } = invoice;

    return { supplierLookupWebsite };
  },
  /**
   * lấy xml từ CQT
   * @param {import('../types').Invoice} invoice
   * @param {{organizationDepartment: import('../types').OrganizationDepartment,organization: import('../types').Organization}} params
   * @param {Transaction} t
   *
   * @returns {Promise<import('../types').Invoice>}
   */
  async getCqtXML(invoice, { organization, organizationDepartment }, t) {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async resolve => {
      if (
        invoice.xmlCqtNotExist != true &&
        !!invoice.invoiceCQTId &&
        (!invoice.xmlFile ||
          !fs.readFileSync(invoice.xmlFile.replace('uploads', UPLOAD_DIR)))
      ) {
        try {
          let token, username, password, expiration;

          if (organization) {
            token = organization.cqtToken;
            expiration = organization.cqtTokenExpiration;
            username = organization.username;
            password = organization.password;
          } else {
            token = organizationDepartment.cqtToken;
            expiration = organizationDepartment.cqtTokenExpiration;
            username = organizationDepartment.username;
            password = organizationDepartment.password;
          }

          if (
            (!expiration || expiration <= new Date()) &&
            username &&
            password
          ) {
            token = (await cqt_authenticate(username, password)).token;
            if (!token) throw new Error(ERROR_CONNECT_CQT_FAIL);

            expiration = moment(new Date()).add(1, 'days').toDate();
            if (organizationDepartment) {
              await OrganizationDepartment.update(
                {
                  cqtToken: token,
                  cqtTokenExpiration: expiration,
                },
                {
                  where: {
                    organizationDepartmentId:
                      organizationDepartment.organizationDepartmentId,
                  },
                },
              );

              organizationDepartment.cqtToken = token;
              organizationDepartment.cqtTokenExpiration = expiration;
            }

            if (organization) {
              await Organization.update(
                {
                  cqtToken: token,
                  cqtTokenExpiration: expiration,
                },
                { where: { organizationId: organization.organizationId } },
              );

              organization.cqtToken = token;
              organization.cqtTokenExpiration = expiration;
            }
          }

          let data = await cqt_export_xml(invoice, {
            Authorization: `Bearer ${token}`,
          });

          if (!data) {
            await Invoice.update(
              {
                xmlCqtNotExist: true,
              },
              { where: { invoiceId: invoice.invoiceId }, transaction: t },
            );

            invoice.xmlCqtNotExist = true;

            resolve(invoice);
            return;
          }

          // fs.writeFileSync(`${TEMP_UPLOAD_DIR}/${utils.generateRandomStr()}-temp.zip`,data)
          // let
          let folderPath = `${TEMP_UPLOAD_DIR}/${utils.generateRandomStr()}-unzipped`;

          if (!fs.existsSync(folderPath)) {
            fs.mkdirSync(folderPath);
          }
          /* FIX sometimes get errors */
          // await utils.sleep(10);

          await decompress(data, folderPath)
            .catch(err => {
              throw err;
            })
            .then(async files => {
              if (fs.existsSync(`${folderPath}/invoice.xml`)) {
                let filepath = `${UPLOAD_DIR}/invoice-xml/${invoice.invoiceId}`;
                let filename = `invoice.xml`;

                if (!fs.existsSync(filepath)) {
                  fs.mkdirSync(filepath);
                }

                fs.copyFileSync(
                  `${folderPath}/invoice.xml`,
                  `${filepath}/${filename}`,
                );

                await Invoice.update(
                  {
                    xmlFile: `uploads/invoice-xml/${invoice.invoiceId}/${filename}`,
                  },
                  { where: { invoiceId: invoice.invoiceId }, transaction: t },
                );

                invoice.xmlFile = `uploads/invoice-xml/${invoice.invoiceId}/${filename}`;

                fs.rmSync(folderPath, { recursive: true });

                /* TODO */
                /* if statusInvoice = 2|3 */
                let invoiceFromXml = await this.invoiceFromXml(
                  fs.readFileSync(
                    invoice.xmlFile.replace('uploads', UPLOAD_DIR),
                  ),
                );
                if (
                  invoiceFromXml.statusInvoice == INVOICE_STATUS[2] ||
                  invoiceFromXml.statusInvoice == INVOICE_STATUS[3]
                ) {
                  if (invoiceFromXml.statusInvoice == INVOICE_STATUS[2]) {
                    invoice.statusInvoice == INVOICE_STATUS[4];
                  } else if (
                    invoiceFromXml.statusInvoice == INVOICE_STATUS[3]
                  ) {
                    invoice.statusInvoice == INVOICE_STATUS[5];
                  }

                  await Invoice.update(
                    {
                      statusInvoice: invoice.statusInvoice,
                      statusInvoiceText:
                        INVOICE_STATUS_TEXT[invoice.statusInvoice],
                    },
                    { where: { invoiceId: invoice.invoiceId }, transaction: t },
                  );
                }
              }

              resolve(invoice);
            });
        } catch (error) {
          // console.warn(error);
          resolve(invoice);
        }
      } else {
        resolve(invoice);
      }
    });
  },
  /**
   * lấy detail invoice từ CQT
   * @param {import('../types').Invoice} invoice
   * @param {{organizationDepartment: import('../types').OrganizationDepartment,organization: import('../types').Organization}} params
   * @param {Transaction} t
   *
   * @returns {Promise<import('../types').Invoice>}
   */
  async getCqtDetailInvoice(
    invoice,
    { organization, organizationDepartment },
    t,
  ) {
    if (
      invoice.invoiceCQTId &&
      invoice.detailCqtNotExist != true &&
      !invoice.parsedDetailFromCqt
    ) {
      try {
        let token, username, password, expiration;

        if (organization) {
          token = organization.cqtToken;
          expiration = organization.cqtTokenExpiration;
          username = organization.username;
          password = organization.password;
        } else {
          token = organizationDepartment.cqtToken;
          expiration = organizationDepartment.cqtTokenExpiration;
          username = organizationDepartment.username;
          password = organizationDepartment.password;
        }

        if ((!expiration || expiration <= new Date()) && username && password) {
          token = (await cqt_authenticate(username, password)).token;
          if (!token) throw new Error(ERROR_CONNECT_CQT_FAIL);

          expiration = moment(new Date()).add(1, 'days').toDate();
          if (organizationDepartment) {
            await OrganizationDepartment.update(
              {
                cqtToken: token,
                cqtTokenExpiration: expiration,
              },
              {
                where: {
                  organizationDepartmentId:
                    organizationDepartment.organizationDepartmentId,
                },
              },
            );

            organizationDepartment.cqtToken = token;
            organizationDepartment.cqtTokenExpiration = expiration;
          }

          if (organization) {
            await Organization.update(
              {
                cqtToken: token,
                cqtTokenExpiration: expiration,
              },
              { where: { organizationId: organization.organizationId } },
            );

            organization.cqtToken = token;
            organization.cqtTokenExpiration = expiration;
          }
        }

        let data = await cqt_detail_invoice(invoice, {
          Authorization: `Bearer ${token}`,
        });

        if (!data) {
          await Invoice.update(
            { detailCqtNotExist: true },
            {
              where: { invoiceId: invoice.invoiceId },
              transaction: t,
            },
          );

          invoice.detailCqtNotExist = true;

          return invoice;
        }

        let invoiceFromCqt = cqt_toInvoice(data);

        invoiceFromCqt.parsedDetailFromCqt = true;
        invoiceFromCqt.typeInvoiceReceive = TYPE_INVOICE_RECEIVE[3];

        await Invoice.update(invoiceFromCqt, {
          where: { invoiceId: invoice.invoiceId },
          transaction: t,
        });

        if (invoiceFromCqt.InvoiceProducts) {
          await InvoiceProduct.destroy({
            where: { invoiceId: invoice.invoiceId },
            transaction: t,
          });

          await InvoiceProduct.bulkCreate(
            invoiceFromCqt.InvoiceProducts.map(item => {
              item.invoiceId = invoice.invoiceId;
              return item;
            }),
            { transaction: t },
          );
        }

        Object.assign(invoice, invoiceFromCqt);
      } catch (error) {
        console.warn(error);
      }
    }

    return invoice;
  },
  /**
   *
   * @param {import('../types').Invoice[]} invoices
   * @param {{organization:import('../types').Organization, organizationDepartment:import('../types').OrganizationDepartment}} props
   */
  async fetchMultipleFromCqt(
    invoices,
    { organization, organizationDepartment },
  ) {
    for (let _invoice of invoices) {
      if (_invoice.invoiceCQTId) {
        if (!_invoice.parsedDetailFromCqt) {
          const t = undefined;
          _invoice = await this.getCqtDetailInvoice(
            _invoice,
            {
              organization,
              organizationDepartment,
            },
            t,
          );

          _invoice = await this.getCqtXML(
            _invoice,
            {
              organization,
              organizationDepartment,
            },
            t,
          );

          _invoice = await this.mergeWithXML(_invoice, t);

          // await t.commit();
          if (
            !_invoice.previewPdfFile ||
            !fs.existsSync(
              _invoice.previewPdfFile.replace('resources', PUBLIC_UPLOAD_DIR),
            )
          ) {
            _invoice = await this.addPreviewPdf(_invoice);
          }
        }
      }
    }

    return invoices;
  },
  /**
   *
   * @param {import('../types').Invoice[]} invoices
   */
  async fetchMultipleFromSupplier(invoices) {
    for (let _invoice of invoices) {
      if (_invoice.supplierKey) {
        const t = undefined;

        _invoice = await this.mergeWithSupplierInvoice(_invoice, t);

        _invoice = await this.mergeWithXML(_invoice, t);

        /* get lookup website */
        if (!_invoice.supplierLookupWebsite) {
          let { supplierLookupWebsite } = await this.getSupplierReference(
            _invoice,
          );

          if (!supplierLookupWebsite) {
            supplierLookupWebsite = await this.findSpecialLookupWebsite(
              _invoice,
            );
          }

          if (supplierLookupWebsite) {
            _invoice.supplierLookupWebsite = supplierLookupWebsite;

            await Invoice.update(
              { supplierLookupWebsite },
              { where: { invoiceId: _invoice.invoiceId }, transaction: t },
            );
          }
        }

        if (
          !_invoice.previewPdfFile ||
          !fs.existsSync(
            _invoice.previewPdfFile.replace('resources', PUBLIC_UPLOAD_DIR),
          )
        ) {
          _invoice = await this.addPreviewPdf(_invoice, t);
        }

        // await t.commit();
      }
    }

    return invoices;
  },
  /**
   * Lưu HĐ vào db.Invoices
   * @param {import('../types').Invoice[]} arrInvoices
   * @param {{organizationId: number, organizationDepartmentId:number, taxCode:string, maxEntries?:number}} params
   * @param {Transaction} t
   */
  async saveToInvoice(
    arrInvoices,
    { organizationId, organizationDepartmentId, taxCode, maxEntries = 0 },
    t,
  ) {
    // let findInvoices = await Invoice.findAll({
    //   where: { organizationId, organizationDepartmentId },
    //   transaction: t,
    // });

    // arrInvoices = arrInvoices.filter(
    //   i => i.buyerTaxCode == taxCode || i.sellerTaxCode == taxCode,
    // );

    /**
     * @type {import('../types').PartnerCompany[]}
     */
    let buyers = [];
    /**
     * @type {import('../types').PartnerCompany[]}
     */
    let sellers = [];

    let created = [];
    for (let data of arrInvoices) {
      // let transaction = await Sequelize.transaction({ transaction: t });
      let invoiceCategory = null;

      if (data.sellerTaxCode == taxCode) {
        invoiceCategory = INVOICE_CATEGORY.OUTPUT_INVOICE;
      } else if (data.buyerTaxCode == taxCode) {
        invoiceCategory = INVOICE_CATEGORY.INPUT_INVOICE;
      }

      data.invoiceCategory = invoiceCategory;

      data.organizationId = organizationId;
      data.organizationDepartmentId = organizationDepartmentId;

      /**
       * @type {[Model<import('../types').Invoice>, boolean]}
       */
      let [invoice, createdNew] = [];

      [invoice, createdNew] =
        created.length == maxEntries
          ? [
              (await Invoice.findOne({
                where: {
                  invoiceNumber: Number(data.invoiceNumber),
                  invoiceTypeCode: data.invoiceTypeCode,
                  serial: data.serial,
                  sellerTaxCode: data.sellerTaxCode,
                  organizationId,
                  organizationDepartmentId,
                },
                transaction: t,
              })) ?? null,
              false,
            ]
          : await Invoice.findOrCreate({
              where: {
                invoiceNumber: Number(data.invoiceNumber),
                invoiceTypeCode: data.invoiceTypeCode,
                serial: data.serial,
                sellerTaxCode: data.sellerTaxCode,
                organizationId,
                organizationDepartmentId,
              },
              defaults: data,
              transaction: t,
            });

      if (!invoice) continue;

      if (createdNew) {
        created.push(invoice);
      }

      let invoiceCheckStatus;
      /* enable re-parsed */
      if (createdNew) {
        console.log('Created new invoice', {
          invoiceNumber: Number(data.invoiceNumber),
          invoiceTypeCode: data.invoiceTypeCode,
          serial: data.serial,
          sellerTaxCode: data.sellerTaxCode,
          organizationId,
          organizationDepartmentId,
          invoiceDate: data.invoiceDate,
        });
        /* parsed */
        invoice = await invoice.reload({
          include: [
            { model: db.Organization, include: [{ model: db.Organization }] },
            {
              model: db.OrganizationDepartment,
              include: [
                {
                  model: db.Organization,
                  as: 'OrganizationBranch',
                  include: [{ model: db.Organization }],
                },
              ],
            },

            { model: db.InvoiceValidate },

            { model: db.InvoiceProduct },
            { model: db.TaxReduceValidate },
          ],
          transaction: t,
        });

        if (!invoice) continue;

        // await invoice.update(
        //   {
        //     parsedDetailFromCqt: false,
        //     parsedDetailFromNCC: false,
        //     parsedFromXml: false,
        //     xmlCqtNotExist: false,
        //     detailCqtNotExist: false,
        //     xmlNCCNotExist: false,
        //     pdfNCCNotExist: false,
        //     reduceTaxResolution: null,
        //   },
        //   { transaction: t },
        // );

        /* get detail from cqt */
        await this.getCqtDetailInvoice(
          invoice,
          {
            organization: invoice.Organization,
            organizationDepartment: invoice.OrganizationDepartment,
          },
          t,
        );

        /* get xml from cqt */
        await this.getCqtXML(
          invoice,
          {
            organization: invoice.Organization,
            organizationDepartment: invoice.OrganizationDepartment,
          },
          t,
        );
        /* from from supplier e.g Vietinvoice */
        await this.mergeWithSupplierInvoice(invoice, t);
        /* merge origin invoice */

        /* merge xml */
        await this.mergeWithXML(invoice, t);

        /* get lookup website */
        if (!invoice.supplierLookupWebsite) {
          let { supplierLookupWebsite } = await this.getSupplierReference(
            invoice,
          );

          if (!supplierLookupWebsite) {
            supplierLookupWebsite = await this.findSpecialLookupWebsite(
              invoice,
            );
          }

          if (supplierLookupWebsite) {
            await invoice.update({ supplierLookupWebsite }, { transaction: t });
          }
        }

        await this.addPreviewPdf(invoice, t);

        await this.validateVatReduce(invoice, t);

        const isCheckCQT = await cqt_validate_invoice(invoice, t);

        invoice.setDataValue('cqtInvoiceHistoryId', data.cqtInvoiceHistoryId);

        invoiceCheckStatus = await check_invoice(invoice, t);

        /**
         * @type {import('../types').PartnerCompany}
         */
        let partnerCompany;
        if (invoice.sellerTaxCode == taxCode) {
          buyers.push(
            (partnerCompany = {
              taxCode: invoice.buyerTaxCode,
              partnerCompanyName: invoice.buyerName,
              address: invoice.buyerAddress,
              bankAccountNumber: invoice.buyerBankAccount,
              bankAccountName: invoice.buyerBankName,
              organizationDepartmentId: organizationDepartmentId,
              organizationId: organizationId,
            }),
          );
        } else if (invoice.buyerTaxCode == taxCode) {
          sellers.push(
            (partnerCompany = {
              taxCode: invoice.sellerTaxCode,
              partnerCompanyName: invoice.sellerName,
              address: invoice.sellerAddress,
              bankAccountNumber: invoice.sellerBankAccount,
              bankAccountName: invoice.sellerBankName,
              organizationDepartmentId: organizationDepartmentId,
              organizationId: organizationId,
            }),
          );
        }

        if (partnerCompany) {
          let _rs = await nntHelper
            .getCompany(partnerCompany.taxCode)
            .catch(console.log);

          if (_rs) {
            let status = PARTNER_COMPANY_STATUS.UNKNOWN;
            switch (utils.escapeStr(_rs.status)) {
              case utils.escapeStr('Đang hoạt động (đã được cấp GCN ĐKT)'):
                status = PARTNER_COMPANY_STATUS.ACTIVE;
                break;
              case utils.escapeStr('Không hoạt động tại địa chỉ đã đăng ký'):
                status = PARTNER_COMPANY_STATUS.INACTIVE;
                break;
              case utils.escapeStr('Tạm nghỉ kinh doanh có thời hạn'):
                status = PARTNER_COMPANY_STATUS.INACTIVE;
                break;
              case utils.escapeStr(
                'Ngừng hoạt động nhưng chưa hoàn thành thủ tục đóng MST',
              ):
                status = PARTNER_COMPANY_STATUS.INACTIVE;
                break;
            }

            Object.assign(partnerCompany, {
              nntStatus: _rs.status ? `NNT ${_rs.status}` : undefined,
              status,
              dateCheck: new Date(),
              partnerCompanyName: _rs.companyName,
              address: _rs.address,
            });
          }
        }

        await db.PartnerCompany.bulkCreate(
          buyers
            .filter(i => i.taxCode)
            .map(item => {
              return item;
            }),
          {
            updateOnDuplicate: [
              'address',
              'bankAccountNumber',
              'bankAccountName',
            ],
            transaction: t,
          },
        );

        await db.PartnerCompany.bulkCreate(
          sellers
            .filter(i => i.taxCode)
            .map(item => {
              return item;
            }),
          {
            updateOnDuplicate: [
              'address',
              'bankAccountNumber',
              'bankAccountName',
            ],
            transaction: t,
          },
        );
      } else {
        invoiceCheckStatus = await check_invoice(invoice, t);

        if (invoice.invoiceCQTId) {
          if (
            invoice._previousDataValues.statusInvoice !== invoice.statusInvoice
          ) {
            await invoice.update(
              { dateDetectChangeStatus: new Date() },
              { transaction: t },
            );
          }
        }
      }

      if (invoice) {
        if (invoice.cqtInvoiceHistoryId) {
          let cqtInvoiceHistory = await db.CqtInvoiceHistory.findOne({
            where: { cqtInvoiceHistoryId: invoice.cqtInvoiceHistoryId },
            transaction: t,
          });

          await cqtInvoiceHistory.update(
            {
              processed: Math.min(
                cqtInvoiceHistory.succeededDownload,
                cqtInvoiceHistory.processed + 1,
              ),
              processing: Math.max(0, cqtInvoiceHistory.processing - 1),
              duplicated: Math.min(
                cqtInvoiceHistory.succeededDownload,
                cqtInvoiceHistory.duplicated +
                  (invoiceCheckStatus == INVOICE_CHECK_STATUS[3] ? 1 : 0),
              ),
            },
            { transaction: t },
          );
        }
      }
    }

    return created;
  },
  /**
   *
   * @param {import('../types').Invoice} invoice
   */
  isInvoiceValid(invoice) {
    let invoiceValidate = invoice.InvoiceValidate;
    return (
      invoiceValidate &&
      invoiceValidate.checkDate &&
      invoiceValidate.checkResultBuyerAddress &&
      invoiceValidate.checkResultBuyerName &&
      invoiceValidate.checkResultBuyerTaxCode &&
      invoiceValidate.checkResultHasCQTRecord &&
      invoiceValidate.checkResultHasInvoiceCode &&
      invoiceValidate.checkResultSellerAddress &&
      invoiceValidate.checkResultSellerName &&
      invoiceValidate.checkResultSignatureCQT &&
      invoiceValidate.checkResultSignatureNCC
    );
  },
  /**
   * Ktra mặt hàng giảm thuế
   * @param {import('../types').Invoice} invoice
   * @param {Transaction} t
   */
  async validateVatReduce(invoice, t) {
    try {
      let taxProducts = [];
      if (
        invoice.InvoiceProducts?.length &&
        (taxProducts = invoice.InvoiceProducts.filter(
          item => item.vat == VAT_TYPE['10%'] || item.vat == VAT_TYPE['8%'],
        )).length
      ) {
        if (!invoice.reduceTaxResolution) {
          let versions = (
            await Config.findOne({
              where: { key: `INVOICE_VERSION_APPLIED_TAX_REDUCE` },
              transaction: t,
            })
          )?.value
            .split(',')
            .map(item => item.trim());
          /**
           * Nghij ddinhj
           */
          let resolutions = await Config.findAll({
            where: { key: { [Op.like]: `TAX_PRODUCT_RESOLUTION_%` } },
            transaction: t,
          });

          if (
            versions &&
            versions.includes(invoice.invoiceVersion) &&
            invoice.invoiceTypeCode == INVOICE_TYPE_CODE[1]
          ) {
            let resolution;

            if (
              (resolution = resolutions.find(item => {
                let range = { from: null, to: null };

                [range.from, range.to] = item.value
                  .split('-')
                  .map(value => moment(value, 'DD/MM/YYYY'));

                return moment(invoice.invoiceDate).isBetween(
                  range.from,
                  range.to,
                  'dates',
                  '[]',
                );
              }))
            ) {
              let _invoice = await Invoice.findOne({
                where: { invoiceId: invoice.invoiceId },
                transaction: t,
              });

              await _invoice.update(
                {
                  reduceTaxResolution: {
                    name: resolution.value,
                    key: resolution.key,
                  },
                },
                { transaction: t },
              );

              /* check */
              let reduceTaxProducts = await Product.findAll({
                where: { reduceTax: true },
                transaction: t,
              });

              /**
               * @type {import('../types').TaxReduceValidate[]}
               */
              let taxReduceValidates = [];

              let acceptTaxReduces = await AcceptTaxReduce.findAll({
                where: {
                  organizationDepartmentId: invoice.organizationDepartmentId,
                  organizationId: invoice.organizationId,
                  // productName: { [Op.in]: taxProducts.map(item => item.name) },
                },
                transaction: t,
              });

              for (let product of taxProducts) {
                // found in reduce tax
                let code = product.code;

                let name = product.name;

                if (!name) continue;

                let chunks = name.split(' ');

                let acceptTaxReduce = acceptTaxReduces.find(
                  item =>
                    utils.escapeStr(item.productName) ==
                    utils.escapeStr(product.name),
                );

                if (acceptTaxReduce) {
                  taxReduceValidates.push({
                    invoiceId: _invoice.invoiceId,
                    checkResult:
                      (product.vat != VAT_TYPE['10%'] &&
                        acceptTaxReduce.isTaxReduce) ||
                      (product.vat == VAT_TYPE['10%'] &&
                        !acceptTaxReduce.isTaxReduce)
                        ? TAX_REDUCE_VALIDATE_RESULT.VALID
                        : TAX_REDUCE_VALIDATE_RESULT.INVALID,
                    productName: product.name,
                    vat: product.vat,
                    isReduceTax: !!acceptTaxReduce.isTaxReduce,
                    productCode: product.code,
                  });

                  continue;
                }

                let arrToSort = reduceTaxProducts.map(item => [
                  item.toJSON(),
                  utils.similarity(
                    utils.escapeStr(item.productName),
                    utils.escapeStr(name),
                  ),
                ]);
                utils.mergeSort(
                  arrToSort,
                  0,
                  arrToSort.length - 1,
                  item => item[1],
                );

                let maxSimilarity = arrToSort[arrToSort.length - 1];

                /* accept if found with similarity > 0.5 */
                if (maxSimilarity[1] >= 0.5) {
                  taxReduceValidates.push({
                    invoiceId: _invoice.invoiceId,
                    checkResult:
                      product.vat != VAT_TYPE['10%']
                        ? TAX_REDUCE_VALIDATE_RESULT.VALID
                        : TAX_REDUCE_VALIDATE_RESULT.INVALID,
                    productName: product.name,
                    vat: product.vat,
                    isReduceTax: true,
                    productCode: product.code,
                  });
                } else {
                  taxReduceValidates.push({
                    invoiceId: _invoice.invoiceId,
                    checkResult:
                      product.vat == VAT_TYPE['10%']
                        ? TAX_REDUCE_VALIDATE_RESULT.VALID
                        : TAX_REDUCE_VALIDATE_RESULT.INVALID,
                    productName: product.name,
                    vat: product.vat,
                    isReduceTax: false,
                    productCode: product.code,
                  });
                }
              }

              await TaxReduceValidate.destroy({
                where: { invoiceId: invoice.invoiceId },
                transaction: t,
              });

              let TaxReduceValidates = await TaxReduceValidate.bulkCreate(
                taxReduceValidates.map(item => {
                  item.invoiceId = invoice.invoiceId;

                  return item;
                }),
                { transaction: t },
              );

              if (
                TaxReduceValidates.some(
                  item =>
                    item.checkResult == TAX_REDUCE_VALIDATE_RESULT.INVALID,
                )
              ) {
                await _invoice.update(
                  {
                    statusTax: TAX_STATUS_TEXT['NOT_VALID'],
                  },
                  { transaction: t },
                );
              } else {
                await _invoice.update(
                  {
                    statusTax: TAX_STATUS_TEXT['VALID'],
                  },
                  { transaction: t },
                );
              }

              _invoice.TaxReduceValidates = TaxReduceValidates;

              Object.assign(invoice, _invoice);
            }
          }
        } else {
          return invoice;
        }
      }

      await Invoice.update(
        {
          statusTax: null,
        },
        { where: { invoiceId: invoice.invoiceId }, transaction: t },
      );
    } catch (error) {
      console.log(error);
    }

    return invoice;
  },
  /**
   *
   * @param {string} str1
   * @param {string} str2
   */
  compareInvoiceValue(str1, str2) {
    str1 = _.toLower(str1).replace(/[^a-zA-Z0-9]/g, '');
    str2 = _.toLower(str2).replace(/[^a-zA-Z0-9]/g, '');

    return str1 == str2;
  },
  /**
   *
   * @param {string} serial
   * @returns
   */
  isValidSerial(serial) {
    return serial && ['C', 'K'].some(char => serial.startsWith(char));
  },
  /**
   * use `invoiceNumber`, `sellerTaxCode`, `buyerTaxCode`, `serial`, `invoiceTypeCode`
   * @param {import('../types').Invoice} invoice
   */
  isMalformedInvoice(invoice) {
    if (!invoice) return true;

    if (!utils.isValidNumber(invoice.invoiceNumber)) return true;

    if (!utils.isValidTaxCode(invoice.sellerTaxCode)) return true;

    if (!utils.isValidTaxCode(invoice.buyerTaxCode)) return true;

    // if (!this.isValidSerial(invoice.serial)) return true;

    if (!INVOICE_TYPE_CODE[invoice.invoiceTypeCode]) return true;

    return false;
  },
  /**
   *
   * @param {import('../types').Invoice} invoice
   * @param {{organizationId:number,organizationDepartmentId:number}} param0
   * @param {Transaction} t
   */
  async findDuplicateInvoice(
    invoice,
    { organizationDepartmentId, organizationId },
    t,
  ) {
    let duplicateInvoice = await Invoice.findOne({
      where: {
        organizationDepartmentId,
        organizationId,
        invoiceNumber: Number(invoice.invoiceNumber),
        sellerTaxCode: invoice.sellerTaxCode,
        invoiceTypeCode: invoice.invoiceTypeCode,
        serial: invoice.serial,
        invoiceCategory: { [Op.not]: null },
      },
      transaction: t,
    });

    return duplicateInvoice;
  },
  /**
   * Tìm ra link tra cứu cho các mẫu xml không lấy được từ thẻ như bình thường
   * @param {import('../types').Invoice} invoice
   */
  async findSpecialLookupWebsite(invoice) {
    let xmlContent;
    if (
      invoice.xmlFile &&
      fs.existsSync(invoice.xmlFile.replace('uploads', UPLOAD_DIR))
    ) {
      xmlContent = fs.readFileSync(
        invoice.xmlFile.replace('uploads', UPLOAD_DIR),
        'utf-8',
      );
    } else if (
      invoice.originXmlFile &&
      fs.existsSync(invoice.originXmlFile.replace('uploads', UPLOAD_DIR))
    ) {
      xmlContent = fs.readFileSync(
        invoice.originXmlFile.replace('uploads', UPLOAD_DIR),
        'utf-8',
      );
    }

    if (!xmlContent) {
      return null;
    }

    let invoiceFromXml = await this.xmlToJSON(xmlContent);
    let lookupWebsite = null;

    /* E-INVOICE */
    /* KH của e-invoice. Có Id='data123' ở thẻ HDon */
    if (invoiceFromXml.HDon?.DLHDon?.[0]?.$?.Id === 'data123') {
      let found;
      if (
        (found = invoiceFromXml.HDon?.DLHDon?.[0]?.TTKhac?.[0]?.TTin?.find(
          item => item.TTruong?.[0] == 'ExtHoaDon',
        ))
      ) {
        lookupWebsite = found?.DLieu?.[0]?.DCTC?.[0];

        return lookupWebsite;
      }

      let nbanMST =
        invoiceFromXml.HDon?.DLHDon?.[0]?.NDHDon?.[0]?.NBan?.[0]?.MST?.[0];

      if (nbanMST) {
        let foundOrg = await InvoiceSupplierOrg.findOne({
          where: { taxCode: nbanMST },
        });

        return foundOrg?.lookupWebsite;
      }
    }

    return lookupWebsite;
  },
};
