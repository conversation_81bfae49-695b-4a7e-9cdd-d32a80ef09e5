const utils = require('.');
const {
  PARTNER_COMPANY_TYPE,
  PARTNER_COMPANY_STATUS,
} = require('../configs/constants/company.constant');
const { PartnerCompany, Invoice } = require('../models');
const nntHelper = require('./nnt.helper');

module.exports = {
  /**
   *
   * @param {Object} param0
   * @param {number} param0.organizationId
   * @param {number} param0.organizationDepartmentId
   * @param {string} param0.taxCode
   *
   */
  async synchronize({ organizationDepartmentId, organizationId, taxCode }) {
    let invoices = await Invoice.findAll({
      where: { organizationId, organizationDepartmentId },
    });

    let buyers = invoices
      .filter(item => item.sellerTaxCode == taxCode)
      .map(item => ({
        taxCode: item.buyerTaxCode,
        partnerCompanyName: item.buyerName,
        address: item.buyerAddress,
        bankAccountNumber: item.buyerBankAccount,
        bankAccountName: item.buyerBankName,
      }))
      .filter(item => item.taxCode);

    let sellers = invoices
      .filter(item => item.buyerTaxCode == taxCode)
      .map(item => ({
        taxCode: item.sellerTaxCode,
        partnerCompanyName: item.sellerName,
        address: item.sellerAddress,
        bankAccountNumber: item.sellerBankAccount,
        bankAccountName: item.sellerBankName,
      }))
      .filter(item => item.taxCode);

    await PartnerCompany.bulkCreate(
      buyers.map(item => {
        item.type = PARTNER_COMPANY_TYPE.BUYER;
        Object.assign(item, { organizationDepartmentId, organizationId });

        return item;
      }),
      {
        updateOnDuplicate: ['address', 'bankAccountNumber', 'bankAccountName'],
      },
    );

    await PartnerCompany.bulkCreate(
      sellers.map(item => {
        item.type = PARTNER_COMPANY_TYPE.SELLER;
        Object.assign(item, { organizationDepartmentId, organizationId });

        return item;
      }),
      {
        updateOnDuplicate: ['address', 'bankAccountNumber', 'bankAccountName'],
      },
    );

    /* check status cqt */
    // try {
    //   let arrPartnerCompanies = await PartnerCompany.findAll({
    //     where: { organizationDepartmentId, organizationId },
    //   });

    //   for (let partnerCompany of arrPartnerCompanies) {
    //     let { isExist } = await cqt_regdStatus(partnerCompany.taxCode);

    //     await partnerCompany.update({
    //       dateCheck: new Date(),
    //       status: isExist
    //         ? PARTNER_COMPANY_STATUS.ACTIVE
    //         : PARTNER_COMPANY_STATUS.INACTIVE,
    //     });
    //   }
    // } catch (error) {}

    /* check status nnt */
    try {
      let arrPartnerCompanies = await PartnerCompany.findAll({
        where: { organizationDepartmentId, organizationId },
      });

      for (let partnerCompany of arrPartnerCompanies) {
        let _rs = await nntHelper
          .getCompany(partnerCompany.taxCode)
          .catch(console.log);

        if (_rs) {
          let status = PARTNER_COMPANY_STATUS.UNKNOWN;
          switch (utils.escapeStr(_rs.status)) {
            case utils.escapeStr('Đang hoạt động (đã được cấp GCN ĐKT)'):
              status = PARTNER_COMPANY_STATUS.ACTIVE;
              break;
            case utils.escapeStr('Không hoạt động tại địa chỉ đã đăng ký'):
              status = PARTNER_COMPANY_STATUS.INACTIVE;
              break;
            case utils.escapeStr('Tạm nghỉ kinh doanh có thời hạn'):
              status = PARTNER_COMPANY_STATUS.INACTIVE;
              break;
            case utils.escapeStr(
              'Ngừng hoạt động nhưng chưa hoàn thành thủ tục đóng MST',
            ):
              status = PARTNER_COMPANY_STATUS.INACTIVE;
              break;
          }

          await partnerCompany.update({
            nntStatus: _rs.status ? `NNT ${_rs.status}` : undefined,
            status,
            dateCheck: new Date(),
            partnerCompanyName: _rs.companyName,
            address: _rs.address,
          });
        }
      }
    } catch (error) {}
  },
};
