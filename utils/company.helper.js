const { Transaction, Op } = require('sequelize');
const { isValidNumber, replaceInString, parseBoolean } = require('.');
const { SERVICE } = require('../configs/constants/account.constant');
const { ORGANIZATION_TYPE } = require('../configs/constants/company.constant');
const { ERROR_INVOICE_MAILBOX_USED } = require('../configs/error.vi');
const {
  Company,
  Organization,
  OrganizationDepartment,
  OrganizationAddress,
  Config,
  ResourceHistory,
} = require('../models');
const moment = require('moment');
const { PACKAGE_TYPE } = require('../configs/constants/package.constant');

module.exports = {
  /**
   * <PERSON>tra hộp thư đã được sử dụng (trong toàn cty)
   * @param {string} invoiceMailbox
   * @param {number} companyId
   * @throws {string} if true
   */
  async checkInvoiceMailboxUsed(invoiceMailbox, companyId) {
    let usedMailboxOrg;
    if (
      !!(usedMailboxOrg = await Organization.findOne({
        where: { companyId: companyId, invoiceMailbox },
        include: [
          { association: 'OrganizationHQ' },
          { model: OrganizationAddress },
        ],
      }))
    ) {
      throw new Error(
        replaceInString(ERROR_INVOICE_MAILBOX_USED, {
          invoiceMailbox,
          name: `${
            usedMailboxOrg.organizationType == ORGANIZATION_TYPE.HQ
              ? 'Công ty'
              : 'Chi nhánh'
          } ${
            usedMailboxOrg.organizationName ??
            usedMailboxOrg.OrganizationAddresses?.[0].organizationName
          }${
            usedMailboxOrg.organizationType == ORGANIZATION_TYPE.BRANCH
              ? `, Công ty ${
                  usedMailboxOrg.OrganizationHQ.organizationName ??
                  usedMailboxOrg.OrganizationAddresses?.[0].organizationName
                }`
              : ''
          }`,
        }),
      );
    }

    let usedMailboxDepartment;
    if (
      !!(usedMailboxDepartment = await OrganizationDepartment.findOne({
        where: { invoiceMailbox },
        include: [
          {
            association: 'OrganizationBranch',
            required: true,
            include: [{ association: 'OrganizationHQ', where: { companyId } }],
          },
        ],
      }))
    ) {
      throw new Error(
        replaceInString(ERROR_INVOICE_MAILBOX_USED, {
          invoiceMailbox,
          name: `Phòng ban ${usedMailboxDepartment.departmentName}, Chi nhánh ${usedMailboxDepartment.OrganizationBranch.organizationName}, Công ty ${usedMailboxDepartment.OrganizationBranch.OrganizationHQ.organizationName}`,
        }),
      );
    }

    return false;
  },
  /**
   *
   * @param {number} companyId
   * @param {Transaction} t
   */
  async getRemainResource(companyId, t) {
    let company = await Company.findOne({
      where: { companyId },
      transaction: t,
    });

    if (!company) return null;

    let quantityRemain = company.quantityRemain;
    let promotionRemain = company.promotionRemain;
    let promotionExpired = company.promotionExpired;
    let dateExpiredPackage = company.dateExpiredPackage;

    let service = company.service;

    let licensePackageOffConfig = await Config.findOne({
      where: { key: 'LICENSE_OFF' },
      transaction: t,
    });

    if (service == SERVICE.I_SMART) {
      /* off license for I-SMART */
      if (
        licensePackageOffConfig &&
        parseBoolean(licensePackageOffConfig.value)
      ) {
        dateExpiredPackage = moment()
          .add(100, 'years')
          .format('YYYY-MM-DD HH:mm:ss');
      } else {
      }
    } else if (service == SERVICE.I_PRO) {
    }

    return {
      quantityRemain,
      dateExpiredPackage,
      promotionExpired,
      promotionRemain,
    };
  },
  /**
   *
   * @param {number} companyId
   * @param {Transaction} t
   */
  async getMaxInvoiceEntries(companyId, t) {
    /**
     * SL cos theer trừ
     */
    let maxEntries = [];

    let remainResource = await this.getRemainResource(companyId, t);

    if (remainResource) {
      /*  */
      /* 31/10: không tính thời hạn riêng từng lần cấp. Cộng dồn SL, thời hạn */
      let resourceHistories = await ResourceHistory.findAll({
        where: {
          companyId,
          left: { [Op.gt]: 0 },
          packageType: PACKAGE_TYPE.QUANTITY,
          active: true,
        },
        transaction: t,
      });

      maxEntries = [remainResource.promotionRemain ?? 0]
        .concat(...resourceHistories.map(item => item.left))
        .filter(i => !!i);
    }
    return maxEntries;
  },
};
