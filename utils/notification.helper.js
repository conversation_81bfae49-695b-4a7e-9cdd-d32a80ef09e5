const { Notification, AccountNotification, Account } = require('../models');
const _ = require('lodash');
const { emitSocketEvent } = require('./websocket.helper');
const { WS_CODE } = require('../configs/constants/websocket.constant');
const { TARGET_CODE } = require('../configs/constants/notification.constant');

module.exports = {
  /**
   *
   * @param {import('../types').Notification[]} notifications
   */
  async createNotification(notifications) {
    let notis = await Notification.bulkCreate(notifications);

    let accNotis = await AccountNotification.bulkCreate(
      _.concat(
        ...notifications.map((item, index) => {
          return item.Accounts.map(({ accountId }) => ({
            accountId,
            notificationId: notis[index].notificationId,
          }));
        }),
      ),
    );

    return notis;
  },
  /**
   *
   * @param {import('../types').Notification[]} notifications
   */
  async createNotificationAndSendSyncedEvent(notifications) {
    let notis = await this.createNotification(notifications);

    for (let [index, { Accounts }] of notifications.entries()) {
      let noti = notis[index];

      for (let { accountId } of Accounts) {
        emitSocketEvent({
          accountId: accountId,
          newNotification: true,
          message: {
            code: WS_CODE.CODE_SYNCHRONIZED,
            targetCode: noti.targetCode,
            result: 'success',
          },
        });
      }
    }

    return notis;
  },
  /**
   * Gửi thông baos WS cho toàn bộ USER thuộc CN/TC
   * @param {Object} param0
   * @param {number} param0.organizationId
   * @param {number} param0.organizationDepartmentId
   * @param {string} targetCode
   */
  async syncEventToOrganization(
    { organizationDepartmentId, organizationId },
    targetCode,
  ) {
    let accounts = await Account.findAll({
      where: { organizationDepartmentId, organizationId, active: 1 },
    });

    for (let account of accounts) {
      emitSocketEvent({
        accountId: account.accountId,
        newNotification: true,
        message: {
          code: WS_CODE.CODE_SYNCHRONIZED,
          targetCode: targetCode,
          result: 'success',
        },
      });
    }
  },
};
