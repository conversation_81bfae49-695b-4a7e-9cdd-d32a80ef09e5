const { default: axios } = require('axios');
const utils = require('.');
const { ERROR_CLIENT_CODE_NOT_FOUND } = require('../configs/error.vi');
const {
  QLBH_BASE_URL,
  QLBH_CLIENT_CODE,
  QLBH_SECRET_KEY,
} = require('../configs/env');
const {
  FIND_CONTACT_CUSTOMER,
  UPDATE_CONTACT_CUSTOMER,
  DETAIL_CONTACT_CUSTOMER,
  DELETE_CONTACT_CUSTOMER,
  SUMMARY_CONTACT_CUSTOMER,
  CREATE_CONTACT_CUSTOMER,
} = require('../configs/constants/contact-customer.constant');

const axiosClient = axios.create({
  baseURL: QLBH_BASE_URL,
  headers: { clientcode: QLBH_CLIENT_CODE },
});

module.exports = {
  /**
   *
   * @param {* any } params
   * @returns
   */
  async findContactCustomer(params) {
    params = utils.sortKeys(params);
    let hash = utils.hmac(JSON.stringify(params), QLBH_SECRET_KEY);

    let response = await axiosClient
      .get(FIND_CONTACT_CUSTOMER, {
        params: { ...params, hash },
      })
      .catch(err => err.response);

    return response.data;
  },
  /**
   *
   * @returns
   */
  async summaryContactCustomer() {
    let hash = utils.hmac(JSON.stringify({}), QLBH_SECRET_KEY);

    let response = await axiosClient
      .get(SUMMARY_CONTACT_CUSTOMER, {
        params: { hash },
      })
      .catch(err => err.response);

    return response.data;
  },
  /**
   * @param {any} body
   * @returns
   */
  async createContactCustomer(body) {
    body = utils.sortKeys(body);
    let hash = utils.hmac(JSON.stringify(body), QLBH_SECRET_KEY);
    let response = await axiosClient
      .post(CREATE_CONTACT_CUSTOMER, { ...body, hash })
      .catch(err => err.response);

    return response.data;
  },
  /**
   *
   * @param {number} contactId
   * @param {any} body
   * @returns
   */
  async updateContactCustomer(contactId, body) {
    body = utils.sortKeys(body);
    let hash = utils.hmac(JSON.stringify(body), QLBH_SECRET_KEY);
    let url = utils.replaceInString(UPDATE_CONTACT_CUSTOMER, { contactId });
    let response = await axiosClient
      .post(url, { ...body, hash })
      .catch(err => err.response);

    return response.data;
  },
  /**
   *
   * @param {number} contactId
   * @param {any} body
   * @returns
   */
  async detailContactCustomer(contactId) {
    let params = { contactId };
    let hash = utils.hmac(JSON.stringify(params), QLBH_SECRET_KEY);
    let url = utils.replaceInString(DETAIL_CONTACT_CUSTOMER, { contactId });
    let response = await axiosClient
      .get(url, { params: { ...params, hash } })
      .catch(err => err.response);

    return response.data;
  },
  /**
   * @param {any} body
   * @returns {Promise<import('axios').AxiosResponse>}
   */
  async deleteContactCustomer(body) {
    body = utils.sortKeys(body);
    let hash = utils.hmac(JSON.stringify(body), QLBH_SECRET_KEY);
    let url = utils.replaceInString(DELETE_CONTACT_CUSTOMER);
    let response = await axiosClient
      .delete(url, {
        body: { ...body, hash },
      })
      .catch(err => err.response);

    return response.data;
  },
};
