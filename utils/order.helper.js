const { Transaction } = require('sequelize');
const { PACKAGE_TYPE } = require('../configs/constants/package.constant');
const moment = require('moment');
const { generateRandomStr, now } = require('.');
const {
  TMN_CODE,
  VNP_URL,
  VNP_RETURN_URL,
  VNPAY_LOG_FILE_PATH,
  STATUS_ORDER,
} = require('../configs/constants/order.constant');
const {
  VNPAY_HASH_SECRET,
  SERVER_BASE_URL,
  CLIENT_BASE_URL,
} = require('../configs/env');
const path = require('path');
const { PROJECT_DIR } = require('../env');
const { appendFileSync } = require('fs');
const { EOL } = require('os');
const utils = require('.');
const { sendEmail } = require('./nodemailer.helper');
const { parseFromHTMLTemplate } = require('./email.helper');
const { EMAIL_TYPE } = require('../configs/constants/other.constant');
const {
  TEMPLATE_ORDER_PAYMENT,
  TEMPLATE_ORDER_PAYMENT_FAILED,
  TEMPLATE_ORDER_PAYMENT_SUCCESS,
} = require('../configs/constants/local-path.constant');
const {
  SUBJECT_ORDER_PAYMENT,
  SUBJECT_ORDER_PAYMENT_FAILED,
  SUBJECT_ORDER_PAYMENT_SUCCESS,
} = require('../configs/message.vi');
const {
  SUPPLY_TYPE,
} = require('../configs/constants/resource-history.constant');
const _ = require('lodash');

module.exports = {
  /**
   * confirm order
   * @param {import('../types').Order} order
   * @param {number} companyId
   * @param {Transaction} t
   */
  async completeOrder(order, companyId, t) {
    const db = require('../models');

    let company = await db.Company.findOne({
      where: { companyId },
      transaction: t,
    });

    let orderItems = order.OrderItems;
    let quantity = 0;
    let duration = 0;

    /**
     * @type {import('../types').ResourceHistory[]}
     */
    let resourceHistories = [];
    for (let index = 0; index < orderItems.length; index++) {
      let add;
      const _orderItem = orderItems[index];
      let package = _orderItem.package;
      if (package.packageType === PACKAGE_TYPE.DURATION) {
        duration += add =
          Number(package.duration) * Number(_orderItem.quantity);
      }
      if (package.packageType === PACKAGE_TYPE.LICENSE) {
        duration += add =
          Number(package.duration) * Number(_orderItem.quantity);
      }
      if (package.packageType === PACKAGE_TYPE.QUANTITY) {
        quantity += add =
          Number(package.quantity) * Number(_orderItem.quantity);
      }

      resourceHistories.push({
        package: package,
        packageType: package.packageType,
        companyId: companyId,
        total: add,
        left: add,
        supplyDate: new Date(),
        active: !!company.daKyHopDongNCC,
        supplyType: SUPPLY_TYPE.ORDER,
        order: _.pick(order, Object.keys(db.Order.getAttributes())),
      });
    }

    let quantityRemain = (company.quantityRemain ?? 0) + quantity;
    let quantityPurchased = (company.quantityPurchased ?? 0) + quantity;
    let dateExpiredPackage = moment(company.dateExpiredPackage).isSameOrBefore()
      ? new Date()
      : moment(company.dateExpiredPackage).toDate();

    if (duration === 0) {
      dateExpiredPackage = undefined;
    } else {
      dateExpiredPackage = moment(dateExpiredPackage)
        .add(duration, 'days')
        .toDate();
    }

    /* create resources histories */
    await db.ResourceHistory.bulkCreate(resourceHistories, { transaction: t });

    await company.update(
      {
        quantityRemain,
        quantityPurchased,
        dateExpiredPackage,
      },
      { transaction: t },
    );
  },
  generateOrderCode(prefix = 'ICO') {
    return `${prefix}${moment().format('YYYYDDMM')}${generateRandomStr(
      4,
    ).toUpperCase()}`;
  },
  createVNPAYPaymentUrl(totalPayment, orderCode, ipAddress) {
    let purchaseId = orderCode;

    var ipAddr = ipAddress;

    var tmnCode = TMN_CODE;
    var secretKey = VNPAY_HASH_SECRET;
    var vnpUrl = VNP_URL;
    var returnUrl = `${CLIENT_BASE_URL}${VNP_RETURN_URL}`;

    var date = new Date();

    var createDate = moment(date).format('YYYYMMDDHHmmss');
    var expiredDate = moment(date).add(15, 'minutes').format('YYYYMMDDHHmmss');
    var orderId = purchaseId;
    var amount = totalPayment;
    var bankCode = undefined; //not used

    var orderInfo = `Thanh toán cho đơn hàng ${orderCode}`;
    orderInfo = utils.nonAccentVietnamese(orderInfo);

    var orderType = 'other'; //not used
    var locale = 'vn';
    if (locale != 'vn' && locale != 'en') {
      locale = 'vn';
    }
    var currCode = 'VND';
    /**
     * @type {import('../types').VNPAY_PaymentParams}
     */
    var vnp_Params = {};

    /* merchant bank info */
    vnp_Params['vnp_Version'] = '2.1.0';
    vnp_Params['vnp_Command'] = 'pay';
    vnp_Params['vnp_TmnCode'] = tmnCode;
    // vnp_Params['vnp_Merchant'] = ''
    vnp_Params['vnp_Locale'] = locale;
    vnp_Params['vnp_CurrCode'] = currCode;
    vnp_Params['vnp_TxnRef'] =
      orderId + ' ' + moment().format('DD/MM/YYYY HH:mm:ss');
    vnp_Params['vnp_OrderInfo'] = orderInfo;
    if (!!orderType) vnp_Params['vnp_OrderType'] = orderType;
    vnp_Params['vnp_Amount'] = amount * 100;
    vnp_Params['vnp_ReturnUrl'] = returnUrl;
    vnp_Params['vnp_IpAddr'] = ipAddr;
    vnp_Params['vnp_CreateDate'] = createDate;
    vnp_Params['vnp_ExpireDate'] = expiredDate;
    if (bankCode !== null && bankCode !== '' && bankCode !== undefined) {
      vnp_Params['vnp_BankCode'] = bankCode;
    }

    vnp_Params = sortObject(vnp_Params);

    console.log({ vnp_Params });

    var querystring = require('qs');
    var signData = querystring.stringify(vnp_Params, { encode: false });
    var crypto = require('crypto');
    var hmac = crypto.createHmac('sha512', secretKey);
    var signed = hmac.update(Buffer.from(signData, 'utf-8')).digest('hex');
    vnp_Params['vnp_SecureHash'] = signed;
    vnpUrl += '?' + querystring.stringify(vnp_Params, { encode: false });

    return vnpUrl;
  },
  /**
   * Log vnpay ra file log
   * @param {'VNPAY'|'SERVER'} sTag
   * @param {{response?:string,request?:string,ipAddr?:string}} data
   */
  logVNPAY(sTag, data) {
    try {
      let vn_log_file_path = path.join(PROJECT_DIR, VNPAY_LOG_FILE_PATH);

      appendFileSync(
        vn_log_file_path,
        `[${sTag}] ${
          sTag == 'VNPAY' ? ` - ${data?.ipAddr}` : ''
        } ${now()}: ${JSON.stringify(data)}${EOL}`,
        { encoding: 'utf8', flag: 'as+' },
      );
    } catch (error) {
      console.warn(error);
    }
  },
  /**
   * @param {Object} param
   * @param {import('../types').Order} param.order
   */
  async sendEmailResultOrder({ order }) {
    await sendEmail({
      emailType: EMAIL_TYPE.OTHER,
      html:
        order.statusOrder == STATUS_ORDER.COMPLETE
          ? await parseFromHTMLTemplate(TEMPLATE_ORDER_PAYMENT_SUCCESS, {
              order,
            })
          : await parseFromHTMLTemplate(TEMPLATE_ORDER_PAYMENT_FAILED, {
              order,
            }),
      recipients: [
        {
          name: order.companyName,
          email: order?.orderEmail,
          taxCode: order?.taxCode,
        },
      ],

      subject:
        order.statusOrder == STATUS_ORDER.COMPLETE
          ? SUBJECT_ORDER_PAYMENT_SUCCESS
          : SUBJECT_ORDER_PAYMENT_FAILED,

      accountId: order.accountId,
    });
  },
};

/**
 * sort for vnpay
 * @param {any} obj
 * @returns
 */
function sortObject(obj) {
  var sorted = {};
  var str = [];
  var key;
  for (key in obj) {
    // eslint-disable-next-line no-prototype-builtins
    if (obj.hasOwnProperty(key)) {
      str.push(encodeURIComponent(key));
    }
  }
  str.sort();
  for (key = 0; key < str.length; key++) {
    sorted[str[key]] = encodeURIComponent(obj[str[key]]).replace(/%20/g, '+');
  }
  return sorted;
}
