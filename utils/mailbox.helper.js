const { default: axios } = require('axios');
const {
  ZIMBRA_SOAP_BASE_URL,
  ZIMBRA_SOAP_ADMIN_AUTH_REQUEST,
  ZIMBRA_SOAP_DETAIL_ACCOUNT,
  ZIMBRA_ADMIN_EMAIL_ADDRESS,
  ZIMBRA_ADMIN_PASSWORD,
  ZIMBRA_DEFAULT_EMAIL_PASSWORD,
  ZIMBRA_SOAP_CREATE_ACCOUNT,
  ZIMBRA_SOAP_DELETE_ACCOUNT,
  ZIMBRA_MAIL_HOST_SERVER,
  STATUS_VALID_MAIL,
  ZIMBRA_SOAP_SET_PASSWORD,
} = require('../configs/constants/mail.constant');
const _ = require('lodash');
const moment = require('moment');
const {
  ERROR_INVALID_ADMIN_MAILBOX_CREDENTIAL,
} = require('../configs/error.vi');
const {
  OrganizationDepartment,
  Organization,
  Mail,
  StatusMail,
  InvoiceProduct,
  Invoice,
  Sequelize,
} = require('../models');
const Imap = require('imap');
const { simpleParser } = require('mailparser');
const fs = require('fs');
const utils = require('.');
const { UPLOAD_DIR } = require('../configs/env');
const invoiceHelper = require('./invoice.helper');
const { cqt_validate_invoice } = require('./cqt.helper');
const notificationHelper = require('./notification.helper');
const { TARGET_CODE } = require('../configs/constants/notification.constant');
const {
  TYPE_INVOICE_RECEIVE,
} = require('../configs/constants/invoice.constant');
const { ISOLATION_LEVELS } = require('sequelize/lib/transaction');
const https = require('https');
const crypto = require('crypto');

const axiosClient = axios.create({
  baseURL: ZIMBRA_SOAP_BASE_URL,
  headers: { 'Content-Type': 'application/xml' },
  httpsAgent: new https.Agent({
    // for self signed you could also add
    // rejectUnauthorized: false,

    // allow legacy server
    secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
  }),
});

let tokenExpire = undefined,
  cookie = undefined;

module.exports = {
  /**
   *
   * @param {Object} param
   * @param {number} param.organizationId
   * @param {number} param.organizationDepartmentId
   * @param {string} param.taxCode
   *
   *
   */
  async synchronizeMailbox({
    organizationId,
    organizationDepartmentId,
    taxCode,
  }) {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async resolve => {
      /* TODO */
      const imapConfig = {
        user: undefined,
        password: ZIMBRA_DEFAULT_EMAIL_PASSWORD,
        host: ZIMBRA_MAIL_HOST_SERVER,
        port: 993,
        tls: true,
      };

      if (organizationId) {
        let organization = await Organization.findOne({
          where: { organizationId },
        });

        imapConfig.user = organization.invoiceMailbox;
      } else if (organizationDepartmentId) {
        let organizationDepartment = await OrganizationDepartment.findOne({
          where: { organizationDepartmentId },
        });
        imapConfig.user = organizationDepartment.invoiceMailbox;
      }

      const imap = new Imap(imapConfig);
      imap.once('error', err => {
        console.warn(err);
        resolve();
      });

      imap.once('end', () => {
        console.log('Connection ended');
        resolve();
      });

      imap.connect();

      imap.on('ready', () => {
        imap.openBox('INBOX', false, () => {
          imap.search(
            ['ALL', ['SINCE', moment().subtract(1, 'days').toDate()]],
            (err, results) => {
              if (err) throw err;
              else if (!results || !results.length) {
                // console.log(
                //   "The server didn't find any emails matching the specified criteria",
                // );
                imap.end();
                resolve(false);
              } else {
                const f = imap.fetch(results, {
                  bodies: '',
                  markSeen: true,
                });
                f.on('message', msg => {
                  msg.on('body', stream => {
                    // console.log('stream', stream);
                    simpleParser(stream, async (_err, parsed) => {
                      // const {from, subject, textAsHtml, text} = parsed;
                      // console.log('=============>',{
                      //   subject: parsed.subject,
                      //   time: moment(parsed.date).format('YYYY/MM/DD HH:mm:ss'),
                      //   attachments: parsed.attachments,
                      //   from: parsed.from,
                      //   html: parsed.html,
                      //   id: parsed.messageId,
                      // });
                      let [mail, createdNew] = await Mail.findOrCreate({
                        where: {
                          organizationDepartmentId,
                          organizationId,
                          messageId: parsed.messageId,
                        },
                        defaults: {
                          organizationDepartmentId,
                          organizationId,
                          messageId: parsed.messageId,
                          subject: parsed.subject,
                          senderEmail: parsed.from.value[0].address,
                          receiveDate: moment(parsed.date).format(
                            'YYYY/MM/DD HH:mm:ss',
                          ),
                          content: parsed.html
                            ? parsed.html
                            : parsed.textAsHtml,
                          senderName: parsed.from.value[0].name,
                        },
                      });

                      // await mail.update({
                      //   subject: parsed.subject,
                      //   senderEmail: parsed.from.value[0].address,
                      //   receiveDate: moment(parsed.date).format(
                      //     'YYYY/MM/DD HH:mm:ss',
                      //   ),
                      //   content: parsed.html ? parsed.html : parsed.textAsHtml,
                      //   messageId: parsed.messageId,
                      //   senderName: parsed.from.value[0].name,
                      // });

                      if (createdNew) {
                        await StatusMail.destroy({
                          where: { mailId: mail.mailId },
                        });

                        if (parsed.attachments?.length) {
                          /**
                           * @type {import('../types').MailAttacment[]}
                           */
                          let attachments = [],
                            xmlFiles = [];
                          let filePath = `${UPLOAD_DIR}/mail-attachment/${mail.messageId}`;
                          if (fs.existsSync(filePath)) {
                            fs.rmSync(filePath, { recursive: true });
                          }
                          fs.mkdirSync(filePath);

                          for (let attachment of parsed.attachments) {
                            let fileName = `${utils.escapeFilename(
                              attachment.filename,
                            )}`;

                            fs.writeFileSync(
                              `${filePath}/${fileName}`,
                              attachment.content,
                            );

                            attachments.push({
                              url: `uploads/mail-attachment/${mail.messageId}/${fileName}`,
                              filename: fileName,
                              contentType: attachment.contentType,
                              size: attachment.size,
                            });

                            if (
                              attachment.contentType == 'application/xml' ||
                              attachment.contentType == 'text/xml'
                            ) {
                              xmlFiles.push(attachment);
                            }
                          }

                          await mail.update({ attachments });

                          /**
                           * @type {import('../types').StatusMail[]}
                           */
                          let statusMails = [];
                          /**
                           * @type {import('../types').StatusMail}
                           */
                          let status = {};

                          if (!xmlFiles.length) {
                            await StatusMail.create({
                              mailId: mail.mailId,
                              statusValidMail:
                                STATUS_VALID_MAIL.INVOICE_NOT_FOUND,
                              count: 0,
                            });
                          } else {
                            let rs = await this.validateEmail([mail], {
                              organizationDepartmentId,
                              organizationId,
                              taxCode,
                            });
                          }
                        } else {
                          await StatusMail.create({
                            mailId: mail.mailId,
                            statusValidMail:
                              STATUS_VALID_MAIL.INVOICE_NOT_FOUND,
                            count: 0,
                          });
                        }

                        await notificationHelper.syncEventToOrganization(
                          {
                            organizationDepartmentId,
                            organizationId,
                          },
                          TARGET_CODE.NEW_MAIL,
                        );
                      }

                      // console.log(mail);
                      /* Make API call to save the data
                   Save the retrieved data into a database.
                   E.t.c
                */
                    });
                  });
                  msg.once('attributes', attrs => {
                    const { uid } = attrs;
                    imap.addFlags(uid, ['\\Seen'], () => {
                      // Mark the email as read after reading it
                      // console.log('Marked as read!');
                    });
                  });
                });
                f.once('error', ex => {
                  console.warn(ex);
                  // return Promise.reject(ex);
                });
                f.once('end', () => {
                  // console.log('Done fetching all messages!');
                  imap.end();
                });
              }
            },
          );
        });
      });
    });
  },
  /**
   *
   * @param {string} emailAddress
   * @param {string=} displayName
   */
  async createMailbox(emailAddress, displayName) {
    let existed = await this.detailMailbox(emailAddress);

    if (!existed) {
      let { cookie: _cookie } = await this.authAdmin();

      let content = `<CreateAccountRequest xmlns="urn:zimbraAdmin" name="${emailAddress}" password="${ZIMBRA_DEFAULT_EMAIL_PASSWORD}">
          <a n="displayName">${displayName ? displayName : emailAddress}</a>
      </CreateAccountRequest>`;

      let response = await axiosClient
        .post(ZIMBRA_SOAP_CREATE_ACCOUNT, formatRequest(content), {
          headers: { Cookie: _cookie },
        })
        .catch(error => {
          console.warn(error);

          return {};
        });

      return { id: response.data.Body.CreateAccountResponse.account[0].id };
    }

    return { id: existed.id };
  },
  /**
   *
   * @param {string} emailAddress
   */
  async deleteMailbox(emailAddress) {
    let existed = await this.detailMailbox(emailAddress);

    if (!!existed) {
      let { cookie: _cookie } = await this.authAdmin();

      let content = `<DeleteAccountRequest xmlns="urn:zimbraAdmin" id="${existed.id}" />`;

      let response = await axiosClient
        .post(ZIMBRA_SOAP_DELETE_ACCOUNT, formatRequest(content), {
          headers: { Cookie: _cookie },
        })
        .catch(error => {
          return {};
        });

      return response.status == 200;
    }

    return false;
  },
  /**
   *
   * @param {string} emailAddress
   */
  async detailMailbox(emailAddress) {
    let { cookie: _cookie } = await this.authAdmin();

    let content = `<GetAccountInfoRequest xmlns="urn:zimbraAdmin">
    <account by="name">${emailAddress}</account>
</GetAccountInfoRequest>`;

    let response = await axiosClient
      .post(ZIMBRA_SOAP_DETAIL_ACCOUNT, formatRequest(content), {
        headers: { Cookie: _cookie },
      })
      .catch(error => {
        console.warn(error);

        return {};
      });

    if (response.status == 200) {
      return {
        id: response.data.Body.GetAccountInfoResponse.a.find(
          item => item.n == 'zimbraId',
        )._content,
      };
    }
    return false;
  },

  /**
   *
   * @param {string} emailAddress
   */
  async changeMailboxPassword(emailAddress) {
    let existed = await this.detailMailbox(emailAddress);

    if (!!existed) {
      let { cookie: _cookie } = await this.authAdmin();

      let content = `<SetPasswordRequest xmlns="urn:zimbraAdmin" id="${existed.id}" newPassword="${ZIMBRA_DEFAULT_EMAIL_PASSWORD}" />`;

      let response = await axiosClient
        .post(ZIMBRA_SOAP_SET_PASSWORD, formatRequest(content), {
          headers: { Cookie: _cookie },
        })
        .catch(error => {
          return {};
        });

      return response.status == 200;
    }

    return false;
  },

  async authAdmin() {
    if (cookie && moment().isBefore(tokenExpire)) {
      return { cookie };
    }

    let content = `<AuthRequest xmlns="urn:zimbraAdmin">
    <name>${ZIMBRA_ADMIN_EMAIL_ADDRESS}</name>
    <password>${ZIMBRA_ADMIN_PASSWORD}</password>
    <csrfTokenSecured>0</csrfTokenSecured>
</AuthRequest>`;

    let response = await axiosClient
      .post(ZIMBRA_SOAP_ADMIN_AUTH_REQUEST, formatRequest(content))
      .catch(error => {
        console.warn(error);

        return {};
      });

    if (response.status == 200) {
      let token = response.data.Body.AuthResponse.authToken[0]._content;
      let lifetime = response.data.Body.AuthResponse.lifetime;

      tokenExpire = moment().add(lifetime, 'milliseconds').toDate();

      cookie = response.headers['set-cookie'];

      return {
        cookie: cookie,
      };
    }
    throw new Error(ERROR_INVALID_ADMIN_MAILBOX_CREDENTIAL);
  },

  /**
   *
   * @param {import('../types').Mail[]} mails
   * @param {Object} param0
   * @param {number} param0.organizationId
   * @param {number} param0.organizationDepartmentId
   * @param {number} param0.taxCode
   *
   */
  async validateEmail(
    mails,
    { organizationDepartmentId, organizationId, taxCode },
  ) {
    /* TODO */
    /**
     * @type {import('../types').StatusMail[]}
     */
    let results = [];

    for (let mail of mails) {
      if (mail.attachments.length) {
        let xmlAttachments = [],
          attachments = _.clone(mail.attachments);

        xmlAttachments = attachments.filter(
          item =>
            item.contentType == 'application/xml' ||
            item.contentType == 'text/xml',
        );

        if (xmlAttachments.length) {
          /* process xml here */

          /**
           * @type {import('../types').StatusMail[]}
           */
          let statusMails = [];

          for (let xml of attachments) {
            if (
              xml.contentType != 'application/xml' &&
              xml.contentType != 'text/xml'
            )
              continue;

            /*  */
            let invoiceFromXml = await invoiceHelper
              .invoiceFromXml(
                fs.readFileSync(
                  xml.url.replace('uploads', UPLOAD_DIR),
                  'utf-8',
                ),
              )
              .catch(() => null);

            if (
              !invoiceFromXml ||
              invoiceHelper.isMalformedInvoice(invoiceFromXml)
            ) {
              statusMails.push({
                count: 1,
                statusValidMail: STATUS_VALID_MAIL.MALFORMED_INVOICE,
              });

              continue;
            }

            /* save to invoice */

            invoiceFromXml.parsedFromXml = true;
            invoiceFromXml.typeInvoiceReceive = TYPE_INVOICE_RECEIVE[2];

            if (invoiceFromXml.cqtCKS || invoiceFromXml.sellerCKS) {
              let { cqtCTS, sellerCTS } =
                invoiceHelper.readX509Cert(invoiceFromXml);
              invoiceFromXml.cqtCKS = cqtCTS;
              invoiceFromXml.sellerCKS = sellerCTS;
            }

            let duplicateInvoice = await invoiceHelper.findDuplicateInvoice(
              invoiceFromXml,
              { organizationDepartmentId, organizationId },
            );

            if (duplicateInvoice) {
              statusMails.push({
                count: 1,
                statusValidMail: STATUS_VALID_MAIL.DUPLICATE_INVOICE,
              });

              continue;
            }

            let t = await Sequelize.transaction();

            try {
              let [invoice] = await invoiceHelper.saveToInvoice(
                [invoiceFromXml],
                {
                  organizationDepartmentId,
                  organizationId,
                  taxCode,
                },
                t,
              );

              let originFilePath = `${UPLOAD_DIR}/invoice-origin-xml/${invoice.invoiceId}`;
              let originFileName = utils.escapeFilename(xml.filename);

              if (fs.existsSync(originFilePath)) {
                fs.rmSync(originFilePath, { recursive: true });
              }
              fs.mkdirSync(originFilePath);

              fs.copyFileSync(
                xml.url.replace('uploads', UPLOAD_DIR),
                `${originFilePath}/${originFileName}`,
              );

              await invoice.update(
                {
                  // xmlFile: `uploads/invoice-xml/${invoice.invoiceId}/${fileName}`,
                  originXmlFile: `uploads/invoice-origin-xml/${invoice.invoiceId}/${originFileName}`,
                },
                { transaction: t },
              );

              xml.invoiceId = invoice.invoiceId;

              /* get lookup website */
              if (true) {
                let { supplierLookupWebsite } =
                  await invoiceHelper.getSupplierReference(invoice);

                if (!supplierLookupWebsite) {
                  supplierLookupWebsite =
                    await invoiceHelper.findSpecialLookupWebsite(invoice);
                }

                if (supplierLookupWebsite) {
                  await invoice.update(
                    { supplierLookupWebsite },
                    { transaction: t },
                  );
                }
              }

              let validate = await cqt_validate_invoice(invoice, t);

              if (invoiceHelper.isInvoiceValid({ InvoiceValidate: validate })) {
                statusMails.push({
                  count: 1,
                  statusValidMail: STATUS_VALID_MAIL.VALID_INVOICE,
                });
              } else {
                statusMails.push({
                  count: 1,
                  statusValidMail: STATUS_VALID_MAIL.INVALID_INVOICE,
                });
              }

              if (invoiceFromXml.InvoiceProducts) {
                await InvoiceProduct.destroy({
                  where: { invoiceId: invoice.invoiceId },
                  transaction: t,
                });

                await InvoiceProduct.bulkCreate(
                  invoiceFromXml.InvoiceProducts.map(item => {
                    item.invoiceId = invoice.invoiceId;

                    return item;
                  }),
                  { transaction: t },
                );
              }

              await t.commit();
            } catch (error) {
              await t.rollback();
            }
          }

          /* group status by statusValidMail*/
          let groupedStatus = _.groupBy(
            statusMails,
            item => item.statusValidMail,
          );

          await StatusMail.destroy({ where: { mailId: mail.mailId } });

          await StatusMail.bulkCreate(
            Object.entries(groupedStatus).map(([key, item]) => {
              return {
                count: item.length,
                mailId: mail.mailId,
                statusValidMail: key,
              };
            }),
          );

          await Mail.update(
            { attachments: attachments },
            { where: { mailId: mail.mailId } },
          );
        } else {
          await StatusMail.destroy({ where: { mailId: mail.mailId } });

          await StatusMail.create({
            mailId: mail.mailId,
            count: 0,
            statusValidMail: STATUS_VALID_MAIL.INVOICE_NOT_FOUND,
          });
        }
      } else {
        await StatusMail.destroy({ where: { mailId: mail.mailId } });

        await StatusMail.create({
          mailId: mail.mailId,
          count: 0,
          statusValidMail: STATUS_VALID_MAIL.INVOICE_NOT_FOUND,
        });
      }
    }

    return results;
  },
};

/**
 *
 * @param {string} content
 * @returns
 */
function formatRequest(content) {
  return `<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
  <soap:Header>
      <context xmlns="urn:zimbra">
          <userAgent name="ZimbraWebClient - GC116 (Mac)"/>
          <session/>
          <authTokenControl voidOnExpired="1"/>
          <format type="js"/>
      </context>
  </soap:Header>
  <soap:Body>
      ${content}
  </soap:Body>
</soap:Envelope>`;
}
