const { default: axios } = require('axios');
const {
  QLBH_CLIENT_CODE,
  QLBH_SECRET_KEY,
  QLBH_BASE_URL,
} = require('../configs/env');
const { sortKeys, hmac, removeEmptyProps } = require('.');
const {
  QLBH_CLIENT_CODE_REGISTER,
  QLBH_FIND_DEALER_BY_DEALER_CODE,
  QLBH_ACCOUNT_SERVICE_FIND_USAGE,
  QLBH_CLIENT_CODE_CREATE_ACCOUNT,
  QLBH_ACCOUNT_FIND_BY_META_ACCOUNT_CODES,
  QLBH_CLIENT_CODE_LOGIN,
  QLBH_CLIENT_CODE_CHANGE_PASSWORD,
  QLBH_CLIENT_CODE_UPDATE,
  QLBH_CLIENT_CODE_GET_VOUCHER_BY_CODE,
  QLBH_CLIENT_CODE_FIND_VOUCHER,
  QLBH_CLIENT_CODE_USE_VOUCHER,
  QLBH_CLIENT_CODE_REQUEST_RESET_PASSWORD,
  QLBH_CLIENT_CODE_RESET_PASSWORD,
  QLBH_CLIENT_ACTIVATE_ACCOUNT,
  QLBH_NOTIFY_COMPANIES,
  QLBH_CLIENT_CODE_DETAIL_ACCOUNT_BY_META_CODE,
  QLBH_TT_HOP_DONG,
} = require('../configs/constants/qlbh.constanst');
const _ = require('lodash');
const fs = require('fs');
const FormData = require('form-data');

const axiosClient = axios.create({
  baseURL: QLBH_BASE_URL,
  headers: { clientcode: QLBH_CLIENT_CODE },
});

module.exports = {
  /**
   *
   * @param {Object} param
   * @param {string} param.fullname
   * @param {string=} param.taxCode
   * @param {string} param.email
   * @param {string=} param.phone
   * @param {string=} param.dealerCode
   * @param {string} param.password
   * @returns {Promise<import('../types').ResponseBody>}
   */
  async clientCodeRegister(param) {
    let data = {
      ...param,
      customerType: 'ENTERPRISE',
    };

    data = sortKeys(data);

    let hash = hmac(JSON.stringify(data), QLBH_SECRET_KEY);

    let response = await axiosClient
      .post(QLBH_CLIENT_CODE_REGISTER, {
        ...data,
        hash,
      })
      .catch(err => err.response);

    return response.data;
  },
  /**
   *
   * @param {string} dealerCode
   * @returns {Promise<import('../types').ResponseBody & {dealer?: {fullname:string}}}>}
   */
  async findDealerByDealerCode(dealerCode) {
    dealerCode = _.toString(dealerCode);
    let params = { dealerCode };

    params = sortKeys(params);

    let hash = hmac(JSON.stringify(params), QLBH_SECRET_KEY);

    let response = await axiosClient
      .get(QLBH_FIND_DEALER_BY_DEALER_CODE, {
        params: { ...params, hash },
      })
      .catch(err => err.response);

    return response.data;
  },
  /**
   *
   * @param {string} metaAccountCode
   * @param {string} password
   * @param {string} taxCode
   * @returns {Promise<import('../types').ResponseBody>}
   */
  async clientCodeLogin(metaAccountCode = '', password = '', taxCode) {
    let data = { metaAccountCode, password, taxCode };

    data = sortKeys(data);

    let hash = hmac(JSON.stringify(data), QLBH_SECRET_KEY);

    let response = await axiosClient
      .post(QLBH_CLIENT_CODE_LOGIN, {
        ...data,
        hash,
      })
      .catch(err => err.response);
    return response.data;
  },
  /**
   *
   * @param {Object} param
   * @param {string} param.fullname
   * @param {string} param.email
   * @param {string=} param.phone
   * @param {string=} param.dealerCode
   * @param {string} param.password
   * @returns {Promise<import('../types').ResponseBody>}
   */
  async clientCodeCreateAccount(param) {
    let { email, fullname, phone, password } = param;
    let data = {
      email,
      fullname,
      phone,
      password,
    };

    data = sortKeys(data);

    let hash = hmac(JSON.stringify(data), QLBH_SECRET_KEY);

    let response = await axiosClient
      .post(QLBH_CLIENT_CODE_CREATE_ACCOUNT, {
        ...data,
        hash,
      })
      .catch(err => err.response);

    return response.data;
  },

  /**
   *
   * @param {string} metaAccountCode
   * @param {string} newPassword
   * @param {string} oldPassword
   * @returns {Promise<import('../types').ResponseBody>}
   */
  async clientCodeChangePassword(
    metaAccountCode = '',
    newPassword = '',
    oldPassword,
  ) {
    let data = { metaAccountCode, newPassword, oldPassword };

    data = sortKeys(data);

    let hash = hmac(JSON.stringify(data), QLBH_SECRET_KEY);

    let response = await axiosClient
      .post(QLBH_CLIENT_CODE_CHANGE_PASSWORD, {
        ...data,
        hash,
      })
      .catch(err => err.response);

    return response.data;
  },
  /**
   *
   * @param {string} metaAccountCode
   * @param {any} props
   *
   * @returns {Promise<import('axios').AxiosResponse>}
   */
  async clientCodeUpdate(metaAccountCode, props) {
    let data = removeEmptyProps({ ...props });

    let nonFileValueData = {};

    Object.entries(data).forEach(([key, value]) => {
      if (!_.isObject(value)) {
        nonFileValueData[key] = value;
      }
    });

    nonFileValueData = sortKeys(nonFileValueData);

    let hash = hmac(JSON.stringify(nonFileValueData), QLBH_SECRET_KEY);

    let formData = new FormData();

    for (let [key, value] of Object.entries(data)) {
      if (!_.isObject(value)) {
        formData.append(key, value);
      } else if (value.uri) {
        formData.append(key, fs.createReadStream(value.uri), {
          filename: value.name,
        });
      }
    }

    formData.append('hash', hash);

    let response = await axiosClient
      .post(`${QLBH_CLIENT_CODE_UPDATE}/${metaAccountCode}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          ...formData.getHeaders(),
        },
      })
      .catch(err => err.response);

    return response;
  },
  /**
   *
   * @param {string} voucherCode
   *
   * @returns {Promise<import('../types').ResponseBody>}
   */
  async clientCodeGetVoucherByCode(voucherCode) {
    voucherCode = _.toString(voucherCode);
    let data = { voucherCode };

    data = sortKeys(data);

    let hash = hmac(JSON.stringify(data), QLBH_SECRET_KEY);

    let response = await axiosClient
      .get(QLBH_CLIENT_CODE_GET_VOUCHER_BY_CODE, { params: { ...data, hash } })
      .catch(err => err.response);

    return response.data;
  },
  /**
   *
   *
   * @returns {Promise<import('../types').ResponseBody>}
   */
  async clientCodeFindVoucher() {
    let data = {};

    data = sortKeys(data);

    let hash = hmac(JSON.stringify(data), QLBH_SECRET_KEY);

    let response = await axiosClient
      .get(QLBH_CLIENT_CODE_FIND_VOUCHER, { params: { ...data, hash } })
      .catch(err => err.response);

    return response.data;
  },
  /**
   *
   * @param {Object} param
   * @param {number} param.promotion
   * @param {number} param.totalCost
   * @param {number} param.metaAccountCode
   *
   */
  async clientCodeCreatePayment({ promotion, totalCost, metaAccountCode }) {
    /* TODO */
  },
  /**
   *
   * @param {string} voucherCode
   * @returns {Promise<import('../types').ResponseBody>}
   */
  async clientCodeUseVoucher(voucherCode) {
    let data = { voucherCode };

    data = sortKeys(data);

    let hash = hmac(JSON.stringify(data), QLBH_SECRET_KEY);

    let response = await axiosClient
      .post(QLBH_CLIENT_CODE_USE_VOUCHER, { ...data, hash })
      .catch(err => err.response);

    return response.data;
  },
  /**
   *
   * @param {string} email
   * @returns {Promise<import('../types').ResponseBody>}
   */
  async requestResetPassword(email) {
    let data = { email };

    data = sortKeys(data);

    let hash = hmac(JSON.stringify(data), QLBH_SECRET_KEY);

    let response = await axiosClient
      .post(QLBH_CLIENT_CODE_REQUEST_RESET_PASSWORD, { ...data, hash })
      .catch(err => err.response);

    return response.data;
  },
  /**
   *
   * @param {string} email
   * @param {string} resetCode
   * @param {string} newPassword
   * @returns {Promise<import('../types').ResponseBody>}
   */
  async resetPassword(email, resetCode, newPassword) {
    let data = { email, resetCode, newPassword };

    data = sortKeys(data);

    let hash = hmac(JSON.stringify(data), QLBH_SECRET_KEY);

    let response = await axiosClient
      .put(QLBH_CLIENT_CODE_RESET_PASSWORD, { ...data, hash })
      .catch(err => err.response);

    return response.data;
  },
  /**
   *
   * @param {string} activateToken
   * @returns {Promise<import('../types').ResponseBody>}
   */
  async activateAccount(activateToken) {
    let params = { activateToken };

    params = sortKeys(params);

    let hash = hmac(JSON.stringify(params), QLBH_SECRET_KEY);

    let response = await axiosClient
      .get(QLBH_CLIENT_ACTIVATE_ACCOUNT, { params: { ...params, hash } })
      .catch(err => err.response);

    return response.data;
  },
  /**
   *
   * @param {any[]} arr
   * @param {{expiredDateDiff:any,expiredQuantityRemain:any}} props
   * @returns {Promise<import('../types').ResponseBody>}
   */
  async notifyCompanies(arr, { expiredDateDiff, expiredQuantityRemain }) {
    let data = { data: arr, expiredDateDiff, expiredQuantityRemain };

    data = sortKeys(data);

    let hash = hmac(JSON.stringify(data), QLBH_SECRET_KEY);

    let response = await axiosClient
      .post(QLBH_NOTIFY_COMPANIES, {
        ...data,
        hash,
      })
      .catch(err => err.response);

    return response.data;
  },

  /**
   *
   * @param {any} params
   * @returns {Promise<import('../types').ResponseBody>}
   */
  async fastTest(params) {
    let data = { ...params };

    data = sortKeys(data);

    let hash = hmac(JSON.stringify(data), QLBH_SECRET_KEY);

    let response = await axiosClient
      .post('/api/v1/test/fast-test', {
        ...data,
        hash,
      })
      .catch(err => err.response);

    return response.data;
  },

  /**
   *
   * @param {string} metaAccountCode
   * @returns {Promise<import('../types').ResponseBody>}
   */
  async ktraTTHopDong(metaAccountCode) {
    let params = {};

    params = sortKeys(params);

    let hash = hmac(JSON.stringify(params), QLBH_SECRET_KEY);

    let response = await axiosClient
      .get(`${QLBH_TT_HOP_DONG}/${metaAccountCode}`, {
        params: {
          ...params,
          hash,
        },
      })
      .catch(err => err.response);

    return response.data;
  },
};
