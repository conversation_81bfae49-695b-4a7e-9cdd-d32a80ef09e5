const jwt = require('jsonwebtoken');
const { JWT_SECRET, NODE_ENV } = require('../configs/env');
const _ = require('lodash');
const moment = require('moment');
const {
  getPublicAccountAttributes,
  getJwtAccountAttributes,
} = require('./account.helper');

/**
 * Verify that token is remain un tampered
 * @param {string} token
 * @returns
 */
const verifyJWTToken = token => {
  try {
    const decoded = jwt.verify(token, JWT_SECRET, { ignoreExpiration: true });
    return true;
  } catch (error) {
    return false;
  }
};

/**
 *
 * @param {string} token
 * @returns
 */
const decodeJwtToken = token => {
  if (!verifyJWTToken(token)) return undefined;
  return jwt.decode(token, {});
};

module.exports = {
  /**
   *
   * @param {import('../types').Account} account
   */
  genAccountAccessToken(account, rememberMe = false) {
    account = _.pick(account, getJwtAccountAttributes());
    const token = jwt.sign(account, JWT_SECRET, {
      expiresIn: !!rememberMe ? '14 days' : '48h',
    });
    return token;
  },
  verifyJWTToken,
  /**
   *
   * @param {string} token
   */
  isJWTTokenExpired(token) {
    if (verifyJWTToken(token)) {
      let decoded = decodeJwtToken(token);
      const { exp, iat } = decoded;
      const now = Date.now();

      if (!exp) return moment().diff(iat * 1000, 'days') <= 15; // no exp time but 15 days max
      return parseInt(exp) * 1000 <= now;
    }
  },
  decodeJwtToken,
};
