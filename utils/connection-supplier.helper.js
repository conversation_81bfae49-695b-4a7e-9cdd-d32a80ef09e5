const { Op, Transaction } = require('sequelize');
const {
  ConnectionSupplier,
  Supplier,
  ConnectionSupplierHistory,
  Sequelize,
  ConnectionSupplierInvoice,
  Organization,
  OrganizationDepartment,
  ResourceHistory,
  Company,
} = require('../models');
const moment = require('moment');
const { SUPPLIER_KEY } = require('../configs/constants/supplier.constant');
const { vietinvoiceHelper } = require('../vietinvoice');
const _ = require('lodash');
const invoiceHelper = require('./invoice.helper');
const {
  TYPE_INVOICE_RECEIVE,
} = require('../configs/constants/invoice.constant');
const companyHelper = require('./company.helper');
const { PACKAGE_TYPE } = require('../configs/constants/package.constant');

module.exports = {
  /**
   *
   * @param {any} from
   * @param {any} to
   * @param {Object} param0
   * @param {number=} param0.supplierId
   * @param {number=} param0.organizationId
   * @param {number=} param0.organizationDepartmentId
   * @param {number=} param0.taxCode
   * @param {Transaction} t
   */
  async synchronize(
    from,
    to,
    { supplierId, organizationDepartmentId, organizationId, taxCode },
    t,
  ) {
    const connectionSuppliers = await ConnectionSupplier.findAll({
      where: {
        organizationDepartmentId,
        organizationId,
        [Op.and]: [supplierId ? { supplierId } : {}],
      },
      include: [{ model: Supplier }],
    });

    let _from = moment(from);
    let _to = moment(to);

    // kết quả trả về của VietInvoice
    /**
     * @type {import('../types').VietInvoiceInvoice[]}
     */
    let arrInvoice = [],
      listInvoice = [];
    for (const i in connectionSuppliers) {
      if (
        connectionSuppliers[i].Supplier.supplierKey == SUPPLIER_KEY.VIETINVOICE
      ) {
        const response = await vietinvoiceHelper._synchronize(
          connectionSuppliers[i].usernameConnection,
          connectionSuppliers[i].passwordConnection,
          _from.startOf('date'),
          _to.endOf('date'),
        );
        await connectionSuppliers[i].update({
          xCSRFToken: response.xCSRFToken,
        });
        if (response) {
          listInvoice = response.data.map(item => {
            item.connectionSupplierId =
              connectionSuppliers[i].connectionSupplierId;

            item.fromPartyTaxId = connectionSuppliers[i].usernameConnection;
            item.connectionSupplier = connectionSuppliers[i];

            // eslint-disable-next-line no-self-assign
            item.invoiceId = item.invoiceId;
            item.supplierKey = SUPPLIER_KEY.VIETINVOICE;

            return item;
          });
        }
        arrInvoice.push(...listInvoice);
      }
    }

    let uniqueDateDicts = _.groupBy(arrInvoice, item =>
      moment(item.invoiceDate).format('YYYY/MM/DD'),
    );

    let uniqueDates = Object.keys(uniqueDateDicts);

    // lọc theo ngày của db mdc
    const findConnectionSupplierHistories =
      await ConnectionSupplierHistory.findAll({
        where: {
          [Op.and]: [
            {
              syncDate: {
                [Op.between]: [_from.startOf('date'), _to.endOf('date')],
              },
            },
            { organizationDepartmentId, organizationId },
          ],
        },
        transaction: t,
      });

    for (let date of uniqueDates) {
      let fcsh = findConnectionSupplierHistories.find(_fcsh =>
        moment(_fcsh.syncDate).isSame(moment(date).toDate(), 'date'),
      );

      if (!fcsh) {
        fcsh = await ConnectionSupplierHistory.create(
          {
            organizationDepartmentId,
            organizationId,
            syncDate: moment(date).toDate(),
          },
          { transaction: t },
        );

        findConnectionSupplierHistories.push(fcsh);
      }

      let _arrInvoice = arrInvoice.filter(i =>
        moment(i.invoiceDate).isSame(date, 'date'),
      );

      let total = _arrInvoice.length,
        hasMaCQT = 0;

      if (total >= 0) {
        // nếu đã có connectionSupplierHistories và trong cùng 1 ngày thì thêm mới một connectionSupplierInvoice

        let rs = await ConnectionSupplierInvoice.bulkCreate(
          _arrInvoice.map(
            /**
             *
             * @param {import('../types').VietInvoiceInvoice} invoice
             * @returns
             */
            invoice => {
              if (invoice.supplierKey === SUPPLIER_KEY.VIETINVOICE) {
                return {
                  connectionSupplierHistoryId: fcsh.connectionSupplierHistoryId,
                  invoiceType: invoice.invoiceForm,
                  serial: invoice.invoiceSerial,
                  invoiceTypeCode: invoice.invoiceForm,
                  invoiceNumber: invoice.invoiceNo,
                  invoiceDate: invoice.invoiceDate,
                  maCQT: invoice.externalId,
                  buyerTaxCode: invoice.toPartyTaxId,
                  vietInvoiceId: invoice.invoiceId,
                  connectionSupplierId: invoice.connectionSupplierId,
                  sellerTaxCode: invoice.fromPartyTaxId,
                  supplierReferenceCode: invoice.referenceNumber,
                  buyerName: invoice.toPartyName,
                  issueStatus: invoice.issueStatus,
                };
              }
            },
          ),
          {
            transaction: t,
            updateOnDuplicate: [
              'invoiceDate',
              'maCQT',
              'buyerName',
              'connectionSupplierId',
              'issueStatus',
              'supplierReferenceCode',
            ],
          },
        );

        hasMaCQT = rs.filter(i => !!i.serial?.startsWith('C')).length;

        /* cập nhật  */
        await fcsh.update(
          {
            succeededDownload: total,
            total: total,
            hasMaCQT: hasMaCQT,
          },
          { transaction: t },
        );
      }
    }

    let companyId;

    if (organizationId) {
      let organization = await Organization.findOne({
        where: { organizationId },
        transaction: t,
      });

      companyId = organization.companyId;
    } else if (organizationDepartmentId) {
      let organizationDepartment = await OrganizationDepartment.findOne({
        where: { organizationDepartmentId },
        include: [{ association: 'OrganizationBranch' }],
        transaction: t,
      });

      companyId = organizationDepartment.OrganizationBranch.companyId;
    }

    /* ktra tai nguyen */
    let canSave = false;
    /**
     * SL cos theer trừ
     */
    let maxEntries = await companyHelper.getMaxInvoiceEntries(companyId, t);

    let remainResource = await companyHelper.getRemainResource(companyId, t);

    if (maxEntries.length) {
      /*  */
      /* 31/10: không tính thời hạn riêng từng lần cấp. Cộng dồn SL, thời hạn */
      let resourceHistories = await ResourceHistory.findAll({
        where: {
          companyId,
          left: { [Op.gt]: 0 },
          packageType: PACKAGE_TYPE.QUANTITY,
        },
        transaction: t,
      });

      canSave = true;

      /* SAVE INVOICE */
      if (canSave) {
        let invoices = await invoiceHelper.saveToInvoice(
          arrInvoice.map(
            /**
             *
             * @param {import('../types').VietInvoiceInvoice} i
             * @returns
             */
            i => {
              i.fromPartyId = i.sellerTaxCode;

              return {
                ...vietinvoiceHelper.toInvoice(i),
                connectionSupplier: i.connectionSupplier,
                typeInvoiceReceive: TYPE_INVOICE_RECEIVE[3],
              };
            },
          ),
          { organizationDepartmentId, organizationId, taxCode, maxEntries },
          t,
        );

        /* reduce left in resource history */
        if (invoices.length) {
          let total = invoices.length;

          /* first use promotion */
          if (remainResource.promotionRemain) {
            let used = Math.min(total, remainResource.promotionRemain);

            total -= used;

            await Company.update(
              { promotionRemain: remainResource.promotionRemain - used },
              { where: { companyId }, transaction: t },
            );
          }

          /* then use resources */
          let useQuantityRemain = total;
          for (let i = 0; i < resourceHistories.length && total > 0; i++) {
            let used = Math.min(total, resourceHistories[i].left);
            total -= used;

            await resourceHistories[i].update(
              { left: resourceHistories[i].left - used },
              { transaction: t },
            );
          }

          await Company.update(
            {
              quantityRemain: remainResource.quantityRemain - useQuantityRemain,
            },
            { where: { companyId }, transaction: t },
          );
        }
      }
    }
  },
};
