const moment = require('moment');
const _ = require('lodash');
const crypto = require('crypto');
const { DATETIME_FORMAT } = require('../configs/constants/other.constant');
const { BASE_URL } = require('../configs/env');

module.exports = {
  _: _,
  /**
   * @description YYYY-MM-DD HH:mm:ss formatted
   * @returns {string}
   */
  now() {
    let today = moment();
    return today.format(DATETIME_FORMAT).toString();
  },
  /**
   * Check if is number, both in string format (`'5'`) or number format (`5`). Return false if is NaN, empty, null, undefined...
   * @param {*} param
   * @return {param is number}
   */
  isValidNumber(param) {
    if (
      ((!_.isString(param) || _.isEmpty(param)) && !_.isNumber(param)) ||
      _.isNull(param) ||
      _.isUndefined(param)
    ) {
      return false;
    }
    return !isNaN(param) && isFinite(param);
  },
  /**
   * check if is stringify by JSON.stringify
   * @param {string} str
   * @returns
   */
  isJSONStringified(str) {
    if (typeof str !== 'string') return false;
    if (this.isValidNumber(str)) return false;

    try {
      JSON.parse(str);
    } catch (e) {
      return false;
    }
    return true;
  },
  /**
   *
   * @param {string} str
   * @returns
   */
  isJSONObject(str) {
    if (typeof str !== 'string') return false;

    try {
      let parsed = JSON.parse(str);

      return _.isObject(parsed);
    } catch (e) {
      return false;
    }
  },
  /**
   *
   * @param {string} str
   * @returns
   */
  isJSONArray(str) {
    if (typeof str !== 'string') return false;

    try {
      let parsed = JSON.parse(str);

      return _.isArray(parsed);
    } catch (e) {
      return false;
    }
  },
  /**
   *
   * @param {number=} size default: `20`. Use `32` for a access token, Use `6` for salt
   * @returns
   */
  generateRandomStr(size = 20) {
    return crypto.randomBytes(size).toString('hex');
  },
  /**
   * No check on `''`, `null`, `undefined`
   * @param {any[]} arr
   */
  hasDuplicates(arr) {
    let dup_dict = {};

    dup_dict = arr.reduce((prev, curr) => {
      if (prev[curr] === undefined) prev[curr] = 0;
      if (curr === undefined || curr === null || curr === '') {
      } else {
        prev[curr]++;
      }
      return prev;
    }, {});

    return Object.values(dup_dict).some(num => num > 1);
  },
  isFalsely(value) {
    if (_.isBoolean(value)) return value;
    if (this.isValidNumber(value)) {
      value = _.toNumber(value);
      return !!value;
    }

    return !!value;
  },
  /**
   * @param {string} str
   * @returns
   */
  nonAccentVietnamese(str) {
    str = _.toString(str)
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '');

    str = str.replace(/A|Á|À|Ã|Ạ|Ả|Â|Ấ|Ầ|Ẫ|Ẩ|Ậ|Ă|Ắ|Ằ|Ẵ|Ặ|Ẳ/g, 'A');
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ|ẳ/g, 'a');
    str = str.replace(/E|É|È|Ẽ|Ẹ|Ẻ|Ê|Ế|Ề|Ễ|Ệ|Ể/, 'E');
    str = str.replace(/e|è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
    str = str.replace(/I|Í|Ì|Ĩ|Ị|Ỉ/g, 'I');
    str = str.replace(/i|ì|í|ị|ỉ|ĩ/g, 'i');
    str = str.replace(/O|Ó|Ò|Õ|Ọ|Ỏ|Ô|Ố|Ồ|Ỗ|Ộ|Ổ|Ơ|Ớ|Ờ|Ỡ|Ợ|Ở/g, 'O');
    str = str.replace(/o|ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
    str = str.replace(/U|Ú|Ù|Ũ|Ụ|Ủ|Ư|Ứ|Ừ|Ữ|Ự|Ử/g, 'U');
    str = str.replace(/u|ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
    str = str.replace(/Y|Ý|Ỳ|Ỹ|Ỵ|Ỷ/g, 'Y');
    str = str.replace(/y|ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
    str = str.replace(/Đ/g, 'D');
    str = str.replace(/đ/g, 'd');
    // Some system encode vietnamese combining accent as individual utf-8 characters
    str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ''); // Huyền sắc hỏi ngã nặng
    str = str.replace(/\u02C6|\u0306|\u031B/g, ''); // Â, Ê, Ă, Ơ, Ư
    return str;
  },
  /**
   * if `undefined`, return `undefined`
   *
   * parse to boolean, if not undefined, e.g 'false'=>false,'0'=>false,'1'=>true,'any'=>true,''=>fasle,...
   * @param {*} value
   * @returns {boolean|undefined}
   */
  parseBoolean(value) {
    if (value === undefined) return undefined;
    if (_.isBoolean(value)) return value;

    if (
      value === 'false' ||
      value === '0' ||
      value === 'null' ||
      value === 'NULL' ||
      value === 'undefined'
    )
      return false;

    return !!value;
  },
  /**
   *
   * @param {any} amount
   * @param {boolean=} hideCurrencySign default `true`
   * @returns
   */
  formatCurrency(amount, hideCurrencySign = true) {
    if (!module.exports.isValidNumber(amount)) {
      return amount;
    }
    amount = _.toNumber(amount);

    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      signDisplay: 'never',
      currency: 'VND',
      compactDisplay: 'short',
    })
      .format(amount)
      .replace('₫', '')
      .trim()
      .concat(hideCurrencySign ? '' : ' đ');
  },
  escapeSpace(str = '') {
    return str.replace(/ /g, '');
  },
  /**
   *
   * @param {string} str
   */
  hashSHA256(str) {
    str = _.toString(str);
    return crypto
      .createHash('sha256')
      .update(str, 'utf-8')
      .digest('hex')
      .toString();
  },
  /**
   *
   * @param {string} email
   * @returns
   */
  isValidEmail(email) {
    let regExp = /^\S+@\S+\.\S+$/;

    return regExp.test(email) /* && /[a-zA-Z]/.test(email.split('@')[0]) */;
  },
  /**
   *
   * @param {string} taxCode
   * @returns
   */
  isValidTaxCode(taxCode) {
    let regExp10so = /^([0-9]{10})$/;
    let regExp10so3so = /^([0-9]{10})-([0-9]{3})$/;
    let regExp9so = /^([0-9]{9})$/;
    let regExp12so = /^([0-9]{12})$/;
    return (
      regExp10so.test(taxCode) ||
      regExp10so3so.test(taxCode) ||
      regExp9so.test(taxCode) ||
      regExp12so.test(taxCode)
    );
  },
  /**
   *
   * @param {string} username
   * @returns
   */
  isValidUsername(username) {
    var regExp = /^(?=.*[A-Za-z0-9]).{3,30}$/;
    var noWhiteSpace = /^\S*$/;
    var musHasChar = /[a-zA-Z]/g;

    return (
      regExp.test(username) &&
      noWhiteSpace.test(username) &&
      musHasChar.test(username) &&
      !this.isValidEmail(username) &&
      !this.isValidTaxCode(username) &&
      !this.isValidPhone(username)
    );
  },
  /**
   *
   * @param {string} phone
   * @returns
   */
  isValidPhone(phone) {
    let regExp = /^(((?:\+?)84[3|5|7|8|9])|(?:0?)[3|5|7|8|9])+([0-9]{8})$/;

    return regExp.test(phone);
  },
  /**
   *
   * replace `key` in str with `value` of `dict`
   * if key is not `$...` formated, it will be change to that format
   * @param {string} str
   * @param {{[x:string]:string}} dict
   */
  replaceInString(str, dict) {
    str = _.toString(str);

    for (let [key, value] of _.entries(dict)) {
      if (!key.startsWith('$')) key = '$'.concat(key);

      str = str.split(key).join(value);
    }
    return str;
  },
  /**
   *
   * @param {any} obj
   */
  sortKeys(obj) {
    return Object.keys(obj)
      .sort()
      .reduce((prev, curr) => {
        prev[curr] = obj[curr];

        return prev;
      }, {});
  },
  hmac(data = '', secretKey = '') {
    var algorithm = 'sha256';

    var hmac = crypto.createHmac(algorithm, secretKey);

    hmac.update(data);

    return hmac.digest('hex').toString();
  },
  /**
   *
   * @param {any} date
   * @returns
   */
  isValidDate(date) {
    return (
      (_.isNumber(date) ||
        _.isDate(date) ||
        _.isString(date) ||
        moment.isMoment(date)) &&
      moment(date).isValid() &&
      moment(date).isAfter('1900-01-01') &&
      moment(date).isBefore('2100-01-01')
    );
  },
  /**
   * Hash string
   * @param {string} str
   * @param {number} seed
   * @returns
   */
  cyrb53(str = '', seed = 0) {
    str = _.toString(str);
    let h1 = 0xdeadbeef ^ seed,
      h2 = 0x41c6ce57 ^ seed;
    for (let i = 0, ch; i < str.length; i++) {
      ch = str.charCodeAt(i);
      h1 = Math.imul(h1 ^ ch, 2654435761);
      h2 = Math.imul(h2 ^ ch, 1597334677);
    }

    h1 =
      Math.imul(h1 ^ (h1 >>> 16), 2246822507) ^
      Math.imul(h2 ^ (h2 >>> 13), 3266489909);
    h2 =
      Math.imul(h2 ^ (h2 >>> 16), 2246822507) ^
      Math.imul(h1 ^ (h1 >>> 13), 3266489909);

    return _.toString(4294967296 * (2097151 & h2) + (h1 >>> 0));
  },
  /**
   *
   * @param {Express.Application} app
   * @param {import('../types').ExpressListRoutesOption} opts
   * @returns {import('../types').ExpressListRouteData[]>}
   */
  expresssListRoutes(app, opts) {
    const path = require('path');

    const defaultOptions = {
      prefix: '',
      spacer: 7,
      logger: console.info,
      color: true,
    };

    const COLORS = {
      yellow: 33,
      green: 32,
      blue: 34,
      red: 31,
      grey: 90,
      magenta: 35,
      clear: 39,
    };

    const spacer = x =>
      x > 0 ? [...new Array(x)].map(() => ' ').join('') : '';

    const colorText = (color, string) =>
      `\u001b[${color}m${string}\u001b[${COLORS.clear}m`;

    function colorMethod(method) {
      switch (method) {
        case 'POST':
          return colorText(COLORS.yellow, method);
        case 'GET':
          return colorText(COLORS.green, method);
        case 'PUT':
          return colorText(COLORS.blue, method);
        case 'DELETE':
          return colorText(COLORS.red, method);
        case 'PATCH':
          return colorText(COLORS.grey, method);
        default:
          return method;
      }
    }

    function getPathFromRegex(regexp) {
      return regexp
        .toString()
        .replace('/^', '')
        .replace('?(?=\\/|$)/i', '')
        .replace(/\\\//g, '/')
        .replace('(?:/(?=$))', '');
    }

    function combineStacks(acc, stack) {
      if (stack.handle.stack) {
        const routerPath = getPathFromRegex(stack.regexp);
        return [
          ...acc,
          // eslint-disable-next-line @typescript-eslint/no-shadow
          ...stack.handle.stack.map(stack => ({ routerPath, ...stack })),
        ];
      }
      return [...acc, stack];
    }

    /**
     *
     * @param {import('express').Application} app
     * @returns
     */
    // eslint-disable-next-line @typescript-eslint/no-shadow
    function getStacks(app) {
      // Express 3
      if (app.routes) {
        // convert to express 4
        return Object.keys(app.routes)
          .reduce((acc, method) => [...acc, ...app.routes[method]], [])
          .map(route => ({ route: { stack: [route] } }));
      }

      // Express 4
      if (app._router && app._router.stack) {
        return app._router.stack.reduce(combineStacks, []);
      }

      // Express 4 Router
      if (app.stack) {
        return app.stack.reduce(combineStacks, []);
      }

      // Express 5
      if (app.router && app.router.stack) {
        return app.router.stack.reduce(combineStacks, []);
      }

      return [];
    }

    // eslint-disable-next-line @typescript-eslint/no-shadow

    const stacks = getStacks(app);
    const options = { ...defaultOptions, ...opts };
    const paths = [];

    if (stacks) {
      for (const stack of stacks) {
        if (stack.route) {
          const routeLogged = {};
          for (const route of stack.route.stack) {
            const method = route.method ? route.method.toUpperCase() : null;
            if (!routeLogged[method] && method) {
              const stackMethod = options.color ? colorMethod(method) : method;
              const stackSpace = spacer(options.spacer - method.length);
              const stackPath = path
                .normalize(
                  [
                    options.prefix,
                    stack.routerPath,
                    stack.route.path,
                    route.path,
                  ]
                    .filter(s => !!s)
                    .join(''),
                )
                .trim();
              options.logger(stackMethod, stackSpace, stackPath);
              paths.push({ method, path: stackPath });
              routeLogged[method] = true;
            }
          }
        }
      }
    }

    return paths;
  },
  /**
   *
   * @param {string} filename
   */
  escapeFilename(filename) {
    return this.nonAccentVietnamese(filename).replace(/ /g, '');
  },
  /**
   *
   * @param {string} str
   */
  escapeStr(str) {
    return this.nonAccentVietnamese(str)
      .replace(/ /g, '')
      .replace(/–/g, '-')
      .toLowerCase();
  },
  /**
   *
   * @param {string} value
   * @param {string} search
   */
  matchSearch(value, search) {
    return this.escapeStr(value)
      .toLowerCase()
      .includes(this.escapeStr(search).toLowerCase());
  },
  /**
   * just 90% true :))
   * @param {string} str
   */
  isVietnamese(str) {
    str = _.toString(str).toLowerCase();

    if (/Á|À|Ã|Ạ|Ả|Â|Ấ|Ầ|Ẫ|Ẩ|Ậ|Ă|Ắ|Ằ|Ẵ|Ặ|Ẳ/g.test(str)) return true;
    if (/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ|ẳ/g.test(str)) return true;
    if (/É|È|Ẽ|Ẹ|Ẻ|Ê|Ế|Ề|Ễ|Ệ|Ể/g.test(str)) return true;
    if (/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g.test(str)) return true;
    if (/Í|Ì|Ĩ|Ị|Ỉ/g.test(str)) return true;
    if (/ì|í|ị|ỉ|ĩ/g.test(str)) return true;
    if (/Ó|Ò|Õ|Ọ|Ỏ|Ô|Ố|Ồ|Ỗ|Ộ|Ổ|Ơ|Ớ|Ờ|Ỡ|Ợ|Ở/g.test(str)) return true;
    if (/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g.test(str)) return true;
    if (/Ú|Ù|Ũ|Ụ|Ủ|Ư|Ứ|Ừ|Ữ|Ự|Ử/g.test(str)) return true;
    if (/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g.test(str)) return true;
    if (/Y|Ý|Ỳ|Ỹ|Ỵ|Ỷ/g.test(str)) return true;
    if (/ỳ|ý|ỵ|ỷ|ỹ/g.test(str)) return true;
    if (/Đ/g.test(str)) return true;
    if (/đ/g.test(str)) return true;
    // Some system encode vietnamese combining accent as individual utf-8 characters
    if (/\u0300|\u0301|\u0303|\u0309|\u0323/g.test(str)) return true; // Huyền sắc hỏi ngã nặng
    if (/\u02C6|\u0306|\u031B/g.test(str)) return true; // Â, Ê, Ă, Ơ, Ư

    return false;
  },
  /**
   * vietnamese cccd only
   * @param {string} cccd
   * @returns
   */
  isValidCCCD(cccd) {
    let regExp9so = /^([0-9]{9})$/;
    let regExp12so = /^([0-9]{12})$/;

    return regExp9so.test(cccd) || regExp12so.test(cccd);
  },
  /**
   * '' are considered empty !
   * @template T
   * @param {T} obj
   * @return {T}
   */
  removeEmptyProps(obj) {
    Object.entries(obj).forEach(([key, value]) => {
      if (value === null || value === undefined) {
        obj[key] = '';
      }
    });

    return obj;
  },
  /**
   *
   * @param {string} string
   * @returns
   */
  isBase64Encoded(string) {
    let regex = /^data:image\/(png|jpe?g|gif);base64,/;
    return regex.test(string);
  },
  /**
   * Format ab.cd
   * @param {number} iNumber
   * @returns
   */
  formatNumber(iNumber) {
    return new Intl.NumberFormat('de-DE').format(_.toNumber(iNumber));
  },
  sleep(ms = 0) {
    return new Promise(resolve => {
      setTimeout(resolve, ms);
    });
  },
  /**
   * @template T
   * @param {T} obj
   * @param {any=} replaceBy
   * @returns {T}
   */
  deleteEmptyProps(obj, replaceBy) {
    Object.entries(obj).forEach(([key, value]) => {
      if (value === undefined || value === null) {
        if (replaceBy === undefined) {
          delete obj[key];
        } else {
          obj[key] = replaceBy;
        }
      }
    });

    return obj;
  },
  /**
   *
   * @param {string} str
   * @returns
   */
  parseNumber(str) {
    return parseFloat(str.replace(/\./g, '').replace(',', '.'));
  },
  /**
   *
   * @param {string} s1
   * @param {string} s2
   * @returns
   */
  similarity(s1, s2) {
    /**
     *
     * @param {string} s1
     * @param {string} s2
     * @returns
     */
    // eslint-disable-next-line @typescript-eslint/no-shadow
    function editDistance(s1, s2) {
      s1 = s1.toLowerCase();
      s2 = s2.toLowerCase();

      var costs = [];
      for (var i = 0; i <= s1.length; i++) {
        var lastValue = i;
        for (var j = 0; j <= s2.length; j++) {
          if (i == 0) costs[j] = j;
          else {
            if (j > 0) {
              var newValue = costs[j - 1];
              if (s1.charAt(i - 1) != s2.charAt(j - 1))
                newValue =
                  Math.min(Math.min(newValue, lastValue), costs[j]) + 1;
              costs[j - 1] = lastValue;
              lastValue = newValue;
            }
          }
        }
        if (i > 0) costs[s2.length] = lastValue;
      }
      return costs[s2.length];
    }

    var longer = s1;
    var shorter = s2;
    if (s1.length < s2.length) {
      longer = s2;
      shorter = s1;
    }
    var longerLength = longer.length;
    if (longerLength == 0) {
      return 1.0;
    }
    return (
      (longerLength - editDistance(longer, shorter)) / parseFloat(longerLength)
    );
  },
  // l is for left index and r is
  // right index of the sub-array
  // of arr to be sorted */
  /**
   * This mutate array
   * @template T
   * @param {T[]} arr
   * @param {number} l left index
   * @param {number} r right index
   * @param {(item:T)=>any=} selector selector to sort
   * @returns
   */
  mergeSort(arr, l, r, selector = a => a) {
    if (l >= r) {
      return; //returns recursively
    }
    var m = l + parseInt((r - l) / 2);
    this.mergeSort(arr, l, m, selector);
    this.mergeSort(arr, m + 1, r, selector);
    merge(arr, l, m, r, selector);
  },
  /**
   *
   * @param  {...any} arr
   */
  mergeData(...arr) {
    // Note: if you change `_.isNil` to `_.isUndefined`
    // then you'd get the `_.defaults()` normal behavior
    const nilMerge = (a, b) => (_.isNil(a) ? b : a);

    const nilMergeDeep = (a, b) =>
      _.isObject(a) && !_.isArray(a) && !_.isDate(a)
        ? // recursively merge objects with nilMergeDeep customizer
          _.mergeWith({}, a, b, nilMergeDeep)
        : // let's use our default customizer
          nilMerge(a, b);

    // defaults deep with null/undefined
    const result2 = _.mergeWith({}, ...arr, nilMergeDeep);

    return result2;
  },
  /**
   *
   * @param {string} url
   */
  getFullUrl(url) {
    if (typeof url != 'string') return url;

    if (!url) return url;

    if (
      !url.startsWith('http://') &&
      !url.startsWith('blob://') &&
      !url.startsWith('file://') &&
      !url.startsWith('https://')
    ) {
      return `${BASE_URL}/${url}`;
    }

    return url;
  },
  generateOTP() {
    return Math.floor(100000 + Math.random() * 900000)
      .toString()
      .slice(0, 7);
  },
};

// Merges two subarrays of arr[].
// First subarray is arr[l..m]
// Second subarray is arr[m+1..r]
/**
 *
 * @template T
 * @param {T[]} arr
 * @param {number} l left index
 * @param {number} m middle index
 * @param {number} r right index
 * @param {(item:T)=>any} selector selector to sort
 */
function merge(arr, l, m, r, selector) {
  var n1 = m - l + 1;
  var n2 = r - m;

  // Create temp arrays
  var L = new Array(n1);
  var R = new Array(n2);

  // Copy data to temp arrays L[] and R[]
  for (let i = 0; i < n1; i++) L[i] = arr[l + i];
  for (let j = 0; j < n2; j++) R[j] = arr[m + 1 + j];

  // Merge the temp arrays back into arr[l..r]

  // Initial index of first subarray
  var i = 0;

  // Initial index of second subarray
  var j = 0;

  // Initial index of merged subarray
  var k = l;

  while (i < n1 && j < n2) {
    if (selector(L[i]) <= selector(R[j])) {
      arr[k] = L[i];
      i++;
    } else {
      arr[k] = R[j];
      j++;
    }
    k++;
  }

  // Copy the remaining elements of
  // L[], if there are any
  while (i < n1) {
    arr[k] = L[i];
    i++;
    k++;
  }

  // Copy the remaining elements of
  // R[], if there are any
  while (j < n2) {
    arr[k] = R[j];
    j++;
    k++;
  }
}
