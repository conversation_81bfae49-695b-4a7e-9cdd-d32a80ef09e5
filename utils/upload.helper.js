const fs = require('fs');
const path = require('path');
const utils = require('.');
const _ = require('lodash');
const { UPLOAD_DIR } = require('../configs/env');

/**
 *
 * @param {import('express').Request} req
 * @param {string} fieldName
 * @param {string} savePath
 * @param {string} current
 * @returns
 */
const uploadFile = async (req, fieldName, savePath, current = '') => {
  if (req.files?.[fieldName]) {
    let file = req.files?.[fieldName];

    if (_.isArray(file)) file = file[0];

    let filepath = `${UPLOAD_DIR}/${savePath}`;
    let filename = `${utils.escapeStr(file.name)}`.split('.');
    filename = `${filename[0]}_${Date.now()}.${filename[1]}`;

    if (!fs.existsSync(filepath)) {
      fs.mkdirSync(filepath);
    }

    await file.mv(path.join(filepath, filename));
    if (current) {
      const currentFilePath = new URL(current).pathname.slice(1);
      fs.unlink(currentFilePath, err => {
        if (err) console.log('File không tồn tại!');
      });
    }

    return `uploads/${savePath}/${filename}`;
  }

  return null;
};
module.exports = uploadFile;
