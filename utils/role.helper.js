const _ = require('lodash');

module.exports = {
  /**
   * Hide non-access organization/department
   * @param {import('../types').Organization} organization
   * @param {import('../types').AccountOrganizationAccess[]} accountOrganizationAccesses
   */
  buildWithOrganizationAccess(organization, accountOrganizationAccesses) {
    // if (
    //   accountOrganizationAccesses.find(
    //     item => item.organizationId == organization.organizationId,
    //   )
    // ) {
    //   return organization;
    // }

    organization.OrganizationBranches = organization.OrganizationBranches.map(
      branch => this.buildWithBranchAccess(branch, accountOrganizationAccesses),
    ).filter(_.isObject);

    if (
      organization.OrganizationBranches.length ||
      accountOrganizationAccesses.find(
        item => item.organizationId == organization.organizationId,
      )
    ) {
      return organization;
    }

    return null;
  },
  /**
   *
   * @param {import('../types').Organization} organizationBranch
   * @param {import('../types').AccountOrganizationAccess[]} accountOrganizationAccesses
   */
  buildWithBranchAccess(organizationBranch, accountOrganizationAccesses) {
    // if (
    //   accountOrganizationAccesses.find(
    //     item => item.organizationId == organizationBranch.organizationId,
    //   )
    // ) {
    //   return organizationBranch;
    // }

    organizationBranch.OrganizationDepartments =
      organizationBranch.OrganizationDepartments.map(deparment =>
        this.buildWithDepartmentAccess(deparment, accountOrganizationAccesses),
      ).filter(_.isObject);

    if (
      organizationBranch.OrganizationDepartments.length ||
      accountOrganizationAccesses.find(
        item => item.organizationId == organizationBranch.organizationId,
      )
    ) {
      return organizationBranch;
    }

    return null;
  },
  /**
   *
   * @param {import('../types').OrganizationDepartment} deparment
   * @param {import('../types').AccountOrganizationAccess[]} accountOrganizationAccesses
   */
  buildWithDepartmentAccess(deparment, accountOrganizationAccesses) {
    if (
      accountOrganizationAccesses.find(
        item =>
          item.organizationDepartmentId == deparment.organizationDepartmentId,
      )
    ) {
      return deparment;
    }
    return null;
  },
  /**
   * has access this organization
   * @param {{organizationId?:number,organizationDepartmentId?:number}} param0
   * @param {import('../types').AccountOrganizationAccess[]} accountOrganizationAccesses
   */
  hasAccess(
    { organizationId, organizationDepartmentId },
    accountOrganizationAccesses,
  ) {
    return accountOrganizationAccesses.find(
      item =>
        (organizationId && item.organizationId == organizationId) ||
        (organizationDepartmentId &&
          item.organizationDepartmentId == organizationDepartmentId),
    );
  },
};
