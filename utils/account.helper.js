const utils = require('.');
const { GENDER } = require('../configs/constants/account.constant');
const { PEPPER } = require('../configs/env');
const _ = require('lodash');
const moment = require('moment');

module.exports = {
  /**
   *
   * @param {string} rawPassword get from client
   * @param {string} salt create by server
   */
  genSavedPassword(rawPassword, salt) {
    return utils.hashSHA256(
      `${utils.hashSHA256(`${rawPassword}${salt}`)}${PEPPER}`,
    );
  },
  /**
   *
   * @param {string} rawPassword get from client
   * @param {string} salt create by server
   * @param {string} storedPassword saved in db
   */
  validatePassword(rawPassword, salt, storedPassword) {
    //console.log(this.genSavedPassword(rawPassword, salt));
    return storedPassword === this.genSavedPassword(rawPassword, salt);
  },
  randomPassword() {
    return Math.random().toString(36).substring(2, 8);
  },
  getPublicAccountAttributes() {
    const { Account } = require('../models');

    let attributes = Object.keys(Account.getAttributes());

    return attributes.filter(
      attr =>
        ![
          'activateToken',
          'password',
          'salt',
          'expirationDateResetPasswordToken',
          'resetPasswordToken',
          'metaAccessToken',
          'metaAccessTokenExpireDate',
        ].includes(attr),
    );
  },
  getJwtAccountAttributes() {
    return ['accountId'];
  },
  getOwnAccountAttributes() {
    const { Account } = require('../models');

    let attributes = Object.keys(Account.getAttributes());

    return attributes.filter(
      attr =>
        ![
          'activateToken',
          'password',
          'salt',
          'expirationDateResetPasswordToken',
          'resetPasswordToken',
          'metaAccessToken',
          'metaAccessTokenExpireDate',
        ].includes(attr),
    );
  },
  /**
   *
   * @param {string} gender
   * @returns
   */
  isValidGender(gender) {
    return !!GENDER[gender];
  },
};
