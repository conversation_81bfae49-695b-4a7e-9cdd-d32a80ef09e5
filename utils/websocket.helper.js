const { Server } = require('http');
const { server: WSServer, connection: WSConnection } = require('websocket');
const { now, isValidEmail, isValidTaxCode, isValidUsername } = require('.');
const _ = require('lodash');
const {
  WS_CODE,
  PROCESS_EMIT_SOCKET_EVENT,
} = require('../configs/constants/websocket.constant');
const {
  ERROR_MISSING_PARAMETERS,
  ERROR_USER_NOT_ACTIVE,
  ERROR_INVALID_TAXCODE,
  ERROR_PERMISSION_DENIED,
  ERROR_TOKEN_EXPIRED,
  ERROR_INVALID_EMAIL,
  ERROR_INVALID_JSON,
  ERROR_INVALID_USERNAME,
  ERROR_USER_NOT_FOUND,
} = require('../configs/error.vi');
const { decodeJwtToken } = require('./jwt.helper');
const jwtHelper = require('./jwt.helper');
const { Op } = require('sequelize');
const accountHelper = require('./account.helper');
const utils = require('.');

const sTag = `WS`;

/**
 * Lưu mảng socket connection của từng accountId
 *
 * **Lưu ý khi dùng với cluster mode**
 * @type {{[x:number|string]:WSConnection[]}}
 */
const account_ws_dict = {};

/**
 * create websocket server
 * @param {Server} httpServer
 */
async function createSocketServer(httpServer) {
  let wsServer = new WSServer({
    httpServer,
    // You should not use autoAcceptConnections for production
    // applications, as it defeats all standard cross-origin protection
    // facilities built into the protocol and the browser.  You should
    // *always* verify the connection's origin and decide whether or not
    // to accept it.
    autoAcceptConnections: false,
    keepaliveInterval: 30000,
    // keepaliveGracePeriod: 15000,
    dropConnectionOnKeepaliveTimeout: false,
    ignoreXForwardedFor: true,
  });

  function originIsAllowed(origin) {
    // put logic here to detect whether the specified origin is allowed.
    return true;
  }

  wsServer.on('connect', connection => {
    console.log(sTag, 'New connection');
    connection.sendUTF(JSON.stringify({ message: 'Hello' }));
  });

  wsServer.on('close', connection => {
    console.log(
      sTag,
      now(),
      `Connection from ${connection.socket.remoteAddress} closed`,
    );

    if (connection.account) {
      let { accountId, jwtToken } = connection.account;
      if (_.isArray(account_ws_dict[accountId])) {
        account_ws_dict[accountId].splice(
          account_ws_dict[accountId].find(conn => conn == connection),
          1,
        );
      }

      if (jwtToken) {
        if (_.isArray(account_ws_dict[jwtToken])) {
          account_ws_dict[jwtToken].splice(
            account_ws_dict[jwtToken].find(conn => conn == connection),
            1,
          );
        }
      }
      delete connection['account'];
    }
  });

  wsServer.on('request', request => {
    if (!originIsAllowed(request.origin)) {
      request.reject();
      console.log(
        sTag,
        now(),
        `Connect request from ${request.origin} from ${request.socket.remoteAddress} denied`,
      );
      return;
    }

    let connection = request.accept(null, request.origin);

    // console.log(
    //   sTag,
    //   now(),
    //   `Connection accepted for ${connection.socket.remoteAddress}`,
    // );

    connection.on('close', () => {
      console.log(
        sTag,
        now(),
        `Connection from ${connection.socket.remoteAddress} closed`,
      );

      // if (connection.account) {
      //   let { accountId, jwtToken } = connection.account;
      //   if (
      //     _.isArray(account_ws_dict[accountId]) &&
      //     account_ws_dict[accountId].findIndex(conn => conn == connection) >= 0
      //   ) {
      //     account_ws_dict[accountId].splice(
      //       account_ws_dict[accountId].findIndex(conn => conn == connection),
      //       1,
      //     );
      //   }

      //   if (jwtToken) {
      //     if (
      //       _.isArray(account_ws_dict[jwtToken]) &&
      //       account_ws_dict[jwtToken].findIndex(conn => conn == connection) >= 0
      //     ) {
      //       account_ws_dict[jwtToken].splice(
      //         account_ws_dict[jwtToken].findIndex(conn => conn == connection),
      //         1,
      //       );
      //     }
      //   }

      //   delete connection['account'];
      // }
    });

    connection.on('message', async data => {
      try {
        if (data.type != 'utf8' || !utils.isJSONStringified(data.utf8Data)) {
          throw new Error(ERROR_INVALID_JSON);
        }

        if (!connection.connected) return;

        let params = JSON.parse(data.utf8Data);

        // console.log('Connection message', { params }, connection.remoteAddress);

        let { code } = params;

        /* login */
        if (code == WS_CODE.CODE_AUTH_LOGIN) {
          let { accessToken } = params;

          if (!accessToken) {
            throw new Error(
              JSON.stringify({
                code: WS_CODE.CODE_AUTH_LOGIN,
                result: 'failed',
                message: ERROR_MISSING_PARAMETERS,
              }),
            );
          }

          const db = require('../models');
          const { Account, AccessToken } = db;

          /**
           * @type {import('../types').Account}
           */
          let account;

          if (!jwtHelper.verifyJWTToken(accessToken)) {
            throw new Error(ERROR_PERMISSION_DENIED);
          }
          if (jwtHelper.isJWTTokenExpired(accessToken)) {
            throw new Error(ERROR_TOKEN_EXPIRED);
          }

          account = jwtHelper.decodeJwtToken(accessToken);

          if (
            !(await AccessToken.findOne({
              where: {
                accessToken: utils.hashSHA256(accessToken),
                accountId: account.accountId,
              },
            }))
          ) {
            throw new Error(ERROR_TOKEN_EXPIRED);
          }

          account = (
            await Account.findOne({
              where: { accountId: account.accountId },
              include: [{ model: db.Company }],
            })
          ).toJSON();

          if (!account.active) {
            throw new Error(
              JSON.stringify({
                code: WS_CODE.CODE_AUTH_LOGIN,
                message: ERROR_USER_NOT_ACTIVE,
              }),
            );
          }

          connection.sendUTF(
            JSON.stringify({
              code: WS_CODE.CODE_AUTH_LOGIN,
              result: 'success',
              email: account.email,
              taxCode: account.taxCode,
              message: `Welcome ${account.fullname ?? account.email}, MST: ${
                account.Company.taxCode
              }`,
            }),
          );

          // process.send({
          //   action: PROCESS_EMIT_SOCKET_EVENT,
          //   payload: 'Test broadcast node processed',
          // });

          /* remove old logged in  */
          if (connection.account) {
            let { accountId } = connection.account;
            if (
              _.isArray(account_ws_dict[accountId]) &&
              account_ws_dict[accountId].findIndex(
                conn => conn == connection,
              ) >= 0
            ) {
              account_ws_dict[accountId].splice(
                account_ws_dict[accountId].findIndex(
                  conn => conn == connection,
                ),
                1,
              );
            }
            delete connection['account'];
          }

          let { accountId } = account;

          if (!account_ws_dict[accountId]) account_ws_dict[accountId] = [];
          account_ws_dict[accountId].push(connection);

          connection['account'] = account;
        } else if (code == WS_CODE.CODE_AUTH_LOGOUT) {
          /* logout */

          let { accountId } = connection.account;
          if (_.isArray(account_ws_dict[accountId])) {
            account_ws_dict[accountId].splice(
              account_ws_dict[accountId].findIndex(conn => conn == connection),
              1,
            );
          }
          delete connection['account'];

          connection.sendUTF(
            JSON.stringify({
              code: WS_CODE.CODE_AUTH_LOGOUT,
              result: 'success',
            }),
          );
        }
      } catch (error) {
        if (utils.isJSONStringified(error.message)) {
          let { code, message } = JSON.parse(error.message);

          connection.sendUTF(
            JSON.stringify({
              code: code ?? WS_CODE.CODE_ERROR_INTERNAL,
              result: 'failed',
              message: message,
            }),
          );
        } else {
          connection.sendUTF(
            JSON.stringify({
              code: WS_CODE.CODE_ERROR_INTERNAL,
              result: 'failed',
              message: error.message,
            }),
          );
        }
      }
    });
  });
}

module.exports.createSocketServer = createSocketServer;

/**
 * Send to socket
 * @typedef Param
 * @property {number} accountId
 * @property {boolean=} newNotification
 * @property {{code: string, result: 'success'|'failed'}=} message

 *
 * @param {Param} param
 * @returns
 */
async function sendMessageToAccountSocket(param) {
  let { accountId } = param;

  if (!accountId) return;

  if (param.newNotification) {
    /* broadcast to all */
    let connections = account_ws_dict[accountId];

    if (_.isArray(connections)) {
      connections.forEach(conn => {
        if (!conn.connected) return;

        conn.sendUTF(JSON.stringify(param.message));
      });
    }
  }
}

module.exports.sendMessageToAccountSocket = sendMessageToAccountSocket;

/**
 * Test only
 * @param {*} param
 */
async function broadcastToAllSocket(param) {
  Object.values(account_ws_dict).forEach(connections => {
    connections.forEach(conn => {
      if (!conn.connected) return;

      conn.sendUTF(`${param} process id: ${process.pid}`);
    });
  });
}

module.exports.broadcastToAllSocket = broadcastToAllSocket;

/**
 * node process emit event
 *
 * @param {Param} param
 * @returns
 */
function emitSocketEvent(param) {
  /**
   * @type {import('cluster').Cluster}
   */
  const cluster = require('cluster');

  if (cluster.isMaster) {
    global.workers.forEach(worker =>
      worker.send(
        { action: PROCESS_EMIT_SOCKET_EVENT, payload: param },
        undefined,
        undefined,
        () => {
          /*  */
        },
      ),
    );
  } else {
    process.send({
      action: PROCESS_EMIT_SOCKET_EVENT,
      payload: param,
    });
  }
}

module.exports.emitSocketEvent = emitSocketEvent;
