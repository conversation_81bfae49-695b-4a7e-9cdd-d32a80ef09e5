/* eslint-disable @typescript-eslint/no-shadow */
const nodemailer = require('nodemailer');
const SMTPTransport = require('nodemailer/lib/smtp-transport');
const { OAuth2Client } = require('google-auth-library');
const {
  GOOGLE_MAILER_CLIENT_ID,
  GOOGLE_MAILER_CLIENT_SECRET,
  GOOGLE_MAILER_REFRESH_TOKEN,
  ADMIN_EMAIL_ADDRESS,
  ADMIN_EMAIL_PASSWORD,
  SMTP_USERNAME,
  SMTP_PASSWORD,
  DEBUG,
  SMTP_SERVER,
} = require('../configs/env');
const { ERROR_RECIPIENTS_EMPTY } = require('../configs/error.vi');

// const { EmailHistory } = db;

const _ = require('lodash');
const utils = require('.');
const { EMAIL_STATUS } = require('../configs/constants/other.constant');
const { LOAI_KHACH_HANG } = require('../configs/constants/account.constant');

const myOAuth2Client = new OAuth2Client(
  GOOGLE_MAILER_CLIENT_ID,
  GOOGLE_MAILER_CLIENT_SECRET,
);

myOAuth2Client.setCredentials({
  refresh_token: GOOGLE_MAILER_REFRESH_TOKEN,
});

// let transporter = nodemailer.createTransport({
//   // service: 'gmail',
//   host: 'mail.i-ca.vn',
//   port: 587,
//   secure: false, // upgrade later with STARTTLS
//   auth: {
//     user: ADMIN_EMAIL_ADDRESS, // generated ethereal user
//     pass: ADMIN_EMAIL_PASSWORD,
//   },
// });

let transporter = nodemailer.createTransport({
  // service: 'gmail',
  host: SMTP_SERVER,
  port: 587,
  secure: false, // upgrade later with STARTTLS
  // tls: {},
  auth: {
    user: SMTP_USERNAME, // generated ethereal user
    pass: SMTP_PASSWORD,
  },
});

/**
 * @description send email notification to account
 *
 * Không nên await khi gọi hàm này vì thời gian để resolve thành công khá lâu
 *
 * @typedef Props
 * @property {string} params.html in html
 * @property {string} params.subject email subject
 * @property {string[]=} params.emails if no target email, will look for accountId
 * @property {import('../types').EmailType} params.emailType
 * @property {{name:string,email:string,taxCode:string}[]} params.recipients người nhan email. chỉ dùng để lưu vào lịch sử gửim mail
 * @property {boolean=} saveToHistory save to email history
 * @property {import('../types').LoaiKhachHang=} loaiKhachHang luu loai khach hang, VIETINVOICE / IBOT
 * @property {number=} accountId id khach hang
 *
 * @param {Props & nodemailer.SendMailOptions}
 * @return {Promise<SMTPTransport.SentMessageInfo|null>} return `null` if send false
 *
 */
async function sendEmail({
  emails,
  emailType,
  recipients,
  saveToHistory = true,
  loaiKhachHang = LOAI_KHACH_HANG.QUAN_LY_HOA_DON,
  accountId,
  ...options
}) {
  try {
    const db = require('../models');
    // const myAccessTokenObject = await myOAuth2Client.getAccessToken();

    // const myAccessToken = myAccessTokenObject?.token;

    // create reusable transporter object using the default SMTP transport
    // let transporter = nodemailer.createTransport({
    //   service: 'gmail',
    //   auth: {
    //     user: ADMIN_EMAIL_ADDRESS, // generated ethereal user
    //     accessToken: myAccessToken,
    //     type: 'OAUTH2',
    //     clientSecret: GOOGLE_MAILER_CLIENT_SECRET,
    //     clientId: GOOGLE_MAILER_CLIENT_ID,
    //     refreshToken: GOOGLE_MAILER_REFRESH_TOKEN,
    //   },
    // });
    let targetEmails = _.isArray(emails) && emails.length > 0 ? emails : [];

    if (!targetEmails.length) {
      if (_.isArray(recipients))
        targetEmails = recipients.map(r => r.email).filter(_.isString);
    }
    targetEmails = targetEmails.filter(utils.isValidEmail);

    if (!targetEmails.length) throw new Error(ERROR_RECIPIENTS_EMPTY);
    // Generate test SMTP service account from ethereal.email
    // Only needed if you don't have a real mail account for testing
    // let testAccount = await nodemailer();

    // targetEmails = _.uniq(targetEmails);

    try {
      // send mail with defined transport object
      let info = await transporter.sendMail({
        from: `${options.sender ? options.sender.name : `ICORP`} ${
          options.sender ? options.sender.address : ADMIN_EMAIL_ADDRESS
        }`, // sender address
        to: targetEmails, // list of receivers
        textEncoding: !!DEBUG ? 'quoted-printable' : 'base64',
        // encoding:'',
        ...options,
      });

      console.log(
        'Message sent: %s',
        info.messageId,
        `, recipients: ${recipients.toString()}`,
      );
      // Message sent: <<EMAIL>>

      // Preview only available when sending through an Ethereal account
      console.log('Preview URL: %s', nodemailer.getTestMessageUrl(info));
      // Preview URL: https://ethereal.email/message/WaQKMgKddxQDoou...

      /* save to Email-History */
      try {
        if (saveToHistory) {
          let rs = await db.EmailHistory.create({
            emailContent: options.html,
            emailTitle: options.subject,
            emailType,
            emailRecipients: _.isArray(recipients)
              ? recipients
              : targetEmails.concat(options.cc).map(e => ({ email: e })),
            status: EMAIL_STATUS.DELIVERED,
            messageId: info.messageId,
            loaiKhachHang,
            accountId,
          });
        }
      } catch (error) {
        console.warn(error);
      }

      return info;
    } catch (error) {
      console.log(error);

      if (saveToHistory) {
        let rs = await db.EmailHistory.create({
          emailContent: options.html,
          emailTitle: options.subject,
          emailType,
          emailRecipients: _.isArray(recipients)
            ? recipients
            : targetEmails.concat(options.cc).map(e => ({ email: e })),
          status: EMAIL_STATUS.FAILED,
          accountId,
        }).catch(console.warn);
      }

      return null;
    }
  } catch (error) {
    console.warn(error);
  }
}

module.exports.sendEmail = sendEmail;
