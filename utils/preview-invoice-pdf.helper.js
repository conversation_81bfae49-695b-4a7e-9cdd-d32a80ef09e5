/* eslint-disable @typescript-eslint/no-shadow */
const _ = require('lodash');
const path = require('path');
const fs = require('fs');
const moment = require('moment');
const PdfPrinter = require('pdfmake');

const {
  isValidNumber,
  isBase64Encoded,
  formatNumber,
  deleteEmptyProps,
  isValidPhone,
  isValidTaxCode,
} = require('.');
const {
  INVOICE_TYPE,
  INVOICE_TYPE_CODE,
  PRODUCT_TYPE,
} = require('../configs/constants/invoice.constant');
const e = require('express');
const utils = require('.');

const setTopMarginOfCellForVerticalCentering = (ri, node) => {
  let padding = 1;
  const calcCellHeight = (cell, ci) => {
    if (cell._height !== undefined) {
      return cell._height;
    }
    let width = 0;
    for (let i = ci; i < ci + (cell.colSpan || 1); i++) {
      width += node.table.widths[i]._calcWidth;
    }
    let calcLines = inlines => {
      let tmpWidth = width;
      let lines = 1;
      inlines.forEach(inline => {
        tmpWidth = tmpWidth - inline.width;
        if (tmpWidth < 0) {
          lines++;
          tmpWidth = width - inline.width;
        }
      });
      return lines;
    };

    cell._height = 0;
    if (cell._inlines && cell._inlines.length) {
      let lines = calcLines(cell._inlines);
      cell._height = cell._inlines[0].height * lines;
    } else if (cell.stack && cell.stack[0] && cell.stack[0]._inlines[0]) {
      cell._height = cell.stack
        .map(item => {
          let lines = calcLines(item._inlines);
          return item._inlines[0].height * lines;
        })
        .reduce((prev, next) => prev + next);
    } else if (cell.table) {
      // TODO...
      // console.log(cell);
    }

    cell._space = cell._height;
    if (cell.rowSpan) {
      for (let i = ri + 1; i < ri + (cell.rowSpan || 1); i++) {
        cell._space +=
          Math.max(...calcAllCellHeights(i)) + padding * (i - ri) * 2;
      }
      return 0;
    }

    ci++;
    return cell._height;
  };
  const calcAllCellHeights = rIndex => {
    return node.table.body[rIndex].map((cell, ci) => {
      return calcCellHeight(cell, ci);
    });
  };

  calcAllCellHeights(ri);
  const maxRowHeights = {};
  node.table.body[ri].forEach(cell => {
    if (
      !maxRowHeights[cell.rowSpan] ||
      maxRowHeights[cell.rowSpan] < cell._space
    ) {
      maxRowHeights[cell.rowSpan] = cell._space;
    }
  });

  node.table.body[ri].forEach((cell, index) => {
    if (cell.ignored) return;

    if (cell._rowSpanCurrentOffset) {
      cell._margin = [0, 0, 0, 0];
    } else if (ri == 0 || index != 1) {
      let topMargin = (maxRowHeights[cell.rowSpan] - cell._height) / 2;
      if (cell._margin) {
        cell._margin[1] += topMargin;
      } else {
        cell._margin = [0, topMargin, 0, 0];
      }
    }
  });

  return 0;
};

/* Type 1 */
const TEMPLATE_1 = {
  metadata: {
    fontFamily: 'Times New Roman',
    fontSize: 14,
    color: 'black',
    lineType: 'dotted',
    lineHeight: 1.7,
    logo: { x: 0, y: 15, width: 138, height: 83 },
    background: { opacity: 100 },
    surround: null,
    region: {
      sellerInfo: {
        alignType: 'ALIGN_TYPE_1',
        tableHeaderColor: null,
        showColumnIndex: false,
      },
      invoiceInfo: {},
      invoiceInfoOther: {},
      buyerInfo: { alignType: 'ALIGN_TYPE_1' },
      tableDetail: { minItemRows: 3 },
      tableDetail1: { minItemRows: 3 },
      tableFooter: {
        fullColumnRowIndexs: [3, 4],
        fullColumnDataFields: ['totalAmountInWords', 'templateNote'],
      },
      tableFooter1: {
        fullColumnRowIndexs: [9, 10],
        fullColumnDataFields: ['TotalAmountInWords', 'TemplateNote'],
      },
      currencyBlock: {},
      signXml: {},
    },
    subFields: { LjF2f: [{ width: 320.5 }, { width: '*' }] },
  },
  region: [
    { regionName: 'firstInvoice', widths: [140, '*'] },
    { regionName: 'invoiceInfo', widths: [123, 415, '*'] },
    { regionName: 'buyerInfo', widths: [630, '*'] },
    {
      regionName: 'tableDetail',
      widths: [
        { width: 38, show: 1, dataIndex: 'lineNumber' },
        { width: 54, show: 0, dataIndex: 'inventoryItemCode' },
        { width: 254, show: 1, dataIndex: 'itemName' },
        { width: 112, show: 0, dataIndex: 'serialNumber' },
        { width: 104, show: 1, dataIndex: 'unitName' },
        { width: 101, show: 1, dataIndex: 'quantity' },
        { width: 110, show: 1, dataIndex: 'unitPrice' },
        { width: '*', show: 1, dataIndex: 'totalAmount' },
        { width: '*', show: 0, dataIndex: 'inventoryItemNote' },
      ],
    },
    {
      regionName: 'tableDetail1',
      widths: [
        { width: 38, show: 1, dataIndex: 'LineNumber' },
        { width: 54, show: 0, dataIndex: 'InventoryItemCode' },
        { width: 192, show: 1, dataIndex: 'ItemName' },
        { width: 112, show: 0, dataIndex: 'SerialNumber' },
        { width: 64, show: 1, dataIndex: 'UnitName' },
        { width: 73, show: 1, dataIndex: 'Quantity' },
        { width: 82, show: 1, dataIndex: 'UnitPrice' },
        { width: 97, show: 1, dataIndex: 'TotalAmount' },
        { width: 65, show: 1, dataIndex: 'VATPercentageDetail' },
        { width: '*', show: 1, dataIndex: 'VATAmount' },
        { width: '*', show: 0, dataIndex: 'InventoryItemNote' },
      ],
    },
    { regionName: 'tableFooter', widths: [180, 112, 255, '*'] },
    { regionName: 'tableFooter1', widths: [360, 137, 90, '*'] },
    {
      regionName: 'signXml',
      widths: [
        { width: 356, dataIndex: 'buyerSignRegion' },
        { width: '*', dataIndex: 'sellerSignRegion' },
      ],
    },
  ],
  calcConfig: null,
  invoiceTemplateId: 34,
  name: 'Hóa đơn GTGT mẫu 01',
  description: 'Hóa đơn GTGT mẫu hệ thống 01',
  mulTaxRateSupport: 1,
  form: 1,
  hasTaCode: 'C',
  year: 23,
  type: 'T',
  managementCode: 'YY',
  directTranfer: 1,
  paperSize: 'A4',
  // logo: 'c487287e-fb1e-4d0f-be78-3010af135dbb.png',
  customBackground: null,
  sellerInfoPosition: 1,
  isShowQRCode: 1,
  duplicateInfoMulPages: 1,
  isShowLabelEn: 1,
  isShowWatermarkInAdjustedInvoice: 0,
  logoPosition: 1,
  decreeCircularEnumId: 'DecCir78',
  decreeCircularEnum: 'Nghị định số 123/2020/NĐ-CP',
  createdByAccountId: 13,
  count: 1,
  active: 1,
  createdAt: '2023-08-09T15:21:16.000Z',
  updatedAt: '2023-08-09T15:21:16.000Z',
  companyId: null,
  backgroundId: 26,
  borderId: null,
  categoryId: 1,
  background: {
    backgroundId: 26,
    filename: '01a1e2e4-66bc-4e3d-86f1-a07b543fdce6.jpg',
    name: 'mau_menh_thuy_3',
  },
  border: null,
  fields: [
    {
      label: { value: 'Đơn vị bán hàng' },
      labelEn: { fontStyle: 'italic', value: '(Seller)' },
      value: {
        value: '',
        fontSize: 20,
        lineHeight: 1.25,
        fontWeight: 'bold',
        autoBreakLine: false,
        editable: false,
      },
      metadata: { displayType: 'CONTENT_LEFT', canHide: false },
      fieldId: 2519,
      parentFieldId: null,
      name: 'sellerName',
      regionName: 'sellerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Mã số thuế' },
      labelEn: { fontStyle: 'italic', value: '(Tax code)' },
      value: { value: '', editable: false },
      metadata: { canHide: false },
      fieldId: 2520,
      parentFieldId: null,
      name: 'sellerTaxCode',
      regionName: 'sellerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Địa chỉ' },
      labelEn: { fontStyle: 'italic', value: '(Address)' },
      value: { value: '', editable: false },
      metadata: {},
      fieldId: 2521,
      parentFieldId: null,
      name: 'sellerAddress',
      regionName: 'sellerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Điện thoại' },
      labelEn: { fontStyle: 'italic', value: '(Tel)' },
      value: { value: '', editable: false },
      metadata: {},
      fieldId: 2522,
      parentFieldId: null,
      name: 'sellerPhone',
      regionName: 'sellerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Fax' },
      labelEn: { fontStyle: 'italic', value: '' },
      value: { value: '', editable: false },
      metadata: {},
      fieldId: 2523,
      parentFieldId: null,
      name: 'sellerFax',
      regionName: 'sellerInfo',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: 'Website' },
      labelEn: { fontStyle: 'italic', value: '' },
      value: { value: '', editable: false },
      metadata: {},
      fieldId: 2524,
      parentFieldId: null,
      name: 'sellerWeb',
      regionName: 'sellerInfo',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: 'Email' },
      labelEn: { fontStyle: 'italic', value: '' },
      value: { value: '', editable: false },
      metadata: {},
      fieldId: 2525,
      parentFieldId: null,
      name: 'sellerEmail',
      regionName: 'sellerInfo',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: 'Số tài khoản' },
      labelEn: { fontStyle: 'italic', value: '(Bank account)' },
      value: { value: '', editable: false },
      metadata: {},
      fieldId: 2526,
      parentFieldId: null,
      name: 'sellerBankAccount',
      regionName: 'sellerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'HÓA ĐƠN GIÁ TRỊ GIA TĂNG',
        fontSize: 20,
        lineHeight: 1.25,
        fontWeight: 'bold',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(VAT INVOICE)',
        fontWeight: 'bold',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2527,
      parentFieldId: null,
      name: 'typeInvoice',
      regionName: 'invoiceInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: '' },
      labelEn: { fontStyle: 'italic', value: '' },
      value: { value: 'Tên khác' },
      metadata: { typeShow: 1 },
      fieldId: 2528,
      parentFieldId: null,
      name: 'subTitleInvoice',
      regionName: 'invoiceInfo',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: '(Hóa đơn chuyển đổi từ hóa đơn điện tử)' },
      labelEn: {
        fontStyle: 'italic',
        value: '(Invoice converted from E-invoice)',
      },
      value: { value: '' },
      metadata: { typeShow: 2, previewField: true },
      fieldId: 2529,
      parentFieldId: null,
      name: 'descriptionInvoicePaperClient',
      regionName: 'invoiceInfo',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: '(Bản thể hiện của hóa đơn điện tử)' },
      labelEn: { fontStyle: 'italic', value: '(E-Invoice viewer)' },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2530,
      parentFieldId: null,
      name: 'descriptionInvoiceClient',
      regionName: 'invoiceInfo',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: '' },
      labelEn: { fontStyle: 'italic', value: '' },
      value: {
        value: 'Ngày (Date)   tháng (month)   năm (year)   ',
        editable: false,
      },
      metadata: { typeShow: 1, canHide: false },
      fieldId: 2531,
      parentFieldId: null,
      name: 'dateInvoice',
      regionName: 'invoiceInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Mã CQT' },
      labelEn: { fontStyle: 'italic', value: '(Code)' },
      value: { value: '', editable: false },
      metadata: { typeShow: 3, canHide: false },
      fieldId: 2532,
      parentFieldId: null,
      name: 'invoiceCode',
      regionName: 'invoiceInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Ký hiệu' },
      labelEn: { fontStyle: 'italic', value: '(Serial)' },
      value: { value: '1K23TYY', fontSize: 15, editable: false },
      metadata: { typeShow: 3, canHide: false },
      fieldId: 2533,
      parentFieldId: null,
      name: 'invoiceSeries',
      regionName: 'invoiceInfoOther',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Số' },
      labelEn: { fontStyle: 'italic', value: '(No.)' },
      value: { value: '00000000', fontWeight: 'bold', editable: false },
      metadata: { typeShow: 3, canHide: false },
      fieldId: 2534,
      parentFieldId: null,
      name: 'invoiceNumber',
      regionName: 'invoiceInfoOther',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Họ tên người mua hàng' },
      labelEn: { fontStyle: 'italic', value: '(Buyer)' },
      value: { value: '', editable: false },
      metadata: { canHide: false },
      fieldId: 2535,
      parentFieldId: null,
      name: 'buyerName',
      regionName: 'buyerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Tên đơn vị' },
      labelEn: { fontStyle: 'italic', value: "(Company's name)" },
      value: { value: '', editable: false },
      metadata: { canHide: false },
      fieldId: 2536,
      parentFieldId: null,
      name: 'buyerLegalName',
      regionName: 'buyerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Mã số thuế' },
      labelEn: { fontStyle: 'italic', value: '(Tax code)' },
      value: { value: '', editable: false },
      metadata: { canHide: false },
      fieldId: 2537,
      parentFieldId: null,
      name: 'BuyerTaxCode',
      regionName: 'buyerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Địa chỉ' },
      labelEn: { fontStyle: 'italic', value: '(Address)' },
      value: { value: '', editable: false },
      metadata: { canHide: false },
      fieldId: 2538,
      parentFieldId: null,
      name: 'buyerAddress',
      regionName: 'buyerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Hình thức thanh toán' },
      labelEn: { fontStyle: 'italic', value: '(Payment method)' },
      value: { value: '', editable: false },
      metadata: { mergeField: '' },
      fieldId: 2539,
      parentFieldId: null,
      name: 'paymentMethod',
      regionName: 'buyerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Đồng tiền thanh toán' },
      labelEn: { fontStyle: 'italic', value: '(Payment Currency)' },
      value: { value: '', editable: false },
      metadata: { mergeField: '' },
      fieldId: 2540,
      parentFieldId: null,
      name: 'PaymentCurrency',
      regionName: 'buyerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Số tài khoản' },
      labelEn: { fontStyle: 'italic', value: '(Bank account)' },
      value: { value: '', editable: false },
      metadata: {},
      fieldId: 2541,
      parentFieldId: null,
      name: 'buyerBankAccount',
      regionName: 'buyerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'STT',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(No)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2542,
      parentFieldId: null,
      name: 'lineNumber',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Mã hàng',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Item code)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2543,
      parentFieldId: null,
      name: 'inventoryItemCode',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: {
        value: 'Tên hàng hóa, dịch vụ',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Name of goods and services)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2544,
      parentFieldId: null,
      name: 'itemName',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Quy cách',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Specification)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2545,
      parentFieldId: null,
      name: 'serialNumber',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: {
        value: 'Đơn vị tính',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Unit)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2546,
      parentFieldId: null,
      name: 'unitName',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Số lượng',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Quantity)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2547,
      parentFieldId: null,
      name: 'quantity',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Đơn giá',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Unit price)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2548,
      parentFieldId: null,
      name: 'unitPrice',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Thành tiền',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Amount)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2549,
      parentFieldId: null,
      name: 'totalAmount',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Ghi chú',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Note)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2550,
      parentFieldId: null,
      name: 'inventoryItemNote',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: {
        value: 'STT',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(No)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2551,
      parentFieldId: null,
      name: 'LineNumber',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Mã hàng',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Item code)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2552,
      parentFieldId: null,
      name: 'InventoryItemCode',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: {
        value: 'Tên hàng hóa, dịch vụ',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Name of goods and services)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2553,
      parentFieldId: null,
      name: 'ItemName',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Quy cách',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Specification)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2554,
      parentFieldId: null,
      name: 'SerialNumber',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: {
        value: 'Đơn vị tính',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Unit)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2555,
      parentFieldId: null,
      name: 'UnitName',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Số lượng',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Quantity)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2556,
      parentFieldId: null,
      name: 'Quantity',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Đơn giá',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Unit price)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2557,
      parentFieldId: null,
      name: 'UnitPrice',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Thành tiền',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Amount)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2558,
      parentFieldId: null,
      name: 'TotalAmount',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Thuế suất GTGT',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(VAT rate)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2559,
      parentFieldId: null,
      name: 'VATPercentageDetail',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Tiền thuế GTGT',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(VAT Amount)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2560,
      parentFieldId: null,
      name: 'VATAmount',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Ghi chú',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Note)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2561,
      parentFieldId: null,
      name: 'InventoryItemNote',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: 'Tỷ lệ CK' },
      labelEn: { fontStyle: 'italic', value: '(Discount rate)' },
      value: { value: '' },
      metadata: { typeShow: 2, previewField: true },
      fieldId: 2562,
      parentFieldId: null,
      name: 'discountRate',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: 'Số tiền chiết khấu' },
      labelEn: { fontStyle: 'italic', value: '(Discount amount)' },
      value: { value: '' },
      metadata: { typeShow: 2, previewField: true },
      fieldId: 2563,
      parentFieldId: null,
      name: 'totalDiscountAmount',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: '' },
      labelEn: { value: '' },
      value: { value: '' },
      metadata: {},
      fieldId: 2564,
      parentFieldId: null,
      name: 'notShow1',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Cộng tiền hàng' },
      labelEn: {
        fontStyle: 'italic',
        value: '(Total amount excl. VAT)',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2565,
      parentFieldId: null,
      name: 'totalAmountWithoutVAT',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Thuế suất GTGT' },
      labelEn: { fontStyle: 'italic', value: '(VAT rate)' },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2566,
      parentFieldId: null,
      name: 'vatPercentage',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Tiền thuế GTGT' },
      labelEn: { fontStyle: 'italic', value: '(VAT amount)' },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2567,
      parentFieldId: null,
      name: 'totalVATAmount',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: '' },
      labelEn: { value: '' },
      value: { value: '' },
      metadata: {},
      fieldId: 2568,
      parentFieldId: null,
      name: 'notShow2',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Tổng tiền thanh toán' },
      labelEn: { fontStyle: 'italic', value: '(Total amount)' },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2569,
      parentFieldId: null,
      name: 'totalAmountWithVAT',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Số tiền viết bằng chữ' },
      labelEn: {
        fontStyle: 'italic',
        value: '(Total amount in words)',
      },
      value: { fontStyle: 'italic', fontWeight: 'bold', value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2570,
      parentFieldId: null,
      name: 'totalAmountInWords',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Ghi chú' },
      labelEn: { fontStyle: 'italic', value: '(Note)' },
      value: { value: '.' },
      metadata: {},
      fieldId: 2571,
      parentFieldId: null,
      name: 'templateNote',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: {
        value: 'Tổng hợp',
        fontWeight: 'bold',
        editable: false,
        textAlign: 'center',
        lineHeight: 1.4,
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(In sumary)',
        fontWeight: 'bold',
        editable: false,
        textAlign: 'center',
        lineHeight: 1.4,
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2572,
      parentFieldId: null,
      name: 'General',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Thành tiền trước thuế GTGT',
        fontWeight: 'bold',
        editable: false,
        textAlign: 'center',
        lineHeight: 1.4,
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Total before VAT)',
        fontWeight: 'bold',
        editable: false,
        textAlign: 'center',
        lineHeight: 1.4,
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2573,
      parentFieldId: null,
      name: 'AmountWithoutVAT',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Tiền thuế GTGT',
        fontWeight: 'bold',
        editable: false,
        textAlign: 'center',
        lineHeight: 1.4,
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(VAT amount)',
        fontWeight: 'bold',
        editable: false,
        textAlign: 'center',
        lineHeight: 1.4,
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2574,
      parentFieldId: null,
      name: 'VATAmountDetail',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Cộng tiền thanh toán',
        fontWeight: 'bold',
        editable: false,
        textAlign: 'center',
        lineHeight: 1.4,
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Total amount)',
        fontWeight: 'bold',
        editable: false,
        textAlign: 'center',
        lineHeight: 1.4,
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2575,
      parentFieldId: null,
      name: 'TotalVATAmount',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Không kê khai thuế GTGT' },
      labelEn: {
        fontStyle: 'italic',
        value: '(Not required to declare, pay VAT)',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2576,
      parentFieldId: null,
      name: 'TotalAmountKKKNTFooter',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Không chịu thuế GTGT' },
      labelEn: { fontStyle: 'italic', value: '(VAT exemption)' },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2577,
      parentFieldId: null,
      name: 'TotalAmountVatKCT',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Thuế suất 0%' },
      labelEn: { fontStyle: 'italic', value: '(VAT rate 0%)' },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2578,
      parentFieldId: null,
      name: 'TotalAmountWithVAT0',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Thuế suất 5%' },
      labelEn: { fontStyle: 'italic', value: '(VAT rate 5%)' },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2579,
      parentFieldId: null,
      name: 'TotalAmountWithVAT5',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Thuế suất 8%' },
      labelEn: { fontStyle: 'italic', value: '(VAT rate 8%)' },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2580,
      parentFieldId: null,
      name: 'TotalAmountWithVAT8',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Thuế suất 10%' },
      labelEn: { fontStyle: 'italic', value: '(VAT rate 10%)' },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2581,
      parentFieldId: null,
      name: 'TotalAmountWithVAT10',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Thuế suất KHÁC' },
      labelEn: { fontStyle: 'italic', value: '(Other VAT rates)' },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2582,
      parentFieldId: null,
      name: 'TotalAmountVATKHACFooter',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Tổng cộng', fontWeight: 'bold' },
      labelEn: {
        fontStyle: 'italic',
        value: '(Total)',
        fontWeight: 'bold',
      },
      value: { value: '', fontWeight: 'bold' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2583,
      parentFieldId: null,
      name: 'TotalSumary',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Số tiền viết bằng chữ' },
      labelEn: {
        fontStyle: 'italic',
        value: '(Total amount in words)',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2584,
      parentFieldId: null,
      name: 'TotalAmountInWords',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Ghi chú' },
      labelEn: { fontStyle: 'italic', value: '(Note)' },
      value: { value: '.' },
      metadata: { typeShow: 3 },
      fieldId: 2585,
      parentFieldId: null,
      name: 'TemplateNote',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: 'Tỷ giá' },
      labelEn: { fontStyle: 'italic', value: '(Exchange Rate)' },
      value: { value: '' },
      metadata: { typeShow: 2, previewField: true },
      fieldId: 2586,
      parentFieldId: null,
      name: 'exchangeRate',
      regionName: 'currencyBlock',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: 'Quy đổi' },
      labelEn: { fontStyle: 'italic', value: '(Equivalence)' },
      value: { value: '' },
      metadata: { typeShow: 2, previewField: true },
      fieldId: 2587,
      parentFieldId: null,
      name: 'translationCurrency',
      regionName: 'currencyBlock',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: 'Người mua hàng', fontWeight: 'bold' },
      labelEn: { fontStyle: 'italic', value: '(Buyer)' },
      value: { value: '' },
      metadata: {
        mergeField: 'buyerSignRegion',
        typeShow: 2,
        canHide: false,
      },
      fieldId: 2588,
      parentFieldId: null,
      name: 'buyerSign',
      regionName: 'signXml',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: '(Chữ ký số (nếu có))', fontStyle: 'italic' },
      labelEn: { fontStyle: 'italic', value: '(Signature, full name)' },
      value: { value: '' },
      metadata: { mergeField: 'buyerSignRegion', typeShow: 2 },
      fieldId: 2589,
      parentFieldId: null,
      name: 'buyerSignFull',
      regionName: 'signXml',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Người bán hàng', fontWeight: 'bold' },
      labelEn: { fontStyle: 'italic', value: '(Seller)' },
      value: { value: '' },
      metadata: {
        mergeField: 'sellerSignRegion',
        typeShow: 2,
        canHide: false,
      },
      fieldId: 2590,
      parentFieldId: null,
      name: 'sellerSign',
      regionName: 'signXml',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: '(Chữ ký điện tử, chữ ký số)',
        fontStyle: 'italic',
      },
      labelEn: { fontStyle: 'italic', value: '(Signature, full name)' },
      value: { value: '' },
      metadata: { mergeField: 'sellerSignRegion', typeShow: 2 },
      fieldId: 2591,
      parentFieldId: null,
      name: 'sellerSignFull',
      regionName: 'signXml',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Ký bởi', fontWeight: 'bold' },
      labelEn: {
        fontStyle: 'italic',
        value: '(Signed By)',
        fontWeight: 'bold',
      },
      value: { value: '' },
      metadata: {
        mergeField: 'sellerSignRegion',
        typeShow: 2,
        canHide: false,
      },
      fieldId: 2592,
      parentFieldId: null,
      name: 'sellerSignByClient',
      regionName: 'signXml',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Ký ngày', fontWeight: 'bold' },
      labelEn: {
        fontStyle: 'italic',
        value: '(Signing Date)',
        fontWeight: 'bold',
      },
      value: { value: '' },
      metadata: {
        mergeField: 'sellerSignRegion',
        typeShow: 2,
        canHide: false,
      },
      fieldId: 2593,
      parentFieldId: null,
      name: 'sellerSignDateClient',
      regionName: 'signXml',
      type: 'STATIC',
      active: 1,
      children: [],
    },
  ],
  category: {
    categoryId: 1,
    form: 1,
    type: 1,
    code: 'CB',
    title: 'Mẫu cơ bản',
    paperSize: 'A4',
  },
  extend_fields: [],
};

/* Type 2 */
const TEMPLATE_2 = {
  metadata: {
    fontFamily: 'Times New Roman',
    fontSize: 14,
    color: 'black',
    lineType: 'dotted',
    lineHeight: 1.7,
    logo: { x: 0, y: 15, width: 138, height: 83 },
    background: { opacity: 100 },
    surround: null,
    region: {
      sellerInfo: {
        alignType: 'ALIGN_TYPE_1',
        tableHeaderColor: null,
        showColumnIndex: false,
      },
      invoiceInfo: {},
      invoiceInfoOther: {},
      buyerInfo: { alignType: 'ALIGN_TYPE_1' },
      tableDetail: { minItemRows: 3 },
      tableDetail1: { minItemRows: 3 },
      tableFooter: {
        fullColumnRowIndexs: [3, 4],
        fullColumnDataFields: ['totalAmountInWords', 'templateNote'],
      },
      tableFooter1: {
        fullColumnRowIndexs: [9, 10],
        fullColumnDataFields: ['TotalAmountInWords', 'TemplateNote'],
      },
      currencyBlock: {},
      signXml: {},
    },
    subFields: { LjF2f: [{ width: 320.5 }, { width: '*' }] },
  },
  region: [
    { regionName: 'firstInvoice', widths: [140, '*'] },
    { regionName: 'invoiceInfo', widths: [123, 415, '*'] },
    { regionName: 'buyerInfo', widths: [630, '*'] },
    {
      regionName: 'tableDetail',
      widths: [
        { width: 38, show: 1, dataIndex: 'lineNumber' },
        { width: 54, show: 0, dataIndex: 'inventoryItemCode' },
        { width: 254, show: 1, dataIndex: 'itemName' },
        { width: 112, show: 0, dataIndex: 'serialNumber' },
        { width: 104, show: 1, dataIndex: 'unitName' },
        { width: 101, show: 1, dataIndex: 'quantity' },
        { width: 110, show: 1, dataIndex: 'unitPrice' },
        { width: '*', show: 1, dataIndex: 'totalAmount' },
        { width: '*', show: 0, dataIndex: 'inventoryItemNote' },
      ],
    },
    {
      regionName: 'tableDetail1',
      widths: [
        { width: 38, show: 1, dataIndex: 'LineNumber' },
        { width: 54, show: 0, dataIndex: 'InventoryItemCode' },
        { width: 192, show: 1, dataIndex: 'ItemName' },
        { width: 112, show: 0, dataIndex: 'SerialNumber' },
        { width: 64, show: 1, dataIndex: 'UnitName' },
        { width: 73, show: 1, dataIndex: 'Quantity' },
        { width: 82, show: 1, dataIndex: 'UnitPrice' },
        { width: 97, show: 1, dataIndex: 'TotalAmount' },
        { width: 65, show: 1, dataIndex: 'VATPercentageDetail' },
        { width: '*', show: 1, dataIndex: 'VATAmount' },
        { width: '*', show: 0, dataIndex: 'InventoryItemNote' },
      ],
    },
    { regionName: 'tableFooter', widths: [180, 112, 255, '*'] },
    { regionName: 'tableFooter1', widths: [360, 137, 90, '*'] },
    {
      regionName: 'signXml',
      widths: [
        { width: 356, dataIndex: 'buyerSignRegion' },
        { width: '*', dataIndex: 'sellerSignRegion' },
      ],
    },
  ],
  calcConfig: null,
  invoiceTemplateId: 34,
  name: 'Hóa đơn GTGT mẫu 02',
  description: 'Hóa đơn GTGT mẫu hệ thống 02',
  mulTaxRateSupport: 1,
  form: 2,
  hasTaCode: 'C',
  year: 23,
  type: 'T',
  managementCode: 'YY',
  directTranfer: 1,
  paperSize: 'A4',
  // logo: 'c487287e-fb1e-4d0f-be78-3010af135dbb.png',
  customBackground: null,
  sellerInfoPosition: 1,
  isShowQRCode: 1,
  duplicateInfoMulPages: 1,
  isShowLabelEn: 1,
  isShowWatermarkInAdjustedInvoice: 0,
  logoPosition: 1,
  decreeCircularEnumId: 'DecCir78',
  decreeCircularEnum: 'Nghị định số 123/2020/NĐ-CP',
  createdByAccountId: 13,
  count: 1,
  active: 1,
  createdAt: '2023-08-09T15:21:16.000Z',
  updatedAt: '2023-08-09T15:21:16.000Z',
  companyId: null,
  backgroundId: 26,
  borderId: null,
  categoryId: 1,
  background: {
    backgroundId: 26,
    filename: '01a1e2e4-66bc-4e3d-86f1-a07b543fdce6.jpg',
    name: 'mau_menh_thuy_3',
  },
  border: null,
  fields: [
    {
      label: { value: 'Đơn vị bán hàng' },
      labelEn: { fontStyle: 'italic', value: '(Seller)' },
      value: {
        value: '',
        fontSize: 20,
        lineHeight: 1.25,
        fontWeight: 'bold',
        autoBreakLine: false,
        editable: false,
      },
      metadata: { displayType: 'CONTENT_LEFT', canHide: false },
      fieldId: 2519,
      parentFieldId: null,
      name: 'sellerName',
      regionName: 'sellerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Mã số thuế' },
      labelEn: { fontStyle: 'italic', value: '(Tax code)' },
      value: { value: '', editable: false },
      metadata: { canHide: false },
      fieldId: 2520,
      parentFieldId: null,
      name: 'sellerTaxCode',
      regionName: 'sellerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Địa chỉ' },
      labelEn: { fontStyle: 'italic', value: '(Address)' },
      value: { value: '', editable: false },
      metadata: {},
      fieldId: 2521,
      parentFieldId: null,
      name: 'sellerAddress',
      regionName: 'sellerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Điện thoại' },
      labelEn: { fontStyle: 'italic', value: '(Tel)' },
      value: { value: '', editable: false },
      metadata: {},
      fieldId: 2522,
      parentFieldId: null,
      name: 'sellerPhone',
      regionName: 'sellerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Fax' },
      labelEn: { fontStyle: 'italic', value: '' },
      value: { value: '', editable: false },
      metadata: {},
      fieldId: 2523,
      parentFieldId: null,
      name: 'sellerFax',
      regionName: 'sellerInfo',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: 'Website' },
      labelEn: { fontStyle: 'italic', value: '' },
      value: { value: '', editable: false },
      metadata: {},
      fieldId: 2524,
      parentFieldId: null,
      name: 'sellerWeb',
      regionName: 'sellerInfo',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: 'Email' },
      labelEn: { fontStyle: 'italic', value: '' },
      value: { value: '', editable: false },
      metadata: {},
      fieldId: 2525,
      parentFieldId: null,
      name: 'sellerEmail',
      regionName: 'sellerInfo',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: 'Số tài khoản' },
      labelEn: { fontStyle: 'italic', value: '(Bank account)' },
      value: { value: '', editable: false },
      metadata: {},
      fieldId: 2526,
      parentFieldId: null,
      name: 'sellerBankAccount',
      regionName: 'sellerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'HÓA ĐƠN BÁN HÀNG',
        fontSize: 20,
        lineHeight: 1.25,
        fontWeight: 'bold',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(VAT INVOICE)',
        fontWeight: 'bold',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2527,
      parentFieldId: null,
      name: 'typeInvoice',
      regionName: 'invoiceInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: '' },
      labelEn: { fontStyle: 'italic', value: '' },
      value: { value: 'Tên khác' },
      metadata: { typeShow: 1 },
      fieldId: 2528,
      parentFieldId: null,
      name: 'subTitleInvoice',
      regionName: 'invoiceInfo',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: '(Hóa đơn chuyển đổi từ hóa đơn điện tử)' },
      labelEn: {
        fontStyle: 'italic',
        value: '(Invoice converted from E-invoice)',
      },
      value: { value: '' },
      metadata: { typeShow: 2, previewField: true },
      fieldId: 2529,
      parentFieldId: null,
      name: 'descriptionInvoicePaperClient',
      regionName: 'invoiceInfo',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: '(Bản thể hiện của hóa đơn điện tử)' },
      labelEn: { fontStyle: 'italic', value: '(E-Invoice viewer)' },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2530,
      parentFieldId: null,
      name: 'descriptionInvoiceClient',
      regionName: 'invoiceInfo',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: '' },
      labelEn: { fontStyle: 'italic', value: '' },
      value: {
        value: 'Ngày (Date)   tháng (month)   năm (year)   ',
        editable: false,
      },
      metadata: { typeShow: 1, canHide: false },
      fieldId: 2531,
      parentFieldId: null,
      name: 'dateInvoice',
      regionName: 'invoiceInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Mã CQT' },
      labelEn: { fontStyle: 'italic', value: '(Code)' },
      value: { value: '', editable: false },
      metadata: { typeShow: 3, canHide: false },
      fieldId: 2532,
      parentFieldId: null,
      name: 'invoiceCode',
      regionName: 'invoiceInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Ký hiệu' },
      labelEn: { fontStyle: 'italic', value: '(Serial)' },
      value: { value: '1K23TYY', fontSize: 15, editable: false },
      metadata: { typeShow: 3, canHide: false },
      fieldId: 2533,
      parentFieldId: null,
      name: 'invoiceSeries',
      regionName: 'invoiceInfoOther',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Số' },
      labelEn: { fontStyle: 'italic', value: '(No.)' },
      value: { value: '00000000', fontWeight: 'bold', editable: false },
      metadata: { typeShow: 3, canHide: false },
      fieldId: 2534,
      parentFieldId: null,
      name: 'invoiceNumber',
      regionName: 'invoiceInfoOther',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Họ tên người mua hàng' },
      labelEn: { fontStyle: 'italic', value: '(Buyer)' },
      value: { value: '', editable: false },
      metadata: { canHide: false },
      fieldId: 2535,
      parentFieldId: null,
      name: 'buyerName',
      regionName: 'buyerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Tên đơn vị' },
      labelEn: { fontStyle: 'italic', value: "(Company's name)" },
      value: { value: '', editable: false },
      metadata: { canHide: false },
      fieldId: 2536,
      parentFieldId: null,
      name: 'buyerLegalName',
      regionName: 'buyerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Mã số thuế' },
      labelEn: { fontStyle: 'italic', value: '(Tax code)' },
      value: { value: '', editable: false },
      metadata: { canHide: false },
      fieldId: 2537,
      parentFieldId: null,
      name: 'BuyerTaxCode',
      regionName: 'buyerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Địa chỉ' },
      labelEn: { fontStyle: 'italic', value: '(Address)' },
      value: { value: '', editable: false },
      metadata: { canHide: false },
      fieldId: 2538,
      parentFieldId: null,
      name: 'buyerAddress',
      regionName: 'buyerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Hình thức thanh toán' },
      labelEn: { fontStyle: 'italic', value: '(Payment method)' },
      value: { value: '', editable: false },
      metadata: { mergeField: '' },
      fieldId: 2539,
      parentFieldId: null,
      name: 'paymentMethod',
      regionName: 'buyerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Đồng tiền thanh toán' },
      labelEn: { fontStyle: 'italic', value: '(Payment Currency)' },
      value: { value: '', editable: false },
      metadata: { mergeField: '' },
      fieldId: 2540,
      parentFieldId: null,
      name: 'PaymentCurrency',
      regionName: 'buyerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Số tài khoản' },
      labelEn: { fontStyle: 'italic', value: '(Bank account)' },
      value: { value: '', editable: false },
      metadata: {},
      fieldId: 2541,
      parentFieldId: null,
      name: 'buyerBankAccount',
      regionName: 'buyerInfo',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'STT',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(No)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2542,
      parentFieldId: null,
      name: 'lineNumber',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Mã hàng',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Item code)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2543,
      parentFieldId: null,
      name: 'inventoryItemCode',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: {
        value: 'Tên hàng hóa, dịch vụ',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Name of goods and services)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2544,
      parentFieldId: null,
      name: 'itemName',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Quy cách',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Specification)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2545,
      parentFieldId: null,
      name: 'serialNumber',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: {
        value: 'Đơn vị tính',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Unit)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2546,
      parentFieldId: null,
      name: 'unitName',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Số lượng',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Quantity)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2547,
      parentFieldId: null,
      name: 'quantity',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Đơn giá',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Unit price)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2548,
      parentFieldId: null,
      name: 'unitPrice',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Thành tiền',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Amount)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2549,
      parentFieldId: null,
      name: 'totalAmount',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Ghi chú',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Note)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2550,
      parentFieldId: null,
      name: 'inventoryItemNote',
      regionName: 'tableDetail',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: {
        value: 'STT',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(No)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2551,
      parentFieldId: null,
      name: 'LineNumber',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Mã hàng',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Item code)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2552,
      parentFieldId: null,
      name: 'InventoryItemCode',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: {
        value: 'Tên hàng hóa, dịch vụ',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Name of goods and services)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2553,
      parentFieldId: null,
      name: 'ItemName',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Quy cách',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Specification)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2554,
      parentFieldId: null,
      name: 'SerialNumber',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: {
        value: 'Đơn vị tính',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Unit)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2555,
      parentFieldId: null,
      name: 'UnitName',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Số lượng',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Quantity)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2556,
      parentFieldId: null,
      name: 'Quantity',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Đơn giá',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Unit price)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2557,
      parentFieldId: null,
      name: 'UnitPrice',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Thành tiền',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Amount)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2558,
      parentFieldId: null,
      name: 'TotalAmount',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Thuế suất GTGT',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(VAT rate)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2559,
      parentFieldId: null,
      name: 'VATPercentageDetail',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Tiền thuế GTGT',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(VAT Amount)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2560,
      parentFieldId: null,
      name: 'VATAmount',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Ghi chú',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Note)',
        lineHeight: 1.4,
        fontWeight: 'bold',
        textAlign: 'center',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2561,
      parentFieldId: null,
      name: 'InventoryItemNote',
      regionName: 'tableDetail1',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: 'Tỷ lệ CK' },
      labelEn: { fontStyle: 'italic', value: '(Discount rate)' },
      value: { value: '' },
      metadata: { typeShow: 2, previewField: true },
      fieldId: 2562,
      parentFieldId: null,
      name: 'discountRate',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: 'Số tiền chiết khấu' },
      labelEn: { fontStyle: 'italic', value: '(Discount amount)' },
      value: { value: '' },
      metadata: { typeShow: 2, previewField: true },
      fieldId: 2563,
      parentFieldId: null,
      name: 'totalDiscountAmount',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: '' },
      labelEn: { value: '' },
      value: { value: '' },
      metadata: {},
      fieldId: 2564,
      parentFieldId: null,
      name: 'notShow1',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Cộng tiền hàng' },
      labelEn: {
        fontStyle: 'italic',
        value: '(Total amount excl. VAT)',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2565,
      parentFieldId: null,
      name: 'totalAmountWithoutVAT',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Thuế suất GTGT' },
      labelEn: { fontStyle: 'italic', value: '(VAT rate)' },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2566,
      parentFieldId: null,
      name: 'vatPercentage',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Tiền thuế GTGT' },
      labelEn: { fontStyle: 'italic', value: '(VAT amount)' },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2567,
      parentFieldId: null,
      name: 'totalVATAmount',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: '' },
      labelEn: { value: '' },
      value: { value: '' },
      metadata: {},
      fieldId: 2568,
      parentFieldId: null,
      name: 'notShow2',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Tổng tiền thanh toán' },
      labelEn: { fontStyle: 'italic', value: '(Total amount)' },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2569,
      parentFieldId: null,
      name: 'totalAmountWithVAT',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Số tiền viết bằng chữ' },
      labelEn: {
        fontStyle: 'italic',
        value: '(Total amount in words)',
      },
      value: { fontStyle: 'italic', fontWeight: 'bold', value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2570,
      parentFieldId: null,
      name: 'totalAmountInWords',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Ghi chú' },
      labelEn: { fontStyle: 'italic', value: '(Note)' },
      value: { value: '.' },
      metadata: {},
      fieldId: 2571,
      parentFieldId: null,
      name: 'templateNote',
      regionName: 'tableFooter',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: {
        value: 'Tổng hợp',
        fontWeight: 'bold',
        editable: false,
        textAlign: 'center',
        lineHeight: 1.4,
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(In sumary)',
        fontWeight: 'bold',
        editable: false,
        textAlign: 'center',
        lineHeight: 1.4,
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2572,
      parentFieldId: null,
      name: 'General',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Thành tiền trước thuế GTGT',
        fontWeight: 'bold',
        editable: false,
        textAlign: 'center',
        lineHeight: 1.4,
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Total before VAT)',
        fontWeight: 'bold',
        editable: false,
        textAlign: 'center',
        lineHeight: 1.4,
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2573,
      parentFieldId: null,
      name: 'AmountWithoutVAT',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Tiền thuế GTGT',
        fontWeight: 'bold',
        editable: false,
        textAlign: 'center',
        lineHeight: 1.4,
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(VAT amount)',
        fontWeight: 'bold',
        editable: false,
        textAlign: 'center',
        lineHeight: 1.4,
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2574,
      parentFieldId: null,
      name: 'VATAmountDetail',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: 'Cộng tiền thanh toán',
        fontWeight: 'bold',
        editable: false,
        textAlign: 'center',
        lineHeight: 1.4,
      },
      labelEn: {
        fontStyle: 'italic',
        value: '(Total amount)',
        fontWeight: 'bold',
        editable: false,
        textAlign: 'center',
        lineHeight: 1.4,
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2575,
      parentFieldId: null,
      name: 'TotalVATAmount',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Không kê khai thuế GTGT' },
      labelEn: {
        fontStyle: 'italic',
        value: '(Not required to declare, pay VAT)',
      },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2576,
      parentFieldId: null,
      name: 'TotalAmountKKKNTFooter',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Không chịu thuế GTGT' },
      labelEn: { fontStyle: 'italic', value: '(VAT exemption)' },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2577,
      parentFieldId: null,
      name: 'TotalAmountVatKCT',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Thuế suất 0%' },
      labelEn: { fontStyle: 'italic', value: '(VAT rate 0%)' },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2578,
      parentFieldId: null,
      name: 'TotalAmountWithVAT0',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Thuế suất 5%' },
      labelEn: { fontStyle: 'italic', value: '(VAT rate 5%)' },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2579,
      parentFieldId: null,
      name: 'TotalAmountWithVAT5',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Thuế suất 8%' },
      labelEn: { fontStyle: 'italic', value: '(VAT rate 8%)' },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2580,
      parentFieldId: null,
      name: 'TotalAmountWithVAT8',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Thuế suất 10%' },
      labelEn: { fontStyle: 'italic', value: '(VAT rate 10%)' },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2581,
      parentFieldId: null,
      name: 'TotalAmountWithVAT10',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Thuế suất KHÁC' },
      labelEn: { fontStyle: 'italic', value: '(Other VAT rates)' },
      value: { value: '' },
      metadata: { typeShow: 2 },
      fieldId: 2582,
      parentFieldId: null,
      name: 'TotalAmountVATKHACFooter',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Tổng cộng', fontWeight: 'bold' },
      labelEn: {
        fontStyle: 'italic',
        value: '(Total)',
        fontWeight: 'bold',
      },
      value: { value: '', fontWeight: 'bold' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2583,
      parentFieldId: null,
      name: 'TotalSumary',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Số tiền viết bằng chữ' },
      labelEn: {
        fontStyle: 'italic',
        value: '(Total amount in words)',
      },
      value: { value: '' },
      metadata: { typeShow: 2, canHide: false },
      fieldId: 2584,
      parentFieldId: null,
      name: 'TotalAmountInWords',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Ghi chú' },
      labelEn: { fontStyle: 'italic', value: '(Note)' },
      value: { value: '.' },
      metadata: { typeShow: 3 },
      fieldId: 2585,
      parentFieldId: null,
      name: 'TemplateNote',
      regionName: 'tableFooter1',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: 'Tỷ giá' },
      labelEn: { fontStyle: 'italic', value: '(Exchange Rate)' },
      value: { value: '' },
      metadata: { typeShow: 2, previewField: true },
      fieldId: 2586,
      parentFieldId: null,
      name: 'exchangeRate',
      regionName: 'currencyBlock',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: 'Quy đổi' },
      labelEn: { fontStyle: 'italic', value: '(Equivalence)' },
      value: { value: '' },
      metadata: { typeShow: 2, previewField: true },
      fieldId: 2587,
      parentFieldId: null,
      name: 'translationCurrency',
      regionName: 'currencyBlock',
      type: 'STATIC',
      active: 0,
      children: [],
    },
    {
      label: { value: 'Người mua hàng', fontWeight: 'bold' },
      labelEn: { fontStyle: 'italic', value: '(Buyer)' },
      value: { value: '' },
      metadata: {
        mergeField: 'buyerSignRegion',
        typeShow: 2,
        canHide: false,
      },
      fieldId: 2588,
      parentFieldId: null,
      name: 'buyerSign',
      regionName: 'signXml',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: '(Chữ ký số (nếu có))', fontStyle: 'italic' },
      labelEn: { fontStyle: 'italic', value: '(Signature, full name)' },
      value: { value: '' },
      metadata: { mergeField: 'buyerSignRegion', typeShow: 2 },
      fieldId: 2589,
      parentFieldId: null,
      name: 'buyerSignFull',
      regionName: 'signXml',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Người bán hàng', fontWeight: 'bold' },
      labelEn: { fontStyle: 'italic', value: '(Seller)' },
      value: { value: '' },
      metadata: {
        mergeField: 'sellerSignRegion',
        typeShow: 2,
        canHide: false,
      },
      fieldId: 2590,
      parentFieldId: null,
      name: 'sellerSign',
      regionName: 'signXml',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: {
        value: '(Chữ ký điện tử, chữ ký số)',
        fontStyle: 'italic',
      },
      labelEn: { fontStyle: 'italic', value: '(Signature, full name)' },
      value: { value: '' },
      metadata: { mergeField: 'sellerSignRegion', typeShow: 2 },
      fieldId: 2591,
      parentFieldId: null,
      name: 'sellerSignFull',
      regionName: 'signXml',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Ký bởi', fontWeight: 'bold' },
      labelEn: {
        fontStyle: 'italic',
        value: '(Signed By)',
        fontWeight: 'bold',
      },
      value: { value: '' },
      metadata: {
        mergeField: 'sellerSignRegion',
        typeShow: 2,
        canHide: false,
      },
      fieldId: 2592,
      parentFieldId: null,
      name: 'sellerSignByClient',
      regionName: 'signXml',
      type: 'STATIC',
      active: 1,
      children: [],
    },
    {
      label: { value: 'Ký ngày', fontWeight: 'bold' },
      labelEn: {
        fontStyle: 'italic',
        value: '(Signing Date)',
        fontWeight: 'bold',
      },
      value: { value: '' },
      metadata: {
        mergeField: 'sellerSignRegion',
        typeShow: 2,
        canHide: false,
      },
      fieldId: 2593,
      parentFieldId: null,
      name: 'sellerSignDateClient',
      regionName: 'signXml',
      type: 'STATIC',
      active: 1,
      children: [],
    },
  ],
  category: {
    categoryId: 1,
    form: 1,
    type: 1,
    code: 'CB',
    title: 'Mẫu cơ bản',
    paperSize: 'A4',
  },
  extend_fields: [],
};

const TABLE_LINE_WIDTH = 0.5;
const PX_TO_PT_RATE = 0.75;
const FONT_DIR = 'assets/fonts';

const FONTS_COMMON = {
  Arial: {
    normal: getFontFamilyPath('Arial', '', ''),
    bold: getFontFamilyPath('Arial', 'bold', ''),
    italics: getFontFamilyPath('Arial', '', 'italic'),
    bolditalics: getFontFamilyPath('Arial', 'bold', 'italic'),
  },
  ['Times New Roman']: {
    normal: getFontFamilyPath('Times New Roman', '', ''),
    bold: getFontFamilyPath('Times New Roman', 'bold', ''),
    italics: getFontFamilyPath('Times New Roman', '', 'italic'),
    bolditalics: getFontFamilyPath('Times New Roman', 'bold', 'italic'),
  },
  Roboto: {
    normal: getFontFamilyPath('Roboto', '', ''),
    bold: getFontFamilyPath('Roboto', 'bold', ''),
    italics: getFontFamilyPath('Roboto', '', 'italic'),
    bolditalics: getFontFamilyPath('Roboto', 'bold', 'italic'),
  },

  FontAwesome: {
    normal: getFontFamilyPath('FontAwesome', 'fa-regular', ''),
  },
};

const STANDARD_PAGE_SIZES = {
  '4A0': [4767.87, 6740.79],
  '2A0': [3370.39, 4767.87],
  A0: [2383.94, 3370.39],
  A1: [1683.78, 2383.94],
  A2: [1190.55, 1683.78],
  A3: [841.89, 1190.55],
  A4: [595.28, 841.89],
  A5: [419.53, 595.28],
  A6: [297.64, 419.53],
  A7: [209.76, 297.64],
  A8: [147.4, 209.76],
  A9: [104.88, 147.4],
  A10: [73.7, 104.88],
  B0: [2834.65, 4008.19],
  B1: [2004.09, 2834.65],
  B2: [1417.32, 2004.09],
  B3: [1000.63, 1417.32],
  B4: [708.66, 1000.63],
  B5: [498.9, 708.66],
  B6: [354.33, 498.9],
  B7: [249.45, 354.33],
  B8: [175.75, 249.45],
  B9: [124.72, 175.75],
  B10: [87.87, 124.72],
  C0: [2599.37, 3676.54],
  C1: [1836.85, 2599.37],
  C2: [1298.27, 1836.85],
  C3: [918.43, 1298.27],
  C4: [649.13, 918.43],
  C5: [459.21, 649.13],
  C6: [323.15, 459.21],
  C7: [229.61, 323.15],
  C8: [161.57, 229.61],
  C9: [113.39, 161.57],
  C10: [79.37, 113.39],
  RA0: [2437.8, 3458.27],
  RA1: [1729.13, 2437.8],
  RA2: [1218.9, 1729.13],
  RA3: [864.57, 1218.9],
  RA4: [609.45, 864.57],
  SRA0: [2551.18, 3628.35],
  SRA1: [1814.17, 2551.18],
  SRA2: [1275.59, 1814.17],
  SRA3: [907.09, 1275.59],
  SRA4: [637.8, 907.09],
  EXECUTIVE: [521.86, 756.0],
  FOLIO: [612.0, 936.0],
  LEGAL: [612.0, 1008.0],
  LETTER: [612.0, 792.0],
  TABLOID: [792.0, 1224.0],
};

const COL_ALIGN_DICT = {
  lineNumber: 'center',
  inventoryItemCode: 'center',
  itemName: 'left',
  serialNumber: 'center',
  unitName: 'center',
  quantity: 'right',
  unitPrice: 'right',
  totalAmount: 'right',
  amount: 'right',
  discountRate: 'right',

  discountAmount: 'right',
  vatPercentage: 'right',

  LineNumber: 'center',
  InventoryItemCode: 'center',
  ItemName: 'left',
  SerialNumber: 'center',
  UnitName: 'center',
  Quantity: 'right',
  UnitPrice: 'right',
  TotalAmountWithoutVATDetail: 'right',
  AmountWithoutVAT: 'right',
  TotalAmount: 'right',
  VATAmount: 'right',

  DiscountAmount: 'right',
  VatPercentageDetail: 'right',
  // VATAmount,
  // InventoryItemNote: "",
};

const INVOICE_PRODUCT_DICT = {
  lineNumber: 'sortOrder',
  // inventoryItemCode: "",
  itemName: 'name',
  // serialNumber: "",
  unitName: 'unit',
  quantity: 'quantity',
  unitPrice: 'price',
  totalAmount: 'amountTotal',
  amount: 'amountTotal',
  discountRate: 'discount',

  discountAmount: 'discountAmount',
  vatPercentage: 'taxRate',

  LineNumber: 'sortOrder',
  // InventoryItemCode: "",
  ItemName: 'name',
  // SerialNumber,
  UnitName: 'unit',
  Quantity: 'quantity',
  UnitPrice: 'price',
  TotalAmountWithoutVATDetail: 'amountTotal',
  AmountWithoutVAT: 'amountTotal',
  TotalAmount: 'amountTotal',
  VATAmount: 'vatAmount',

  DiscountAmount: 'discountAmount',
  VatPercentageDetail: 'vat',
  VATPercentageDetail: 'vat',
  // VATAmount,
  // InventoryItemNote: "",
};

const INVOICE_INVOICE_TEMPLATE_DICT = {
  sellerName: 'sellerName',
  sellerTaxCode: 'sellerTaxCode',
  sellerAddress: 'sellerAddress',
  sellerPhone: 'sellerPhone',
  sellerFax: 'sellerFax',
  sellerWeb: 'sellerWeb',
  sellerEmail: 'sellerEmail',
  sellerBankAccount: 'sellerBankAccount',
  sellerBankName: 'sellerBankName',
  //   typeInvoice: "",
  //   subTitleInvoice: "",
  //   descriptionInvoicePaperClient: "",
  //   descriptionInvoiceClient: "",
  dateInvoice: 'invoiceDate',
  //   customInvoiceInfo_0: "",
  invoiceCode: 'maCQT',
  invoiceSeries: 'serial',
  invoiceNumber: 'invoiceNumber',
  buyerName: 'buyerPersonName',
  buyerLegalName: 'buyerName',
  BuyerTaxCode: 'buyerTaxCode',
  buyerAddress: 'buyerAddress',
  buyerBankName: 'buyerBankName',
  paymentMethod: 'paymentMethod',
  buyerBankAccount: 'buyerBankAccount',
  PaymentCurrency: 'paymentCurrency',
  //   CustomField1: "",
  //   discountRate: "",
  //   totalDiscountAmount: "",
  //   notShow1: "",
  totalAmountWithoutVAT: 'amountBeforeVat',
  vatPercentage: 'taxRate',
  totalVATAmount: 'amountVat',
  //   notShow2: "",
  totalAmountWithVAT: 'finalAmount',
  totalAmountInWords: 'finalAmountInWord',
  TotalAmountInWords: 'finalAmountInWord',
  //   templateNote: "",
  //   exchangeRate: "",
  //   translationCurrency: "",
  //   buyerSign: "",
  //   buyerSignFull: "",
  //   customSign0Name: "",
  //   customSign0Full: "",
  //   sellerSign: "",
  //   sellerSignFull: "",
  sellerSignByClient: 'sellerName',
  sellerSignDateClient: 'sellerSignDate',
};

const PAYMENT_METHOD_DICT = {
  1: 'TM/CK',
  2: 'Tiền mặt',
  3: 'Chuyển khoản',
  4: 'Đối trừ công nợ',
  5: 'Không thu tiền',
};

function htmlToPdfmakeStyle(style) {
  let {
    defaultFontSize,
    fontSize,
    fontWeight,
    lineHeight,
    fontStyle,
    textAlign,
    ...rest
  } = style;
  let returnValue = { ...rest };

  if (fontSize) {
    returnValue.fontSize = fontSize * PX_TO_PT_RATE;
  }
  if (fontWeight) {
    returnValue.bold = fontWeight == 'bold' ? true : false;
  }
  if (fontStyle) {
    returnValue.italics = fontStyle == 'italic' ? true : false;
  }
  if (lineHeight) {
    returnValue.lineHeight =
      lineHeight * 0.9 /* / (fontSize || defaultFontSize) */;
  }
  if (textAlign) {
    returnValue.alignment = textAlign;
  }

  // returnValue.background = "#" + Math.floor(Math.random()*16777215).toString(16);

  return returnValue;
}

function getFontFamilyPath(fontFamily, fontWeight, fontStyle) {
  let fileName = `${fontFamily}`;

  if (fontWeight == 'bold') {
    fileName = `${fileName}-${fontWeight}`;
  }

  if (fontStyle == 'italic') {
    fileName = `${fileName}-${fontStyle}`;
  }

  return path.join(FONT_DIR, `${fontFamily}/${fileName}.ttf`);
}

function genHorizontalParagraph(
  defaultFontSize,
  isShowLabelEn,
  label,
  labelEn,
  value,
  twoDot = true,
  metadata,
  maxWidth,
  alignType,
) {
  let returnValue = { columns: [], gap: 0 };
  if (metadata?.displayType != 'CONTENT_LEFT') {
    let transparentColor = {};
    if (metadata?.displayType == 'CONTENT') {
      transparentColor.color = '#ffffff';
    }

    let columnsContainer = { columns: [], gap: 0 };
    if (label?.value) {
      let { value: labelV, ...rest } = label;

      columnsContainer.columns.push({
        text: labelV + ' ',
        width: 'auto',
        noWrap: true,
        ...htmlToPdfmakeStyle({ ...rest, defaultFontSize }),
        ...transparentColor,
      });
    }
    if (isShowLabelEn && labelEn?.value) {
      let { value: labelEnV, ...rest } = labelEn;
      columnsContainer.columns.push({
        text: labelEnV + ' ',
        width: 'auto',
        noWrap: true,
        margin: [2, 0, 0, 0],
        ...htmlToPdfmakeStyle({ ...rest, defaultFontSize }),
        ...transparentColor,
      });
    }

    if (twoDot && alignType == 'ALIGN_TYPE_2') {
      let { value: labelV, ...rest } = label || {};

      columnsContainer.columns.push({
        text: ': ',
        width: 'auto',
        ...htmlToPdfmakeStyle({ ...rest, defaultFontSize }),
        ...transparentColor,
      });
    }

    if (maxWidth && _.isNumber(maxWidth)) {
      columnsContainer.width = maxWidth * 0.75;
    } else {
      columnsContainer.width = 'auto';
    }
    returnValue.columns.push(columnsContainer);

    if (twoDot && alignType != 'ALIGN_TYPE_2') {
      let { value: labelV, ...rest } = label || {};

      returnValue.columns.push({
        text: ': ',
        width: 'auto',
        ...htmlToPdfmakeStyle({ ...rest, defaultFontSize }),
        ...transparentColor,
      });
    }
  }
  if (value?.value || value?.value == 0) {
    let { value: valueV, ...rest } = value;
    if (metadata?.mstSplitCells) {
      returnValue.columns.push({
        layout: {
          paddingLeft: () => 0,
          paddingRight: () => 0,
          paddingTop: setTopMarginOfCellForVerticalCentering,
          paddingBottom: () => 0,
          hLineWidth: (i, node) => 0.6,
          vLineWidth: (i, node) => 0.6,
          hLineColor: (i, node) => '#ccc',
          vLineColor: (i, node) => '#ccc',
        },
        table: {
          headerRows: 0,
          widths: valueV.split('').map(num => 15),
          body: [
            valueV.split('').map(num => ({
              text: num,
              ...htmlToPdfmakeStyle({ ...rest, defaultFontSize }),
              alignment: 'center',
            })),
          ],
        },
        margin: [2, 0, 0, 0],
      });
    } else {
      returnValue.columns.push({
        text: breakNumberAnyWhereString(valueV),
        width: 'auto',
        margin: [2, 0, 0, 0],
        ...htmlToPdfmakeStyle({ ...rest, defaultFontSize }),
      });
    }
  }
  return returnValue;
}

function genHorizontalParagraphText(
  defaultFontSize,
  isShowLabelEn,
  label,
  labelEn,
  value,
  twoDot = true,
  metadata,
  maxWidth,
  alignType,
) {
  let returnValue = { text: [] };
  if (metadata?.displayType != 'CONTENT_LEFT') {
    let transparentColor = {};
    if (metadata?.displayType == 'CONTENT') {
      transparentColor.color = '#ffffff';
    }
    let textContainer = { text: [] };
    if (label?.value) {
      let { value: labelV, ...rest } = label;
      textContainer.text.push({
        text: labelV + ' ',
        ...htmlToPdfmakeStyle({ ...rest, defaultFontSize }),
        ...transparentColor,
      });
    }
    if (isShowLabelEn && labelEn?.value) {
      let { value: labelEnV, ...rest } = labelEn;
      textContainer.text.push({
        text: labelEnV + ' ',
        ...htmlToPdfmakeStyle({ ...rest, defaultFontSize }),
        ...transparentColor,
      });
    }

    if (twoDot && alignType == 'ALIGN_TYPE_2') {
      let { value: labelV, ...rest } = label || {};

      textContainer.text.push({
        text: ': ',
        ...htmlToPdfmakeStyle({ ...rest, defaultFontSize }),
        ...transparentColor,
      });
    }

    if (maxWidth && _.isNumber(maxWidth)) {
      textContainer.width = maxWidth * 0.75;
    } else {
      textContainer.width = 'auto';
    }
    returnValue.text.push(textContainer);

    if (twoDot && alignType != 'ALIGN_TYPE_2') {
      let { value: labelV, ...rest } = label || {};

      returnValue.text.push({
        text: ': ',
        ...htmlToPdfmakeStyle({ ...rest, defaultFontSize }),
        ...transparentColor,
      });
    }
  }
  if (value?.value || value?.value == 0) {
    let { value: valueV, ...rest } = value;
    returnValue.text.push({
      text: valueV + ' ',
      ...htmlToPdfmakeStyle({ ...rest, defaultFontSize }),
    });
  }
  return returnValue;
}

function genVerticalParagraph(
  defaultFontSize,
  isShowLabelEn,
  label,
  labelEn,
  value,
) {
  let returnValue = { stack: [] };
  if (label?.value) {
    let { value: labelV, ...restLabel } = label;
    returnValue.stack.push({
      text: labelV + ' ',
      ...htmlToPdfmakeStyle({ ...restLabel, defaultFontSize }),
    });
  }
  if (isShowLabelEn && labelEn?.value) {
    let { value: labelEnV, ...restLabelEn } = labelEn;
    returnValue.stack.push({
      text: labelEnV + ' ',
      ...htmlToPdfmakeStyle({ ...restLabelEn, defaultFontSize }),
    });
  }
  if (value?.value || value?.value === 0) {
    let { value: valueV, ...restValue } = value;
    returnValue.stack.push({
      text: valueV + ' ',
      ...htmlToPdfmakeStyle({ ...restValue, defaultFontSize }),
    });
  }

  return returnValue;
}

function genHrElement() {
  return {
    table: {
      headerRows: 0,
      widths: ['*'],
      body: [['']],
    },
    layout: {
      hLineWidth: (i, node) => {
        return i === 1 ? 0.5 : 0;
      },
      hLineColor: (i, node) => {
        return i === 1 ? '#ccc' : 'black';
      },
      vLineWidth: (i, node) => {
        return 0;
      },
    },
    margin: [0, -5, 0, 0],
  };
}

function genSignDiv(signByRow, signDateRow, bgColor, signValid = false) {
  let returnValue = {
    margin: [25, 15, 25, 0],
    table: {
      widths: [/* '*' */ 200],
      body: [
        [
          {
            fillColor: bgColor,
            columns: [{ text: 'Signature Valid', bold: true }],
          },
        ],
        [
          {
            fillColor: bgColor,
            columns: signByRow,
          },
        ],
        [
          {
            fillColor: bgColor,
            columns: signDateRow,
          },
        ],
      ],
    },
    layout: 'signDivTable',
  };

  if (signValid) {
    returnValue.table.body.unshift([
      {
        fillColor: bgColor,
        text: '\uf00c',
        font: 'FontAwesome',
        fontSize: 55,
        color: '#29a739',
        alignment: 'center',
        relativePosition: {
          x: 0,
          y: 5,
        },
      },
    ]);
  }

  return returnValue;
}

function groupByMergeFieldArr(fields) {
  let mergeFieldIndexObj = {};
  let groupArr = fields?.reduce((group, field, index) => {
    let metadata = utils.isJSONStringified(field.metadata)
      ? JSON.parse(field.metadata)
      : field.metadata;
    let { mergeField } = metadata;
    if (mergeField) {
      mergeFieldIndexObj[mergeField] = mergeFieldIndexObj[mergeField] ?? index;
      let mergeFieldIndex = mergeFieldIndexObj[mergeField];

      group[mergeFieldIndex] = group[mergeFieldIndex] ?? [mergeField];
      group[mergeFieldIndex].push(field);
    } else {
      group.push(['', field]);
    }

    return group;
  }, []);

  return [...groupArr]?.filter(item => item);
}

function breakNumberAnyWhereString(str) {
  str = str + '';
  var regex = /(\d+(\.\d+)?)/g;
  return str.replace(regex, function (match) {
    return match.split('').join('\u200b');
  });
}

function fillInvoiceProductDataToField(fieldName, product, calcConfig) {
  let { discount } = calcConfig || {};
  let value;
  switch (fieldName) {
    case 'vat':
    case 'vatAmount':
    case 'discount':
      value = product[INVOICE_PRODUCT_DICT[fieldName]];
      return isValidNumber(value) ? value + '%' : value;
    case 'finalAmount':
      let VATAmount = product['vatAmount'] || 0;
      let returnValue =
        product['amountTotal'] + VATAmount - (product['discountAmount'] || 0);
      return isValidNumber(returnValue)
        ? formatNumber(returnValue)
        : returnValue;
    case 'totalAmount':
      value = product[INVOICE_PRODUCT_DICT[fieldName]];
      if (discount && isValidNumber(product['discountMoney'])) {
        value = value - product['discountMoney'];
      }

      return isValidNumber(value) ? formatNumber(value) : value;
    // case 'ItemName':
    //     let productId = product[INVOICE_PRODUCT_DICT[fieldName]];

    //     return productId;
    default:
      break;
  }

  if (fieldName.startsWith('extendField')) {
    return product.extend_fields?.find(({ name }) => name == fieldName)
      ?.invoice_product_extend_field.value;
  }

  let returnValue = product[INVOICE_PRODUCT_DICT[fieldName]];
  return returnValue == 0
    ? 0
    : isValidNumber(returnValue)
    ? formatNumber(returnValue)
    : returnValue || '';
}

function fillDataTableFooter1Row(name, taxArray) {
  let convertDict = {
    TotalAmountKKKNTFooter: 'KKKNT',
    TotalAmountVatKCT: 'KCT',
    TotalAmountWithVAT0: '0',
    TotalAmountWithVAT5: '5',
    TotalAmountWithVAT8: '8',
    TotalAmountWithVAT10: '10',
    TotalAmountVATKHACFooter: 'KHAC',
    TotalSumary: 'TOTAL',
  };
  switch (name) {
    case 'TotalAmountKKKNTFooter':
    case 'TotalAmountVatKCT':
    case 'TotalAmountWithVAT0':
    case 'TotalAmountWithVAT5':
    case 'TotalAmountWithVAT8':
    case 'TotalAmountWithVAT10':
    case 'TotalAmountVATKHACFooter':
    case 'TotalSumary':
      let result = taxArray.find(({ taxRate }) => taxRate == convertDict[name]);
      if (!result) return {};
      let { taxRate, ...rest } = result;
      return rest;
    default:
      return {};
  }
}

function fillInvoiceDataToFields(fields, invoiceData) {
  if (!fields || !Array.isArray(fields) || !invoiceData) return;

  let discount = false;
  let returnValue = fields.map(field => {
    if (utils.isJSONStringified(field.value)) {
      field.value = JSON.parse(field.value);
    }

    let realValue = invoiceData[INVOICE_INVOICE_TEMPLATE_DICT[field.name]];
    switch (field.name) {
      case 'sellerBankAccount':
        realValue = `${
          invoiceData[INVOICE_INVOICE_TEMPLATE_DICT['sellerBankAccount']]
        } - ${invoiceData[INVOICE_INVOICE_TEMPLATE_DICT['sellerBankName']]}`;
        break;
      case 'paymentMethod':
        realValue = PAYMENT_METHOD_DICT[realValue] || '';
        break;
      case 'dateInvoice':
        let day = moment(realValue).date();
        let month = moment(realValue).month() + 1;
        /* Months are zero indexed, so January is month 0. */
        let year = moment(realValue).year();

        realValue = field.value.value;
        realValue = realValue.replace('   ', ' ' + day + ' ');
        realValue = realValue.replace('   ', ' ' + month + ' ');
        realValue = realValue.replace('   ', ' ' + year + ' ');
        break;
      case 'invoiceNumber':
        if (!realValue) {
          realValue = `<Chưa cấp số>`;
        }
        break;
      case 'sellerSignDateClient':
        realValue = moment(realValue).format('DD/MM/YYYY');
        break;
      case 'VATAmount':
        break;
      case 'buyerBankAccount':
        let customerAccountNumber =
          invoiceData[INVOICE_INVOICE_TEMPLATE_DICT['buyerBankAccount']];
        let customerBankName =
          invoiceData[INVOICE_INVOICE_TEMPLATE_DICT['buyerBankName']];
        realValue =
          customerAccountNumber && customerBankName
            ? `${customerAccountNumber} - Ngân hàng ${customerBankName}`
            : '';
        break;

      case 'vatPercentage':
        realValue = isValidNumber(realValue) ? realValue + '%' : realValue;
        break;
      case 'notShowDiscountRate':
        if (
          [1, 2].includes(invoiceData.discountType) &&
          invoiceData.discountMoney
        ) {
          field.active = 1;
          discount = true;
        }
        break;
      case 'discountRate':
        if (
          field.regionName != 'tableDetail' &&
          [1, 2].includes(invoiceData.discountType) &&
          invoiceData.discountMoney
        ) {
          field.active = 1;
          realValue = (invoiceData.discountMoney / invoiceData.total) * 100;
          realValue += '%';
          discount = true;
        }
        break;
      case 'totalDiscountAmount':
        if (discount) {
          field.active = 1;
          realValue = invoiceData.discountMoney;
        }
        break;
      case 'totalAmountWithoutVAT' /* Cộng tiền hàng */:
        if (discount) {
          field.label.value += ' (Đã trừ chiết khấu)';
          realValue -= invoiceData.discountMoney;
        }
        break;
      case 'sellerPhone':
        realValue = `${realValue}`;
        break;
      default:
        if (field.name.startsWith('extendField-buyerInfo')) {
          let value =
            invoiceData?.extend_fields?.find(({ name }) => name == field.name)
              ?.invoice_extend_field?.value ||
            (invoiceData?.extendFields && invoiceData.extendFields[field.name]);
          realValue = value ?? realValue;
        } else {
          realValue =
            isValidNumber(realValue) &&
            !isValidPhone(realValue) &&
            !isValidTaxCode(realValue)
              ? formatNumber(realValue)
              : realValue;
        }
        break;
    }

    field.value.value = realValue;

    return field;
  });

  return returnValue;
}

const TABLE_LAYOUTS_COMMON = {
  noBordersNoPaddings: {
    hLineWidth: () => 0,
    vLineWidth: () => 0,
    paddingLeft: () => 0,
    paddingRight: () => 0,
    paddingTop: () => 0,
    paddingBottom: () => 0,
  },
  signDivTable: {
    hLineWidth: (i, node) => {
      if (i === 0 || i === node.table.body.length) {
        return TABLE_LINE_WIDTH * 2;
      }
      return 0;
    },
    vLineWidth: (i, node) => {
      if (i === 0 || i === 1) {
        return TABLE_LINE_WIDTH * 2;
      }
      return 0;
    },
    hLineColor: (i, node) => '#7cc576',
    vLineColor: (i, node) => '#7cc576',
  },
};

/**
 *
 * @param {{invoice: import('../types').Invoice}} param0
 * @returns
 */
async function createPreviewInvoicePdf({ invoice }) {
  let _invoice = _.cloneDeep(invoice);
  let template = _.cloneDeep(TEMPLATE_1);

  _invoice = deleteEmptyProps(_invoice, '');
  _invoice = calculateForPreview(_invoice);

  if (invoice.invoiceTypeCode == INVOICE_TYPE_CODE[2]) {
    template = _.cloneDeep(TEMPLATE_2);
  }

  template.hasTaCode = _invoice.serial[0];

  if (template.hasTaCode == 'C') {
    template.fields.find(item => item.labelEn.value == '(Code)').active = true;
  }

  _invoice.serial = `${_invoice.invoiceTypeCode}${_invoice.serial}`;

  template.fields = fillInvoiceDataToFields(template.fields, _invoice);

  let duplicateInfoMulPages = false;
  let exportOptions = { type: 1, format: 'pdf', mulTaxRate: true };
  let sample = false;
  let invoiceProducts = _invoice.InvoiceProducts;

  let {
    name,
    sellerInfoPosition,
    isShowQRCode,
    isShowWatermarkInAdjustedInvoice,
    isShowLabelEn,
    logoPosition,
    metadata,
    region,
    paperSize,
    fields,
    logo: logoBody,
    customBackground,
    calcConfig,
  } = template;

  /* isShowLabelEn */
  if (isShowLabelEn == 1) {
    isShowLabelEn = 1;
  } else {
    isShowLabelEn = 0;
  }

  /* isShowQRCode */
  if (isShowQRCode == 1) {
    isShowQRCode = 1;
  } else {
    isShowQRCode = 0;
  }

  /* metadata */
  if (utils.isJSONStringified(metadata)) {
    metadata = JSON.parse(metadata);
  }

  /* region */
  if (utils.isJSONStringified(region)) {
    region = JSON.parse(region);
  }

  /* exportOptions */
  if (utils.isJSONStringified(exportOptions)) {
    exportOptions = JSON.parse(exportOptions);
  }

  let {
    format,
    type: exType,
    discount,
    foreginCurrency,
    mulTaxRate,
  } = exportOptions;
  mulTaxRate = mulTaxRate == 0 ? false : mulTaxRate;

  /* calcConfig */
  if (utils.isJSONStringified(calcConfig)) {
    calcConfig = JSON.parse(calcConfig);
  }

  /* border */
  let border;
  let borderColor = metadata?.surround?.surroundColor || 'black';

  /* background */
  let background = fs.readFileSync('./assets/images/tax-bg.jpg');
  let backgroundColor = metadata?.background?.backgroundColor || 'black';
  let backgroundOpacity = (metadata?.background?.opacity || 0) / 100;
  let backgroundWidth = metadata?.background?.width;
  let backgroundHeight = metadata?.background?.height;
  let backgroundX = metadata?.background?.x || 0;
  let backgroundY = metadata?.background?.y || 0;
  let backgroundInstance;

  /* defaultStyle */
  let defaultStyle = {};
  let { fontFamily, fontSize, color, lineType, lineHeight } = metadata;
  if (fontFamily) {
    defaultStyle.font = fontFamily;
  }
  if (fontSize) {
    defaultStyle.fontSize = fontSize * PX_TO_PT_RATE;
  }
  if (color) {
    defaultStyle.color = color;
  }
  if (lineHeight) {
    defaultStyle.lineHeight = lineHeight * 0.9 /* / fontSize */;
  }

  var docDefinition = {
    info: { title: name ?? 'Mẫu hóa đơn' },
    pageSize: paperSize,
    pageOrientation: paperSize == 'A5' ? 'landscape' : 'portrait',
    pageMargins: paperSize == 'A5' ? 20 : 30,
    background: [],
    content: [
      {
        style: 'tableExample',
        layout: 'noBordersNoPaddings',
        table: {
          headerRows: duplicateInfoMulPages == 1 ? 1 : 0,
          widths: ['*'],
          body: [[[]], [[]]],
        },
      },
    ],
    style: {
      tableExample: {
        margin: 0,
      },
    },
    pageBreakBefore: (
      currentNode,
      followingNodesOnPage,
      nodesOnNextPage,
      previousNodesOnPage,
    ) => {
      if (currentNode.id == 'contentEnd') {
        return currentNode.startPosition.top > (paperSize == 'A5' ? 392 : 772);
      } else if (currentNode.id == 'signXmlStart') {
        return (
          followingNodesOnPage.find(item => item.id == 'contentEnd')
            ?.startPosition.top > (paperSize == 'A5' ? 392 : 772)
        );
      }
      return false;
    },
  };

  let watermarkOpacity = (metadata?.watermark?.opacity || 0) / 100;
  if (_invoice?.type == 4 && isShowWatermarkInAdjustedInvoice) {
    docDefinition.watermark = {
      text: 'ĐÃ BỊ THAY THẾ',
      fontSize: 30,
      color: 'red',
      bold: true,
      angle: -30,
      opacity: watermarkOpacity,
    };
  } else if (_invoice?.type == 5 && isShowWatermarkInAdjustedInvoice) {
    docDefinition.watermark = {
      text: 'ĐÃ BỊ ĐIỀU CHỈNH',
      fontSize: 30,
      color: 'red',
      bold: true,
      angle: -30,
      opacity: watermarkOpacity,
    };
  } else if (sample) {
    docDefinition.watermark = {
      text: 'MẪU',
      fontSize: 50,
      color: 'red',
      bold: true,
      angle: -30,
    };
  }

  if (background) {
    docDefinition.background.push({
      image: background,
      // width: STANDARD_PAGE_SIZES[paperSize].at(0),
      height: STANDARD_PAGE_SIZES[paperSize].at(1),
      opacity: backgroundOpacity,
      alignment: 'center',
    });
  }

  if (customBackground) {
    let customBackgroundMetadata = metadata?.customBackground || {};
    let { width, height, x, y, opacity } = customBackgroundMetadata;

    docDefinition.background.push({
      image: customBackground,
      width: width * 0.75,
      height: height * 0.75,
      opacity: opacity / 100,

      absolutePosition: {
        x: x * 0.75,
        y: y * 0.75,
      },
    });
  }

  if (border) {
    docDefinition.background.push({ svg: border });
  }

  if (defaultStyle) {
    docDefinition.defaultStyle = defaultStyle;
  }

  /* fields */
  let groupByRegionName = fields.reduce((group, field) => {
    const { regionName } = field;
    group[regionName] = group[regionName] ?? [];
    group[regionName].push(field);

    return group;
  }, {});

  /* sellerInfo start */
  let sellerInfoStack;
  if (groupByRegionName['sellerInfo']?.length) {
    let sellerInfoMetadata = metadata?.region?.sellerInfo || {};
    let { alignType, maxWidth } = sellerInfoMetadata;
    sellerInfoStack = [];
    groupByMergeFieldArr(groupByRegionName['sellerInfo']).forEach(
      ([mergeField, ...subFields]) => {
        if (subFields?.length > 1) {
          let subFieldsMetadata = metadata.subFields[mergeField];
          let column = { columns: [], gap: 0 };
          subFields.forEach((field, index) => {
            if (field.active != 1) return;

            let width = {};
            let { label, labelEn, value, metadata } = field;
            if (utils.isJSONStringified(label)) {
              label = JSON.parse(label);
            }
            if (utils.isJSONStringified(labelEn)) {
              labelEn = JSON.parse(labelEn);
            }
            if (utils.isJSONStringified(value)) {
              value = JSON.parse(value);
            }
            if (utils.isJSONStringified(metadata)) {
              metadata = JSON.parse(metadata);
            }
            if (subFieldsMetadata) {
              width = subFieldsMetadata[index];
              width.width = width.width == '*' ? '*' : width.width * 0.75;
            }
            column.columns.push({
              ...genHorizontalParagraph(
                fontSize,
                isShowLabelEn,
                { ...label },
                { ...labelEn },
                { ...value },
                true,
                metadata,
                maxWidth,
                alignType,
              ),
              ...width,
            });
          });
          sellerInfoStack.push(column);
        } else {
          let subField = subFields[0];
          if (subField.active != 1) return;

          let { label, labelEn, value, metadata } = subField;
          if (utils.isJSONStringified(label)) {
            label = JSON.parse(label);
          }
          if (utils.isJSONStringified(labelEn)) {
            labelEn = JSON.parse(labelEn);
          }
          if (utils.isJSONStringified(value)) {
            value = JSON.parse(value);
          }
          if (utils.isJSONStringified(metadata)) {
            metadata = JSON.parse(metadata);
          }

          sellerInfoStack.push(
            genHorizontalParagraph(
              fontSize,
              isShowLabelEn,
              { ...label },
              { ...labelEn },
              { ...value },
              true,
              metadata,
              maxWidth,
              alignType,
            ),
          );
        }
      },
    );
  }
  /* sellerInfo end */

  /* invoiceInfo start*/
  let invoiceInfoTable = {
    table: { style: 'tableExample' },
    layout: 'noBordersNoPaddings',
    margin: [0, 2, 0, 2],
  };

  let invoiceInfoWidths = region.find(
    ({ regionName }) => regionName === 'invoiceInfo',
  )?.widths;
  if (!invoiceInfoWidths) throw new Error('Thiếu invoiceInfo region');
  invoiceInfoWidths = invoiceInfoWidths
    .map(item => (item.dataIndex ? item.width : item))
    .map(w => (isValidNumber(w) ? w * 0.75 : w));
  invoiceInfoTable.table.widths = invoiceInfoWidths;

  let invoiceInfoRow = [
    sellerInfoPosition == 3 ? sellerInfoStack : { text: '' },
  ];
  if (groupByRegionName['invoiceInfo']?.length) {
    let stack = [];
    groupByRegionName['invoiceInfo'].forEach(field => {
      if (field.name == 'descriptionInvoicePaperClient' && exType == 0)
        field.active = 1;

      if (field.active != 1) return;
      let { label, labelEn, value, metadata } = field;
      if (utils.isJSONStringified(label)) {
        label = JSON.parse(label);
      }
      if (utils.isJSONStringified(labelEn)) {
        labelEn = JSON.parse(labelEn);
      }
      if (utils.isJSONStringified(value)) {
        value = JSON.parse(value);
      }
      if (utils.isJSONStringified(metadata)) {
        metadata = JSON.parse(metadata);
      }

      if (metadata?.typeShow == 3) {
        stack.push(
          genHorizontalParagraphText(
            fontSize,
            isShowLabelEn,
            { ...label },
            { ...labelEn },
            { ...value },
          ),
        );
      } else {
        stack.push(
          genVerticalParagraph(
            fontSize,
            isShowLabelEn,
            { ...label },
            { ...labelEn },
            { ...value },
          ),
        );
      }
    });
    invoiceInfoRow.push({ stack, alignment: 'center' });
  }
  if (groupByRegionName['invoiceInfoOther']?.length) {
    let stack = [];
    groupByRegionName['invoiceInfoOther'].forEach(field => {
      if (field.active != 1) return;
      let { label, labelEn, value } = field;
      if (utils.isJSONStringified(label)) {
        label = JSON.parse(label);
      }
      if (utils.isJSONStringified(labelEn)) {
        labelEn = JSON.parse(labelEn);
      }
      if (utils.isJSONStringified(value)) {
        value = JSON.parse(value);
      }
      stack.push(
        genHorizontalParagraph(
          fontSize,
          isShowLabelEn,
          { ...label },
          { ...labelEn },
          { ...value },
        ),
      );
    });
    invoiceInfoRow.push({ stack });
  }
  invoiceInfoTable.table.body = [invoiceInfoRow];
  /* invoiceInfo end*/

  /* firstInvoice start*/
  let logo;
  // let file = file ?? logoBody;
  if (logoBody && !isBase64Encoded(logoBody)) {
    logo = fs.readFileSync(path.join(path.resolve('public/logo'), logoBody));
  } else {
    logo = logoBody;
  }
  let firstInvoiceRow = [];
  let logoImage;
  if (logo) {
    logoImage = { image: logo };
    let logoMetadata = metadata?.logo || {};
    let { width, height, x, y } = logoMetadata;

    if (width) {
      logoImage.width = metadata.logo.width * 0.75;
    }
    if (height) {
      logoImage.height = metadata.logo.height * 0.75;
    }

    if (_.isNumber(x) && _.isNumber(y)) {
      logoImage.margin = [x * 0.75, y * 0.75, 0, 0];
    }
  } else {
    logoImage = '';
  }
  // firstInvoiceRow.push(logoImage);

  let firstInvoiceSecondContent;
  if (sellerInfoPosition == 1) {
    if (sellerInfoStack) {
      firstInvoiceSecondContent = { stack: sellerInfoStack };
      // firstInvoiceRow.push({ stack: sellerInfoStack });
    }
  } else {
    if (invoiceInfoTable) {
      firstInvoiceSecondContent = invoiceInfoTable;
      // firstInvoiceRow.push(invoiceInfoTable);
    }
  }

  if (logoPosition == 1) {
    firstInvoiceRow.push(logoImage, firstInvoiceSecondContent);
  } else {
    firstInvoiceRow.push(firstInvoiceSecondContent, logoImage);
  }

  let firstInvoiceWidths = region.find(
    ({ regionName }) => regionName === 'firstInvoice',
  )?.widths;
  if (!firstInvoiceWidths) throw new Error('Thiếu firstInvoice region');
  firstInvoiceWidths = firstInvoiceWidths
    .map(item => (item.dataIndex ? item.width : item))
    .map(w => (isValidNumber(w) ? w * 0.75 : w));
  /*  */
  docDefinition.content[0].table.body[0][0].push(
    {
      table: {
        style: 'tableExample',
        widths: firstInvoiceWidths,
        body: [firstInvoiceRow],
      },
      margin: [0, 0, 0, 2],
      layout: 'noBordersNoPaddings',
    },
    genHrElement(),
  );
  /* firstInvoice end*/

  /* secondInvoice start*/
  if (sellerInfoPosition == '1') {
    docDefinition.content[0].table.body[0][0].push(invoiceInfoTable);
  } else if (sellerInfoPosition == '2') {
    docDefinition.content[0].table.body[0][0].push({
      stack: sellerInfoStack,
      margin: [0, 2, 0, 0],
    });
  }
  /* secondInvoice end*/

  /* buyerInfo start */
  let buyerInfoTable = {
    table: { style: 'tableExample' },
    layout: 'noBordersNoPaddings',
    margin: [0, 2, 0, 2],
  };

  let buyerInfoWidths = region.find(
    ({ regionName }) => regionName === 'buyerInfo',
  )?.widths;
  if (!buyerInfoWidths) throw new Error('Thiếu buyerInfo region');
  buyerInfoWidths = buyerInfoWidths
    .map(item => (item.dataIndex ? item.width : item))
    .map(w => (isValidNumber(w) ? w * 0.75 : w));
  buyerInfoTable.table.widths = buyerInfoWidths;

  let buyerInfoRow = [];
  if (groupByRegionName['buyerInfo']?.length) {
    let buyerInfoMetadata = metadata?.region?.buyerInfo || {};
    let { alignType, maxWidth } = buyerInfoMetadata;

    let stack = [];
    groupByMergeFieldArr(groupByRegionName['buyerInfo']).forEach(
      ([mergeField, ...subFields]) => {
        if (subFields?.length > 1) {
          let subFieldsMetadata = metadata.subFields[mergeField];
          let column = { columns: [], gap: 0 };
          subFields.forEach((field, index) => {
            if (field.active != 1) return;

            let width = {};
            let { label, labelEn, value, metadata } = field;
            if (utils.isJSONStringified(label)) {
              label = JSON.parse(label);
            }
            if (utils.isJSONStringified(labelEn)) {
              labelEn = JSON.parse(labelEn);
            }
            if (utils.isJSONStringified(value)) {
              value = JSON.parse(value);
            }
            if (utils.isJSONStringified(metadata)) {
              metadata = JSON.parse(metadata);
            }
            if (subFieldsMetadata) {
              width = subFieldsMetadata[index];
              width.width = width.width == '*' ? '*' : width.width * 0.75;
            }
            column.columns.push({
              ...genHorizontalParagraph(
                fontSize,
                isShowLabelEn,
                { ...label },
                { ...labelEn },
                { ...value },
                true,
                {},
                maxWidth,
                alignType,
              ),
              ...width,
              margin: [(metadata.mrRank || 0) * 12, 0, 0, 0],
            });
          });
          stack.push(column);
        } else {
          let subField = subFields[0];
          if (subField.active != 1) return;

          let { label, labelEn, value, metadata } = subField;
          if (utils.isJSONStringified(label)) {
            label = JSON.parse(label);
          }
          if (utils.isJSONStringified(labelEn)) {
            labelEn = JSON.parse(labelEn);
          }
          if (utils.isJSONStringified(value)) {
            value = JSON.parse(value);
          }
          if (utils.isJSONStringified(metadata)) {
            metadata = JSON.parse(metadata);
          }
          stack.push({
            ...genHorizontalParagraph(
              fontSize,
              isShowLabelEn,
              { ...label },
              { ...labelEn },
              { ...value },
              true,
              {},
              maxWidth,
              alignType,
            ),
            margin: [(metadata.mrRank || 0) * 12, 0, 0, 0],
          });
        }
      },
    );

    buyerInfoRow.push({ stack });
  }
  buyerInfoRow.push(
    isShowQRCode
      ? {
          qr: 'https://hoadon.vietinvoice.vn/',
          fit: (paperSize == 'A5' ? 70 : 80) * 0.75,
          margin: [0, 5, 0, 0],
        }
      : '',
  );
  buyerInfoTable.table.body = [buyerInfoRow];
  docDefinition.content[0].table.body[0][0].push(
    genHrElement(),
    buyerInfoTable,
  );
  /* buyerInfo end */

  if (_invoice?.type == 1) {
    docDefinition.content[0].table.body[0][0].push(
      `Thay thế cho hóa đơn ký hiệu ${_invoice?.Original?.serial}, số ${
        _invoice?.Original?.no
      }, ${moment(_invoice?.Original?.date)
        .locale('vi')
        .format('[ngày] DD [tháng] MM [năm] YYYY')}. Lý do: ${
        _invoice.description
      }`,
    );
  } else if (_invoice?.type == 2) {
    docDefinition.content[0].table.body[0][0].push(
      `Điều chỉnh cho hóa đơn ký hiệu ${_invoice?.Original?.serial}, số ${
        _invoice?.Original?.no
      }, ${moment(_invoice?.Original?.date)
        .locale('vi')
        .format('[ngày] DD [tháng] MM [năm] YYYY')}. Lý do: ${
        _invoice.description
      }`,
    );
  }

  let hLineStyle = null;
  switch (lineType) {
    case 'dotted':
      hLineStyle = { dash: { length: 1, space: 1 } };
      break;
    case 'dashed':
      hLineStyle = { dash: { length: 4, space: 2 } };
      break;
    case 'solid':
    case 'none':
      hLineStyle = null;
  }
  /* tableDetail start */
  if (!mulTaxRate) {
    let tableDetailTable = {
      id: 'tableDetail',
      table: { style: 'tableExample', headerRows: 1 },
      layout: {
        paddingLeft: i => (i == 0 ? 0 : 2),
        paddingRight: i => (i == 0 ? 0 : 2),
        paddingTop: setTopMarginOfCellForVerticalCentering,
        paddingBottom: () => 0,
        hLineWidth: (i, node) => {
          return i == 0 || i == 1 || i == node.table.body.length
            ? TABLE_LINE_WIDTH
            : lineType == 'none'
            ? 0
            : TABLE_LINE_WIDTH;
        },
        vLineWidth: (i, node) => {
          return TABLE_LINE_WIDTH;
        },
        hLineColor: (i, node) =>
          i == 0 || i == 1 || i == node.table.body.length ? 'black' : color,
        hLineStyle: (i, node) =>
          i == 0 || i == 1 || i == node.table.body.length ? null : hLineStyle,
      },
    };

    let tableDetailWidths = region.find(
      ({ regionName }) => regionName === 'tableDetail',
    )?.widths;
    if (!tableDetailWidths) throw new Error('Thiếu tableDetail region');
    tableDetailWidths = tableDetailWidths
      .filter(({ show }) => !!show)
      .map(item => (item.dataIndex ? item.width : item))
      .map(w => (isValidNumber(w) ? w * 0.75 : w));
    tableDetailTable.table.widths = tableDetailWidths;

    let tableDetailRow = [];
    let activeFieldNames = [];
    if (groupByRegionName['tableDetail']?.length) {
      groupByRegionName['tableDetail'].forEach(field => {
        if (field.active != 1) return;
        let { label, labelEn, value } = field;
        if (utils.isJSONStringified(label)) {
          label = JSON.parse(label);
        }
        if (utils.isJSONStringified(labelEn)) {
          labelEn = JSON.parse(labelEn);
        }
        if (utils.isJSONStringified(value)) {
          value = JSON.parse(value);
        }
        tableDetailRow.push(
          genVerticalParagraph(
            fontSize,
            isShowLabelEn,
            { ...label },
            { ...labelEn },
            { ...value },
          ),
        );

        activeFieldNames.push(field.name);
      });
    }
    tableDetailTable.table.body = [tableDetailRow];

    let tableDetailMetadata = metadata?.region?.tableDetail;
    let { tableHeaderColor, minItemRows } = tableDetailMetadata;

    if (invoiceProducts?.length) {
      invoiceProducts.forEach(product => {
        let productRow = activeFieldNames.map((name, index) => ({
          text: fillInvoiceProductDataToField(name, product, calcConfig),
          alignment: COL_ALIGN_DICT[name] || 'center',
        }));
        tableDetailTable.table.body.push(productRow);
      });
      if (minItemRows > invoiceProducts.length) {
        [...Array(minItemRows - invoiceProducts.length).keys()].forEach(num => {
          let spaceRow = [...Array(tableDetailRow.length).keys()].map(
            x => '\u0020',
          );
          tableDetailTable.table.body.push(spaceRow);
        });
      }
    } else if (minItemRows) {
      [...Array(minItemRows).keys()].forEach(num => {
        let spaceRow = [...Array(tableDetailRow.length).keys()].map(
          x => '\u0020',
        );
        tableDetailTable.table.body.push(spaceRow);
      });
    }

    if (tableHeaderColor) {
      tableDetailTable.layout.fillColor = (i, node) =>
        i == 0 ? tableHeaderColor : null;
    }

    docDefinition.content[0].table.body[1][0].push(
      genHrElement(),
      tableDetailTable,
    );
  }
  /* tableDetail end */

  /* tableDetail1 start */
  if (mulTaxRate) {
    let tableDetail1Table = {
      id: 'tableDetail1',
      table: { style: 'tableExample', headerRows: 1 },
      layout: {
        paddingLeft: () => 0,
        paddingRight: () => 0,
        paddingTop: setTopMarginOfCellForVerticalCentering /* () => 0 */,
        paddingBottom: () => 0,
        hLineWidth: (i, node) => {
          return i == 0 || i == 1 || i == node.table.body.length
            ? TABLE_LINE_WIDTH
            : lineType == 'none'
            ? 0
            : TABLE_LINE_WIDTH;
        },
        vLineWidth: (i, node) => {
          return TABLE_LINE_WIDTH;
        },
        hLineColor: (i, node) =>
          i == 0 || i == 1 || i == node.table.body.length ? 'black' : color,
        hLineStyle: (i, node) =>
          i == 0 || i == 1 || i == node.table.body.length ? null : hLineStyle,
      },
    };

    let FieldNamesHasChildrent =
      groupByRegionName['tableDetail1']
        ?.filter(item =>
          utils.isJSONStringified(item.children)
            ? JSON.parse(item.children)?.length
            : item.children?.length,
        )
        .map(({ name }) => name) || [];
    if (FieldNamesHasChildrent?.length) {
      tableDetail1Table.table.headerRows = 2;
    }

    let tableDetail1Widths = region.find(
      ({ regionName }) => regionName === 'tableDetail1',
    )?.widths;
    if (!tableDetail1Widths) throw new Error('Thiếu tableDetail1 region');
    tableDetail1Widths = tableDetail1Widths
      .filter(({ show }) => !!show)
      .reduce(
        (arr, item) =>
          FieldNamesHasChildrent.includes(item.dataIndex)
            ? [...arr, item, item]
            : [...arr, item],
        [],
      )
      .map(item => (item.dataIndex ? item.width : item))
      .map(w => (isValidNumber(w) ? w * 0.75 : w));
    tableDetail1Table.table.widths = tableDetail1Widths;

    let tableDetail1Row = [];
    let tableDetail1Row2 = [];
    let activeFieldNames1 = [];
    if (groupByRegionName['tableDetail1']?.length) {
      groupByRegionName['tableDetail1'].forEach(field => {
        if (field.active != 1) return;
        let { label, labelEn, value, children } = field;
        if (utils.isJSONStringified(label)) {
          label = JSON.parse(label);
        }
        if (utils.isJSONStringified(labelEn)) {
          labelEn = JSON.parse(labelEn);
        }
        if (utils.isJSONStringified(value)) {
          value = JSON.parse(value);
        }
        if (utils.isJSONStringified(children)) {
          children = JSON.parse(children);
          if (children?.length) {
            children.forEach(child => {
              tableDetail1Row2.push({
                ...genVerticalParagraph(
                  fontSize,
                  isShowLabelEn,
                  { ...child.label },
                  { ...child.labelEn },
                  { ...child.value },
                ),
              });
            });
          } else {
            tableDetail1Row2.push('');
          }
        }
        tableDetail1Row.push({
          ...genVerticalParagraph(
            fontSize,
            isShowLabelEn,
            { ...label },
            { ...labelEn },
            { ...value },
          ),
          colSpan: children?.length || 1,
          rowSpan: FieldNamesHasChildrent?.length && !children?.length ? 2 : 1,
        });
        if (children?.length) {
          tableDetail1Row.push({});
        }
        // console.log(field.name);
        activeFieldNames1.push(field.name);
      });
    }
    tableDetail1Table.table.body = [tableDetail1Row];
    if (tableDetail1Row2?.some(item => item)) {
      tableDetail1Table.table.body.push(tableDetail1Row2);
    }

    let tableDetail1Metadata = metadata?.region?.tableDetail1 || {};
    let { tableHeaderColor: tableHeaderColor1, minItemRows: minItemRows1 } =
      tableDetail1Metadata;

    if (invoiceProducts?.length) {
      invoiceProducts.forEach(product => {
        let productRow = activeFieldNames1.map(name => ({
          text: fillInvoiceProductDataToField(name, product, calcConfig),
          alignment: COL_ALIGN_DICT[name] || 'center',
          margin: [4, 0, 4, 0],
        }));
        tableDetail1Table.table.body.push(productRow);
      });

      if (minItemRows1 > invoiceProducts.length) {
        [...Array(minItemRows1 - invoiceProducts.length).keys()].forEach(
          num => {
            let spaceRow = [...Array(tableDetail1Row.length).keys()].map(
              x => '\u0020',
            );
            tableDetail1Table.table.body.push(spaceRow);
          },
        );
      }
    } else if (minItemRows1) {
      [...Array(minItemRows1).keys()].forEach(num => {
        let spaceRow = [...Array(tableDetail1Row.length).keys()].map(
          x => '\u0020',
        );
        tableDetail1Table.table.body.push(spaceRow);
      });
    }

    if (tableHeaderColor1) {
      tableDetail1Table.layout.fillColor = (i, node) =>
        i == 0 ? tableHeaderColor1 : null;
    }

    docDefinition.content[0].table.body[1][0].push(
      genHrElement(),
      tableDetail1Table,
    );
  }
  /* tableDetail1 end */

  /* tableFooter start */
  if (!mulTaxRate) {
    let tableFooterTable = {
      table: { style: 'tableExample' },
      unbreakable: true,
      layout: {
        paddingLeft: i => (i == 0 ? 2 : 0),
        paddingRight: () => 0,
        paddingBottom: () => 0,
        paddingTop: () => 0,
        hLineWidth: (i, node) => {
          return TABLE_LINE_WIDTH;
        },
        vLineWidth: (i, node) => {
          return i == 0 || i == node.table.body.length ? TABLE_LINE_WIDTH : 0;
        },
      },
    };

    let tableFooterWidths = region.find(
      ({ regionName }) => regionName === 'tableFooter',
    )?.widths;
    if (!tableFooterWidths) throw new Error('Thiếu tableFooter region');
    tableFooterWidths = tableFooterWidths
      .map(item => (item.dataIndex ? item.width : item))
      .map(w => (isValidNumber(w) ? w * 0.75 : w));
    tableFooterTable.table.widths = tableFooterWidths;

    let tableFooterMetadata = metadata?.region?.tableFooter;
    let { fullColumnDataFields: TF_fullColumnDataFields } = tableFooterMetadata;

    let tableFooterBody = [];
    let tableFooterRow = [];
    if (groupByRegionName['tableFooter']?.length) {
      groupByRegionName['tableFooter'].forEach(field => {
        if (
          discount &&
          ['discountRate', 'totalDiscountAmount'].includes(field.name)
        )
          field.active = 1;

        if (field.active != 1) return;
        let { label, labelEn, value, name, metadata } = field;

        if (name.includes('notShow')) {
          tableFooterRow.push('', '');
          return;
        }

        if (utils.isJSONStringified(label)) {
          label = JSON.parse(label);
        }
        if (utils.isJSONStringified(labelEn)) {
          labelEn = JSON.parse(labelEn);
        }
        if (utils.isJSONStringified(value)) {
          value = JSON.parse(value);
        }
        if (utils.isJSONStringified(metadata)) {
          metadata = JSON.parse(metadata);
        }

        if (TF_fullColumnDataFields?.includes(name)) {
          tableFooterBody.push([
            {
              ...genHorizontalParagraph(
                fontSize,
                isShowLabelEn,
                { ...label },
                { ...labelEn },
                { ...value },
              ),
              colSpan: 4,
              margin: [(metadata.mrRank || 0) * 12, 0, 0, 0],
            },
            '',
            '',
            '',
          ]);
          return;
        }
        tableFooterRow.push(
          {
            ...genHorizontalParagraph(
              fontSize,
              isShowLabelEn,
              { ...label },
              { ...labelEn },
              {},
            ),
            margin: [(metadata.mrRank || 0) * 12, 0, 0, 0],
          },
          {
            // ...genHorizontalParagraph(
            //     fontSize,
            //     isShowLabelEn,
            //     {},
            //     {},
            //     { ...value },
            //     false
            // ),
            text: value.value,
            alignment: 'right',
            margin: [0, 0, 4, 0],
          },
        );
        if (tableFooterRow.length == 4) {
          tableFooterBody.push(tableFooterRow);
          tableFooterRow = [];
        }
      });
    }
    tableFooterTable.table.body = tableFooterBody;
    docDefinition.content[0].table.body[1][0].push(tableFooterTable);
  }
  /* tableFooter end */

  /* tableFooter1 start */
  if (mulTaxRate) {
    let tableFooter1Table = {
      table: { style: 'tableExample', headerRows: 1 },
      unbreakable: true,
      layout: {
        paddingTop: setTopMarginOfCellForVerticalCentering,
        paddingLeft: i => (i == 0 ? 2 : 0),
        paddingRight: i => (i == 0 ? 0 : 2),
        paddingBottom: () => 0,
        hLineWidth: (i, node) => {
          return TABLE_LINE_WIDTH;
        },
        vLineWidth: (i, node) => {
          return TABLE_LINE_WIDTH;
        },
      },
    };

    let tableFooter1Widths = region.find(
      ({ regionName }) => regionName === 'tableFooter1',
    )?.widths;
    if (!tableFooter1Widths) throw new Error('Thiếu tableFooter1 region');
    tableFooter1Widths = tableFooter1Widths
      .map(item => (item.dataIndex ? item.width : item))
      .map(w => (isValidNumber(w) ? w * 0.75 : w));
    tableFooter1Table.table.widths = tableFooter1Widths;

    let tableFooter1Metadata = metadata?.region?.tableFooter1;
    let { type, fullColumnDataFields, fullColumnRowIndexs } =
      tableFooter1Metadata || {};

    if (type == 2) {
      let tableFooter1Body = [];
      let tableFooter1Row = [];
      if (groupByRegionName['tableFooter1']?.length) {
        groupByRegionName['tableFooter1'].forEach(field => {
          if (
            discount &&
            ['DiscountRate', 'TotalDiscountAmount'].includes(field.name)
          )
            field.active = 1;

          if (field.active != 1) return;
          let { label, labelEn, value, name, metadata } = field;

          if (name.includes('NotShow')) {
            tableFooter1Row.push('', '');
            return;
          }

          if (utils.isJSONStringified(label)) {
            label = JSON.parse(label);
          }
          if (utils.isJSONStringified(labelEn)) {
            labelEn = JSON.parse(labelEn);
          }
          if (utils.isJSONStringified(value)) {
            value = JSON.parse(value);
          }
          if (utils.isJSONStringified(metadata)) {
            metadata = JSON.parse(metadata);
          }

          if (fullColumnDataFields?.includes(name)) {
            tableFooter1Body.push([
              {
                ...genHorizontalParagraphText(
                  fontSize,
                  isShowLabelEn,
                  { ...label },
                  { ...labelEn },
                  { ...value },
                ),
                colSpan: 4,
                margin: [(metadata.mrRank || 0) * 12, 0, 0, 0],
              },
              '',
              '',
              '',
            ]);
            return;
          }
          tableFooter1Row.push(
            {
              ...genHorizontalParagraphText(
                fontSize,
                isShowLabelEn,
                { ...label },
                { ...labelEn },
                {},
              ),
              margin: [(metadata.mrRank || 0) * 12, 0, 0, 0],
            },
            genHorizontalParagraphText(
              fontSize,
              isShowLabelEn,
              {},
              {},
              { ...value },
              false,
            ),
          );
          if (tableFooter1Row.length == 4) {
            tableFooter1Body.push(tableFooter1Row);
            tableFooter1Row = [];
          }
        });
      }
      tableFooter1Table.table.body = tableFooter1Body;
      docDefinition.content[0].table.body[1][0].push(tableFooter1Table);
    } else {
      let tableFooter1Body = [];
      let tableFooter1Row = [];

      let taxArray = [];
      invoiceProducts
        ?.filter(item => item.productType != PRODUCT_TYPE.DG)
        .forEach(product => {
          const existingTax = taxArray.find(i => i.taxRate == product.vatRate);

          if (existingTax) {
            if (product?.productType == PRODUCT_TYPE.HHDV) {
              existingTax.taxMoney += product?.vatAmount;
              existingTax.total += product.amountTotal - product.discountAmount;
              existingTax.totalAmount +=
                product.amountTotal + product?.vatAmount;
            } else if (product?.productType == PRODUCT_TYPE.CKTM) {
              existingTax.taxMoney -= product?.vatAmount;
              existingTax.total -= product.amountTotal;
              existingTax.totalAmount -=
                product.amountTotal + product?.vatAmount;
            }
          } else {
            if (product?.productType == PRODUCT_TYPE.HHDV) {
              let total = product.amountTotal - product.discountAmount;

              let taxMoney = product?.vatAmount;

              let totalAmount =
                product.amountTotal + product?.vatAmount; /* total + taxMoney */

              taxArray.push({
                taxRate: product.vatRate,
                total,
                taxMoney,
                totalAmount,
              });
            } else if (product?.productType == PRODUCT_TYPE.CKTM) {
              let total = -product.amountTotal;

              let taxMoney = -product?.vatAmount;

              let totalAmount = -(
                product.amountTotal + product?.vatAmount
              ); /* total + taxMoney */

              taxArray.push({
                taxRate: product.vatRate,
                total,
                taxMoney,
                totalAmount,
              });
            }
          }
        });
      let TotalSumary = taxArray.reduce((result, item) => {
        return {
          total: (result.total || 0) + item.total,
          taxMoney: (result.taxMoney || 0) + item.taxMoney,
          totalAmount: (result.totalAmount || 0) + item.totalAmount,
        };
      }, {});
      // TotalSumary calc
      taxArray.push({
        taxRate: 'TOTAL',
        total:
          isValidNumber(invoice.amountBeforeVat) &&
          Number(invoice.amountBeforeVat)
            ? invoice.amountBeforeVat
            : TotalSumary?.total,
        taxMoney:
          isValidNumber(invoice.amountVat) && Number(invoice.amountVat)
            ? invoice.amountVat
            : TotalSumary?.taxMoney,
        totalAmount:
          isValidNumber(invoice.finalAmount) && Number(invoice.finalAmount)
            ? invoice.finalAmount
            : TotalSumary?.totalAmount,
      });

      if (groupByRegionName['tableFooter1']?.length) {
        groupByRegionName['tableFooter1'].forEach(field => {
          if (
            discount &&
            ['discountRate', 'totalDiscountAmount'].includes(field.name)
          )
            field.active = 1;

          if (field.active != 1) return;
          let { label, labelEn, value, name } = field;

          if (utils.isJSONStringified(label)) {
            label = JSON.parse(label);
          }
          if (utils.isJSONStringified(labelEn)) {
            labelEn = JSON.parse(labelEn);
          }
          if (utils.isJSONStringified(value)) {
            value = JSON.parse(value);
          }

          if (['TotalAmountInWords', 'TemplateNote'].includes(name)) {
            tableFooter1Body.push([
              {
                ...genHorizontalParagraphText(
                  fontSize,
                  isShowLabelEn,
                  { ...label },
                  { ...labelEn },
                  { ...value },
                ),
                colSpan: 4,
              },
            ]);
            return;
          }

          if (
            [
              'General',
              'AmountWithoutVAT',
              'VATAmountDetail',
              'TotalVATAmount',
            ].includes(name)
          ) {
            tableFooter1Row.push(
              genVerticalParagraph(
                fontSize,
                isShowLabelEn,
                { ...label },
                { ...labelEn },
                { ...value },
              ),
            );
          } else {
            let { total, taxMoney, totalAmount } = fillDataTableFooter1Row(
              name,
              taxArray,
            );
            total = isValidNumber(total) ? formatNumber(total) : total;
            taxMoney = isValidNumber(taxMoney)
              ? formatNumber(taxMoney)
              : taxMoney;
            totalAmount = isValidNumber(totalAmount)
              ? formatNumber(totalAmount)
              : totalAmount;

            tableFooter1Row.push(
              genHorizontalParagraphText(
                fontSize,
                isShowLabelEn,
                { ...label },
                { ...labelEn },
                { ...value },
              ),
              {
                text: total || '',
                alignment: 'right',
                bold: name == 'TotalSumary',
              },
              {
                text: taxMoney || '',
                alignment: 'right',
                bold: name == 'TotalSumary',
              },
              {
                text: totalAmount || '',
                alignment: 'right',
                bold: name == 'TotalSumary',
              },
            );
          }
          if (tableFooter1Row.length == 4) {
            tableFooter1Body.push(tableFooter1Row);
            tableFooter1Row = [];
          }
        });
      }
      tableFooter1Table.table.body = tableFooter1Body;
      docDefinition.content[0].table.body[1][0].push(tableFooter1Table);
    }
  }
  /* tableFooter1 end */

  /* currencyBlock start */
  let currencyBlockStack = { stack: [] };
  if (groupByRegionName['currencyBlock']?.length) {
    groupByRegionName['currencyBlock'].forEach(field => {
      if (
        foreginCurrency &&
        ['exchangeRate', 'translationCurrency'].includes(field.name)
      )
        field.active = 1;

      if (field.active != 1) return;
      let { label, labelEn, value, name } = field;

      if (utils.isJSONStringified(label)) {
        label = JSON.parse(label);
      }
      if (utils.isJSONStringified(labelEn)) {
        labelEn = JSON.parse(labelEn);
      }
      if (utils.isJSONStringified(value)) {
        value = JSON.parse(value);
      }

      currencyBlockStack.stack.push(
        genHorizontalParagraphText(
          fontSize,
          isShowLabelEn,
          { ...label },
          { ...labelEn },
          { ...value },
        ),
      );
    });

    docDefinition.content[0].table.body[1][0].push(currencyBlockStack);
  }
  /* currencyBlock end */

  docDefinition.content[0].table.body[1][0].push({
    text: '\u0020',
    id: 'signXmlStart',
    fontSize: 1,
  });

  /* signXml start */
  let signXmlTable = {
    margin: [0, 7, 0, 0],
    table: { style: 'tableExample', headerRows: 1, keepWithHeaderRows: true },
    layout: 'noBordersNoPaddings',
  };

  let signXmlWidths = region.find(
    ({ regionName }) => regionName === 'signXml',
  )?.widths;
  if (!signXmlWidths) throw new Error('Thiếu signXml region');
  signXmlWidths = signXmlWidths
    .map(item => (item.dataIndex ? item.width : item))
    .map(w => (isValidNumber(w) ? w * 0.75 : w));
  signXmlTable.table.widths = signXmlWidths;

  let signXmlRow = [];
  if (groupByRegionName['signXml']?.length) {
    let groupByMergeField = groupByRegionName['signXml'].reduce(
      (group, field) => {
        let { metadata } = field;
        if (utils.isJSONStringified(metadata)) {
          metadata = JSON.parse(metadata);
        }
        group[metadata?.mergeField] = group[metadata?.mergeField] ?? [];
        group[metadata?.mergeField].push(field);

        return group;
      },
      [],
    );
    for (let mergeField in groupByMergeField) {
      let stack = [];
      let signByRow = [];
      let signDateRow = [];
      let groupFields = groupByMergeField[mergeField];
      groupFields.forEach((field, index) => {
        if (field.active != 1) return;
        let { label, labelEn, value } = field;
        if (utils.isJSONStringified(label)) {
          label = JSON.parse(label);
        }
        if (utils.isJSONStringified(labelEn)) {
          labelEn = JSON.parse(labelEn);
        }
        if (utils.isJSONStringified(value)) {
          value = JSON.parse(value);
        }
        if (index == 0) {
          stack.push({
            ...genHorizontalParagraphText(
              fontSize,
              isShowLabelEn,
              { ...label },
              { ...labelEn },
              { ...value },
              false,
            ),
            alignment: 'center',
          });
        } else if (index == 1) {
          stack.push({
            ...genVerticalParagraph(
              fontSize,
              isShowLabelEn,
              { ...label },
              { ...labelEn },
              { ...value },
            ),
            alignment: 'center',
          });
        } else if (index == 2) {
          signByRow.push({
            ...genHorizontalParagraphText(
              fontSize,
              isShowLabelEn,
              { ...label },
              { ...labelEn },
              !sample ? { ...value } : {},
            ),
          });
        } else {
          signDateRow.push({
            ...genHorizontalParagraphText(
              fontSize,
              isShowLabelEn,
              { ...label },
              { ...labelEn },
              !sample ? { ...value } : {},
            ),
          });
        }
      });

      if (invoice.sellerCKS) {
        if (signByRow.length && signDateRow.length) {
          stack.push(genSignDiv(signByRow, signDateRow, '#E3F2E4', !sample));
        }
      }
      signXmlRow.push({ stack });
    }
  }
  signXmlTable.table.body = [signXmlRow];
  docDefinition.content[0].table.body[1][0].push(signXmlTable);
  /* signXml end */

  /* contentEnd */
  docDefinition.content[0].table.body[1][0].push({
    text: '\u0020',
    id: 'contentEnd',
    fontSize: 1,
  });

  /* searchBlock start*/
  let searchBlock = {
    stack: [
      {
        text: `Tra cứu tại Website${
          isShowLabelEn ? ' (Search in Website)' : ''
        }: ${_invoice.supplierLookupWebsite} - Mã tra cứu${
          isShowLabelEn ? ' (Invoice code)' : ''
        }: ${_invoice?.supplierReferenceCode || ''}`,
        // margin: [40, 0, 0, 0],
        alignment: 'center',
      },
      {
        text: `(Cần kiểm tra, đối chiếu khi lập, giao, nhận hóa đơn)${
          isShowLabelEn
            ? ' (You need to check invoice when issuing, delivering and receiving)'
            : ''
        }`,
        alignment: 'center',
      },
      // {
      //   text: 'Phát hành bởi phần mềm VietInvoice.vn - CÔNG TY CỔ PHẦN ICORP (www.icorp.vn) - MST 0106870211',
      //   alignment: 'center',
      // },
    ],
    margin: [0, paperSize == 'A5' ? -30 : -46, 0, 0],
    fontSize: 0.95 * defaultStyle.fontSize,
  };
  /* searchBlock end */
  docDefinition.footer = function (currentPage, pageCount) {
    return currentPage == pageCount ? [searchBlock] : null;
  };

  var fonts = FONTS_COMMON;
  var tableLayouts = TABLE_LAYOUTS_COMMON;
  var printer = new PdfPrinter(fonts);

  var doc = printer.createPdfKitDocument(docDefinition, { tableLayouts });

  /**
   * @type {Buffer}
   */
  const pdfBuf = await new Promise((resolve, reject) => {
    try {
      var chunks = [];
      doc.on('data', chunk => chunks.push(chunk));
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.end();
    } catch (err) {
      reject(err);
    }
  });

  return { contentType: 'application/pdf', buf: pdfBuf };
}

/**
 *
 * @param {import('../types').Invoice} invoice
 */
function calculateForPreview(invoice) {
  const invoiceHelper = require('./invoice.helper');

  if (_.isArray(invoice.InvoiceProducts)) {
    invoice.InvoiceProducts.forEach(item => {
      if (!isValidNumber(item.amountTotal)) {
        if (isValidNumber(item.quantity) && isValidNumber(item.price)) {
          item.amountTotal = item.quantity * item.price;
        }
      }

      if (!isValidNumber(item.vatAmount)) {
        if (typeof invoiceHelper.getVatFromVatType(item.vat) == 'number') {
          item.vatAmount =
            (invoiceHelper.getVatFromVatType(item.vat) / 100) *
            item.amountTotal;
        }
      }
    });
  }

  return invoice;
}

module.exports = { createPreviewInvoicePdf, fillInvoiceDataToFields };
