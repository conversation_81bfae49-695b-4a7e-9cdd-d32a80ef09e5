const sequelize = require('sequelize');
const utils = require('../utils');

/**
 *
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').AuditLog> & import('../types').AuditLog>}
   */
  const AuditLog = Sequelize.define('AuditLogs', {
    auditLogId: {
      type: sequelize.INTEGER.UNSIGNED,
      primaryKey: true,
      autoIncrement: true,
    },
    accountId: sequelize.INTEGER.UNSIGNED,
    action: sequelize.STRING,
    description: sequelize.STRING,
    reference: sequelize.STRING,
    organization: sequelize.STRING,
    ipAddress: sequelize.STRING,
    extraData: {
      type: sequelize.JSON,
      get() {
        let raw = this.getDataValue('extraData');
        if (utils.isJSONStringified(raw)) return JSON.parse(raw);
        return raw;
      },
    },
  });

  return AuditLog;
};
