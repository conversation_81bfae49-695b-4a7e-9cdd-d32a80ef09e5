const sequelize = require('sequelize');
const {
  TAX_REDUCE_VALIDATE_RESULT,
  VAT_TYPE,
} = require('../configs/constants/invoice.constant');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').TaxReduceValidate> & import('../types').TaxReduceValidate>}
   */
  const TaxReduceValidate = Sequelize.define('TaxReduceValidates', {
    taxReduceValidateId: {
      type: sequelize.INTEGER.UNSIGNED,
      primaryKey: true,
      autoIncrement: true,
    },
    invoiceId: { type: sequelize.INTEGER.UNSIGNED, allowNull: false },
    checkResult: {
      type: sequelize.ENUM(...Object.values(TAX_REDUCE_VALIDATE_RESULT)),
      defaultValue: TAX_REDUCE_VALIDATE_RESULT.CHECKING,
    },
    productName: sequelize.STRING,
    isReduceTax: sequelize.BOOLEAN,
    vat: sequelize.ENUM(...Object.values(VAT_TYPE)),
    productCode: sequelize.STRING,
  });

  return TaxReduceValidate;
};
