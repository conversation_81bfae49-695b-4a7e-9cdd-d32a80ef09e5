const sequelize = require('sequelize');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').AcceptTaxReduce> & import('../types').AcceptTaxReduce>}
   */
  const AcceptTaxReduce = Sequelize.define(
    'AcceptTaxReduces',
    {
      acceptTaxReduceId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      acceptBy: { type: sequelize.INTEGER.UNSIGNED, allowNull: true },
      isTaxReduce: sequelize.BOOLEAN,
      productName: sequelize.STRING,
      organizationDepartmentId: sequelize.INTEGER.UNSIGNED,
      organizationId: sequelize.INTEGER.UNSIGNED,
    },
    {
      indexes: [
        {
          name: 'productName_org',
          unique: true,
          fields: ['productName', 'organizationId'],
        },
        {
          name: 'productName_dep',
          unique: true,
          fields: ['productName', 'organizationDepartmentId'],
        },
      ],
    },
  );

  return AcceptTaxReduce;
};
