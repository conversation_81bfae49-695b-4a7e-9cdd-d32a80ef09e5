const sequelize = require('sequelize');
const {
  INVOICE_TYPE,
  INVOICE_TYPE_CODE,
} = require('../configs/constants/invoice.constant');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   *  @type {sequelize.ModelStatic<sequelize.Model<import('../types').ConnectionSupplierInvoice> & import('../types').ConnectionSupplierInvoice>}
   */
  const ConnectionSupplierInvoice = Sequelize.define(
    'ConnectionSupplierInvoices',
    {
      connectionSupplierInvoiceId: {
        primaryKey: true,
        type: sequelize.INTEGER.UNSIGNED,
        autoIncrement: true,
      },
      connectionSupplierHistoryId: sequelize.INTEGER.UNSIGNED,
      invoiceType: sequelize.ENUM(...Object.values(INVOICE_TYPE)),
      serial: sequelize.STRING,
      invoiceTypeCode: sequelize.ENUM(...Object.values(INVOICE_TYPE_CODE)),
      invoiceNumber: sequelize.STRING,
      invoiceDate: sequelize.DATE || sequelize.STRING,
      maCQT: sequelize.STRING,
      buyerTaxCode: sequelize.STRING,
      vietInvoiceId: sequelize.STRING,
      connectionSupplierId: sequelize.INTEGER.UNSIGNED,
      buyerName: sequelize.STRING,
      sellerTaxCode: sequelize.STRING,
      supplierReferenceCode: sequelize.STRING,
      issueStatus: sequelize.STRING,
      duplicateInvoiceId: sequelize.INTEGER.UNSIGNED,
    },
    {
      indexes: [
        {
          name: 'historyId_invoice',
          unique: true,
          fields: [
            'connectionSupplierHistoryId',
            'invoiceNumber',
            'sellerTaxCode',
            'serial',
            'invoiceTypeCode',
          ],
        },
        {
          name: 'historyId_vietInvoiceId',
          unique: true,
          fields: ['connectionSupplierHistoryId', 'vietInvoiceId'],
        },
      ],
    },
  );

  return ConnectionSupplierInvoice;
};
