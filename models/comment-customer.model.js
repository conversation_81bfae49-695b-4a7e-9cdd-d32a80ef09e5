const sequelize = require('sequelize');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').CommentCustomer> & import('../types').CommentCustomer}
   */
  const CommentCustomer = Sequelize.define('CommentCustomers', {
    commentCustomerId: {
      type: sequelize.INTEGER.UNSIGNED,
      primaryKey: true,
      autoIncrement: true,
    },
    display: {
      type: sequelize.BOOLEAN,
    },
    commentCustomerContent: { type: sequelize.TEXT('long') },
    customerName: { type: sequelize.STRING },
    customerPosition: { type: sequelize.STRING },
    customerAvatar: { type: sequelize.STRING },
  });

  return CommentCustomer;
};
