const sequelize = require('sequelize');
const {
  NOTIFICATION_TYPE,
} = require('../configs/constants/notification.constant');
const { getFullUrl } = require('../utils');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   *  @type {sequelize.ModelStatic<sequelize.Model<import('../types').Notification> & import('../types').Notification>}
   */
  const Notification = Sequelize.define('Notifications', {
    notificationId: {
      primaryKey: true,
      type: sequelize.INTEGER.UNSIGNED,
      autoIncrement: true,
    },
    type: {
      type: sequelize.ENUM(...Object.values(NOTIFICATION_TYPE)),
      allowNull: false,
    },
    thumbnail: {
      type: sequelize.STRING,
      get() {
        let raw = this.getDataValue('thumbnail');

        if (raw) {
          return getFullUrl(raw);
        }

        return null;
      },
    },
    description: sequelize.TEXT('long'),
    url: sequelize.STRING,
    title: { type: sequelize.STRING, allowNull: false },
    targetCode: sequelize.STRING,
  });

  return Notification;
};
