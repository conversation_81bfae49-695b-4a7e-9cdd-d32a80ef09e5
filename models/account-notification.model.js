const sequelize = require('sequelize');
const { WS_CODE } = require('../configs/constants/websocket.constant');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   *  @type {sequelize.ModelStatic<sequelize.Model<import('../types').AccountNotification> & import('../types').AccountNotification>}
   */
  const AccountNotification = Sequelize.define(
    'AccountNotifications',
    {
      accountNotificationId: {
        primaryKey: true,
        type: sequelize.INTEGER.UNSIGNED,
        autoIncrement: true,
      },
      accountId: {
        type: sequelize.INTEGER.UNSIGNED,
      },
      notificationId: {
        type: sequelize.INTEGER.UNSIGNED,
      },
    },
    {
      indexes: [
        {
          name: 'accountId_notificationId',
          unique: true,
          fields: ['accountId', 'notificationId'],
        },
      ],
    },
  );

  AccountNotification.afterCreate(async instance => {
    const { emitSocketEvent } = require('../utils/websocket.helper');
    const { Notification } = require('.');

    if (instance.accountId) {
      await instance.reload({ include: [{ model: Notification }] });
      emitSocketEvent({
        accountId: instance.accountId,
        newNotification: true,
        message: {
          code: WS_CODE.CODE_NEW_NOTI,
          result: 'success',
          ...instance.toJSON().Notification,
        },
      });
    }
  });

  AccountNotification.afterBulkCreate(async (instances, { transaction: t }) => {
    const { emitSocketEvent } = require('../utils/websocket.helper');
    const { Notification } = require('.');

    for (let instance of instances) {
      if (instance.accountId) {
        await instance.reload({
          include: [{ model: Notification }],
          transaction: t,
        });

        if (instance)
          emitSocketEvent({
            accountId: instance.accountId,
            newNotification: true,
            message: {
              code: WS_CODE.CODE_NEW_NOTI,
              result: 'success',
              ...instance.toJSON().Notification,
            },
          });
      }
    }
  });

  return AccountNotification;
};
