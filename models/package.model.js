const sequelize = require('sequelize');
const {
  PACKAGE_TYPE,
  PACKAGE_UNIT,
} = require('../configs/constants/package.constant');
const { SERVICE } = require('../configs/constants/account.constant');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').Package> & import('../types').Package>}
   */
  const Package = Sequelize.define(
    'Packages',
    {
      packageId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      packageCode: { type: sequelize.STRING },
      packageCost: { type: sequelize.BIGINT },
      unit: { type: sequelize.ENUM(...Object.values(PACKAGE_UNIT)) },
      packageType: {
        type: sequelize.ENUM(...Object.values(PACKAGE_TYPE)),
        allowNull: false,
      },
      quantity: { type: sequelize.BIGINT },
      duration: {
        type: sequelize.INTEGER,
        comment: 'Ngày',
      },
      description: { type: sequelize.TEXT('long') },
      recommend: {
        type: sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      dealerPackage: { type: sequelize.BOOLEAN, defaultValue: false },
      service: {
        type: sequelize.ENUM(...Object.values(SERVICE)),
        // defaultValue: SERVICE.I_SMART,
        allowNull: true,
      },
      dealerMetaAccountCode: sequelize.STRING,
      kmCode: {
        type: sequelize.STRING,
        get() {
          let raw = this.getDataValue('kmCode');

          return raw ? raw : '';
        },
      },
    },
    {
      indexes: [{ name: 'packageCode', unique: true, fields: ['packageCode'] }],
    },
  );

  return Package;
};
