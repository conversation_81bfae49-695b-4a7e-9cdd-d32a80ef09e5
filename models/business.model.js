const sequelize = require('sequelize');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').Business> & import('../types').Business>}
   */
  const Business = Sequelize.define('Businesses', {
    businessCode: { primaryKey: true, type: sequelize.STRING },
    name: sequelize.STRING,
  });

  return Business;
};
