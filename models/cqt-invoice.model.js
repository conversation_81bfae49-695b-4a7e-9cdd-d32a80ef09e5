const sequelize = require('sequelize');
const {
  INVOICE_CATEGORY,
  INVOICE_TYPE_CODE,
  INVOICE_CHECK_STATUS,
} = require('../configs/constants/invoice.constant');
const utils = require('../utils');

/**
 *
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').CqtInvoice> & import('../types').CqtInvoice>}
   */
  const CqtInvoice = Sequelize.define(
    'CqtInvoices',
    {
      cqtInvoiceId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      cqtInvoiceHistoryId: sequelize.INTEGER.UNSIGNED,
      invoiceCQTId: sequelize.STRING,
      invoiceTypeCode: sequelize.ENUM(...Object.values(INVOICE_TYPE_CODE)),
      maCQT: sequelize.STRING,
      invoiceDate: sequelize.DATE,
      invoiceNumber: sequelize.STRING,
      serial: sequelize.STRING,
      description: sequelize.STRING,
      buyerName: sequelize.STRING,
      buyerTaxCode: sequelize.STRING,
      sellerName: sequelize.STRING,
      sellerTaxCode: sequelize.STRING,

      invoiceCheckStatus: sequelize.ENUM(
        ...Object.values(INVOICE_CHECK_STATUS),
      ),
      invoiceCheckStatusText: sequelize.STRING,

      duplicateInvoiceId: sequelize.INTEGER.UNSIGNED,
    },
    {
      indexes: [
        {
          name: 'history_invoice',
          unique: true,
          fields: [
            'sellerTaxCode',
            'invoiceNumber',
            'invoiceTypeCode',
            'serial',
            'cqtInvoiceHistoryId',
          ],
        },
      ],
    },
  );

  CqtInvoice.beforeCreate(instance => {
    if (utils.isValidNumber(instance.invoiceNumber)) {
      instance.setDataValue('invoiceNumber', Number(instance.invoiceNumber));
    }
  });

  CqtInvoice.beforeBulkCreate(instances => {
    for (let instance of instances) {
      if (utils.isValidNumber(instance.invoiceNumber)) {
        instance.setDataValue('invoiceNumber', Number(instance.invoiceNumber));
      }
    }
  });

  return CqtInvoice;
};
