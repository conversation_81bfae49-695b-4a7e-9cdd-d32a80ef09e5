const sequelize = require('sequelize');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   *  @type {sequelize.ModelStatic<sequelize.Model<import('../types').ConnectionSupplierHistory> & import('../types').ConnectionSupplierHistory>}
   */
  const ConnectionSupplierHistory = Sequelize.define(
    'ConnectionSupplierHistories',
    {
      connectionSupplierHistoryId: {
        primaryKey: true,
        type: sequelize.INTEGER.UNSIGNED,
        autoIncrement: true,
      },
      // connectionSupplierId: sequelize.INTEGER.UNSIGNED,
      organizationId: sequelize.INTEGER.UNSIGNED,
      organizationDepartmentId: sequelize.INTEGER.UNSIGNED,
      succeededDownload: { type: sequelize.INTEGER.UNSIGNED, defaultValue: 0 },
      failedDownload: sequelize.INTEGER.UNSIGNED,
      processed: sequelize.INTEGER.UNSIGNED,
      processing: sequelize.INTEGER.UNSIGNED,
      duplicated: sequelize.INTEGER.UNSIGNED,
      malformed: sequelize.INTEGER.UNSIGNED,
      syncDate: sequelize.DATEONLY,
      total: { type: sequelize.INTEGER.UNSIGNED, defaultValue: 0 },
      hasMaCQT: { type: sequelize.INTEGER.UNSIGNED, defaultValue: 0 },
    },
    {
      indexes: [
        {
          name: 'org_date',
          fields: ['organizationId', 'syncDate'],
          unique: true,
        },
        {
          name: 'dep_date',
          fields: ['organizationDepartmentId', 'syncDate'],
          unique: true,
        },
      ],
    },
  );

  return ConnectionSupplierHistory;
};
