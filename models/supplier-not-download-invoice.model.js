const sequelize = require('sequelize');
/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types/company').SupplierNotDownloadInvoice> & import('../types').SupplierNotDownloadInvoice>}
   */
  const SupplierNotDownloadInvoice = Sequelize.define(
    'SupplierNotDownloadInvoices',
    {
      supplierNotDownloadInvoiceId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      organizationId: sequelize.INTEGER.UNSIGNED,
      organizationDepartmentId: sequelize.INTEGER.UNSIGNED,
      partnerCompanyId: sequelize.INTEGER.UNSIGNED,
    },
    {
      indexes: [
        {
          name: 'organizationId_partnerCompanyId',
          unique: true,
          fields: ['organizationId', 'partnerCompanyId'],
        },
        {
          name: 'organizationDepartmentId_partnerCompanyId',
          unique: true,
          fields: ['organizationDepartmentId', 'partnerCompanyId'],
        },
      ],
    },
  );

  return SupplierNotDownloadInvoice;
};
