const sequelize = require('sequelize');
const { BASE_URL } = require('../configs/env');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').TaxDocument> & import('../types').TaxDocument>}
   */
  const TaxReducedDocument = Sequelize.define(
    'TaxReducedDocuments',
    {
      code: {
        primaryKey: true,
        type: sequelize.INTEGER.UNSIGNED,
        autoIncrement: true,
      },
      title: {
        type: sequelize.STRING,
        allowNull: false,
      },
      description: {
        type: sequelize.STRING,
        allowNull: false,
      },
      author: {
        type: sequelize.STRING,
      },
      publishedDate: {
        type: sequelize.DATE,
      },
      pdfUrl: {
        type: sequelize.STRING,
        get() {
          const { getFullUrl } = require('../utils');
          let raw = this.getDataValue('pdfUrl');
          if (raw) {
            return getFullUrl(raw);
          } else {
            return raw;
          }
        },
      },
      note: { type: sequelize.TEXT },
    },
    {
      indexes: [{ name: 'code', unique: true, fields: ['code'] }],
    },
  );

  return TaxReducedDocument;
};
