const sequelize = require('sequelize');
const {
  STATUS_VALID_MAIL,
  READ_STATUS,
} = require('../configs/constants/mail.constant');

/**
 *
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').Mail> & import('../types').Mail>}
   */
  const Mail = Sequelize.define(
    'Mails',
    {
      mailId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      messageId: { type: sequelize.STRING, allowNull: false },

      organizationDepartmentId: sequelize.INTEGER.UNSIGNED,
      organizationId: sequelize.INTEGER.UNSIGNED,
      subject: sequelize.STRING,
      senderEmail: sequelize.STRING,
      senderName: sequelize.STRING,
      receiveDate: { type: sequelize.DATE },
      content: sequelize.TEXT('long'),
      attachments: {
        type: sequelize.JSON,
        get() {
          let raw = this.getDataValue('attachments');

          if (Array.isArray(raw))
            return raw.map(item => ({
              filename: item.filename,
              contentType: item.contentType,
              size: item.size,
              url: item.url,
              invoiceId: item.invoiceId,
            }));
          return [];
        },
      },
      readStatus: {
        type: sequelize.ENUM(...Object.values(READ_STATUS)),
        defaultValue: READ_STATUS.UNREAD,
      },
      isImportant: { type: sequelize.BOOLEAN, defaultValue: false },
      isTrash: { type: sequelize.BOOLEAN, defaultValue: false },
    },
    {
      indexes: [
        {
          name: 'messageId_org',
          fields: ['messageId', 'organizationId'],
          unique: true,
        },
        {
          name: 'messageId_dep',
          fields: ['messageId', 'organizationDepartmentId'],
          unique: true,
        },
      ],
    },
  );

  return Mail;
};
