const sequelize = require('sequelize');
const {
  INVOICE_TYPE,
  INVOICE_TYPE_CODE,
  PAYMENT_METHOD,
  INVOICE_CATEGORY,
  PAYMENT_STATUS,
  TAX_STATUS_TEXT,
  INVOICE_STATUS,
  INVOICE_HANDLE_STATUS,
  TYPE_INVOICE_RECEIVE,
  INVOICE_CHECK_STATUS,
  INVOICE_STATUS_TEXT,
} = require('../configs/constants/invoice.constant');
const { existsSync } = require('fs');
const { UPLOAD_DIR } = require('../configs/env');
const utils = require('../utils');
const {
  PARTNER_COMPANY_STATUS,
} = require('../configs/constants/company.constant');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').Invoice> & import('../types').Invoice>}
   */
  const Invoice = Sequelize.define(
    'Invoices',
    {
      invoiceId: {
        primaryKey: true,
        autoIncrement: true,
        type: sequelize.INTEGER.UNSIGNED,
      },
      invoiceVersion: sequelize.STRING,
      cqtInvoiceHistoryId: sequelize.INTEGER.UNSIGNED,
      invoiceDuplicateId: sequelize.INTEGER.UNSIGNED,
      invoiceCQTId: { type: sequelize.STRING }, //id
      vietInvoiceId: sequelize.STRING, // vietinvoice id
      invoiceRelationId: sequelize.INTEGER,
      originInvoiceId: sequelize.INTEGER.UNSIGNED,
      targetInvoiceId: sequelize.INTEGER.UNSIGNED,
      invoiceType: { type: sequelize.ENUM(...Object.values(INVOICE_TYPE)) }, //hdon
      serial: { type: sequelize.STRING }, //khhdon
      invoiceTypeCode: {
        type: sequelize.ENUM(...Object.values(INVOICE_TYPE_CODE)), //khmshdon
      },
      invoiceNumber: {
        type: sequelize.STRING,
        get() {
          let raw = this.getDataValue('invoiceNumber');

          if (raw) {
            if (utils.isValidNumber(raw)) {
              return Number(raw);
            }
          }
          return raw;
        },
      }, //shdon
      invoiceDate: { type: sequelize.DATEONLY }, //tdlap

      providedCodeDate: { type: sequelize.DATE }, //ncma
      signDate: { type: sequelize.DATE }, //nky
      invoiceReceiveDate: { type: sequelize.DATE }, //ntnhan
      finalAmountExchange: { type: sequelize.BIGINT },
      statusInvoice: {
        type: sequelize.ENUM(...Object.values(INVOICE_STATUS)),
        defaultValue: INVOICE_STATUS[1],
      }, //tthai
      statusInvoiceText: {
        type: sequelize.STRING,
        defaultValue: INVOICE_STATUS_TEXT[1],
      },
      statusHandleInvoice: {
        type: sequelize.ENUM(...Object.values(INVOICE_HANDLE_STATUS)),
      }, //ttxly
      statusHandleInvoiceText: sequelize.STRING,
      sellerCode: { type: sequelize.STRING }, //
      sellerName: { type: sequelize.STRING }, //nbten
      sellerTaxCode: { type: sequelize.STRING }, //nbmst
      sellerBankAccount: { type: sequelize.STRING }, //nbstkhoan
      sellerBankName: { type: sequelize.STRING }, //nbtnhang
      sellerAddress: { type: sequelize.STRING }, // nbdchi
      sellerPhone: { type: sequelize.STRING }, //nbsdthoai
      sellerMail: { type: sequelize.STRING }, // nbdctdtu
      buyerName: { type: sequelize.STRING }, //nmten
      buyerTaxCode: { type: sequelize.STRING }, //nmmst
      buyerAddress: { type: sequelize.STRING }, //nmdchi
      buyerPersonName: { type: sequelize.STRING }, //nmtnmua
      buyerBankAccount: { type: sequelize.STRING }, //nbstkhoan
      buyerBankName: { type: sequelize.STRING }, //nbtnhang
      buyerCode: sequelize.STRING,
      buyerPhone: sequelize.STRING, //nmsdthoai
      buyerMail: sequelize.STRING, //nmdctdtu
      amountAfterVat: { type: sequelize.BIGINT }, //tgtttbso
      amountBeforeVat: { type: sequelize.BIGINT }, //tgtcthue
      finalAmount: { type: sequelize.BIGINT }, //tgtttbso
      discount: { type: sequelize.BIGINT },
      discountWithoutVAT: { type: sequelize.BIGINT },
      discountOther: { type: sequelize.BIGINT },
      amountBeforeDiscount: { type: sequelize.BIGINT },

      vat10: { type: sequelize.BIGINT },
      amountVat10: { type: sequelize.BIGINT },
      amountAfterVat10: sequelize.BIGINT,
      amountBeforeVat10: sequelize.BIGINT,

      vat8: { type: sequelize.BIGINT },
      amountVat8: { type: sequelize.BIGINT },
      amountAfterVat8: sequelize.BIGINT,
      amountBeforeVat8: sequelize.BIGINT,

      vat5: { type: sequelize.BIGINT },
      amountVat5: { type: sequelize.BIGINT },
      amountAfterVat5: { type: sequelize.BIGINT },
      amountBeforeVat5: sequelize.BIGINT,

      vat0: { type: sequelize.BIGINT },
      amountVat0: { type: sequelize.BIGINT },
      amountAfterVat0: { type: sequelize.BIGINT },
      amountBeforeVat0: sequelize.BIGINT,

      vatKHAC: { type: sequelize.BIGINT },
      amountVatKHAC: { type: sequelize.BIGINT },
      amountAfterVatKHAC: { type: sequelize.BIGINT },
      amountBeforeVatKHAC: sequelize.BIGINT,

      vatKKKNT: { type: sequelize.BIGINT },
      amountVatKKKNT: { type: sequelize.BIGINT },
      amountAfterVatKKKNT: { type: sequelize.BIGINT },
      amountBeforeVatKKKNT: sequelize.BIGINT,

      vatKCT: { type: sequelize.BIGINT },
      amountVatKCT: { type: sequelize.BIGINT },
      amountAfterVatKCT: { type: sequelize.BIGINT },
      amountBeforeVatKCT: sequelize.BIGINT,

      amountVat: { type: sequelize.BIGINT }, //tgtthue
      totalDiscountAmount: { type: sequelize.BIGINT },
      paymentCurrency: { type: sequelize.STRING },
      paymentExchangeRate: { type: sequelize.BIGINT },
      paymentMethod: { type: sequelize.STRING },
      invoiceCategory: {
        type: sequelize.ENUM(...Object.values(INVOICE_CATEGORY)),
        allowNull: true,
        defaultValue: INVOICE_CATEGORY.INPUT_INVOICE,
      },
      noteInvoice: { type: sequelize.STRING }, //gchu
      organizationDepartmentId: {
        type: sequelize.INTEGER.UNSIGNED,
        allowNull: true,
        defaultValue: null,
      },
      organizationId: {
        type: sequelize.INTEGER.UNSIGNED,
        allowNull: true,
        defaultValue: null,
      },
      pdfFile: sequelize.STRING,
      previewPdfFile: sequelize.STRING,
      xmlFile: sequelize.STRING,
      originXmlFile: sequelize.STRING,
      maCQT: sequelize.STRING,
      issueDate: sequelize.DATE,
      finalAmountInWord: sequelize.STRING,
      issueStatus: sequelize.STRING,
      supplierKey: sequelize.STRING,
      supplierReferenceCode: sequelize.STRING,
      supplierTaxCode: sequelize.STRING,
      supplierLookupWebsite: sequelize.STRING,
      connectionSupplier: sequelize.JSON,
      noDocument: sequelize.STRING,
      accountingDate: sequelize.DATEONLY,
      accountingPersonName: sequelize.STRING,
      statusPayment: sequelize.ENUM(...Object.values(PAYMENT_STATUS)),
      reminderPaymentDate: sequelize.DATEONLY,
      expiredPaymentDate: sequelize.DATEONLY,
      debtPayment: sequelize.BIGINT,
      amountPayment: sequelize.BIGINT,
      isDeleted: sequelize.BOOLEAN,
      statusTax: {
        type: sequelize.ENUM(...Object.values(TAX_STATUS_TEXT)),
        defaultValue: TAX_STATUS_TEXT['NOT_CHECKED'],
      },
      typeInvoiceReceive: sequelize.ENUM(
        ...Object.values(TYPE_INVOICE_RECEIVE),
      ),
      dateReturnPayment: sequelize.DATE,
      dateReturnAccounting: sequelize.DATE,
      description: sequelize.STRING,

      cqtCKS: {
        type: sequelize.JSON,
        get() {
          let raw = this.getDataValue('cqtCKS');

          if (utils.isJSONStringified(raw)) return JSON.parse(raw);
          return raw;
        },
      },
      sellerCKS: {
        type: sequelize.JSON,
        get() {
          let raw = this.getDataValue('sellerCKS');

          if (utils.isJSONStringified(raw)) return JSON.parse(raw);
          return raw;
        },
      },
      cqtSignDate: sequelize.DATE,
      sellerSignDate: sequelize.DATE,
      dateCQTChangeStatus: sequelize.DATE,
      dateDetectChangeStatus: sequelize.DATE,
      isSendMailSupplier: { type: sequelize.BOOLEAN, defaultValue: false },

      parsedFromXml: sequelize.BOOLEAN,
      parsedDetailFromCqt: sequelize.BOOLEAN,
      parsedDetailFromNCC: sequelize.BOOLEAN,
      xmlCqtNotExist: sequelize.BOOLEAN,
      detailCqtNotExist: sequelize.BOOLEAN,

      xmlNCCNotExist: sequelize.BOOLEAN,
      pdfNCCNotExist: sequelize.BOOLEAN,

      hdgocData: {
        type: sequelize.JSON,
        get() {
          let raw = this.getDataValue('hdgocData');
          if (!raw) return null;

          let { khhdon, khmshdon, shdon, tdlap } = raw;

          return { khhdon, khmshdon, shdon, tdlap };
        },
      },
      reduceTaxResolution: {
        type: sequelize.JSON,
        get() {
          let raw = this.getDataValue('reduceTaxResolution');

          if (!raw) return null;

          return { name: raw.name, key: raw.key };
        },
      },
      imageFile: sequelize.STRING,
      isManualInput: sequelize.BOOLEAN,
      cqtMtt: sequelize.BOOLEAN,
    },
    {
      indexes: [
        {
          name: 'org_invoice_vietinvoice',
          unique: true,
          fields: ['organizationId', 'vietInvoiceId'],
        },
        {
          name: 'org_cqt_invoice',
          unique: true,
          fields: ['organizationId', 'invoiceCQTId'],
        },
        {
          name: 'dep_invoice_vietinvoice',
          unique: true,
          fields: ['organizationDepartmentId', 'vietInvoiceId'],
        },
        {
          name: 'dep_cqt_invoice',
          unique: true,
          fields: ['organizationDepartmentId', 'invoiceCQTId'],
        },
        {
          name: 'invoice_org',
          unique: true,
          fields: [
            'sellerTaxCode',
            'invoiceNumber',
            'invoiceTypeCode',
            'serial',
            'organizationId',
          ],
        },
        {
          name: 'invoice_dep',
          unique: true,
          fields: [
            'sellerTaxCode',
            'invoiceNumber',
            'invoiceTypeCode',
            'serial',
            'organizationDepartmentId',
          ],
        },
        {
          name: 'org_date',
          fields: ['organizationId', 'invoiceDate'],
        },
        {
          name: 'dep_date',
          fields: ['organizationDepartmentId', 'invoiceDate'],
        },

        {
          name: 'org_accountingDate',
          fields: ['organizationId', 'accountingDate'],
        },
        {
          name: 'dep_accountingDate',
          fields: ['organizationDepartmentId', 'accountingDate'],
        },

        {
          name: 'org_invoiceReceiveDate',
          fields: ['organizationId', 'invoiceReceiveDate'],
        },
        {
          name: 'dep_invoiceReceiveDate',
          fields: ['organizationDepartmentId', 'invoiceReceiveDate'],
        },

        {
          name: 'org_reminderPaymentDate',
          fields: ['organizationId', 'reminderPaymentDate'],
        },
        {
          name: 'dep_reminderPaymentDate',
          fields: ['organizationDepartmentId', 'reminderPaymentDate'],
        },

        {
          name: 'org_expiredPaymentDate',
          fields: ['organizationId', 'expiredPaymentDate'],
        },
        {
          name: 'dep_expiredPaymentDate',
          fields: ['organizationDepartmentId', 'expiredPaymentDate'],
        },
      ],
    },
  );

  Invoice.afterFind(instancesOrInstance => {
    if (!Array.isArray(instancesOrInstance) && instancesOrInstance) {
      instancesOrInstance = [instancesOrInstance];
    }

    if (Array.isArray(instancesOrInstance)) {
      instancesOrInstance.forEach(item => {
        if (item.xmlFile) {
          if (!existsSync(item.xmlFile.replace('uploads', UPLOAD_DIR))) {
            item.setDataValue('xmlFile', null);
          }
        }

        if (item.pdfFile) {
          if (!existsSync(item.pdfFile.replace('uploads', UPLOAD_DIR))) {
            item.setDataValue('pdfFile', null);
          }
        }

        if (item.originXmlFile) {
          if (!existsSync(item.originXmlFile.replace('uploads', UPLOAD_DIR))) {
            item.setDataValue('originXmlFile', null);
          }
        }

        if (item.imageFile) {
          if (!existsSync(item.imageFile.replace('uploads', UPLOAD_DIR))) {
            item.setDataValue('imageFile', null);
          }
        }

        if (utils.isValidNumber(item.invoiceNumber)) {
          item.setDataValue('invoiceNumber', Number(item.invoiceNumber));
        }
      });
    }
  });

  Invoice.afterUpdate(async instance => {
    if (instance.xmlFile != instance.previous().xmlFile) {
      instance.setDataValue('previewPdfFile', null);
      instance.setDataValue('parsedFromXml', false);
    }
  });

  Invoice.beforeCreate(instance => {
    if (utils.isValidNumber(instance.invoiceNumber)) {
      instance.setDataValue('invoiceNumber', Number(instance.invoiceNumber));
    }

    if (!utils.isValidNumber(instance.totalDiscountAmount)) {
      instance.setDataValue('totalDiscountAmount', undefined);
    }
  });

  Invoice.beforeUpdate(instance => {
    if (utils.isValidNumber(instance.invoiceNumber)) {
      instance.setDataValue('invoiceNumber', Number(instance.invoiceNumber));
    }

    if (!utils.isValidNumber(instance.totalDiscountAmount)) {
      instance.setDataValue('totalDiscountAmount', undefined);
    }
  });

  Invoice.beforeBulkCreate(instances => {
    if (Array.isArray(instances)) {
      for (let instance of instances) {
        if (utils.isValidNumber(instance.invoiceNumber)) {
          instance.setDataValue(
            'invoiceNumber',
            Number(instance.invoiceNumber),
          );
        }

        if (!utils.isValidNumber(instance.totalDiscountAmount)) {
          instance.setDataValue('totalDiscountAmount', undefined);
        }
      }
    }
  });

  Invoice.beforeBulkUpdate(instances => {
    if (Array.isArray(instances)) {
      for (let instance of instances) {
        if (utils.isValidNumber(instance.invoiceNumber)) {
          instance.setDataValue(
            'invoiceNumber',
            Number(instance.invoiceNumber),
          );
        }

        if (!utils.isValidNumber(instance.totalDiscountAmount)) {
          instance.setDataValue('totalDiscountAmount', undefined);
        }
      }
    }
  });

  Invoice.beforeUpdate(instance => {
    if (utils.isValidNumber(instance.invoiceNumber)) {
      instance.setDataValue('invoiceNumber', Number(instance.invoiceNumber));
    }

    if (instance.invoiceCQTId) {
      if (instance.previous().statusInvoice !== instance.statusInvoice) {
        instance.setDataValue('dateDetectChangeStatus', new Date());
      }
    }
  });

  return Invoice;
};
