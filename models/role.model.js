const sequelize = require('sequelize');

/**
 *
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').Role> & import('../types').Role>}
   */
  const Role = Sequelize.define(
    'Roles',
    {
      roleId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: sequelize.STRING,
        allowNull: false,
      },
      companyId: { type: sequelize.INTEGER.UNSIGNED, allowNull: false },
      permissionCodes: {
        type: sequelize.JSON,
        get() {
          let raw = this.getDataValue('permissionCodes');

          if (!Array.isArray(raw)) return [];
          return raw;
        },
      },
    },
    {
      indexes: [
        { name: 'companyId_name', unique: true, fields: ['name', 'companyId'] },
      ],
    },
  );

  return Role;
};
