const sequelize = require('sequelize');
const { ORGANIZATION_TYPE } = require('../configs/constants/company.constant');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').OrganizationAddress> & import('../types').OrganizationAddress>}
   */
  const OrganizationAddress = Sequelize.define('OrganizationAddresses', {
    organizationAddressId: {
      type: sequelize.INTEGER.UNSIGNED,
      primaryKey: true,
      autoIncrement: true,
    },
    organizationId: {
      type: sequelize.INTEGER.UNSIGNED,
      allowNull: false,
    },

    organizationName: { type: sequelize.STRING, allowNull: false },

    businessPermitDateStart: { type: sequelize.DATEONLY, allowNull: false },
    businessPermitDateEnd: { type: sequelize.DATEONLY, allowNull: true },
    businessPermitAddress: { type: sequelize.STRING, allowNull: false },

    acceptBy: { type: sequelize.STRING, allowNull: false },
  });

  return OrganizationAddress;
};
