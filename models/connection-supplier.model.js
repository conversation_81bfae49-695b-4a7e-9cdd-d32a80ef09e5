const sequelize = require('sequelize');
/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').ConnectionSupplier> & import('../types').ConnectionSupplier>}
   */
  const ConnectionSupplier = Sequelize.define(
    'ConnectionSuppliers',
    {
      connectionSupplierId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      organizationDepartmentId: {
        type: sequelize.INTEGER.UNSIGNED,
      },
      organizationId: { type: sequelize.INTEGER.UNSIGNED },
      supplierId: { type: sequelize.INTEGER.UNSIGNED, allowNull: false },
      usernameConnection: sequelize.STRING,
      url: sequelize.STRING,
      passwordConnection: sequelize.STRING,
      xCSRFToken: sequelize.STRING,
    },
    {
      indexes: [
        {
          name: 'organizationDepartmentId_supplierId_usernameConnection_url',
          unique: true,
          fields: [
            'organizationDepartmentId',
            'supplierId',
            'usernameConnection',
            'url',
          ],
        },
        {
          name: 'organizationId_supplierId_usernameConnection_url',
          unique: true,
          fields: ['organizationId', 'supplierId', 'usernameConnection', 'url'],
        },
      ],
    },
  );

  return ConnectionSupplier;
};
