const sequelize = require('sequelize');
const {
  PRODUCT_TYPE,
  VAT_TYPE,
} = require('../configs/constants/invoice.constant');
const { isValidNumber } = require('../utils');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').InvoiceProduct> & import('../types').InvoiceProduct>}
   */
  const InvoiceProduct = Sequelize.define('InvoiceProducts', {
    invoiceProductId: {
      primaryKey: true,
      type: sequelize.INTEGER.UNSIGNED,
      autoIncrement: true,
    },
    invoiceId: { type: sequelize.INTEGER.UNSIGNED, allowNull: false },
    name: sequelize.TEXT('medium'),
    code: sequelize.STRING,
    sortOrder: sequelize.INTEGER.UNSIGNED,
    price: {
      type: sequelize.BIGINT,
      get() {
        let raw = this.getDataValue('price');

        return isValidNumber(raw) ? Number(raw) : null;
      },
    },
    amountTotal: {
      type: sequelize.BIGINT,
      get() {
        let raw = this.getDataValue('amountTotal');

        return isValidNumber(raw) ? Number(raw) : null;
      },
    },
    quantity: sequelize.INTEGER.UNSIGNED,
    productType: sequelize.ENUM(...Object.values(PRODUCT_TYPE)),
    unit: sequelize.STRING,
    vat: sequelize.ENUM(...Object.values(VAT_TYPE)),
    vatRate: {
      type: sequelize.BIGINT,
      get() {
        let raw = this.getDataValue('vatRate');

        return isValidNumber(raw) ? Number(raw) : null;
      },
    },
    vatAmount: {
      type: sequelize.BIGINT,
      get() {
        let raw = this.getDataValue('vatAmount');

        return isValidNumber(raw) ? Number(raw) : null;
      },
    },
    discount: sequelize.BIGINT,
    discountAmount: {
      type: sequelize.BIGINT,
      get() {
        let raw = this.getDataValue('discountAmount');

        return isValidNumber(raw) ? Number(raw) : null;
      },
    },
    finalAmount: {
      type: sequelize.BIGINT,
      get() {
        let raw = this.getDataValue('finalAmount');

        return isValidNumber(raw) ? Number(raw) : null;
      },
    },
  });

  InvoiceProduct.beforeBulkCreate(async instances => {
    for (let product of instances) {
      if (product.vat && product.vat.startsWith(VAT_TYPE.KHAC)) {
        product.setDataValue('vat', VAT_TYPE.KHAC);
      }
    }
  });

  return InvoiceProduct;
};
