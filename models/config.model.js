const sequelize = require('sequelize');
const fs = require('fs');
const { getFullUrl } = require('../utils');
const { PUBLIC_UPLOAD_DIR } = require('../configs/env');
/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').Config> & import('../types').Config>}
   */
  const Config = Sequelize.define(
    'Configs',
    {
      configId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      key: { type: sequelize.STRING },
      name: { type: sequelize.STRING },
      value: {
        type: sequelize.TEXT('medium'),
        get() {
          let raw = this.getDataValue('value');

          if (
            typeof raw == 'string' &&
            fs.existsSync(raw.replace('resources', PUBLIC_UPLOAD_DIR)) &&
            fs.lstatSync(raw.replace('resources', PUBLIC_UPLOAD_DIR)).isFile()
          ) {
            return getFullUrl(raw);
          }

          return raw;
        },
      },
    },
    {
      indexes: [
        {
          name: 'key',
          unique: true,
          fields: ['key'],
        },
      ],
    },
  );

  return Config;
};
