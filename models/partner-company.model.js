const { type } = require('jquery');
const sequelize = require('sequelize');
const {
  PARTNER_COMPANY_STATUS,
  PARTNER_COMPANY_TYPE,
} = require('../configs/constants/company.constant');

/**
 *
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').PartnerCompany> & import('../types').PartnerCompany>}
   */
  const PartnerCompany = Sequelize.define(
    'PartnerCompanies',
    {
      partnerCompanyId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      taxCode: {
        type: sequelize.STRING,
        allowNull: false,
      },
      organizationId: {
        type: sequelize.INTEGER.UNSIGNED,
        allowNull: true,
      },
      organizationDepartmentId: {
        type: sequelize.INTEGER.UNSIGNED,
        allowNull: true,
      },
      unitCode: {
        type: sequelize.STRING,
      },
      partnerCompanyName: {
        type: sequelize.STRING,
        allowNull: false,
      },
      bankAccountNumber: { type: sequelize.STRING },
      bankAccountName: { type: sequelize.STRING },
      bank: { type: sequelize.STRING },
      bankBranch: { type: sequelize.STRING },
      expirePayment: { type: sequelize.INTEGER.UNSIGNED, defaultValue: 30 },
      contactName: { type: sequelize.STRING },
      contactEmail: { type: sequelize.STRING },
      contactPhone: { type: sequelize.STRING },
      contactPosition: { type: sequelize.STRING },
      dateCheck: { type: sequelize.DATE },
      status: {
        type: sequelize.ENUM(...Object.values(PARTNER_COMPANY_STATUS)),
        allowNull: false,
        defaultValue: PARTNER_COMPANY_STATUS.UNKNOWN,
      },
      address: { type: sequelize.STRING },
      buyInvoices: { type: sequelize.INTEGER.UNSIGNED, defaultValue: 0 },
      sellInvoices: { type: sequelize.INTEGER.UNSIGNED, defaultValue: 0 },
      nntStatus: { type: sequelize.STRING },
    },
    {
      indexes: [
        {
          name: 'organizationId_taxCode',
          unique: true,
          fields: ['organizationId', 'taxCode'],
        },
        {
          name: 'organizationDeparmentId_taxCode',
          unique: true,
          fields: ['organizationDepartmentId', 'taxCode'],
        },
        {
          name: 'organizationId_unitCode',
          unique: true,
          fields: ['organizationId', 'unitCode'],
        },
        {
          name: 'organizationDepartmentId_unitCode',
          unique: true,
          fields: ['organizationDepartmentId', 'unitCode'],
        },
      ],
    },
  );

  return PartnerCompany;
};
