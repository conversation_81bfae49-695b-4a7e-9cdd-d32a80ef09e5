const sequelize = require('sequelize');
const { ORGANIZATION_TYPE } = require('../configs/constants/company.constant');
const { parseBoolean } = require('../utils');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').Organization> & import('../types').Organization>}
   */
  const Organization = Sequelize.define(
    'Organizations',
    {
      organizationId: {
        primaryKey: true,
        autoIncrement: true,
        type: sequelize.INTEGER.UNSIGNED,
      },
      companyId: {
        type: sequelize.INTEGER.UNSIGNED,
        allowNull: false,
      },
      parentOrganizationId: {
        type: sequelize.INTEGER.UNSIGNED,
      },
      organizationName: { type: sequelize.STRING, allowNull: true },
      organizationType: {
        type: sequelize.ENUM(...Object.values(ORGANIZATION_TYPE)),
        allowNull: false,
      },
      businessPermitDate: { type: sequelize.DATEONLY, allowNull: true },
      businessPermitAddress: { type: sequelize.STRING, allowNull: true },
      invoiceMailbox: { type: sequelize.STRING, allowNull: false },
      taxCode: { type: sequelize.STRING, allowNull: false },
      linkWebsite: { type: sequelize.STRING },
      username: { type: sequelize.STRING },
      usernameOld: { type: sequelize.STRING },
      password: { type: sequelize.STRING },
      isInput: { type: sequelize.BOOLEAN },
      isOutput: { type: sequelize.BOOLEAN },
      cqtToken: sequelize.STRING,
      cqtTokenExpiration: sequelize.DATE,

      // notification
      remindNotify: { type: sequelize.BOOLEAN, defaultValue: true },
      remindBeforeDays: { type: sequelize.INTEGER.UNSIGNED, defaultValue: 5 },

      // name file
      nameFileBuyerTaxCode: { type: sequelize.BOOLEAN, defaultValue: false },
      nameFileSerial: { type: sequelize.BOOLEAN, defaultValue: false },
      nameFileInvoiceDate: { type: sequelize.BOOLEAN, defaultValue: false },
      nameFileSample: { type: sequelize.BOOLEAN, defaultValue: false },
      nameFileInvoiceNum: { type: sequelize.BOOLEAN, defaultValue: true },
      nameFileSupplierName: { type: sequelize.BOOLEAN, defaultValue: false },

      // Tải toàn bộ hóa đơn đầu ra (bao gồm hóa đơn phát hành bởi ICORP Invoice)
      downloadAllOutputInvoice: {
        type: sequelize.BOOLEAN,
        defaultValue: false,
      },
      // Tải về hóa đơn không mã gửi theo hình thức bảng tổng hợp
      downloadOutputInvoiceNotTable: {
        type: sequelize.BOOLEAN,
        defaultValue: false,
      },

      selectDownloadTypeOutputInvoice: {
        type: sequelize.JSON,
        get() {
          let raw = this.getDataValue('selectDownloadTypeOutputInvoice');

          let parseValue = value =>
            value != undefined ? parseBoolean(value) : true;

          return {
            hoaDonDienTuGiaTriGiaTang: {
              hoaDonDienTu: !!parseValue(
                raw?.hoaDonDienTuGiaTriGiaTang?.hoaDonDienTu,
              ),
              khoiTaoTuMayTinhTien: !!parseValue(
                raw?.hoaDonDienTuGiaTriGiaTang?.khoiTaoTuMayTinhTien,
              ),
            },
            hoaDonDienTuBanHang: {
              hoaDonDienTu: !!parseValue(
                raw?.hoaDonDienTuBanHang?.hoaDonDienTu,
              ),
              khoiTaoTuMayTinhTien: !!parseValue(
                raw?.hoaDonDienTuBanHang?.khoiTaoTuMayTinhTien,
              ),
            },
            hoaDonDienTuBanTaiSanCong: !!parseValue(
              raw?.hoaDonDienTuBanTaiSanCong,
            ),
            hoaDonDienTuBanHangDuTruQuocGia: !!parseValue(
              raw?.hoaDonDienTuBanHangDuTruQuocGia,
            ),
            temVeTheDienTu: {
              hoaDonDienTu: !!parseValue(raw?.temVeTheDienTu?.hoaDonDienTu),
              khoiTaoTuMayTinhTien: !!parseValue(
                raw?.temVeTheDienTu?.khoiTaoTuMayTinhTien,
              ),
            },
            phieuXuatKhoDienTu: {
              khiemVanChuyenNoiBo: !!parseValue(
                raw?.phieuXuatKhoDienTu?.khiemVanChuyenNoiBo,
              ),
              hangGuiBanDaiLy: !!parseValue(
                raw?.phieuXuatKhoDienTu?.hangGuiBanDaiLy,
              ),
            },
          };
        },
      },

      // hoa don dau vao

      adjustNotDownloadWithSupplier: {
        type: sequelize.BOOLEAN,
        defaultValue: false,
      }, //Thiết lập không tải về hóa đơn theo nhà cung cấp
      autoAdjustInvoice: { type: sequelize.BOOLEAN, defaultValue: false },
      downloadInputInvoiceNotTable: {
        type: sequelize.BOOLEAN,
        defaultValue: false,
      },
      selectDownloadTypeInputInvoice: {
        type: sequelize.JSON,
        get() {
          let raw = this.getDataValue('selectDownloadTypeInputInvoice');

          let parseValue = value =>
            value != undefined ? parseBoolean(value) : true;

          return {
            hoaDonDienTuGiaTriGiaTang: {
              hoaDonDienTu: !!parseValue(
                raw?.hoaDonDienTuGiaTriGiaTang?.hoaDonDienTu,
              ),
              khoiTaoTuMayTinhTien: !!parseValue(
                raw?.hoaDonDienTuGiaTriGiaTang?.khoiTaoTuMayTinhTien,
              ),
            },
            hoaDonDienTuBanHang: {
              hoaDonDienTu: !!parseValue(
                raw?.hoaDonDienTuBanHang?.hoaDonDienTu,
              ),
              khoiTaoTuMayTinhTien: !!parseValue(
                raw?.hoaDonDienTuBanHang?.khoiTaoTuMayTinhTien,
              ),
            },
            hoaDonDienTuBanTaiSanCong: !!parseValue(
              raw?.hoaDonDienTuBanTaiSanCong,
            ),
            hoaDonDienTuBanHangDuTruQuocGia: !!parseValue(
              raw?.hoaDonDienTuBanHangDuTruQuocGia,
            ),
            temVeTheDienTu: {
              hoaDonDienTu: !!parseValue(raw?.temVeTheDienTu?.hoaDonDienTu),
              khoiTaoTuMayTinhTien: !!parseValue(
                raw?.temVeTheDienTu?.khoiTaoTuMayTinhTien,
              ),
            },
            phieuXuatKhoDienTu: {
              khiemVanChuyenNoiBo: !!parseValue(
                raw?.phieuXuatKhoDienTu?.khiemVanChuyenNoiBo,
              ),
              hangGuiBanDaiLy: !!parseValue(
                raw?.phieuXuatKhoDienTu?.hangGuiBanDaiLy,
              ),
            },
          };
        },
      },

      autoAdjustWithBuyerTaxCode: {
        type: sequelize.BOOLEAN,
        defaultValue: false,
      },
      // autoAdjustWithEmail: {
      //   type: sequelize.BOOLEAN,
      //   defaultValue: false,
      // },
      warningInListInvoice: { type: sequelize.BOOLEAN, defaultValue: false },

      representativeName: sequelize.STRING,
      representativeEmail: sequelize.STRING,
      representativeJobTitle: sequelize.STRING,
      representativePhone: sequelize.STRING,

      dangDongBo: {
        type: sequelize.BOOLEAN,
      },
      dangExport: { type: sequelize.BOOLEAN },
    },
    {
      indexes: [
        {
          name: 'companyId_invoiceMailbox',
          unique: true,
          fields: ['companyId', 'invoiceMailbox'],
        },
      ],
    },
  );

  Organization.afterCreate(async instance => {
    const mailboxHelper = require('../utils/mailbox.helper');

    let invoiceMailbox = instance.invoiceMailbox;

    await mailboxHelper
      .createMailbox(invoiceMailbox, instance.organizationName)
      .catch(console.warn);

    await mailboxHelper
      .changeMailboxPassword(invoiceMailbox)
      .catch(console.warn);
  });

  return Organization;
};
