const sequelize = require('sequelize');
const { AUTH_LOG_STATUS } = require('../configs/constants/auth-log.constanst');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').AuthLog> & import('../types').AuthLog>}
   */
  const AuthLog = Sequelize.define('AuthLogs', {
    authLogId: {
      type: sequelize.INTEGER.UNSIGNED,
      primaryKey: true,
      autoIncrement: true,
    },
    accountId: {
      type: sequelize.INTEGER.UNSIGNED,
      allowNull: false,
    },
    browser: sequelize.STRING,
    device: sequelize.STRING,
    ipAddress: sequelize.STRING,
    status: {
      type: sequelize.ENUM(...Object.values(AUTH_LOG_STATUS)),
      defaultValue: AUTH_LOG_STATUS.SUCCESS,
      allowNull: false,
    },
  });

  return AuthLog;
};
