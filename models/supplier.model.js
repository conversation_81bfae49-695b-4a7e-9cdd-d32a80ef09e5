const sequelize = require('sequelize');
const {
  SUPPLIER_TYPE,
  SUPPLIER_VISIBLE,
} = require('../configs/constants/supplier.constant');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').Supplier> & import('../types').Supplier>}
   */
  const Supplier = Sequelize.define('Suppliers', {
    supplierId: {
      type: sequelize.INTEGER.UNSIGNED,
      primaryKey: true,
      autoIncrement: true,
    },
    logo: sequelize.STRING,
    description: sequelize.STRING,
    urls: {
      type: sequelize.JSON,
      get() {
        let raw = this.getDataValue('urls');

        if (!Array.isArray(raw)) return [];
        return raw;
      },
    },
    supplierType: sequelize.ENUM(...Object.values(SUPPLIER_TYPE)),
    supplierKey: sequelize.STRING,
    companyName: sequelize.STRING,
    visible: {
      type: sequelize.ENUM(...Object.values(SUPPLIER_VISIBLE)),
      defaultValue: SUPPLIER_VISIBLE.SUPPLIER_SHOW,
    },
  });

  return Supplier;
};
