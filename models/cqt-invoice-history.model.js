const sequelize = require('sequelize');
const { INVOICE_CATEGORY } = require('../configs/constants/invoice.constant');

/**
 *
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').CqtInvoiceHistory> & import('../types').CqtInvoiceHistory>}
   */
  const CqtInvoiceHistory = Sequelize.define(
    'CqtInvoiceHistories',
    {
      cqtInvoiceHistoryId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      syncDate: { type: sequelize.DATEONLY },
      type: {
        type: sequelize.ENUM(...Object.values(INVOICE_CATEGORY)),
        allowNull: false,
      },
      organizationId: sequelize.INTEGER.UNSIGNED,
      organizationDepartmentId: sequelize.INTEGER.UNSIGNED,
      succeededDownload: { type: sequelize.INTEGER.UNSIGNED, defaultValue: 0 },
      failedDownload: { type: sequelize.INTEGER.UNSIGNED, defaultValue: 0 },
      processed: { type: sequelize.INTEGER.UNSIGNED, defaultValue: 0 },
      processing: { type: sequelize.INTEGER.UNSIGNED, defaultValue: 0 },
      duplicated: { type: sequelize.INTEGER.UNSIGNED, defaultValue: 0 },
      skipped: { type: sequelize.INTEGER.UNSIGNED, defaultValue: 0 },
    },
    {
      indexes: [
        {
          name: 'org_date',
          fields: ['organizationId', 'syncDate', 'type'],
          unique: true,
        },
        {
          name: 'dep_date',
          fields: ['organizationDepartmentId', 'syncDate', 'type'],
          unique: true,
        },
      ],
    },
  );

  return CqtInvoiceHistory;
};
