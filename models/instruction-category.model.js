const sequelize = require('sequelize');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').InstructionCategory>>}
   */
  const InstructionCategory = Sequelize.define('InstructionCategories', {
    instructionCategoryId: {
      type: sequelize.INTEGER.UNSIGNED,
      primaryKey: true,
      autoIncrement: true,
    },
    description: sequelize.STRING,
    title: { type: sequelize.STRING, allowNull: false },
  });

  return InstructionCategory;
};
