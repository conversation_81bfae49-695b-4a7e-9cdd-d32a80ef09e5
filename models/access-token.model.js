const sequelize = require('sequelize');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').AccessToken> & import('../types').AccessToken>}
   */
  const AccessToken = Sequelize.define('AccessTokens', {
    accessTokenId: {
      primaryKey: true,
      type: sequelize.INTEGER.UNSIGNED,
      autoIncrement: true,
    },
    accountId: {
      type: sequelize.INTEGER.UNSIGNED,
      allowNull: false,
    },
    accessToken: { type: sequelize.STRING, allowNull: false },
    expireAt: { type: sequelize.DATE },
  });

  return AccessToken;
};
