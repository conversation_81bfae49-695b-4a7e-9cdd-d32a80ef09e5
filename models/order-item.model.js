const sequelize = require('sequelize');
const utils = require('../utils');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').OrderItem> & import('../types').OrderItem>}
   */
  const OrderItem = Sequelize.define('OrderItems', {
    orderItemId: {
      type: sequelize.INTEGER.UNSIGNED,
      primaryKey: true,
      autoIncrement: true,
    },
    orderId: { type: sequelize.INTEGER.UNSIGNED, allowNull: false },
    packageCode: { type: sequelize.STRING },
    quantity: { type: sequelize.INTEGER },
    price: { type: sequelize.INTEGER },
    cost: { type: sequelize.INTEGER },
    package: {
      type: sequelize.JSON,
      allowNull: false,
      get() {
        let raw = this.getDataValue('package');
        if (utils.isJSONStringified(raw)) return JSON.parse(raw);
        return raw;
      },
    },
  });

  return OrderItem;
};
