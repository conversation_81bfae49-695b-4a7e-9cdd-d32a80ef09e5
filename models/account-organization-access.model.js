const sequelize = require('sequelize');
/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').AccountOrganizationAccess> & import('../types').AccountOrganizationAccess>}
   */
  const AccountOrganizationAccess = Sequelize.define(
    'AccountOrganizationAccesses',
    {
      accountOrganizationAccessId: {
        primaryKey: true,
        type: sequelize.INTEGER.UNSIGNED,
        autoIncrement: true,
      },
      accountId: { type: sequelize.INTEGER.UNSIGNED, allowNull: false },
      organizationDepartmentId: { type: sequelize.INTEGER.UNSIGNED },
      organizationId: { type: sequelize.INTEGER.UNSIGNED },
    },
    {
      indexes: [
        {
          name: 'accountId_organizationDepartmentId',
          unique: true,
          fields: ['accountId', 'organizationDepartmentId'],
        },
        {
          name: 'accountId_organizationId',
          unique: true,
          fields: ['accountId', 'organizationId'],
        },
      ],
    },
  );

  return AccountOrganizationAccess;
};
