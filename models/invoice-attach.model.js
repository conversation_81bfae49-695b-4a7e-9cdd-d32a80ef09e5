const sequelize = require('sequelize');

/**
 *
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').InvoiceAttach> & import('../types').InvoiceAttach>}
   */
  const InvoiceAttach = Sequelize.define('InvoiceAttachs', {
    invoiceAttachId: {
      type: sequelize.INTEGER.UNSIGNED,
      primaryKey: true,
      autoIncrement: true,
    },
    invoiceId: { type: sequelize.INTEGER.UNSIGNED, allowNull: false },
    url: { type: sequelize.STRING },
    fileName: { type: sequelize.STRING },
    size: { type: sequelize.INTEGER },
  });

  return InvoiceAttach;
};
