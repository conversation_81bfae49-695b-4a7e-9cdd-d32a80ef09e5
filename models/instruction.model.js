const sequelize = require('sequelize');
const {
  INSTRUCTION_TYPE,
} = require('../configs/constants/instruction.constant');
const { BASE_URL } = require('../configs/env');

/**
 *
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').Instruction> & import('../types').Instruction>}
   */
  const Instruction = Sequelize.define('Instructions', {
    instructionId: {
      type: sequelize.INTEGER.UNSIGNED,
      primaryKey: true,
      autoIncrement: true,
    },
    instructionCategoryId: {
      type: sequelize.INTEGER.UNSIGNED,
    },
    description: sequelize.STRING,
    title: sequelize.STRING,
    thumbnail: {
      type: sequelize.STRING,
      get() {
        const { getFullUrl } = require('../utils');
        let raw = this.getDataValue('thumbnail');

        if (raw) {
          return getFullUrl(raw);
        }
        return null;
      },
    },
    type: sequelize.ENUM(...Object.values(INSTRUCTION_TYPE)),
    url: {
      type: sequelize.STRING,
      get() {
        const { getFullUrl } = require('../utils');
        let raw = this.getDataValue('url');

        if (raw) {
          return getFullUrl(raw);
        }
        return null;
      },
    },
  });

  return Instruction;
};
