const dbConfig = require('../configs/db.config.js');
const sequelize = require('sequelize');

const Sequelize = new sequelize.Sequelize(
  dbConfig.DB,
  dbConfig.USER,
  dbConfig.PASSWORD,
  {
    host: dbConfig.HOST,
    port: dbConfig.PORT,
    dialect: dbConfig.DB_DIALECT,
    // operatorsAliases: false,
    pool: {
      max: dbConfig.pool.max,
      min: dbConfig.pool.min,
      acquire: dbConfig.pool.acquire,
      idle: dbConfig.pool.idle,
    },
    timezone: '+07:00',
    logging: /* NODE_ENV == 'dev' ? console.log : */ false,
  },
);

const db = {};
db.Sequelize = Sequelize;

/* MODELS DECLARATIONS */
// Accounts
db.Account = require('./account.model.js')(Sequelize);
db.Invoice = require('./invoice.model.js')(Sequelize);
db.Company = require('./company.model.js')(Sequelize);
db.CompanyTitle = require('./company-title.model.js')(Sequelize);
db.AccessToken = require('./access-token.model')(Sequelize);
db.EmailHistory = require('./email-history.model.js')(Sequelize);
db.Organization = require('./organization.model.js')(Sequelize);
db.OrganizationDepartment = require('./organization-department.model.js')(
  Sequelize,
);
db.AccountOrganizationAccess =
  require('./account-organization-access.model.js')(Sequelize);
db.InstructionCategory = require('./instruction-category.model.js')(Sequelize);
db.Instruction = require('./instruction.model.js')(Sequelize);
db.FAQ = require('./faq.model.js')(Sequelize);
db.Role = require('./role.model.js')(Sequelize);
db.PartnerCompany = require('./partner-company.model.js')(Sequelize);
db.Tag = require('./tag.model.js')(Sequelize);
db.InvoiceTag = require('./invoice-tag.model.js')(Sequelize);
db.Notification = require('./notification.model.js')(Sequelize);
db.AccountNotification = require('./account-notification.model.js')(Sequelize);
db.AuditLog = require('./audit-log.model.js')(Sequelize);
db.Supplier = require('./supplier.model.js')(Sequelize);
db.ConnectionSupplier = require('./connection-supplier.model.js')(Sequelize);
db.CqtInvoiceHistory = require('./cqt-invoice-history.model.js')(Sequelize);
db.CqtInvoice = require('./cqt-invoice.model.js')(Sequelize);
db.ConnectionSupplierHistory =
  require('./connection-supplier-history.model.js')(Sequelize);
db.ConnectionSupplierInvoice =
  require('./connection-supplier-invoice.model.js')(Sequelize);
db.AuthLog = require('./auth-log.model.js')(Sequelize);
db.CommentCustomer = require('./comment-customer.model.js')(Sequelize);
/**
 * @deprecated
 */
// db.ContactCustomer = require('./contact-customer.model.js')(Sequelize);
db.TaxReducedDocument = require('./tax-reduced-document.model.js')(Sequelize);
db.RiskyCompany = require('./risky-company.model.js')(Sequelize);
db.Package = require('./package.model.js')(Sequelize);
db.OrganizationAddress = require('./organization-address.model.js')(Sequelize);
db.Order = require('./order.model.js')(Sequelize);
db.OrderItem = require('./order-item.model.js')(Sequelize);
db.Config = require('./config.model.js')(Sequelize);
db.InvoiceHistory = require('./invoice-history.model.js')(Sequelize);
db.InvoiceValidate = require('./invoice-validate.model.js')(Sequelize);
db.ResourceHistory = require('./resource-history.model.js')(Sequelize);
db.InvoiceProduct = require('./invoice-product.model')(Sequelize);
db.SupplierNotDownloadInvoice =
  require('./supplier-not-download-invoice.model.js')(Sequelize);
db.Product = require('./product.model.js')(Sequelize);
db.InvoiceAttach = require('./invoice-attach.model')(Sequelize);
db.InvoiceSupplierOrg = require('./invoice-supplier-org.model.js')(Sequelize);
db.Mail = require('./mail.model.js')(Sequelize);
db.AutoTransferInvoiceFromEmail =
  require('./auto-transfer-invoice-from-email.model.js')(Sequelize);
db.AutoTransferInvoice = require('./auto-transfer-invoice.model.js')(Sequelize);
db.StatusMail = require('./status-mail.model.js')(Sequelize);
db.AcceptPartnerCompanyDetail =
  require('./accept-partner-company-detail.model.js')(Sequelize);
db.TaxReduceValidate = require('./tax-reduce-validate.model.js')(Sequelize);
db.AcceptTaxReduce = require('./accept-tax-reduce.model.js')(Sequelize);
db.Business = require('./business.model.js')(Sequelize);
db.BusinessCompany = require('./business-company.model.js')(Sequelize);
/* ASSOCIATIONS */

db.AccessToken.belongsTo(db.Account, {
  foreignKey: 'accountId',
  onDelete: 'CASCADE',
});
db.Account.belongsTo(db.Company, {
  foreignKey: 'companyId',
  onDelete: 'CASCADE',
});

/* organization */
db.Organization.belongsTo(db.Company, {
  foreignKey: 'companyId',
  onDelete: 'CASCADE',
});

db.OrganizationDepartment.belongsTo(db.Organization, {
  as: 'OrganizationBranch',
  foreignKey: 'organizationId',
  onDelete: 'CASCADE',
});
db.Organization.hasMany(db.OrganizationDepartment, {
  foreignKey: 'organizationId',
  onDelete: 'CASCADE',
});
db.Organization.hasMany(db.Organization, {
  foreignKey: 'parentOrganizationId',
  as: 'OrganizationBranches',
  onDelete: 'CASCADE',
});
db.Organization.belongsTo(db.Organization, {
  foreignKey: 'parentOrganizationId',
  as: 'OrganizationHQ',
  onDelete: 'CASCADE',
});

db.AccountOrganizationAccess.belongsTo(db.Account, {
  foreignKey: 'accountId',
  onDelete: 'CASCADE',
});
db.Account.hasMany(db.AccountOrganizationAccess, {
  foreignKey: 'accountId',
  onDelete: 'CASCADE',
});
db.AccountOrganizationAccess.belongsTo(db.Organization, {
  foreignKey: 'organizationId',
  onDelete: 'CASCADE',
});
db.AccountOrganizationAccess.belongsTo(db.OrganizationDepartment, {
  foreignKey: 'organizationDepartmentId',
  onDelete: 'CASCADE',
});
db.Account.belongsTo(db.Organization, {
  foreignKey: 'organizationId',
  onDelete: 'SET NULL',
});
db.Account.belongsTo(db.OrganizationDepartment, {
  foreignKey: 'organizationDepartmentId',
  onDelete: 'SET NULL',
});

/*  */
db.Account.belongsTo(db.CompanyTitle, {
  foreignKey: 'companyTitleId',
  onDelete: 'SET NULL',
});
db.CompanyTitle.hasMany(db.Account, {
  foreignKey: 'companyTitleId',
  onDelete: 'SET NULL',
});

db.Company.hasMany(db.CompanyTitle, {
  foreignKey: 'companyId',
  onDelete: 'CASCADE',
});

db.SupplierNotDownloadInvoice.belongsTo(db.OrganizationDepartment, {
  foreignKey: 'organizationDepartmentId',
  onDelete: 'CASCADE',
});
db.SupplierNotDownloadInvoice.belongsTo(db.Organization, {
  foreignKey: 'organizationId',
  onDelete: 'CASCADE',
});
db.SupplierNotDownloadInvoice.belongsTo(db.PartnerCompany, {
  foreignKey: 'partnerCompanyId',
  onDelete: 'CASCADE',
});

/* instruction */
db.Instruction.belongsTo(db.InstructionCategory, {
  foreignKey: 'instructionCategoryId',
  onDelete: 'CASCADE',
});

db.InstructionCategory.hasMany(db.Instruction, {
  foreignKey: 'instructionCategoryId',
  onDelete: 'CASCADE',
});

/* role */
db.Account.belongsTo(db.Role, {
  foreignKey: 'roleId',
  onDelete: 'SET NULL',
});
db.Role.hasMany(db.Account, { foreignKey: 'roleId', onDelete: 'SET NULL' });
db.Role.belongsTo(db.Company, { foreignKey: 'companyId', onDelete: 'CASCADE' });

/* Company */

db.PartnerCompany.belongsTo(db.Organization, {
  foreignKey: 'organizationId',
  onDelete: 'CASCADE',
});

db.PartnerCompany.belongsTo(db.OrganizationDepartment, {
  foreignKey: 'organizationDepartmentId',
  onDelete: 'CASCADE',
});

db.Tag.belongsTo(db.Account, {
  foreignKey: 'accountId',
  onDelete: 'CASCADE',
});
/* connection order - orderItem */
db.OrderItem.belongsTo(db.Order, {
  foreignKey: 'orderId',
  onDelete: 'CASCADE',
});

db.Order.hasMany(db.OrderItem, {
  foreignKey: 'orderId',
  onDelete: 'CASCADE',
});
/* connection order - company */
db.Order.belongsTo(db.Company, {
  foreignKey: 'companyId',
  onDelete: 'CASCADE',
});
db.Order.belongsTo(db.Account, {
  foreignKey: 'accountId',
  onDelete: 'CASCADE',
});

db.Company.hasMany(db.Order, {
  foreignKey: 'companyId',
  onDelete: 'CASCADE',
});

/* invoice tag  */
db.Invoice.belongsToMany(db.Tag, {
  through: db.InvoiceTag,
  foreignKey: 'invoiceId',
  onDelete: 'CASCADE',
});
db.Tag.belongsToMany(db.Invoice, {
  through: db.InvoiceTag,
  foreignKey: 'tagId',
  onDelete: 'CASCADE',
});

/* notification */
db.AccountNotification.belongsTo(db.Account, {
  foreignKey: 'accountId',
  onDelete: 'CASCADE',
});

db.AccountNotification.belongsTo(db.Notification, {
  foreignKey: 'notificationId',
  onDelete: 'CASCADE',
});

db.Notification.belongsToMany(db.Account, {
  through: db.AccountNotification,
  foreignKey: 'notificationId',
  onDelete: 'CASCADE',
});

db.Account.belongsToMany(db.Notification, {
  through: db.AccountNotification,
  foreignKey: 'accountId',
  onDelete: 'CASCADE',
});

/* audit log */
db.AuditLog.belongsTo(db.Account, {
  foreignKey: 'accountId',
  onDelete: 'SET NULL',
});

db.Account.hasMany(db.AuditLog, {
  foreignKey: 'accountId',
  onDelete: 'SET NULL',
});

/* connection supplier */
db.ConnectionSupplier.belongsTo(db.Supplier, {
  foreignKey: 'supplierId',
  onDelete: 'CASCADE',
});

db.ConnectionSupplier.belongsTo(db.Organization, {
  foreignKey: 'organizationId',
  onDelete: 'CASCADE',
});

db.ConnectionSupplier.belongsTo(db.OrganizationDepartment, {
  foreignKey: 'organizationDepartmentId',
  onDelete: 'CASCADE',
});

db.CqtInvoiceHistory.belongsTo(db.Organization, {
  foreignKey: 'organizationId',
  onDelete: 'CASCADE',
});

db.CqtInvoiceHistory.belongsTo(db.OrganizationDepartment, {
  foreignKey: 'organizationDepartmentId',
  onDelete: 'CASCADE',
});

db.CqtInvoiceHistory.hasMany(db.CqtInvoice, {
  foreignKey: 'cqtInvoiceHistoryId',
  onDelete: 'CASCADE',
});

db.CqtInvoice.belongsTo(db.Invoice, {
  foreignKey: 'duplicateInvoiceId',
  onDelete: 'SET NULL',
});

db.Invoice.belongsTo(db.CqtInvoiceHistory, {
  foreignKey: 'cqtInvoiceHistoryId',
  onDelete: 'SET NULL',
});
db.Invoice.belongsTo(db.Invoice, {
  foreignKey: 'invoiceDuplicateId',
  as: 'InvoiceDuplicate',
  onDelete: 'SET NULL',
});
db.Invoice.belongsTo(db.Invoice, {
  foreignKey: 'originInvoiceId',
  as: 'InvoiceOrigin',
  onDelete: 'SET NULL',
});
db.Invoice.belongsTo(db.Invoice, {
  foreignKey: 'targetInvoiceId',
  as: 'InvoiceTarget',
  onDelete: 'SET NULL',
});

db.ConnectionSupplierHistory.belongsTo(db.Organization, {
  foreignKey: 'organizationId',
  onDelete: 'CASCADE',
});

db.ConnectionSupplierHistory.belongsTo(db.OrganizationDepartment, {
  foreignKey: 'organizationDepartmentId',
  onDelete: 'CASCADE',
});

db.ConnectionSupplierInvoice.belongsTo(db.ConnectionSupplierHistory, {
  foreignKey: 'connectionSupplierHistoryId',
  onDelete: 'CASCADE',
});

db.ConnectionSupplierInvoice.belongsTo(db.ConnectionSupplier, {
  foreignKey: 'connectionSupplierId',
  onDelete: 'CASCADE',
});

db.ConnectionSupplierInvoice.belongsTo(db.Invoice, {
  foreignKey: 'duplicateInvoiceId',
  onDelete: 'SET NULL',
});

/* auth-log */
db.AuthLog.belongsTo(db.Account, {
  foreignKey: 'accountId',
  onDelete: 'CASCADE',
});

/* OrganizationAddress */
db.OrganizationAddress.belongsTo(db.Organization, {
  foreignKey: 'organizationId',
  onDelete: 'CASCADE',
});
db.Organization.hasMany(db.OrganizationAddress, {
  foreignKey: 'organizationId',
  onDelete: 'CASCADE',
});

/* Invoice organization */
db.Invoice.belongsTo(db.Organization, {
  foreignKey: 'organizationId',
  onDelete: 'CASCADE',
});
db.Invoice.belongsTo(db.OrganizationDepartment, {
  foreignKey: 'organizationDepartmentId',
  onDelete: 'CASCADE',
});
db.Organization.belongsTo(db.Organization, {
  foreignKey: 'parentOrganizationId',
  onDelete: 'CASCADE',
});

/* Invoice - InvoiceHistory */
db.InvoiceHistory.belongsTo(db.Invoice, {
  foreignKey: 'invoiceId',
  onDelete: 'CASCADE',
});
db.Invoice.hasMany(db.InvoiceHistory, {
  foreignKey: 'invoiceId',
  onDelete: 'CASCADE',
});

/* Invoice Validate */
db.InvoiceValidate.belongsTo(db.Invoice, {
  foreignKey: 'invoiceId',
  onDelete: 'CASCADE',
});
db.Invoice.hasOne(db.InvoiceValidate, {
  foreignKey: 'invoiceId',
  onDelete: 'CASCADE',
});

// resource history
db.ResourceHistory.belongsTo(db.Company, {
  foreignKey: 'companyId',
  onDelete: 'CASCADE',
});

/* Invoice-tag */
db.Invoice.hasMany(db.InvoiceTag, {
  foreignKey: 'invoiceId',
  onDelete: 'CASCADE',
});
db.InvoiceTag.belongsTo(db.Invoice, {
  foreignKey: 'invoiceId',
  onDelete: 'CASCADE',
});
db.Tag.hasMany(db.InvoiceTag, {
  foreignKey: 'tagId',
  onDelete: 'CASCADE',
});
db.InvoiceTag.belongsTo(db.Tag, {
  foreignKey: 'tagId',
  onDelete: 'CASCADE',
});

/* Invoice product */
db.InvoiceProduct.belongsTo(db.Invoice, {
  foreignKey: 'invoiceId',
  onDelete: 'CASCADE',
});
db.Invoice.hasMany(db.InvoiceProduct, {
  foreignKey: 'invoiceId',
  onDelete: 'CASCADE',
});

/* Invoice attach */
db.Invoice.hasMany(db.InvoiceAttach, {
  foreignKey: 'invoiceId',
  onDelete: 'CASCADE',
});
/* Invoice attach */
db.InvoiceAttach.belongsTo(db.Invoice, {
  foreignKey: 'invoiceId',
  onDelete: 'CASCADE',
});

/* Mail*/
db.Mail.belongsTo(db.Organization, {
  foreignKey: 'organizationId',
  onDelete: 'CASCADE',
});
db.Mail.belongsTo(db.OrganizationDepartment, {
  foreignKey: 'organizationDepartmentId',
  onDelete: 'CASCADE',
});
db.Mail.hasMany(db.StatusMail, { foreignKey: 'mailId', onDelete: 'CASCADE' });

db.AutoTransferInvoiceFromEmail.belongsTo(db.OrganizationDepartment, {
  foreignKey: 'organizationDepartmentId',
  onDelete: 'CASCADE',
});
db.AutoTransferInvoiceFromEmail.belongsTo(db.Organization, {
  foreignKey: 'organizationId',
  onDelete: 'CASCADE',
});
db.AutoTransferInvoiceFromEmail.belongsTo(db.OrganizationDepartment, {
  foreignKey: 'organizationDepartmentBId',
  onDelete: 'CASCADE',
});
db.AutoTransferInvoiceFromEmail.belongsTo(db.Organization, {
  foreignKey: 'organizationBId',
  onDelete: 'CASCADE',
});

db.AutoTransferInvoice.belongsTo(db.OrganizationDepartment, {
  foreignKey: 'organizationDepartmentId',
  onDelete: 'CASCADE',
});
db.AutoTransferInvoice.belongsTo(db.Organization, {
  foreignKey: 'organizationId',
  onDelete: 'CASCADE',
});

db.AutoTransferInvoice.belongsTo(db.OrganizationDepartment, {
  foreignKey: 'autoTransferOrganizationDepartmentId',
  onDelete: 'CASCADE',
});
db.AutoTransferInvoice.belongsTo(db.Organization, {
  foreignKey: 'autoTransferOrganizationId',
  onDelete: 'CASCADE',
});

/* AcceptPartnerCompanyDetail */
db.AcceptPartnerCompanyDetail.belongsTo(db.Organization, {
  foreignKey: 'organizationId',
  onDelete: 'CASCADE',
});
db.AcceptPartnerCompanyDetail.belongsTo(db.OrganizationDepartment, {
  foreignKey: 'organizationDepartmentId',
  onDelete: 'CASCADE',
});
db.AcceptPartnerCompanyDetail.belongsTo(db.Account, {
  foreignKey: 'acceptBy',
  onDelete: 'CASCADE',
});

/*  */
db.TaxReduceValidate.belongsTo(db.Invoice, {
  foreignKey: 'invoiceId',
  onDelete: 'CASCADE',
});
db.Invoice.hasMany(db.TaxReduceValidate, {
  foreignKey: 'invoiceId',
  onDelete: 'CASCADE',
});

db.AcceptTaxReduce.belongsTo(db.Organization, {
  foreignKey: 'organizationId',
  onDelete: 'CASCADE',
});
db.AcceptTaxReduce.belongsTo(db.OrganizationDepartment, {
  foreignKey: 'organizationDepartmentId',
  onDelete: 'CASCADE',
});
db.AcceptTaxReduce.belongsTo(db.Account, {
  foreignKey: 'acceptBy',
  onDelete: 'SET NULL',
});

/* Business */
db.BusinessCompany.belongsTo(db.Business, {
  foreignKey: 'businessCode',
  onDelete: 'CASCADE',
});

/* email history */
db.EmailHistory.belongsTo(db.Account, {
  foreignKey: 'accountId',
  onDelete: 'CASCADE',
});

for (let key in db) {
  if (db[key] && typeof db[key].customSync == 'function') {
    db[key].customSync();
  }
}

module.exports = db;
