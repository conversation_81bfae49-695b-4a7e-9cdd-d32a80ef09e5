const sequelize = require('sequelize');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {import('sequelize').ModelStatic<sequelize.Model<import('../types').AcceptPartnerCompanyDetail> & import('../types').AcceptPartnerCompanyDetail>}
   */
  const AcceptPartnerCompanyDetail = Sequelize.define(
    'AcceptPartnerCompanyDetails',
    {
      acceptPartnerCompanyDetailId: {
        primaryKey: true,
        type: sequelize.INTEGER.UNSIGNED,
        autoIncrement: true,
      },
      acceptAddress: sequelize.STRING,
      acceptName: sequelize.STRING,
      from: sequelize.DATE,
      to: sequelize.DATE,
      acceptBy: { type: sequelize.INTEGER.UNSIGNED, allowNull: false },
      organizationDepartmentId: sequelize.INTEGER.UNSIGNED,
      organizationId: sequelize.INTEGER.UNSIGNED,
      taxCode: { type: sequelize.STRING, allowNull: false },
    },
  );

  return AcceptPartnerCompanyDetail;
};
