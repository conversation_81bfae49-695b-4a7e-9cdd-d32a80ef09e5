const sequelize = require('sequelize');

/**
 *
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').CompanyTitle> & import('../types').CompanyTitle>}
   */
  const CompanyTitle = Sequelize.define(
    'CompanyTitles',
    {
      companyTitleId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      companyId: {
        type: sequelize.INTEGER.UNSIGNED,
        allowNull: false,
      },
      name: {
        type: sequelize.STRING,
        allowNull: false,
      },
      description: { type: sequelize.STRING },
    },
    {
      indexes: [
        { name: 'companyId_name', unique: true, fields: ['companyId', 'name'] },
      ],
    },
  );

  return CompanyTitle;
};
