const sequelize = require('sequelize');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   *  @type {sequelize.ModelStatic<sequelize.Model<import('../types').Tag> & import('../types').Tag>}
   */
  const Tag = Sequelize.define('Tags', {
    tagId: {
      primaryKey: true,
      type: sequelize.INTEGER.UNSIGNED,
      autoIncrement: true,
    },
    accountId: { type: sequelize.INTEGER.UNSIGNED, allowNull: false },
    name: {
      type: sequelize.STRING,
      allowNull: false,
    },
    color: {
      type: sequelize.STRING,
    },
    partnerCompanyTaxCodes: {
      type: sequelize.JSON,
      get() {
        let raw = this.getDataValue('partnerCompanyTaxCodes');

        if (!Array.isArray(raw)) return [];
        return raw;
      },
    },
  });

  return Tag;
};
