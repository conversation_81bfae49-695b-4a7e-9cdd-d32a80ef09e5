const sequelize = require('sequelize');
const { PACKAGE_TYPE } = require('../configs/constants/package.constant');
const {
  SUPPLY_TYPE,
  RESOURCE_HISTORY_STATUS,
} = require('../configs/constants/resource-history.constant');
const utils = require('../utils');

/**
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').ResourceHistory> & import('../types').ResourceHistory>}
   */
  const ResourceHistory = Sequelize.define('ResourceHistories', {
    resourceHistoryId: {
      type: sequelize.INTEGER.UNSIGNED,
      primaryKey: true,
      autoIncrement: true,
    },
    supplyDate: sequelize.DATE,
    expiredDate: sequelize.DATE,
    total: sequelize.BIGINT.UNSIGNED,
    description: sequelize.STRING,
    left: {
      type: sequelize.BIGINT.UNSIGNED,
      defaultValue: 0,
      allowNull: false,
    },
    package: {
      type: sequelize.JSON,
      get() {
        let raw = this.getDataValue('package');
        if (utils.isJSONStringified(raw)) return JSON.parse(raw);
        return raw;
      },
    },
    packageType: sequelize.ENUM(...Object.values(PACKAGE_TYPE)),
    metaContractCode: sequelize.STRING,
    active: {
      type: sequelize.BOOLEAN,
      defaultValue: false,
    },
    supplyBy: {
      type: sequelize.JSON,
      get() {
        let raw = this.getDataValue('supplyBy');
        if (utils.isJSONStringified(raw)) return JSON.parse(raw);
        return raw;
      },
    },
    supplyType: {
      type: sequelize.ENUM(...Object.values(SUPPLY_TYPE)),
      defaultValue: SUPPLY_TYPE.ORDER,
    },
    using: { type: sequelize.BOOLEAN, defaultValue: false },
    order: {
      type: sequelize.JSON,
      get() {
        let raw = this.getDataValue('order');
        if (utils.isJSONStringified(raw)) return JSON.parse(raw);
        return raw;
      },
    },

    companyId: { type: sequelize.INTEGER.UNSIGNED, allowNull: false },

    listPrice: sequelize.BIGINT,
    purchasedPrice: sequelize.BIGINT,

    status: {
      type: sequelize.ENUM(...Object.values(RESOURCE_HISTORY_STATUS)),
      defaultValue: RESOURCE_HISTORY_STATUS.DANG_HOAT_DONG,
    },
  });

  ResourceHistory.afterUpdate(async (instance, { transaction }) => {
    if (instance.left === 0 && instance.previous().left > 0) {
      await instance.update({ using: false }, { transaction, hooks: false });
    } else {
      if (
        instance.previous().left == instance.total &&
        instance.left < instance.previous().left
      ) {
        await instance.update({ using: true }, { transaction, hooks: false });
      }
    }
  });

  // ResourceHistory.customSync = async function () {
  //   await this.sync({ alter: true });
  //   console.log(this.name, 'customSync done!!');
  // };

  return ResourceHistory;
};
