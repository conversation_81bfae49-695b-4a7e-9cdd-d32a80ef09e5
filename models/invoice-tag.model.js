const sequelize = require('sequelize');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').InvoiceTag> & import('../types').InvoiceTag>}
   */
  const InvoiceTag = Sequelize.define(
    'InvoiceTags',
    {
      invoiceTagId: {
        primaryKey: true,
        autoIncrement: true,
        type: sequelize.INTEGER.UNSIGNED,
      },
      invoiceId: {
        type: sequelize.INTEGER.UNSIGNED,
        allowNull: false,
      },
      tagId: {
        type: sequelize.INTEGER.UNSIGNED,
        allowNull: false,
      },
    },
    {
      indexes: [
        {
          name: 'invoiceId_tagId',
          unique: true,
          fields: ['invoiceId', 'tagId'],
        },
      ],
    },
  );

  return InvoiceTag;
};
