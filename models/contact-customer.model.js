const sequelize = require('sequelize');
const {
  CONTACT_STATUS,
} = require('../configs/constants/contact-status.constant');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').ContactCustomer> & import('../types').ContactCustomer>}
   */
  const ContactCustomer = Sequelize.define('ContactCustomers', {
    contactId: {
      type: sequelize.INTEGER.UNSIGNED,
      primaryKey: true,
      autoIncrement: true,
    },
    nameCustomer: { type: sequelize.STRING },
    emailCustomer: { type: sequelize.STRING },
    phoneCustomer: { type: sequelize.STRING },
    status: {
      type: sequelize.ENUM(...Object.values(CONTACT_STATUS)),
      allowNull: false,
    },
  });

  return ContactCustomer;
};
