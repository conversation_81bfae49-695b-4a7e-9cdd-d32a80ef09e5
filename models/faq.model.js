const sequelize = require('sequelize');

/**
 *
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').FAQ> & import('../types').FAQ>}
   */
  const FAQ = Sequelize.define('FAQs', {
    faqId: {
      type: sequelize.INTEGER.UNSIGNED,
      primaryKey: true,
      autoIncrement: true,
    },
    answer: {
      type: sequelize.TEXT('long'),
    },
    question: {
      type: sequelize.STRING,
    },
  });

  return FAQ;
};
