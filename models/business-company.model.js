const sequelize = require('sequelize');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').BusinessCompany> & import('../types').BusinessCompany>}
   */
  const BusinessCompany = Sequelize.define(
    'BusinessCompanies',
    {
      businessCompanyId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      businessCode: { type: sequelize.STRING, allowNull: false },
      taxCode: { type: sequelize.STRING, allowNull: false },
    },
    {
      indexes: [
        {
          name: 'businessCode_taxCode',
          fields: ['businessCode', 'taxCode'],
          unique: true,
        },
      ],
    },
  );

  return BusinessCompany;
};
