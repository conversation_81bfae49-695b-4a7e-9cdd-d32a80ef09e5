const sequelize = require('sequelize');
const { STATUS_VALID_MAIL } = require('../configs/constants/mail.constant');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').StatusMail> & import('../types').StatusMail>}
   */
  const StatusMail = Sequelize.define('StatusMails', {
    statusMailId: {
      primaryKey: true,
      type: sequelize.INTEGER.UNSIGNED,
      autoIncrement: true,
    },
    mailId: { type: sequelize.INTEGER.UNSIGNED, allowNull: false },
    count: sequelize.INTEGER.UNSIGNED,
    statusValidMail: {
      type: sequelize.ENUM(...Object.values(STATUS_VALID_MAIL)),
      defaultValue: STATUS_VALID_MAIL.VALID_INVOICE,
    },
  });

  return StatusMail;
};
