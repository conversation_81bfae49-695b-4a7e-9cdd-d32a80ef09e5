const sequelize = require('sequelize');

/**
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').Product> & import('../types').Product>}
   */
  const Product = Sequelize.define(
    'Products',
    {
      productId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      productCode: { type: sequelize.STRING },
      productName: sequelize.TEXT('long'),
      content: sequelize.TEXT('long'),
      note: sequelize.TEXT('long'),
      reduceTax: { type: sequelize.BOOLEAN, defaultValue: true },
      hsCode: sequelize.TEXT('long'),
    },
    {
      indexes: [{ name: 'productCode', fields: ['productCode'], unique: true }],
    },
  );

  return Product;
};
