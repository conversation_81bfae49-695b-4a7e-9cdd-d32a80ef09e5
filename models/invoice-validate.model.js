const sequelize = require('sequelize');

/**
 *
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').InvoiceValidate> & import('../types').InvoiceValidate>}
   */
  const InvoiceValidate = Sequelize.define(
    'InvoiceValidates',
    {
      invoiceValidateId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      invoiceId: { type: sequelize.INTEGER.UNSIGNED, allowNull: false },
      checkResultBuyerName: { type: sequelize.BOOLEAN, defaultValue: null },
      resultBuyerName: { type: sequelize.STRING },
      fromDateAcceptBuyerName: { type: sequelize.DATE },
      toDateAcceptBuyerName: { type: sequelize.DATE },
      checkResultBuyerTaxCode: { type: sequelize.BOOLEAN, defaultValue: null },
      resultBuyerTaxCode: { type: sequelize.STRING },
      fromDateAcceptBuyerTaxCode: { type: sequelize.DATE },
      toDateAcceptBuyerTaxCode: { type: sequelize.DATE },
      checkResultBuyerAddress: { type: sequelize.BOOLEAN, defaultValue: null },
      resultBuyerAddress: { type: sequelize.STRING },
      fromDateAcceptBuyerAddress: { type: sequelize.DATE },
      toDateAcceptBuyerAddress: { type: sequelize.DATE },
      checkResultSellerName: { type: sequelize.BOOLEAN, defaultValue: null },
      resultSellerName: { type: sequelize.STRING },
      fromDateAcceptSellerName: { type: sequelize.DATE },
      toDateAcceptSellerName: { type: sequelize.DATE },
      checkResultSellerAddress: {
        type: sequelize.BOOLEAN,
        defaultValue: null,
      },
      resultSellerAddress: { type: sequelize.STRING },
      resultSellerTaxCode: { type: sequelize.STRING },
      checkResultSellerTaxCode: { type: sequelize.BOOLEAN },
      fromDateAcceptSellerAddress: { type: sequelize.DATE },
      toDateAcceptSellerAddress: { type: sequelize.DATE },
      checkResultSignatureNCC: { type: sequelize.BOOLEAN, defaultValue: null },
      checkResultSignatureCQT: { type: sequelize.BOOLEAN, defaultValue: null },
      checkResultHasInvoiceCode: {
        type: sequelize.BOOLEAN,
        defaultValue: null,
      },
      checkResultHasCQTRecord: {
        type: sequelize.BOOLEAN,
        defaultValue: null,
      },
      checkDate: { type: sequelize.DATE },
      cqt_cts_con_hieu_luc: sequelize.BOOLEAN,
      cqt_cts_hop_le: sequelize.BOOLEAN,
      cqt_file_xml_chua_bi_sua: sequelize.BOOLEAN,
      cqt_thong_tin_cts_hop_le: sequelize.BOOLEAN,
      nb_cts_con_hieu_luc: sequelize.BOOLEAN,
      nb_cts_hop_le: sequelize.BOOLEAN,
      nb_file_xml_chua_bi_sua: sequelize.BOOLEAN,
      nb_thong_tin_cts_hop_le: sequelize.BOOLEAN,
      mauso_kyhieu_da_tb: sequelize.BOOLEAN,
      nb_dang_hoat_dong: sequelize.BOOLEAN,
      nb_khong_rui_ro_tai_tdlap: sequelize.BOOLEAN,
      ngay_hoa_don_tu_ngay_su_dung: sequelize.BOOLEAN,
      so_hd_thuoc_khoang_phat_hanh: sequelize.BOOLEAN,
    },
    { indexes: [{ name: 'invoiceId', unique: true, fields: ['invoiceId'] }] },
  );

  return InvoiceValidate;
};
