const sequelize = require('sequelize');
const { RISKY_COMPANY_TYPE } = require('../configs/constants/company.constant');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types/company').RiskyCompany> & import('../types/company').RiskyCompany>}
   */
  const RiskyCompany = Sequelize.define(
    'RiskyCompanies',
    {
      companyId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      companyName: {
        type: sequelize.STRING,
        allowNull: false,
      },
      taxCode: {
        type: sequelize.STRING,
        allowNull: false,
      },
      cqtCode: {
        type: sequelize.STRING,
      },
      cqtName: {
        type: sequelize.STRING,
      },
      note: {
        type: sequelize.STRING,
      },
      type: {
        type: sequelize.ENUM(...Object.values(RISKY_COMPANY_TYPE)),
        defaultValue: RISKY_COMPANY_TYPE.RUI_RO,
      },
    },
    {
      indexes: [
        {
          name: 'taxCode',
          unique: true,
          fields: ['taxCode'],
        },
      ],
    },
  );

  return RiskyCompany;
};
