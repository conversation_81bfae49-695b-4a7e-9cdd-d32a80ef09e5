const sequelize = require('sequelize');
const { SERVICE, GENDER } = require('../configs/constants/account.constant');
const { BASE_URL } = require('../configs/env');
const { getFullUrl } = require('../utils');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types/company').Company> & import('../types').Company>}
   */
  const Company = Sequelize.define(
    'Companies',
    {
      companyId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      companyName: { type: sequelize.STRING, allowNull: false },
      companyEmail: {
        type: sequelize.STRING,
      },
      taxCode: { type: sequelize.STRING, allowNull: false },
      companyLogo: {
        type: sequelize.STRING(512),
        get() {
          let raw = this.getDataValue('companyLogo');

          if (raw) {
            return getFullUrl(raw);
          }

          return null;
        },
      },
      businessPermitAddress: { type: sequelize.STRING },
      businessPermitDate: { type: sequelize.DATEONLY },
      companyAddress: { type: sequelize.STRING },
      companyPhone: { type: sequelize.STRING(16) },
      bankAccountNumber: { type: sequelize.STRING },
      bankName: { type: sequelize.STRING },
      contactPersonName: { type: sequelize.STRING },
      contactPersonEmail: { type: sequelize.STRING },
      contactPersonPhone: { type: sequelize.STRING },
      contactPersonPosition: { type: sequelize.STRING },
      contactPersonDoB: { type: sequelize.DATE },
      contactPersonGender: {
        type: sequelize.ENUM(...Object.values(GENDER)),
        allowNull: true,
      },
      fax: { type: sequelize.STRING },
      website: { type: sequelize.STRING },
      quantityPurchased: { type: sequelize.BIGINT.UNSIGNED, defaultValue: 0 },
      quantityRemain: { type: sequelize.BIGINT.UNSIGNED, defaultValue: 0 },
      dateExpiredPackage: { type: sequelize.DATE },
      service: sequelize.ENUM(...Object.values(SERVICE)),
      promotionExpired: { type: sequelize.DATE, allowNull: null },
      promotionRemain: { type: sequelize.INTEGER.UNSIGNED, allowNull: null },
      daKyHopDongNCC: {
        type: sequelize.BOOLEAN,
        get() {
          return 1;
        },
      },
      linkKyHopDong: sequelize.STRING,
      districtCode: sequelize.STRING,
      provinceCode: sequelize.STRING,
      taxAuthority: sequelize.STRING,
    },
    {
      indexes: [
        {
          name: 'taxCode',
          unique: true,
          fields: ['taxCode'],
        },
      ],
    },
  );

  return Company;
};
