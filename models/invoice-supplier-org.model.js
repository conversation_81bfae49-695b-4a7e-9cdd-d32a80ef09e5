const sequelize = require('sequelize');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').InvoiceSupplierOrg> & import('../types').InvoiceSupplierOrg>}
   */
  const InvoiceSupplierOrg = Sequelize.define(
    'InvoiceSupplierOrgs',
    {
      invoiceSupplierOrgId: {
        primaryKey: true,
        type: sequelize.INTEGER.UNSIGNED,
        autoIncrement: true,
      },
      address: sequelize.STRING,
      companyName: sequelize.STRING,
      lookupWebsite: sequelize.STRING,
      taxCode: { type: sequelize.STRING, allowNull: false },
      website: sequelize.STRING,
      note: sequelize.STRING,
    },
    {
      indexes: [{ name: 'taxCode', unique: true, fields: ['taxCode'] }],
    },
  );

  return InvoiceSupplierOrg;
};
