const sequelize = require('sequelize');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types/auto-adjust-invoice-from-email').Company> & import('../types').AutoTransferInvoiceFromEmail>}
   */
  const AutoTransferInvoiceFromEmail = Sequelize.define(
    'AutoTransferInvoiceFromEmail',
    {
      autoTransferInvoiceFromEmailId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      organizationId: sequelize.INTEGER.UNSIGNED,
      organizationDepartmentId: sequelize.INTEGER.UNSIGNED,

      organizationBId: sequelize.INTEGER.UNSIGNED,
      organizationDepartmentBId: sequelize.INTEGER.UNSIGNED,
    },
    {
      indexes: [
        {
          name: 'organizationId_organizationBId',
          unique: true,
          fields: ['organizationId', 'organizationBId'],
        },
        {
          name: 'organizationId_organizationDepartmentBId',
          unique: true,
          fields: ['organizationId', 'organizationDepartmentBId'],
        },
        {
          name: 'organizationDepartmentId_organizationBId',
          unique: true,
          fields: ['organizationDepartmentId', 'organizationBId'],
        },
        {
          name: 'organizationDepartmentId_organizationDepartmentBId',
          unique: true,
          fields: ['organizationDepartmentId', 'organizationDepartmentBId'],
        },
      ],
    },
  );

  return AutoTransferInvoiceFromEmail;
};
