const sequelize = require('sequelize');
const utils = require('../utils');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types/company').AutoTransferInvoice> & import('../types').AutoTransferInvoice>}
   */
  const AutoTransferInvoice = Sequelize.define(
    'AutoTransferInvoices',
    {
      autoTransferInvoiceId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      organizationId: sequelize.INTEGER.UNSIGNED,
      organizationDepartmentId: sequelize.INTEGER.UNSIGNED,

      autoTransferOrganizationId: sequelize.INTEGER.UNSIGNED,
      autoTransferOrganizationDepartmentId: sequelize.INTEGER.UNSIGNED,
      partnerCompanyIds: {
        type: sequelize.JSON,
        get() {
          let raw = this.getDataValue('partnerCompanyIds');
          if (utils.isJSONStringified(raw)) return JSON.parse(raw);
          return raw;
        },
      },
    },
    {
      indexes: [
        {
          name: 'organizationDepartmentId_autoTransferOrganizationId',
          unique: true,
          fields: ['organizationDepartmentId', 'autoTransferOrganizationId'],
        },
        {
          name: 'organizationDepartmentId_autoTransferOrganizationDepartmentId',
          unique: true,
          fields: [
            'organizationDepartmentId',
            'autoTransferOrganizationDepartmentId',
          ],
        },
        {
          name: 'organizationId_autoTransferOrganizationId',
          unique: true,
          fields: ['organizationId', 'autoTransferOrganizationId'],
        },
        {
          name: 'organizationId_autoTransferOrganizationDepartmentId',
          unique: true,
          fields: ['organizationId', 'autoTransferOrganizationDepartmentId'],
        },
      ],
    },
  );

  return AutoTransferInvoice;
};
