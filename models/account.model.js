const sequelize = require('sequelize');
const {
  ACCOUNT_LEVEL,
  GENDER,
  SERVICE,
} = require('../configs/constants/account.constant');
const {
  ERROR_INVALID_EMAIL,
  ERROR_INVALID_DATE,
  ERROR_INVALID_GENDER,
} = require('../configs/error.vi');
const { WS_CODE } = require('../configs/constants/websocket.constant');
const { BASE_URL } = require('../configs/env');
const { getFullUrl } = require('../utils');
const utils = require('../utils');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').Account> & import('../types').Account>}
   */
  const Account = Sequelize.define(
    'Accounts',
    {
      accountId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      accountLevel: {
        type: sequelize.ENUM(...Object.values(ACCOUNT_LEVEL)),
        allowNull: false,
      },
      avatar: {
        type: sequelize.STRING,
        get() {
          let raw = this.getDataValue('avatar');

          if (raw) {
            return getFullUrl(raw);
          } else {
            return raw;
          }
        },
      },
      phone: sequelize.STRING,
      metaAccountCode: {
        type: sequelize.STRING,
        comment: 'Kết nối với mã QLBH',
      },
      metaAccountServiceCode: {
        type: sequelize.STRING,
        comment: 'Kết nối với mã dịch vụ đang sử dụng QLBH',
      },
      companyId: { type: sequelize.INTEGER.UNSIGNED, allowNull: false },
      roleId: { type: sequelize.INTEGER.UNSIGNED },
      companyTitleId: { type: sequelize.INTEGER.UNSIGNED },
      metaAccessToken: { type: sequelize.TEXT('medium') },
      metaAccessTokenExpireDate: { type: sequelize.DATE },
      fullname: {
        type: sequelize.STRING,
        comment: 'Tên dùng trong Hoá đơn điện tử',
      },
      active: {
        type: sequelize.BOOLEAN,
        allowNull: false,
        /* TEST */
        defaultValue: true,
        comment: 'Ngừng sử dụng dịch vụ HDĐT',
      },
      companyUsername: { type: sequelize.STRING },
      email: { type: sequelize.STRING, allowNull: false },
      organizationDepartmentId: sequelize.INTEGER.UNSIGNED,
      organizationId: sequelize.INTEGER.UNSIGNED,
      activateToken: sequelize.STRING,
      password: { type: sequelize.STRING },
      salt: { type: sequelize.STRING(16) },
      cccdBackImg: {
        type: sequelize.STRING,
        get() {
          let raw = this.getDataValue('cccdBackImg');

          if (raw) {
            return getFullUrl(raw);
          } else {
            return raw;
          }
        },
      },
      cccdFrontImg: {
        type: sequelize.STRING,
        get() {
          let raw = this.getDataValue('cccdFrontImg');

          if (raw) {
            return getFullUrl(raw);
          } else {
            return raw;
          }
        },
      },
      cccdDate: sequelize.DATE,
      cccdNumber: sequelize.STRING,
      gender: {
        type: sequelize.ENUM(...Object.values(GENDER)),
        defaultValue: GENDER.OTHER,
      },
      cccdCreatePlace: sequelize.STRING,
      dob: sequelize.DATE,
      address: sequelize.STRING,
      resetPasswordToken: sequelize.STRING,
      expirationDateResetPasswordToken: sequelize.DATE,
      locked: { type: sequelize.BOOLEAN, defaultValue: false },
      loaiKhachHang: {
        type: sequelize.JSON,
        get() {
          let raw = this.getDataValue('loaiKhachHang');

          raw = raw ? raw : {};

          if (utils.isJSONStringified(raw)) {
            raw = JSON.parse(raw);
          }

          return raw;
        },
      },
    },
    {
      indexes: [
        {
          name: 'metaAccountCode_metaAccountServiceCode',
          unique: true,
          fields: ['metaAccountCode', 'metaAccountServiceCode'],
        },
        {
          name: 'companyId_metaAccountCode',
          unique: true,
          fields: ['companyId', 'metaAccountCode'],
        },
        {
          name: 'companyId_metaAccountServiceCode',
          unique: true,
          fields: ['companyId', 'metaAccountServiceCode'],
        },
        {
          name: 'companyId_email',
          unique: true,
          fields: ['companyId', 'email'],
        },
        {
          name: 'companyId_companyUsername',
          unique: true,
          fields: ['companyId', 'companyUsername'],
        },
      ],
    },
  );

  Account.afterFind((instances, options) => {
    if (Array.isArray(instances)) {
      instances.forEach(instance => {
        instance.setDataValue(
          'isAdmin',
          instance.accountLevel == ACCOUNT_LEVEL.COMPANY_ADMIN,
        );
      });
    } else {
      let instance = instances;

      if (instance) {
        instance.setDataValue(
          'isAdmin',
          instance.accountLevel == ACCOUNT_LEVEL.COMPANY_ADMIN,
        );

        if (instance.Organization) {
          instance.setDataValue('taxCode', instance.Organization.taxCode);
        }

        if (instance.OrganizationDepartment) {
          instance.setDataValue(
            'taxCode',
            instance.OrganizationDepartment.OrganizationBranch.taxCode,
          );
        }
      }
    }
  });

  Account.afterUpdate(async instance => {
    const { emitSocketEvent } = require('../utils/websocket.helper');

    if (instance.previous().organizationId != instance.organizationId) {
      emitSocketEvent({
        message: { code: WS_CODE.CODE_ORGANIZATION_CHANGED, result: 'success' },
        accountId: instance.accountId,
        newNotification: true,
      });
    }

    if (
      instance.previous().organizationDepartmentId !=
      instance.organizationDepartmentId
    ) {
      emitSocketEvent({
        message: { code: WS_CODE.CODE_ORGANIZATION_CHANGED, result: 'success' },
        accountId: instance.accountId,
        newNotification: true,
      });
    }

    if (!instance.previous().locked && !!instance.locked) {
      const { AccessToken } = require('.');

      await AccessToken.destroy({ where: { accountId: instance.accountId } });
    }
  });

  return Account;
};
