const sequelize = require('sequelize');
const {
  EMAIL_TYPE,
  EMAIL_STATUS,
} = require('../configs/constants/other.constant');
const { LOAI_KHACH_HANG } = require('../configs/constants/account.constant');

/**
 *
 *
 * @param {sequelize.Sequelize} Sequelize
 *
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').EmailHistory> & import('../types').EmailHistory>}
   */
  const EmailHistory = Sequelize.define('EmailHistories', {
    emailHistoryId: {
      primaryKey: true,
      autoIncrement: true,
      type: sequelize.INTEGER.UNSIGNED,
    },
    emailType: {
      type: sequelize.ENUM(...Object.values(EMAIL_TYPE)),
      defaultValue: EMAIL_TYPE.OTHER,
    },
    emailTitle: { type: sequelize.STRING, allowNull: false },
    emailContent: { type: sequelize.TEXT('long'), allowNull: false },
    emailRecipients: {
      type: sequelize.JSON,
      get() {
        let raw = this.getDataValue('emailRecipients');

        if (!Array.isArray(raw)) return [];

        return raw;
      },
    },
    sentTime: {
      type: sequelize.DATE,
      defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    status: {
      type: sequelize.ENUM(...Object.values(EMAIL_STATUS)),
      allowNull: false,
      defaultValue: EMAIL_STATUS.DELIVERED,
    },
    messageId: sequelize.STRING,
    loaiKhachHang: {
      type: sequelize.ENUM(...Object.values(LOAI_KHACH_HANG)),
      defaultValue: LOAI_KHACH_HANG.QUAN_LY_HOA_DON,
      allowNull: false,
    },
    accountId: { type: sequelize.INTEGER.UNSIGNED },
  });

  return EmailHistory;
};
