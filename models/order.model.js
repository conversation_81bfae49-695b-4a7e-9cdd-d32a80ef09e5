const sequelize = require('sequelize');
const {
  TYPE_PAYMENT,
  STATUS_ORDER,
  POSITION,
} = require('../configs/constants/order.constant');
const { sendEmailResultOrder } = require('../utils/order.helper');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').Order> & import('../types').Order>}
   */
  const Order = Sequelize.define(
    'Orders',
    {
      orderId: {
        type: sequelize.INTEGER.UNSIGNED,
        primaryKey: true,
        autoIncrement: true,
      },
      accountId: { type: sequelize.INTEGER.UNSIGNED, allowNull: false },
      companyId: { type: sequelize.INTEGER.UNSIGNED, allowNull: false },
      // country: { type: sequelize.STRING },
      // city: { type: sequelize.STRING },
      // province: { type: sequelize.STRING },
      // wards: { type: sequelize.STRING },
      taxCode: { type: sequelize.STRING },
      companyName: { type: sequelize.STRING },
      address: { type: sequelize.STRING },
      // presidentName: { type: sequelize.STRING },
      // presidentPhone: { type: sequelize.STRING },
      // presidentEmail: { type: sequelize.STRING },
      // presidentPosition: {
      //   type: sequelize.ENUM(...Object.values(PRESIDENT_POSITION)),
      // },
      orderName: { type: sequelize.STRING },
      orderPhone: { type: sequelize.STRING },
      orderEmail: { type: sequelize.STRING },
      orderPosition: { type: sequelize.STRING },
      invoiceCustomerName: { type: sequelize.STRING },
      invoiceCustomerPhone: { type: sequelize.STRING },
      invoiceCustomerEmail: { type: sequelize.STRING },
      invoiceCustomerAddress: { type: sequelize.STRING },
      voucherCode: { type: sequelize.STRING },
      // voucher: { type: sequelize.JSON },
      totalCost: { type: sequelize.BIGINT },
      promotion: { type: sequelize.INTEGER },
      totalPayment: { type: sequelize.BIGINT },
      typePayment: { type: sequelize.ENUM(...Object.values(TYPE_PAYMENT)) },
      statusOrder: { type: sequelize.ENUM(...Object.values(STATUS_ORDER)) },

      metaPaymentCode: { type: sequelize.STRING },

      orderCode: { type: sequelize.STRING, allowNull: false },
      clientOrderCode: { type: sequelize.STRING },

      transactionNo: sequelize.STRING,
      bankTranNo: sequelize.STRING,
    },
    {
      indexes: [
        {
          name: 'clientOrderCode',
          unique: true,
          fields: ['clientOrderCode'],
        },
        {
          name: 'orderCode',
          unique: true,
          fields: ['orderCode'],
        },
      ],
    },
  );

  Order.afterUpdate(async instance => {
    if (instance.statusOrder !== instance.previous().statusOrder) {
      // console.log(instance.statusOrder);
      /* gui mail kq  */
      if (instance.statusOrder == STATUS_ORDER.COMPLETE) {
        await sendEmailResultOrder({ order: instance });
        /* mail thanh cong */
      } else {
        if (instance.statusOrder == STATUS_ORDER.FAILED) {
          await sendEmailResultOrder({ order: instance });
        }
      }
    }
  });
  return Order;
};
