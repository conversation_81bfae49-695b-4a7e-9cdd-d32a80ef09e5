const sequelize = require('sequelize');

/**
 *
 * @param {sequelize.Sequelize} Sequelize
 */
module.exports = Sequelize => {
  /**
   * @type {sequelize.ModelStatic<sequelize.Model<import('../types').InvoiceHistory> & import('../types').InvoiceHistory>}
   */
  const InvoiceHistories = Sequelize.define('InvoiceHistories', {
    invoiceHistoryId: {
      primaryKey: true,
      autoIncrement: true,
      type: sequelize.INTEGER.UNSIGNED,
    },
    invoiceId: { type: sequelize.INTEGER.UNSIGNED, allowNull: false },
    paymentDate: sequelize.DATE, // vietinvoice id
    paymentMoney: sequelize.INTEGER,
    paymentPersonName: sequelize.STRING, //người thanh toán
    noDatePayment: sequelize.INTEGER, //số ngày phải thanh toán tiếp theo
  });

  return InvoiceHistories;
};
