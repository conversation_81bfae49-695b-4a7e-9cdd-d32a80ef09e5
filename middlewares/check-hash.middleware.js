const { QLBH_SECRET_KEY } = require('../configs/env');
const { ERROR_PERMISSION_DENIED } = require('../configs/error.vi');
const { ClientCode } = require('../models');
const utils = require('../utils');
const { checkPathPrefix, QLBH_PATHS } = require('./authorization.middleware');

/**
 * use after fileUpload.middleware if called from client code
 * @type {import('../types').RequestHandler}
 */
module.exports = async (req, res, next) => {
  let path = req.baseUrl;

  if (checkPathPrefix(path, QLBH_PATHS)) {
    let method = req.method;

    let secretKey = QLBH_SECRET_KEY;

    req.secretKey = secretKey;

    let { hash, ...data } = (method == 'GET' ? req.query : req.body) ?? {};

    if (!hash) {
      let error = new Error(ERROR_PERMISSION_DENIED);
      error.status = 401;

      return next(error);
    }

    data = utils.sortKeys(data);

    let hmac = utils.hmac(JSON.stringify(data), secretKey);

    if (hmac !== hash) {
      let error = new Error(ERROR_PERMISSION_DENIED);
      error.status = 401;

      return next(error);
    }
  }

  return next();
};
