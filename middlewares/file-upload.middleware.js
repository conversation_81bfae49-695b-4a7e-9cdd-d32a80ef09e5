const fileUpload = require('express-fileupload');
const deleteEmptyBodyMiddleware = require('./delete-empty-body.middleware');
const { replaceInString } = require('../utils');
const { ERROR_FILE_SIZE_LIMIT } = require('../configs/error.vi');
const parseJsonMiddleware = require('./parse-json.middleware');
const { TEMP_UPLOAD_DIR } = require('../configs/env');

/**
 *
 * @param {fileUpload.Options=} config default limits is 2MB;
 */
module.exports = (config = {}) => {
  config = {
    createParentPath: true,
    useTempFiles: true,
    tempFileDir: TEMP_UPLOAD_DIR,
    // uriDecodeFileNames: true,
    defParamCharset: 'utf8',
    abortOnLimit: true,
    limits: { fileSize: 2048 * 1024 },
    limitHandler(req, res, next) {
      let error = new Error(
        replaceInString(ERROR_FILE_SIZE_LIMIT, { fileSize: 2 }),
      );

      next(error);
    },
    ...config,
  };

  return [fileUpload(config), deleteEmptyBodyMiddleware, parseJsonMiddleware];
};
