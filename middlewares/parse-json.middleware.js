const utils = require('../utils');

/**
 * @type {import('../types').RequestHandler}
 */
module.exports = async (req, res, next) => {
  if (req.body) {
    for (let [key, value] of Object.entries(req.body)) {
      if (utils.isJSONStringified(value)) {
        req.body[key] = JSON.parse(value);
      }
    }
  }

  if (req.query) {
    for (let [key, value] of Object.entries(req.query)) {
      if (utils.isJSONStringified(value)) {
        req.query[key] = JSON.parse(value);
      }
    }
  }

  next();
};
