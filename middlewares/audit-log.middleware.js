const { <PERSON><PERSON>ars<PERSON> } = require('ua-parser-js');
const { AuditLog } = require('../models');
const { getPublicAccountAttributes } = require('../utils/account.helper');
const _ = require('lodash');

/**
 * @type {import('../types').RequestHandler}
 */
module.exports = async (req, res, next) => {
  const parser = new UAParser(req.headers['user-agent']);

  let { device, os, browser, engine, ua } = parser.getResult();

  if (os.name || device.name) {
    if (req.account && req.path !== '/api/v1') {
      // console.log(
      //   '[AudiLogMiddleware]',
      //   'Request from',
      //   {
      //     accountId: req.account.accountId,
      //     fullname: req.account.fullname,
      //   },
      //   { device, os, browser, engine, ip: req.ip, ua },
      // );

      await AuditLog.create({
        accountId: req.account.accountId,
        ipAddress: req.ip,
        action: `API: ${req.path}`,
        description: '',
        organization: '',
        extraData: {
          from: _.pick(req.account, getPublicAccountAttributes()),
        },
      });
    } else {
      // console.log('[AudiLogMiddleware]', 'no-authorized');
    }
  } else {
    // console.log(
    //   '[AudiLogMiddleware]',
    //   'This is not request from any device, browser',
    // );
  }

  next();
};
