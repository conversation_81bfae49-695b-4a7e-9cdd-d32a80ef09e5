const {
  CLIENT_BASE_URL,
  DEV_CLIENT_BASE_URL,
  PROD_CLIENT_BASE_URL,
  LOCAL_CLIENT_BASE_URL,
  ADMIN_CLIENT_BASE_URL,
  DEV_ADMIN_CLIENT_BASE_URL,
  LOCAL_ADMIN_CLIENT_BASE_URL,
} = require('../configs/env');
const { ERROR_NOT_FOUND } = require('../configs/error.vi');
const _ = require('lodash');

/**
 * <PERSON><PERSON><PERSON> tra referer của nơi gọi có đc phép
 * @type {import('../types').RequestHandler}
 */
module.exports = (req, res, next) => {
  req.ip = req.ip || req.headers['x-forwarded-for'] || req.socket.remoteAddress;

  let referer = req.headers.referer;

  next();
};
