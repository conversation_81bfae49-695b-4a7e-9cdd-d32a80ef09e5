const { REQ_ERROR_CODE } = require('../configs/constants/other.constant');
const {
  PATH_ACCOUNT_LOGOUT,
} = require('../configs/constants/server-path.constant');
const { ERROR_INTERNAL } = require('../configs/error.vi');
const { isVietnamese } = require('../utils');

/**
 * @type {import('../types').ErrorRequestHandler}
 */
module.exports = (err, req, res, next) => {
  console.warn('[ERROR]', req.path, err.stack, err.message);

  if (res.writableEnded) {
    console.warn(
      '[HEADS UP!!] THIS ERROR occured after response',
      err.stack,
      err.message,
    );
    return;
  }

  /* if logout with false token, alright */
  if (req.path.startsWith(PATH_ACCOUNT_LOGOUT)) {
    res.send({ result: 'success' });

    return;
  }

  if (!isVietnamese(err.message)) {
    err.message = ERROR_INTERNAL;
  }

  res
    .status(err.status ?? REQ_ERROR_CODE)
    .send({ result: 'failed', reason: err.message });
};
