const _ = require('lodash');
const {
  ROLE_PERMISSION_ROUTES,
  PERMISSION_ROUTES,
} = require('../configs/constants/role-route.constant');
const { ERROR_PERMISSION_DENIED } = require('../configs/error.vi');

/**
 * @type {import('../types').RequestHandler}
 */
module.exports = async (req, res, next) => {
  // kiem tra permission dua tren role cua account
  const path = req.path;
  if (
    !PERMISSION_ROUTES.find(
      route =>
        route === path ||
        (route === path.split('/').slice(0, 5).join('/') &&
          path.split('/').length === 6),
    )
  ) {
    next();
    return;
  }
  if (req?.account?.accountLevel === 'COMPANY_ADMIN') {
    next();
    return;
  }
  const permissionCodes = req?.account?.Role?.permissionCodes;
  if (!permissionCodes || permissionCodes?.length === 0) {
    let error = new Error(ERROR_PERMISSION_DENIED);
    error.status = 401;
    return next(error);
  }
  const isAuthorized = permissionCodes?.find(
    permissionCode =>
      ROLE_PERMISSION_ROUTES[permissionCode]?.includes(path) ||
      (ROLE_PERMISSION_ROUTES[permissionCode]?.includes(
        path.split('/').slice(0, 5).join('/'),
      ) &&
        path.split('/').length === 6),
  );
  if (!isAuthorized) {
    let error = new Error(ERROR_PERMISSION_DENIED);
    error.status = 401;
    return next(error);
  }

  next();
};
