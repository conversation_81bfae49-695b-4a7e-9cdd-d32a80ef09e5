const {
  PATH_ACCOUNT_LOGIN,
  PATH_ACCOUNT_REGISTER,
  PATH_ACCOUNT_ACTIVATE,
  PATH_FIND_DEALER_BY_REFERER_CODE,
  PATH_MISC,
  PATH_COMPANY_FIND_BY_TAXCODE,
  PATH_COMPANY_FIND_BY_TAXCODES,
  PATH_COMMENT_CUSTOMER_FIND,
  PATH_CONTACT_CUSTOMER_CREATE,
  PATH_PACKAGE_FIND,
  PATH_PACKAGE_DETAIL,
  PATH_CONFIG_FIND,
  PATH_TEST,
  PATH_ACCOUNT_REQUEST_RESET_PASSWORD,
  PATH_ACCOUNT_RESET_PASSWORD,
  PATH_VNPAY_IPN,
  PATH_FAQ_DETAIL,
  PATH_FAQ_FIND,
  PATH_INSTRUCTION_FIND,
  PATH_QLBH,
  PATH_RISKY_COMPANY_FIND,
  PATH_VALIDATE_ACCESS_TOKEN,
  PATH_FIND_DEALER_BY_DEALER_CODE,
  PATH_EMAIL_HISTORY_RESEND,
  PATH_EMAIL_HISTORY_FIND,
  PATH_EMAIL_HISTORY_DELETE,
} = require('../configs/constants/server-path.constant');
const { QLBH_SECRET_KEY } = require('../configs/env');
const {
  ERROR_PERMISSION_DENIED,
  ERROR_TOKEN_EXPIRED,
  ERROR_USER_NOT_ACTIVE,
  ERROR_ACCOUNT_LOCKED,
} = require('../configs/error.vi');
const {
  AccessToken,
  Account,
  AccountOrganizationAccess,
  Role,
  Organization,
  OrganizationDepartment,
} = require('../models');
const utils = require('../utils');
const { now } = require('../utils');
const jwtHelper = require('../utils/jwt.helper');

const NO_AUTH_PATHS = [
  PATH_TEST,
  PATH_ACCOUNT_LOGIN,
  PATH_ACCOUNT_REGISTER,
  PATH_ACCOUNT_ACTIVATE,
  PATH_FIND_DEALER_BY_REFERER_CODE,
  PATH_MISC,
  PATH_COMPANY_FIND_BY_TAXCODE,
  PATH_COMMENT_CUSTOMER_FIND,
  PATH_CONTACT_CUSTOMER_CREATE,
  PATH_PACKAGE_FIND,
  PATH_PACKAGE_DETAIL,
  PATH_CONFIG_FIND,
  PATH_TEST,
  PATH_ACCOUNT_REQUEST_RESET_PASSWORD,
  PATH_ACCOUNT_RESET_PASSWORD,
  PATH_VNPAY_IPN,
  PATH_FAQ_DETAIL,
  PATH_FAQ_FIND,
  PATH_INSTRUCTION_FIND,
  PATH_COMPANY_FIND_BY_TAXCODES,
  PATH_RISKY_COMPANY_FIND,
  PATH_VALIDATE_ACCESS_TOKEN,
  PATH_FIND_DEALER_BY_DEALER_CODE,
  PATH_EMAIL_HISTORY_RESEND,
  PATH_EMAIL_HISTORY_FIND,
  PATH_EMAIL_HISTORY_DELETE,
];

/**
 * Bên QLBH sẽ gọi sang
 */
const QLBH_PATHS = [PATH_QLBH];

/**
 * @type {import('../types').RequestHandler}
 */
module.exports = async (req, res, next) => {
  let path = req.path;

  // console.log(
  //   now() + ` - [HTTP] Processing request [${req.method}] ${path}...`,
  //   req.ip,
  // );

  if (checkPathPrefix(path, NO_AUTH_PATHS)) {
    next();
    return;
  }

  if (checkPathPrefix(path, QLBH_PATHS)) {
    /* check in each api handler */
    if (req.headers['content-type']?.startsWith('multipart/form-data')) {
      next();
      return;
    }

    let method = req.method;

    let { hash, ...data } = (method == 'GET' ? req.query : req.body) ?? {};

    if (!hash) {
      let error = new Error(ERROR_PERMISSION_DENIED);
      error.status = 401;

      return next(error);
    }

    data = utils.sortKeys(data);

    let hmac = utils.hmac(JSON.stringify(data), QLBH_SECRET_KEY);

    if (hmac !== hash) {
      let error = new Error(ERROR_PERMISSION_DENIED);
      error.status = 401;

      return next(error);
    }

    return next();
  }

  /* use access token */
  let tokenStr = req.headers['authorization'];
  let accessToken =
    tokenStr != null ? tokenStr.match('(?<=Bearer).*$')?.[0] : null;

  if (accessToken) {
    accessToken = accessToken.trim();
    if (!jwtHelper.verifyJWTToken(accessToken)) {
      let error = new Error(ERROR_PERMISSION_DENIED);
      error.status = 401;
      return next(error);
    }

    if (jwtHelper.isJWTTokenExpired(accessToken)) {
      let error = new Error(ERROR_TOKEN_EXPIRED);
      error.status = 401;
      return next(error);
    }

    /**
     * @type {import('../types').Account}
     */
    let account = jwtHelper.decodeJwtToken(accessToken);
    if (
      !(await AccessToken.findOne({
        where: {
          accessToken: utils.hashSHA256(accessToken),
          accountId: account.accountId,
        },
      }))
    ) {
      let error = new Error(ERROR_TOKEN_EXPIRED);
      error.status = 401;
      return next(error);
    }

    req.account = (
      await Account.findOne({
        where: { accountId: account.accountId },
        include: [
          { model: AccountOrganizationAccess },
          { model: Role },
          { model: Organization },
          {
            model: OrganizationDepartment,
            include: [{ association: 'OrganizationBranch' }],
          },
        ],
      })
    ).toJSON();

    if (!req.account.active) {
      let error = new Error(ERROR_USER_NOT_ACTIVE);
      error.status = 403;
      return next(error);
    }

    if (!!req.account.locked) {
      let error = new Error(ERROR_ACCOUNT_LOCKED);
      error.status = 403;
      return next(error);
    }
    req.accessToken = accessToken;
  } else {
    let error = new Error(ERROR_PERMISSION_DENIED);
    error.status = 403;
    return next(error);
  }

  next();
};

/**
 *
 * @param {string} path
 * @param {string[]} prefixes
 */
function checkPathPrefix(path, prefixes) {
  return prefixes.some(
    prefix => path === prefix || path.startsWith(`${prefix}/`),
  );
}

module.exports.checkPathPrefix = checkPathPrefix;
module.exports.QLBH_PATHS = QLBH_PATHS;
