const Imap = require('imap');
const { simpleParser } = require('mailparser');
const moment = require('moment');
const { Invoice, Mail } = require('../models');

const imapConfig = {
  user: '<EMAIL>',
  password: '123456',
  host: 'mail.i-ca.vn',
  port: 993,
  tls: true,
};
module.exports = {
  getEmails() {
    console.log('start get mail');
    try {
      const imap = new Imap(imapConfig);
      imap.once('ready', () => {
        imap.openBox('INBOX', false, () => {
          imap.search(
            ['ALL', ['SINCE', moment().subtract(1, 'days').toDate()]],
            (err, results) => {
              if (err) throw err;
              else if (!results || !results.length) {
                console.log(
                  "The server didn't find any emails matching the specified criteria",
                );
              } else {
                const f = imap.fetch(results, {
                  bodies: '',
                  markSeen: false,
                });
                f.on('message', msg => {
                  msg.on('body', stream => {
                    // console.log('stream', stream);
                    simpleParser(stream, async (_err, parsed) => {
                      // const {from, subject, textAsHtml, text} = parsed;
                      // console.log('=============>',{
                      //   subject: parsed.subject,
                      //   time: moment(parsed.date).format('YYYY/MM/DD HH:mm:ss'),
                      //   attachments: parsed.attachments,
                      //   from: parsed.from,
                      //   html: parsed.html,
                      //   id: parsed.messageId,
                      // });
                      let mail = await Mail.create({
                        organizationId: 24,
                        subject: parsed.subject,
                        senderEmail: parsed.from.text,
                        receiveDate: moment(parsed.date).format(
                          'YYYY/MM/DD HH:mm:ss',
                        ),
                        content: parsed.html,
                        // attachment: parsed.attachments,
                        messageId: parsed.messageId,
                      });
                      console.log(mail);
                      /* Make API call to save the data
                     Save the retrieved data into a database.
                     E.t.c
                  */
                    });
                  });
                  msg.once('attributes', attrs => {
                    const { uid } = attrs;
                    imap.addFlags(uid, ['\\Seen'], () => {
                      // Mark the email as read after reading it
                      console.log('Marked as read!');
                    });
                  });
                });
                f.once('error', ex => {
                  console.log(ex);
                  // return Promise.reject(ex);
                });
                f.once('end', () => {
                  console.log('Done fetching all messages!');
                  imap.end();
                });
              }
            },
          );
        });
      });

      imap.once('error', err => {
        console.log(err);
      });

      imap.once('end', () => {
        console.log('Connection ended');
      });

      imap.connect();
    } catch (ex) {
      console.log('an error occurred');
    }
  },
};
