# Qlbh Backend

## QUY CHUẨN CODE

- M<PERSON>i biến constant phải được khai báo trong constants.js. <PERSON><PERSON> dụ

```javascript
let a = 'VALUE_A'; // false
let b = constansts.VALUE_A; // true
```

- <PERSON><PERSON><PERSON> định nghĩa mọi kiểu dữ liệu, mọi trường thông tin có thể. <PERSON><PERSON> dụ:

```javascript
\\ account.ts
export interface Account {
    accountId: number
}

\\ index.js
/**
 * @type {import('../types').Account}
 */
let account = {}
```

- <PERSON><PERSON> viết API cần khai báo kiểu cho hàm xử lý API đó. VD:

```javascript
/**
 * @type {import('../types').RequestHandler}
 */
async login(req, res, next) {}
```

## Tài khoản test

Mã số thuế: **********
Mật khẩu FE: 123456
M<PERSON>t khẩu đã băm: 8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92
